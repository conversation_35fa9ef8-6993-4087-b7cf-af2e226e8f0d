package org.aihealth.ineck.googlepay

import android.app.Activity
import android.content.Context
import com.android.billingclient.api.BillingClient
import com.android.billingclient.api.BillingClientStateListener
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.BillingResult
import com.android.billingclient.api.ConsumeParams
import com.android.billingclient.api.ProductDetails
import com.android.billingclient.api.Purchase
import com.android.billingclient.api.PurchasesUpdatedListener
import com.android.billingclient.api.QueryProductDetailsParams
import com.android.billingclient.api.consumePurchase
import com.android.billingclient.api.queryProductDetails
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import org.aihealth.ineck.util.AppCoroutineScope
import org.aihealth.ineck.util.LogUtil
import kotlin.coroutines.resume

class BillingHelper(private val context: Context) {
    private val purchasesUpdatedListener =
        PurchasesUpdatedListener { billingResult: BillingResult, purchases: List<Purchase>? ->
            LogUtil.e("purchasesUpdatedListener: $billingResult")
            LogUtil.e("purchasesUpdatedListener: $purchases")
            when (billingResult.responseCode) {
                BillingClient.BillingResponseCode.OK -> {
                    if (!purchases.isNullOrEmpty()) {
                        purchases.forEach {
                            when (it.purchaseState) {
                                Purchase.PurchaseState.PURCHASED -> {
                                    // Handle successful purchase
                                    handlePurchase(it)
                                }

                                Purchase.PurchaseState.PENDING -> {
                                    // Handle pending purchase
                                }

                                Purchase.PurchaseState.UNSPECIFIED_STATE -> {
                                    // Handle unspecified state
                                }
                            }
                        }
                    }
                }

                BillingClient.BillingResponseCode.USER_CANCELED -> {
                    //用户取消支付
                }

                else -> {

                }
            }
        }


    private lateinit var billingClient: BillingClient

    suspend fun startConnection(context: Context) {
        billingClient = buildBillingClient(context, purchasesUpdatedListener)
        startConnection(billingClient)
    }

    private suspend fun startConnection(billingClient: BillingClient): BillingResult? {
        return withContext(Dispatchers.Default) {
            if (billingClient.isReady) {
                return@withContext null
            }
            return@withContext suspendCancellableCoroutine { continuation ->
                billingClient.startConnection(object : BillingClientStateListener {
                    override fun onBillingSetupFinished(billingResult: BillingResult) {
                        if (!continuation.isCompleted) {
                            continuation.resume(value = billingResult)
                        }
                    }

                    override fun onBillingServiceDisconnected() {
                        if (!continuation.isCompleted) {
                            continuation.resume(value = null)
                        }
                    }
                })
            }
        }
    }

    private fun buildBillingClient(
        context: Context,
        listener: PurchasesUpdatedListener
    ): BillingClient {
        return BillingClient.newBuilder(context)
            .setListener(listener)
//            .enablePendingPurchases()
            .build()
    }

    suspend fun processPurchases(onPurchaseComplete: (ProductDetails?) -> Unit) {
        // 检查 billingClient 是否已经初始化和连接
        if (!::billingClient.isInitialized) {
            LogUtil.e("BillingHelper: BillingClient not initialized in processPurchases")
            onPurchaseComplete(null)
            return
        }
        
        if (!billingClient.isReady) {
            LogUtil.e("BillingHelper: BillingClient not ready in processPurchases")
            onPurchaseComplete(null)
            return
        }
        
        val productDetailsList = queryProductDetails()
        if (productDetailsList.isNullOrEmpty()) {
            onPurchaseComplete(null)
            return
        }
        onPurchaseComplete(productDetailsList.firstOrNull())
    }

    private suspend fun queryProductDetails(): List<ProductDetails>? {
        // 添加额外检查以确保安全
        if (!::billingClient.isInitialized || !billingClient.isReady) {
            LogUtil.e("BillingHelper: BillingClient not ready for queryProductDetails")
            return null
        }
        
        //查询一次性商品
        return queryProductDetails(
            billingClient = billingClient,
            productIdList = setOf("com.aihealth.re.membership.monthly"),
            productType = BillingClient.ProductType.INAPP
        )
    }


    private suspend fun queryProductDetails(
        billingClient: BillingClient,
        productIdList: Set<String>,
        productType: String
    ): List<ProductDetails>? {
        return withContext(context = Dispatchers.Default) {
            if (!billingClient.isReady || productIdList.isEmpty()) {
                return@withContext null
            }
            val productDetailParamsList = productIdList.map {
                QueryProductDetailsParams
                    .Product
                    .newBuilder()
                    .setProductId(it)
                    .setProductType(productType)
                    .build()
            }
            val queryProductDetailsParams = QueryProductDetailsParams
                .newBuilder()
                .setProductList(productDetailParamsList)
                .build()
            val productDetailsResult = billingClient.queryProductDetails(queryProductDetailsParams)
            productDetailsResult.productDetailsList
        }
    }

    private fun handlePurchase(purchase: Purchase) {
        // Verify the purchase and consume it if it's a consumable product
        if (purchase.purchaseState == Purchase.PurchaseState.PURCHASED) {
            // Consume the purchase
            AppCoroutineScope.scope.launch {
                val isConsumed = consumePurchase(billingClient, purchase)
                if (isConsumed) {
                    LogUtil.e("consumePurchase: $purchase")
                    // Handle successful consumption
                }
            }
        }
    }

    suspend fun launchPurchaseFlow(activity: Activity, productDetails: ProductDetails): BillingResult {
        return withContext(Dispatchers.Main) {
            // 检查 billingClient 是否已经初始化和连接
            if (!::billingClient.isInitialized) {
                LogUtil.e("BillingHelper: BillingClient not initialized")
                return@withContext BillingResult.newBuilder()
                    .setResponseCode(BillingClient.BillingResponseCode.SERVICE_UNAVAILABLE)
                    .setDebugMessage("BillingClient not initialized")
                    .build()
            }
            
            if (!billingClient.isReady) {
                LogUtil.e("BillingHelper: BillingClient not ready")
                return@withContext BillingResult.newBuilder()
                    .setResponseCode(BillingClient.BillingResponseCode.SERVICE_DISCONNECTED)
                    .setDebugMessage("BillingClient not ready")
                    .build()
            }
            
            // 检查 Activity 是否有效
            if (activity.isFinishing || activity.isDestroyed) {
                LogUtil.e("BillingHelper: Activity is finishing or destroyed")
                return@withContext BillingResult.newBuilder()
                    .setResponseCode(BillingClient.BillingResponseCode.ERROR)
                    .setDebugMessage("Activity is finishing or destroyed")
                    .build()
            }
            
            try {
                val productDetailsParams = BillingFlowParams
                    .ProductDetailsParams
                    .newBuilder()
                    .setProductDetails(productDetails)
                    .build()
                val billingFlowParams = BillingFlowParams
                    .newBuilder()
                    .setProductDetailsParamsList(listOf(productDetailsParams))
                    .build()
                
                val result = billingClient.launchBillingFlow(activity, billingFlowParams)
                LogUtil.e("BillingHelper: launchBillingFlow result = $result")
                return@withContext result
            } catch (e: Exception) {
                LogUtil.e("BillingHelper: Exception in launchPurchaseFlow: ${e.message}")
                return@withContext BillingResult.newBuilder()
                    .setResponseCode(BillingClient.BillingResponseCode.ERROR)
                    .setDebugMessage("Exception: ${e.message}")
                    .build()
            }
        }
    }

    private suspend fun launchBilling(
        activity: Activity,
        billingClient: BillingClient,
        productDetails: ProductDetails
    ): BillingResult {
        return withContext(context = Dispatchers.Main.immediate) {
            val productDetailsParams = BillingFlowParams
                .ProductDetailsParams
                .newBuilder()
                .setProductDetails(productDetails)
                .build()
            val billingFlowParams = BillingFlowParams
                .newBuilder()
                .setProductDetailsParamsList(listOf(productDetailsParams))
                .build()
            billingClient.launchBillingFlow(activity, billingFlowParams)
        }
    }

    private suspend fun consumePurchase(
        billingClient: BillingClient,
        purchase: Purchase
    ): Boolean {
        return withContext(context = Dispatchers.Default) {
            if (purchase.purchaseState != Purchase.PurchaseState.PURCHASED) {
                return@withContext false
            }
            if (!billingClient.isReady) {
                return@withContext false
            }
            val consumeParams = ConsumeParams
                .newBuilder()
                .setPurchaseToken(purchase.purchaseToken)
                .build()
            val consumeResult = billingClient.consumePurchase(consumeParams)
            consumeResult.billingResult.responseCode == BillingClient.BillingResponseCode.OK
        }
    }
}