package org.aihealth.ineck.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.widget.TextView
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import org.aihealth.ineck.BaseApplication
import timber.log.Timber


class WXEntryActivity : Activity(), IWXAPIEventHandler {
    protected override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val textView = TextView(this)
        setContentView(textView)
        processIntent(intent)
    }

    protected override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        processIntent(intent)
    }

    /**
     * 处理微信回调结果
     *
     * @param intent
     */
    private fun processIntent(intent: Intent) {
        BaseApplication.instance.wxapi.handleIntent(intent, this)
    }

    /**
     * 微信请求我们
     */
    override fun onReq(baseReq: BaseReq) {
        Timber.d("onReq: ${baseReq}")
        finish()
    }

    /**
     * 微信响应
     */
    override fun onResp(baseResp: BaseResp) {
        Timber.d("onResp: ${baseResp}")
        finish()
        if (baseResp.type == ConstantsAPI.COMMAND_SENDAUTH) {
            //登录回调
            //发送通知
            BaseApplication.instance.processWechatStatusChanged?.let { it(baseResp) }
        }
    }

}
