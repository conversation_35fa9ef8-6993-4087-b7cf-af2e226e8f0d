package org.aihealth.ineck.wxapi

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.google.gson.Gson
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.toJson

class WXPayEntryActivity : AppCompatActivity(), IWXAPIEventHandler {


    companion object{
        const val TAG = "WXPayEntryActivity"
        private lateinit var  wechatCallback: WechatCallback
    }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val api = WXAPIFactory.createWXAPI(this, getString(R.string.APP_ID_WX))
        api.handleIntent(intent, this)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        val api = WXAPIFactory.createWXAPI(this, getString(R.string.APP_ID_WX))
        api.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq) {
        // 处理微信请求
    }

    override fun onResp(resp: BaseResp) {
        LogUtil.i("resp: " + resp.toJson())
        val gson = Gson()
        try {
            wechatCallback.call(gson.fromJson(resp.toJson(), WechatPayResult::class.java))

        }finally {
            finish()
        }
//        when (resp.errCode) {
//            BaseResp.ErrCode.ERR_OK -> {
//                LogUtil.i("WX onResp: " + "支付成功")
//            }
//            BaseResp.ErrCode.ERR_COMM -> {
//                LogUtil.i("WX onResp: " + "支付失败")
//            }
//            BaseResp.ErrCode.ERR_USER_CANCEL -> {
//                LogUtil.i("WX onResp: " + "支付取消")
//            }
//        }
    }

    override fun onStop() {
        super.onStop()
        wechatCallback.call(WechatPayResult("", "", -1))
    }

    fun setWechatCallback(wechatCallback: WechatCallback) {
        WXPayEntryActivity.wechatCallback = wechatCallback
    }
}