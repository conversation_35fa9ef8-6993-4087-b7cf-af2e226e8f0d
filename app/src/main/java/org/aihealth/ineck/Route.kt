package org.aihealth.ineck

import android.annotation.SuppressLint
import androidx.compose.runtime.remember
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavType
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import kotlinx.coroutines.flow.update
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.model.improvement.ImprovementDetailType
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.directions.HistoryIndexStatusDirections
import org.aihealth.ineck.view.directions.HistoryPainDirections
import org.aihealth.ineck.view.directions.HistoryVitalSignsDirections
import org.aihealth.ineck.view.directions.ImprovementDetailDirections
import org.aihealth.ineck.view.directions.MatchingDeviceDirections
import org.aihealth.ineck.view.directions.OdiRecordDirections
import org.aihealth.ineck.view.directions.PromisRecordDirections
import org.aihealth.ineck.view.screen.AddAttentionRoute
import org.aihealth.ineck.view.screen.BirthdateEditRoute
import org.aihealth.ineck.view.screen.CalibrationDeviceScreen
import org.aihealth.ineck.view.screen.ChangeCityScreen
import org.aihealth.ineck.view.screen.ConnectedDeviceRoute
import org.aihealth.ineck.view.screen.DeviceSettingsScreen
import org.aihealth.ineck.view.screen.DisclaimerScreen
import org.aihealth.ineck.view.screen.EmailEditRoute
import org.aihealth.ineck.view.screen.EvaluateScreen
import org.aihealth.ineck.view.screen.FeedbackScreen
import org.aihealth.ineck.view.screen.FormatDeviceRoute
import org.aihealth.ineck.view.screen.FunctionIntroductionScreen
import org.aihealth.ineck.view.screen.GenderEditRoute
import org.aihealth.ineck.view.screen.HealthTipScreen
import org.aihealth.ineck.view.screen.HelpFeedbackScreen
import org.aihealth.ineck.view.screen.HistoryIndexStatusScreen
import org.aihealth.ineck.view.screen.HistoryPainScreen
import org.aihealth.ineck.view.screen.HistoryVitalSignsScreen
import org.aihealth.ineck.view.screen.MainScreen
import org.aihealth.ineck.view.screen.MatchingRoute
import org.aihealth.ineck.view.screen.MemberAgreementScreen
import org.aihealth.ineck.view.screen.MyAttentionScreen
import org.aihealth.ineck.view.screen.NameEditRoute
import org.aihealth.ineck.view.screen.NeuralRecordScreen
import org.aihealth.ineck.view.screen.OdiPromisRecordScreen
import org.aihealth.ineck.view.screen.PainRecordScreen
import org.aihealth.ineck.view.screen.PersonalDataRoute
import org.aihealth.ineck.view.screen.PhoneEditRoute
import org.aihealth.ineck.view.screen.PrivacyAndSecurityScreen
import org.aihealth.ineck.view.screen.PrivateTermScreen
import org.aihealth.ineck.view.screen.TextNetWorkScreen
import org.aihealth.ineck.view.screen.ThirdPartyDataSourcesScreen
import org.aihealth.ineck.view.screen.UseRedemptionCodeScreen
import org.aihealth.ineck.view.screen.UserAgreementScreen
import org.aihealth.ineck.view.screen.VersionInformationScreen
import org.aihealth.ineck.view.screen.aijoint.AiJointDetailScreen
import org.aihealth.ineck.view.screen.aijoint.jointexercise.KneeExerciseRoute
import org.aihealth.ineck.view.screen.aijoint.jointexercise.KneeExerciseScreen
import org.aihealth.ineck.view.screen.chat.ChatScreen
import org.aihealth.ineck.view.screen.device.CalibrationDeviceRoute
import org.aihealth.ineck.view.screen.exercise.ExerciseEvaluationScreen
import org.aihealth.ineck.view.screen.exercise.ImproveDetailScreen
import org.aihealth.ineck.view.screen.exercise.PhotoTrainingScreen
import org.aihealth.ineck.view.screen.exercise.TrainingScreen
import org.aihealth.ineck.view.screen.healquestionnairehistory.HealthQuestionnaireHistoryScreen
import org.aihealth.ineck.view.screen.healquestionnairehistory.QuestionnaireReportResultScreen
import org.aihealth.ineck.view.screen.info.SetUerInfoRoute
import org.aihealth.ineck.view.screen.login.ForgetPasswordScreen
import org.aihealth.ineck.view.screen.login.LoginCNSignUpScreen
import org.aihealth.ineck.view.screen.login.LoginENSignUpScreen
import org.aihealth.ineck.view.screen.login.LoginScreen
import org.aihealth.ineck.view.screen.logoff.LogOffRequireScreen
import org.aihealth.ineck.view.screen.logoff.LogOffScreen
import org.aihealth.ineck.view.screen.logoff.LogOffSuccessScreen
import org.aihealth.ineck.view.screen.mapToVitalSignState
import org.aihealth.ineck.view.screen.meeting.MeetingDemo
import org.aihealth.ineck.view.screen.membershipcenter.MemberShipCenterScreen
import org.aihealth.ineck.view.screen.message.MessageCenterScreen
import org.aihealth.ineck.view.screen.my.LanguageSettingsScreen
import org.aihealth.ineck.view.screen.my.MyAboutScreen
import org.aihealth.ineck.view.screen.my.UnitSettingRoute
import org.aihealth.ineck.view.screen.onboarding.OnBoardingScreen
import org.aihealth.ineck.view.screen.payment.PaymentScreen
import org.aihealth.ineck.view.screen.questionnaire.QuestionnaireGuideScreen
import org.aihealth.ineck.view.screen.questionnaire.QuestionnaireReportScreen
import org.aihealth.ineck.view.screen.questionnaire.QuestionnaireWelcomeScreen
import org.aihealth.ineck.view.screen.questionnaire.aiback.AiBackQuestionnaireGuideScreen
import org.aihealth.ineck.view.screen.questionnaire.aiback.AiBackQuestionnaireWelcomeScreen
import org.aihealth.ineck.view.screen.splash.SplashScreen
import org.aihealth.ineck.viewmodel.HistoryPainViewModel
import org.aihealth.ineck.viewmodel.HistoryVitalSignsViewModel
import org.aihealth.ineck.viewmodel.ImproveDetailViewModel
import org.aihealth.ineck.viewmodel.LoginChinaViewModel
import org.aihealth.ineck.viewmodel.LoginUSAViewModel
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.MemberShipCenterViewModel
import org.aihealth.ineck.viewmodel.MyAttentionViewModel
import org.aihealth.ineck.viewmodel.dao.RedemptionCodeViewModel


@SuppressLint("StateFlowValueCalledInComposition")
fun NavGraphBuilder.Route(navController: NavController, requestCameraPermissionEvent: () -> Unit) {
    /**
     * 启动页
     */
    composable(route = Screen.Splash.route) {
        SplashScreen(
            event = {
                LogUtil.i("SplashScreen startScreen $it")
                startScreen(it, true)
            }
        )
    }

    /**
     * 第一次打开app的图片轮播页
     */
    composable(Screen.OnBoarding.route) {
        OnBoardingScreen(
            onEvent = {
                SPUtil.putBoolean(SPConstant.IS_FIRST_START, false)
                startScreen(Screen.Login.route, true)
            }
        )
    }

    /**
     * 登入页
     */
    composable(Screen.Login.route) { LoginScreen() }

    /**
     * 忘记密码页
     */
    composable(
        Screen.ForgetPassword.route + "?email={email}",
        arguments = listOf(navArgument("email") { type = NavType.StringType })
    ) { backStackEntry ->
        val email = backStackEntry.arguments?.getString("email") ?: ""
        ForgetPasswordScreen(email)
    }

    /**
     * 国外服务器邮箱注册界面
     *
     */
    composable(Screen.LoginSignUpEN.route) { backStackEntry ->
        val loginENEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Login.route)
        }
        val parentViewModel = viewModel<LoginUSAViewModel>(loginENEntry)
        LoginENSignUpScreen(parentViewModel)

    }

    /**
     * 非中国区域用户token创建
     */
    composable(Screen.LoginSignUpChina.route) { backStackEntry ->
        val loginENEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Login.route)
        }
        val parentViewModel = viewModel<LoginChinaViewModel>(loginENEntry)
        LoginCNSignUpScreen(parentViewModel)

    }
    /**
     * 一级页面容器
     */
    composable(Screen.Main.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)
        LogUtil.i("MainScreen MainViewModel $viewModel")
        LogUtil.i("MainScreen MainViewModel pageIndex ${MainViewModel.pageIndex}")
        MainScreen(viewModel = viewModel)
//        viewModel.homeScreen.currentDeviceType = DeviceType.aiBack
//        CalibrationDeviceScreen(viewModel)
    }

    /**
     * 用户信息填写如用户模型创建
     */
    composable(Screen.FirstUpdateData.route) { SetUerInfoRoute() }

    /**
     * 设备搜寻与连接
     */
    composable(
        route = MatchingDeviceDirections.route,
        arguments = MatchingDeviceDirections.argumentsList
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("MatchingDeviceScreen MainViewModel $viewModel")
        val (model) = MatchingDeviceDirections.parseArguments(backStackEntry)
        val deviceType = model.deviceType
        MatchingRoute(viewModel, deviceType)
    }

    /**
     * 设备连接界面
     */
    composable(
        route = Screen.DeviceConnect.route + "?deviceType={deviceType}&mac={mac}",
        arguments = listOf(
            navArgument("deviceType") {
                type = NavType.StringType
            },
            navArgument("mac") {
                type = NavType.StringType
            }
        )
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("PainRecordScreen MainViewModel $viewModel")
        val deviceType = backStackEntry.arguments?.getString("deviceType") ?: DeviceType.aiNeck.name
        val mac = backStackEntry.arguments?.getString("mac") ?: ""
        ConnectedDeviceRoute(viewModel, deviceType, mac)
    }

    // 设备模式选择界面
    composable(Screen.FormatDevice.route) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("FormatDeviceScreen MainViewModel $viewModel")
        FormatDeviceRoute(viewModel)
    }
    // 隐私政策界面
    composable(Screen.PrivateTerm.route) { PrivateTermScreen() }
    // 免责条款界面
    composable(Screen.Disclaimer.route) { DisclaimerScreen() }
    // 用户协议
    composable(Screen.UserAgreement.route) { UserAgreementScreen() }
    // 会员协议
    composable(Screen.MemberAgreement.route) { MemberAgreementScreen() }
    // 修改地区界面
    composable(Screen.ChangeCity.route) { ChangeCityScreen() }

    // 设备设置界面
    composable(Screen.DeviceSettings.route+ "?deviceType={deviceType}",
        arguments = listOf(
            navArgument("deviceType") {
                type = NavType.StringType
            }
        )
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val deviceName = backStackEntry.arguments?.getString("deviceType") ?: DeviceType.aiNeck.name
        val deviceType = when (deviceName) {
            DeviceType.aiNeck.name -> DeviceType.aiNeck
            DeviceType.aiBack.name -> DeviceType.aiBack
            else -> DeviceType.aiNeck
        }
        DeviceSettingsScreen(deviceType)
    }

    //设备校准界面
    composable(
        route = Screen.CalibrationDevice.route,
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("CalibrationDeviceScreen MainViewModel $viewModel")
        CalibrationDeviceScreen(viewModel)
    }
    composable(
        route = Screen.CalibrationDeviceRoute.route + "?deviceType={deviceType}",
        arguments = listOf(
            navArgument("deviceType") {
                type = NavType.StringType
            }
        )
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val deviceName = backStackEntry.arguments?.getString("deviceType") ?: DeviceType.aiNeck.name
        val deviceType = when (deviceName) {
            DeviceType.aiNeck.name -> DeviceType.aiNeck
            DeviceType.aiBack.name -> DeviceType.aiBack
            else -> DeviceType.aiNeck
        }
        CalibrationDeviceRoute(deviceType)
    }

    // 个人资料界面
    composable(Screen.PersonalData.route) { PersonalDataRoute() }
    // 姓名编辑界面
    composable(Screen.NameEdit.route) { NameEditRoute() }
    // 性别编辑界面
    composable(Screen.GenderEdit.route) { GenderEditRoute() }
    // 出生日期编辑界面
    composable(Screen.BirthdateEdit.route) { BirthdateEditRoute() }
    // 邮箱编辑界面
    composable(Screen.EmailEdit.route) { EmailEditRoute() }
    // 手机号码编辑界面
    composable(Screen.PhoneEdit.route) { PhoneEditRoute() }
    // 版本信息界面
    composable(Screen.VersionInformation.route) { VersionInformationScreen() }
    /**
     * 关于我们界面
     */
    composable(Screen.MyAbout.route) { MyAboutScreen() }
    /**
     * 功能介绍界面
     */
    composable(Screen.FunctionIntroduction.route) { FunctionIntroductionScreen() }
    // 帮助与反馈界面
    composable(Screen.HelpFeedback.route) { HelpFeedbackScreen() }
    // 意见反馈界面
    composable(Screen.Feedback.route) { FeedbackScreen() }

    // 健康贴士界面
    composable(Screen.HealthTip.route) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("HealthTipScreen MainViewModel $viewModel")
        HealthTipScreen(viewModel)
    }

    // 疼痛记录表单界面
    composable(
        route = Screen.PainRecord.route + "?isHistory={isHistory}",
        arguments = listOf(navArgument("isHistory") {
            type = NavType.BoolType
            defaultValue = false
        })
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("PainRecordScreen MainViewModel $viewModel")
        val isHistory = backStackEntry.arguments?.getBoolean("isHistory") ?: false
        LogUtil.i("history:$isHistory")
        PainRecordScreen(viewModel, isHistory)
    }

    composable(
        Screen.NeuralScaleScreen.route + "?isHistory={isHistory}",
        arguments = listOf(navArgument("isHistory") {
            type = NavType.BoolType
            defaultValue = false
        })
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("PainRecordScreen MainViewModel $viewModel")
        val isHistory = backStackEntry.arguments?.getBoolean("isHistory") ?: false
        NeuralRecordScreen(viewModel, isHistory)
    }

    // ODI记录表单界面
    composable(
        route = OdiRecordDirections.route,
        arguments = OdiRecordDirections.argumentsList
    ) { backStackEntry ->
        val (model) = OdiRecordDirections.parseArguments(backStackEntry)
        val isPromis = model.isPromis
        val showToggleButton = model.showToggleButton
        val baseTime = model.baseTime
        LogUtil.i("ODI baseTime:$baseTime isPromis")
        OdiPromisRecordScreen(
            isPromis = isPromis,
            showToggleButton = showToggleButton,
            baseTime = baseTime
        )
    }
    // Promis记录表单界面
    composable(
        route = PromisRecordDirections.route,
        arguments = PromisRecordDirections.argumentsList
    ) { backStackEntry ->
        val (model) = PromisRecordDirections.parseArguments(backStackEntry)
        val isPromis = model.isPromis
        val showToggleButton = model.showToggleButton
        val baseTime = model.baseTime
        OdiPromisRecordScreen(
            isPromis = isPromis,
            showToggleButton = showToggleButton,
            baseTime = baseTime
        )
    }

    /**
     * 我的关注界面
     */
    composable(route = Screen.MyAttention.route) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("MyAttentionScreen MainViewModel $viewModel")
        MyAttentionScreen(viewModel)
    }

    /**
     * 添加关注界面
     */
    composable(route = Screen.AddAttention.route) { backStackEntry ->
        val entry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.MyAttention.route)
        }
        val viewModel = viewModel<MyAttentionViewModel>(entry)
        AddAttentionRoute(viewModel)
    }

    // 聊天界面
    composable(
        route = Screen.Chat.route,
    ) { backStackEntry ->
        ChatScreen()
    }

    // 历史记录界面
    composable(
        route = HistoryIndexStatusDirections.route,
        arguments = HistoryIndexStatusDirections.argumentsList
    ) { backStackEntry ->
        val (model) = HistoryIndexStatusDirections.parseArguments(backStackEntry)
        val startTime = model.startTime
        val endTime = model.endTime
        val isPromis = model.isPromis
        HistoryIndexStatusScreen(
            startTime = startTime,
            endTime = endTime,
            isPromis = isPromis
        )
    }

    /* 生命体征历史记录页面 */
    composable(
        route = HistoryVitalSignsDirections.route,
        arguments = HistoryVitalSignsDirections.argumentsList
    ) {
        val mainEntry = remember(it) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val mainViewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("HistoryVitalSignsScreen MainViewModel $mainViewModel")
        val (model) = HistoryVitalSignsDirections.parseArguments(it)
        LogUtil.i("model: $model")
        val vitalSignNumberState = model.vitalSign
        val isCelsius = model.isCelsius
        val isMmolL = model.isMmolL
        val viewModel = viewModel<HistoryVitalSignsViewModel>(
            factory = object : ViewModelProvider.Factory {
                override fun <T : ViewModel> create(modelClass: Class<T>): T {
                    return HistoryVitalSignsViewModel(
                        vitalSignsState = vitalSignNumberState.mapToVitalSignState(),
                        isCelsius = isCelsius,
                        isMmolL = isMmolL
                    ) as T
                }
            }
        )
        HistoryVitalSignsScreen(
            viewModel = viewModel,
            vitalSignNumberState = vitalSignNumberState,
            mainViewModel = mainViewModel
        )
    }

    // 注销账号界面
    composable(Screen.LogOff.route) {
        LogOffScreen()
    }
    composable(Screen.LogOffRequireScreen.route) {
        LogOffRequireScreen()
    }

    /**
     * 注销账户成功页
     */
    composable(Screen.LogOffSuccess.route) {
        LogOffSuccessScreen()
    }

    composable(Screen.Test.route) {
        TextNetWorkScreen()
    }

    /* 疼痛历史记录页 */
    composable(
        route = HistoryPainDirections.route,
        arguments = HistoryPainDirections.argumentsList
    ) { backStackEntry ->
        val (model) = HistoryPainDirections.parseArguments(backStackEntry)
        val painTypeNumberState = model.painTypeNumberState
        val viewModel = viewModel<HistoryPainViewModel>()
        HistoryPainScreen(
            viewModel = viewModel,
            painNumberState = painTypeNumberState
        )
    }

    /**
     *  改善方案详情页面 视频和图文
     */
    composable(
        route = ImprovementDetailDirections.route,
        arguments = ImprovementDetailDirections.argumentsList
    ) { backStackEntry ->
        val (model) = ImprovementDetailDirections.parseArguments(backStackEntry)
        val materialId = model.materialId
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val mainViewModel = viewModel<MainViewModel>(mainEntry)
        val improvementDetailViewModel = viewModel<ImproveDetailViewModel>(mainEntry)
        improvementDetailViewModel.materialId = materialId

        LogUtil.i("ImprovementDetail ImproveDetailViewModel $improvementDetailViewModel")
        if (
            model.needVip &&
            MainViewModel.onlineTime < MainViewModel.trailTimeLimit &&
            userSP.getBoolean("isShowTailMembershipDialog", true)
        ) {
            mainViewModel.isShowTailMembershipDialog = true
        }
        if (model.type == ImprovementDetailType.VIDEO.type) {
            improvementDetailViewModel.getImprovementProgramDetailData()
            improvementDetailViewModel.getImproveProgramDetailData(mainViewModel)
            improvementDetailViewModel.getImprovementProgramDataTwo(mainViewModel)
            ImproveDetailScreen(
                mainViewModel = mainViewModel,
                viewModel = improvementDetailViewModel
            )
        } else {
            improvementDetailViewModel.pageIndex = 0
            /* 进行初始化数据请求加载 */
            improvementDetailViewModel.startTime = System.currentTimeMillis()
            improvementDetailViewModel.getImproveProgramDetailData(mainViewModel)
            improvementDetailViewModel.getImprovementProgramDetailData()
            PhotoTrainingScreen(
                mainViewModel,
                improvementDetailViewModel,
            )
        }

    }

    /**
     * 视频播放界面
     */
    composable(route = Screen.Training.route)
    { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val mainViewModel = viewModel<MainViewModel>(mainEntry)
        val viewModel = viewModel<ImproveDetailViewModel>(mainEntry)
        LogUtil.i("Training ImproveDetailViewModel $viewModel")
        TrainingScreen(viewModel, mainViewModel)
    }

    composable(route = Screen.TrainingEvaluation.route) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<ImproveDetailViewModel>(mainEntry)
        LogUtil.i("TrainingEvaluation ImproveDetailViewModel $viewModel")
        ExerciseEvaluationScreen(
            viewModel = viewModel
        )
    }

    /**
     * 会员中心页
     */
    composable(
        route = Screen.MemberShipCenter.route + "?renewal={renewal}",
        arguments = listOf(navArgument("renewal") {
            type = NavType.BoolType
            defaultValue = false
        })
    ) { backStackEntry ->
        val renewal = backStackEntry.arguments?.getBoolean("renewal") ?: false
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }

        val mainViewModel = viewModel<MainViewModel>(mainEntry)
        LogUtil.i("member MainViewModel $mainViewModel")
        val memberShipCenterViewModel: MemberShipCenterViewModel = viewModel()
        memberShipCenterViewModel.renewal.update { renewal }
        LogUtil.i("nav memberShipCenterViewModel $memberShipCenterViewModel")
        MemberShipCenterScreen(mainViewModel)
    }

    /**
     * 兑换码页
     */
    composable(Screen.UseRedemptionCodeScreen.route) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val redemptionCodeViewModel = viewModel<RedemptionCodeViewModel>(mainEntry)
        UseRedemptionCodeScreen(redemptionCodeViewModel)
    }

    /**
     * 收银台页
     */
    composable(
        route = Screen.PaymentScreen.route + "{index}",
        arguments = listOf(navArgument("index") {
            type = NavType.IntType
        })
    ) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val index = backStackEntry.arguments?.getInt("index")
        val mainViewModel = viewModel<MainViewModel>(mainEntry)
        PaymentScreen(index!!)
    }

    /**
     * 评价页
     */
    composable(Screen.EvaluationScreen.route) { backStackEntry ->
        val mainEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val mainViewModel = viewModel<MainViewModel>()
        EvaluateScreen()
    }


    /**
     * 隐私与安全
     */
    composable(Screen.PrivacyAndSecurity.route) {
        PrivacyAndSecurityScreen()
    }

    /**
     * 问卷欢迎页
     */
    composable(Screen.QuestionnaireWelcome.route) {
        QuestionnaireWelcomeScreen()
    }


    /**
     * 问卷欢迎页
     */
    composable(Screen.AiBackQuestionnaireWelcome.route) {
        AiBackQuestionnaireWelcomeScreen()
    }


    /**
     * 问卷入口
     */
    composable(Screen.QuestionnaireGuide.route) {
        QuestionnaireGuideScreen()
    }

    /**
     * 问卷入口
     */
    composable(Screen.AiBackQuestionnaireGuide.route) {
        AiBackQuestionnaireGuideScreen()
    }


    /**
     * 第三方数据中心
     */
    composable(Screen.ThirdPartyDataSources.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)
        ThirdPartyDataSourcesScreen(viewModel)

    }

    /**
     * 健康问卷链路历史记录
     */
    composable(Screen.QuestionnaireResultScreen.route) { backStackEntry ->
        HealthQuestionnaireHistoryScreen()
    }

    composable(Screen.QuestionnaireReportResultScreen.route) {
        QuestionnaireReportResultScreen()
    }

    /**
     * 信息中心
     */
    composable(Screen.MessageCenter.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)
        MessageCenterScreen()
    }

    composable(Screen.LanguageSettings.route) {
        LanguageSettingsScreen()
    }

    /**
     * aiJoint
     */
    composable(Screen.AiJoint.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)

        AiJointDetailScreen(
            viewModel = viewModel
        )
    }

    /**
     * knee exercise
     */
    composable(Screen.KneeExerciseScreen.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)

        KneeExerciseScreen(viewModel)
    }

    /**
     * knee exercise report
     */
    composable(Screen.KneeExerciseReportScreen.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)

        KneeExerciseRoute(viewModel)
    }

    /**
     * 单位设置
     */
    composable(Screen.UnitSettings.route) {
        UnitSettingRoute()
    }

    composable(Screen.QuestionnaireReportScreen.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)

        QuestionnaireReportScreen(
            mainViewModel = viewModel,
            onBack = {
                startScreen(Screen.QuestionnaireWelcome.route, true)
            }
        )
    }

    composable(Screen.AiBackQuestionnaireReportScreen.route) { backStackEntry ->
        val parentEntry = remember(backStackEntry) {
            navController.getBackStackEntry(Screen.Main.route)
        }
        val viewModel = viewModel<MainViewModel>(parentEntry)

        QuestionnaireReportScreen(
            type = DeviceType.aiBackCV,
            mainViewModel = viewModel,
            onBack = {
                startScreen(Screen.AiBackQuestionnaireWelcome.route, true)
            }
        )
    }

    composable(Screen.MeetingScreen.route) { backStackEntry ->
        MeetingDemo()
    }
}

