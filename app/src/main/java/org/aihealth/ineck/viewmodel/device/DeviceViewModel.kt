package org.aihealth.ineck.viewmodel.device

import android.annotation.SuppressLint
import android.bluetooth.BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.TimeoutCancellationException
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import no.nordicsemi.android.dfu.DfuServiceInitiator
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.bluetooth.BleService
import org.aihealth.ineck.bluetooth.BluetoothConnection
import org.aihealth.ineck.bluetooth.BluetoothUtil
import org.aihealth.ineck.bluetooth.BluetoothUtils
import org.aihealth.ineck.bluetooth.BluetoothUtils.bytesToHex
import org.aihealth.ineck.bluetooth.ConnectionState
import org.aihealth.ineck.bluetooth.DFUService
import org.aihealth.ineck.bluetooth.DeviceUUID
import org.aihealth.ineck.bluetooth.ScanUiState
import org.aihealth.ineck.database
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.Angle
import org.aihealth.ineck.model.copy
import org.aihealth.ineck.util.DeviceConfigManager
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.viewmodel.user
import java.nio.BufferUnderflowException
import java.nio.ByteBuffer
import java.util.ArrayDeque
import java.util.Calendar
import java.util.UUID
import kotlin.math.absoluteValue

/**
 * 设备UI状态
 */
sealed class DeviceUIState {
    object Idle : DeviceUIState()
    object Scanning : DeviceUIState()
    object ScanError : DeviceUIState()
    object Connecting : DeviceUIState()
    object Connected : DeviceUIState()
    data class Error(val errorRes: Int) : DeviceUIState()
    object NeedUpgrade : DeviceUIState()
    object Upgrading : DeviceUIState()
    object UpgradeSuccess : DeviceUIState()
    object UpgradeFailed : DeviceUIState()
}

sealed class ConnectionError {
    object BluetoothNotAvailable : ConnectionError()
    object ServiceNotInitialized : ConnectionError()
    object InvalidAddress : ConnectionError()
    object DeviceNotFound : ConnectionError()
    object ConnectionFailed : ConnectionError()
    data class UnknownError(val message: String) : ConnectionError()

    fun getErrorResId(): Int {
        return when (this) {
            is BluetoothNotAvailable -> R.string.error_bluetooth_not_available
            is ServiceNotInitialized -> R.string.error_service_not_initialized
            is InvalidAddress -> R.string.error_invalid_address
            is DeviceNotFound -> R.string.error_device_not_found
            is ConnectionFailed -> R.string.error_connection_failed
            is UnknownError -> R.string.error_unknown
        }
    }
}

class DeviceViewModel() : ViewModel() {
    // BluetoothService 实例和 Messenger
    private var bleService: BleService? = null

    val angleDao by lazy {
        database.angleDao()
    }

    // 连接状态 - 优化：合并临时状态与最终状态
    private val _neckConnectionState = MutableStateFlow<ConnectionState?>(null)
    val neckConnectionState: StateFlow<ConnectionState?> = _neckConnectionState.asStateFlow()

    // 连接状态 - 优化：合并临时状态与最终状态
    private val _backConnectionState = MutableStateFlow<ConnectionState?>(null)
    val backConnectionState: StateFlow<ConnectionState?> = _backConnectionState.asStateFlow()

    // 连接进度
    var serviceConnection: ServiceConnection? = null

    // 电池电量
    private val _neckBatteryLevel = MutableStateFlow<Int?>(null)
    val neckBatteryLevel: StateFlow<Int?> = _neckBatteryLevel.asStateFlow()

    // 设备版本号
    private val _neckDeviceVersion = MutableStateFlow<String?>(null)
    val neckDeviceVersion: StateFlow<String?> = _neckDeviceVersion.asStateFlow()

    // 设备序列号
    private val _neckDeviceSN = MutableStateFlow<String?>(null)
    val neckDeviceSN: StateFlow<String?> = _neckDeviceSN.asStateFlow()

    // 角度
    private val _neckAngle = MutableStateFlow<Int?>(null)
    val neckAngle: StateFlow<Int?> = _neckAngle.asStateFlow()

    // 最大角度
    private val _neckMaxAngle = MutableStateFlow<Int?>(null)
    val neckMaxAngle: StateFlow<Int?> = _neckMaxAngle.asStateFlow()

    // 角度测量时间 (存储为格式化后的字符串)
    private val _neckAngleTime = MutableStateFlow<String?>(null)
    val neckAngleTime: StateFlow<String?> = _neckAngleTime.asStateFlow()

    // 本地角度
    private val _neckLocalAngle = MutableStateFlow<Int?>(null)
    val neckLocalAngle: StateFlow<Int?> = _neckLocalAngle.asStateFlow()

    // 本地角度
    private val _neckLocalMaxAngle = MutableStateFlow<Int?>(null)
    val neckLocalMaxAngle: StateFlow<Int?> = _neckLocalMaxAngle.asStateFlow()

    // 本地角度测量时间 (存储为格式化后的字符串)
    private val _neckLocalAngleTime = MutableStateFlow<String?>(null)
    val neckLocalAngleTime: StateFlow<String?> = _neckLocalAngleTime.asStateFlow()

    // 震动模式状态 (假设默认关闭, 需要根据设备实际情况调整初始值或读取逻辑)
    private val _neckIsVibrationModeEnabled = MutableStateFlow(false)
    val neckIsVibrationModeEnabled: StateFlow<Boolean> = _neckIsVibrationModeEnabled.asStateFlow()

    // 查找设备状态
    private val _neckIsFindingDevice = MutableStateFlow(false)
    val neckIsFindingDevice: StateFlow<Boolean> = _neckIsFindingDevice.asStateFlow()

    // 疼痛记录列表
    private val _neckPainRecords = MutableStateFlow<List<PainRecord>>(emptyList())
    val neckPainRecords: StateFlow<List<PainRecord>> = _neckPainRecords.asStateFlow()

    // 校准状态
    private val _neckIsFirstCalibrated = MutableStateFlow(false)
    val neckIsFirstCalibrated: StateFlow<Boolean> = _neckIsFirstCalibrated.asStateFlow()

    private val _neckIsSecondCalibrated = MutableStateFlow(false)
    val neckIsSecondCalibrated: StateFlow<Boolean> = _neckIsSecondCalibrated.asStateFlow()

    // 第二步校准是否成功
    var isNeckSecondCalibratedSuccess = false
    private val _neckIsNeedCalibration = MutableStateFlow(false)
    val isNeckNeedCalibration: StateFlow<Boolean> = _neckIsNeedCalibration.asStateFlow()

    // 电池电量
    private val _backBatteryLevel = MutableStateFlow<Int?>(null)
    val backBatteryLevel: StateFlow<Int?> = _backBatteryLevel.asStateFlow()

    // 设备版本号
    private val _backDeviceVersion = MutableStateFlow<String?>(null)
    val backDeviceVersion: StateFlow<String?> = _backDeviceVersion.asStateFlow()

    // 设备序列号
    private val _backDeviceSN = MutableStateFlow<String?>(null)
    val backDeviceSN: StateFlow<String?> = _backDeviceSN.asStateFlow()

    // 角度
    private val _backAngle = MutableStateFlow<Int?>(null)
    val backAngle: StateFlow<Int?> = _backAngle.asStateFlow()

    // 最大角度
    private val _backMaxAngle = MutableStateFlow<Int?>(null)
    val backMaxAngle: StateFlow<Int?> = _backMaxAngle.asStateFlow()

    // 角度测量时间 (存储为格式化后的字符串)
    private val _backAngleTime = MutableStateFlow<String?>(null)
    val backAngleTime: StateFlow<String?> = _backAngleTime.asStateFlow()

    // 本地角度
    private val _backLocalAngle = MutableStateFlow<Int?>(null)
    val backLocalAngle: StateFlow<Int?> = _backLocalAngle.asStateFlow()

    // 本地角度
    private val _backLocalMaxAngle = MutableStateFlow<Int?>(null)
    val backLocalMaxAngle: StateFlow<Int?> = _backLocalMaxAngle.asStateFlow()

    // 本地角度测量时间 (存储为格式化后的字符串)
    private val _backLocalAngleTime = MutableStateFlow<String?>(null)
    val backLocalAngleTime: StateFlow<String?> = _backLocalAngleTime.asStateFlow()

    // 震动模式状态 (假设默认关闭, 需要根据设备实际情况调整初始值或读取逻辑)
    private val _backIsVibrationModeEnabled = MutableStateFlow(false)
    val backIsVibrationModeEnabled: StateFlow<Boolean> = _backIsVibrationModeEnabled.asStateFlow()

    // 查找设备状态
    private val _backIsFindingDevice = MutableStateFlow(false)
    val backIsFindingDevice: StateFlow<Boolean> = _backIsFindingDevice.asStateFlow()

    // 疼痛记录列表
    private val _backPainRecords = MutableStateFlow<List<PainRecord>>(emptyList())
    val backPainRecords: StateFlow<List<PainRecord>> = _backPainRecords.asStateFlow()

    // 校准状态
    private val _backIsFirstCalibrated = MutableStateFlow(false)
    val backIsFirstCalibrated: StateFlow<Boolean> = _backIsFirstCalibrated.asStateFlow()

    private val _backIsSecondCalibrated = MutableStateFlow(false)
    val backIsSecondCalibrated: StateFlow<Boolean> = _backIsSecondCalibrated.asStateFlow()

    // 第二步校准是否成功
    var isBackSecondCalibratedSuccess = false
    private val _backIsNeedCalibration = MutableStateFlow(false)
    val isBackNeedCalibration: StateFlow<Boolean> = _backIsNeedCalibration.asStateFlow()

    /**
     * 用于 DeviceSetting 中的设备类型
     */
    private val _currentDeviceType = MutableStateFlow(DeviceType.None)
    val currentDeviceType: StateFlow<DeviceType> = _currentDeviceType.asStateFlow()

    // 用StateFlow保存最新的设备配置
    val neckDeviceConfig: StateFlow<DeviceConfig> =
        DeviceConfig.getDeviceConfigFlow(DeviceType.aiNeck)
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = DeviceConfig().apply { this.deviceType = DeviceType.aiNeck }
            )
    val backDeviceConfig: StateFlow<DeviceConfig> =
        DeviceConfig.getDeviceConfigFlow(DeviceType.aiBack)
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(5000),
                initialValue = DeviceConfig().apply { this.deviceType = DeviceType.aiBack }
            )

    private val _scanUiState = MutableStateFlow(ScanUiState())
    val scanUiState = _scanUiState.stateIn(
        viewModelScope, SharingStarted.WhileSubscribed(5000), ScanUiState()
    )

    /**
     * 蓝牙操作工具类
     */
    var bluetoothUtil: BluetoothUtil = BluetoothUtil()

    // 全局连接错误状态 - 将被独立状态替代
    private val _connectionError = MutableStateFlow<ConnectionError?>(null)
    val connectionError: StateFlow<ConnectionError?> = _connectionError.asStateFlow()
    
    // aiNeck设备错误状态
    private val _neckConnectionError = MutableStateFlow<ConnectionError?>(null)
    val neckConnectionError: StateFlow<ConnectionError?> = _neckConnectionError.asStateFlow()
    
    // aiBack设备错误状态
    private val _backConnectionError = MutableStateFlow<ConnectionError?>(null)
    val backConnectionError: StateFlow<ConnectionError?> = _backConnectionError.asStateFlow()

    // aiNeck设备UI状态
    private val _neckDeviceUIState = MutableStateFlow<DeviceUIState>(DeviceUIState.Idle)
    val neckDeviceUIState: StateFlow<DeviceUIState> = _neckDeviceUIState.asStateFlow()
    
    // aiBack设备UI状态
    private val _backDeviceUIState = MutableStateFlow<DeviceUIState>(DeviceUIState.Idle)
    val backDeviceUIState: StateFlow<DeviceUIState> = _backDeviceUIState.asStateFlow()

    // 添加设备连接状态追踪
    private val _neckDeviceConnected = MutableStateFlow(false)
    val neckDeviceConnected: StateFlow<Boolean> = _neckDeviceConnected.asStateFlow()

    private val _backDeviceConnected = MutableStateFlow(false)
    val backDeviceConnected: StateFlow<Boolean> = _backDeviceConnected.asStateFlow()

    private val _neckDeviceConnecting = MutableStateFlow(false)
    val neckDeviceConnecting: StateFlow<Boolean> = _neckDeviceConnecting.asStateFlow()

    private val _backDeviceConnecting = MutableStateFlow(false)
    val backDeviceConnecting: StateFlow<Boolean> = _backDeviceConnecting.asStateFlow()


    init {
        viewModelScope.launch {
            bluetoothUtil.foundDevices.collect { devices ->
                _scanUiState.update {
                    it.copy(
                        foundDevices = devices
                    )
                }
            }
        }
        viewModelScope.launch {
            bluetoothUtil.isScanning.collect { isScanning ->
                _scanUiState.update { it.copy(isScanning = isScanning) }
            }
        }
        // neck UI
        viewModelScope.launch {
            try {
                _neckConnectionState.collect { state ->
                    LogUtil.d(TAG, "neckConnectionState: $state")
                    when (state) {
                        is ConnectionState.Connecting -> {
                            // 更新连接状态
                            _neckDeviceConnecting.value = true
                        }

                        is ConnectionState.ServicesDiscovered -> {
                        }

                        is ConnectionState.DFU -> {
                            // 设备进入DFU模式，此时不处理断开连接状态
                            LogUtil.d(TAG, "设备进入DFU模式: ${state.deviceAddress}")
                        }

                        is ConnectionState.Connected -> {}
                        else -> {
                            // 在处理断开或失败状态前，检查是否处于DFU状态
                            if (_neckDeviceUIState.value !is DeviceUIState.Upgrading &&
                                _neckDeviceUIState.value !is DeviceUIState.NeedUpgrade
                            ) {
                                // 连接断开或失败时重置连接状态
                                LogUtil.d(TAG, "连接断开或失败: $state")
                                _neckDeviceConnecting.value = false
                                _neckDeviceConnected.value = false
                            } else {
                                LogUtil.d(TAG, "设备处于升级状态，忽略断开连接状态: $state")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "监听连接状态出错", e)
            }
        }

        // back UI
        viewModelScope.launch {
            try {
                _backConnectionState.collect { state ->
                    LogUtil.d(TAG, "backConnectionState: $state")
                    when (state) {
                        is ConnectionState.Connecting -> {
                            // 更新连接状态
                            _backDeviceConnecting.value = true
                        }

                        is ConnectionState.ServicesDiscovered -> {
                        }

                        is ConnectionState.DFU -> {
                            // 设备进入DFU模式，此时不处理断开连接状态
                            LogUtil.d(TAG, "设备进入DFU模式: ${state.deviceAddress}")
                        }

                        is ConnectionState.Connected -> {}
                        else -> {
                            // 在处理断开或失败状态前，检查是否处于DFU状态
                            if (_backDeviceUIState.value !is DeviceUIState.Upgrading &&
                                _backDeviceUIState.value !is DeviceUIState.NeedUpgrade
                            ) {
                                // 连接断开或失败时重置连接状态
                                LogUtil.d(TAG, "连接断开或失败: $state")
                                _backDeviceConnecting.value = false
                                _backDeviceConnected.value = false
                            } else {
                                LogUtil.d(TAG, "设备处于升级状态，忽略断开连接状态: $state")
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "监听连接状态出错", e)
            }
        }

        // 监听连接状态和错误状态的变化 - aiNeck设备
        viewModelScope.launch {
            combine(
                _neckConnectionState,
                _neckConnectionError,
                _neckDeviceUIState
            ) { connectionState, error, currentUiState ->
                LogUtil.d(
                    TAG,
                    "aiNeck状态组合: connectionState=$connectionState, error=$error, currentUiState=$currentUiState"
                )

                // 检查是否处于DFU相关的状态，如果是则保持当前状态
                if (currentUiState is DeviceUIState.Upgrading ||
                    currentUiState is DeviceUIState.NeedUpgrade ||
                    currentUiState is DeviceUIState.UpgradeSuccess ||
                    currentUiState is DeviceUIState.UpgradeFailed
                ) {
                    LogUtil.d(TAG, "aiNeck设备处于升级相关状态，保持当前UI状态: $currentUiState")
                    return@combine currentUiState
                }

                // 如果是DFU连接状态，也保持当前UI状态
                if (connectionState is ConnectionState.DFU) {
                    LogUtil.d(TAG, "aiNeck设备处于DFU连接状态，保持当前UI状态: $currentUiState")
                    return@combine currentUiState
                }

                // 如果当前正在扫描，不应该被旧的断开连接状态覆盖
                if (currentUiState is DeviceUIState.Scanning && connectionState == null) {
                    LogUtil.d(TAG, "aiNeck设备正在扫描且连接状态已清除，保持扫描状态")
                    return@combine currentUiState
                }

                when {
                    error != null -> {
                        LogUtil.d(TAG, "aiNeck状态变化: 发生错误 - ${error.javaClass.simpleName}")
                        DeviceUIState.Error(error.getErrorResId())
                    }

                    connectionState is ConnectionState.Disconnected ||
                            connectionState is ConnectionState.Failed -> {
                        // 只有当前不是扫描状态时，才设置为错误状态
                        if (currentUiState !is DeviceUIState.Scanning) {
                            LogUtil.d(TAG, "aiNeck状态变化: 连接失败或断开")
                            DeviceUIState.Error(R.string.connect_device_error)
                        } else {
                            LogUtil.d(TAG, "aiNeck设备正在扫描，忽略断开连接状态")
                            currentUiState
                        }
                    }

                    else -> {
                        LogUtil.d(TAG, "aiNeck状态变化: 保持当前状态")
                        currentUiState
                    }
                }
            }.collect { newState ->
                LogUtil.d(TAG, "aiNeck当前设备UI状态: $newState")
                _neckDeviceUIState.value = newState
            }
        }

        // 监听连接状态和错误状态的变化 - aiBack设备
        viewModelScope.launch {
            combine(
                _backConnectionState,
                _backConnectionError,
                _backDeviceUIState
            ) { connectionState, error, currentUiState ->
                LogUtil.d(
                    TAG,
                    "aiBack状态组合: connectionState=$connectionState, error=$error, currentUiState=$currentUiState"
                )

                // 检查是否处于DFU相关的状态，如果是则保持当前状态
                if (currentUiState is DeviceUIState.Upgrading ||
                    currentUiState is DeviceUIState.NeedUpgrade ||
                    currentUiState is DeviceUIState.UpgradeSuccess ||
                    currentUiState is DeviceUIState.UpgradeFailed
                ) {
                    LogUtil.d(TAG, "aiBack设备处于升级相关状态，保持当前UI状态: $currentUiState")
                    return@combine currentUiState
                }

                // 如果是DFU连接状态，也保持当前UI状态
                if (connectionState is ConnectionState.DFU) {
                    LogUtil.d(TAG, "aiBack设备处于DFU连接状态，保持当前UI状态: $currentUiState")
                    return@combine currentUiState
                }

                // 如果当前正在扫描，不应该被旧的断开连接状态覆盖
                if (currentUiState is DeviceUIState.Scanning && connectionState == null) {
                    LogUtil.d(TAG, "aiBack设备正在扫描且连接状态已清除，保持扫描状态")
                    return@combine currentUiState
                }

                when {
                    error != null -> {
                        LogUtil.d(TAG, "aiBack状态变化: 发生错误 - ${error.javaClass.simpleName}")
                        DeviceUIState.Error(error.getErrorResId())
                    }

                    connectionState is ConnectionState.Disconnected ||
                            connectionState is ConnectionState.Failed -> {
                        // 只有当前不是扫描状态时，才设置为错误状态
                        if (currentUiState !is DeviceUIState.Scanning) {
                            LogUtil.d(TAG, "aiBack状态变化: 连接失败或断开")
                            DeviceUIState.Error(R.string.connect_device_error)
                        } else {
                            LogUtil.d(TAG, "aiBack设备正在扫描，忽略断开连接状态")
                            currentUiState
                        }
                    }

                    else -> {
                        LogUtil.d(TAG, "aiBack状态变化: 保持当前状态")
                        currentUiState
                    }
                }
            }.collect { newState ->
                LogUtil.d(TAG, "aiBack当前设备UI状态: $newState")
                _backDeviceUIState.value = newState
            }
        }

        // 监听连接错误状态变化 - aiNeck
        viewModelScope.launch {
            _neckConnectionError.collect { error ->
                error?.let {
                    LogUtil.e(TAG, "aiNeck连接错误: ${it.javaClass.simpleName}")
                    when (it) {
                        is ConnectionError.BluetoothNotAvailable -> LogUtil.e(TAG, "蓝牙不可用")
                        is ConnectionError.ServiceNotInitialized -> LogUtil.e(TAG, "服务未初始化")
                        is ConnectionError.InvalidAddress -> LogUtil.e(TAG, "无效的MAC地址")
                        is ConnectionError.DeviceNotFound -> LogUtil.e(TAG, "设备未找到")
                        is ConnectionError.ConnectionFailed -> LogUtil.e(TAG, "连接失败")
                        is ConnectionError.UnknownError -> LogUtil.e(TAG, "未知错误: ${it.message}")
                    }
                }
            }
        }

        // 监听连接错误状态变化 - aiBack
        viewModelScope.launch {
            _backConnectionError.collect { error ->
                error?.let {
                    LogUtil.e(TAG, "aiBack连接错误: ${it.javaClass.simpleName}")
                    when (it) {
                        is ConnectionError.BluetoothNotAvailable -> LogUtil.e(TAG, "蓝牙不可用")
                        is ConnectionError.ServiceNotInitialized -> LogUtil.e(TAG, "服务未初始化")
                        is ConnectionError.InvalidAddress -> LogUtil.e(TAG, "无效的MAC地址")
                        is ConnectionError.DeviceNotFound -> LogUtil.e(TAG, "设备未找到")
                        is ConnectionError.ConnectionFailed -> LogUtil.e(TAG, "连接失败")
                        is ConnectionError.UnknownError -> LogUtil.e(TAG, "未知错误: ${it.message}")
                    }
                }
            }
        }

        // 监听设备版本变化并保存 - aiNeck
        viewModelScope.launch {
            _neckDeviceVersion.collect { version ->
                if (!version.isNullOrEmpty()) {
                    LogUtil.d(TAG, "aiNeck设备版本: $version")
                    updateDeviceConfigField(DeviceType.aiNeck) { config ->
                        config.version = version
                    }

                    // 检查设备版本，判断是否需要升级
                    val needUpgrade = needDeviceVersion(version)
                    _neckDeviceConnecting.value = false
                    if (needUpgrade) {
                        _neckDeviceUIState.value = DeviceUIState.NeedUpgrade
                    } else {
                        _neckDeviceConnected.value = true
                        _neckDeviceUIState.value = DeviceUIState.Connected
                        
                        // 设备无需升级，应用保存的设置
                        LogUtil.d(TAG, "aiNeck设备无需升级，开始应用保存的设置")
                        applyDeviceSettings(DeviceType.aiNeck)
                    }
                }
            }
        }

        // 监听aiBack设备版本变化并保存
        viewModelScope.launch {
            _backDeviceVersion.collect { version ->
                if (!version.isNullOrEmpty()) {
                    LogUtil.d(TAG, "aiBack设备版本: $version")
                    updateDeviceConfigField(DeviceType.aiBack) { config ->
                        config.version = version
                    }

                    // 检查设备版本，判断是否需要升级
                    val needUpgrade = needDeviceVersion(version)
                    _backDeviceConnecting.value = false
                    if (needUpgrade) {
                        _backDeviceUIState.value = DeviceUIState.NeedUpgrade
                    } else {
                        _backDeviceConnected.value = true
                        _backDeviceUIState.value = DeviceUIState.Connected
                        
                        // 设备无需升级，应用保存的设置
                        LogUtil.d(TAG, "aiBack设备无需升级，开始应用保存的设置")
                        applyDeviceSettings(DeviceType.aiBack)
                    }
                }
            }
        }

        // 监听设备序列号变化并保存
        viewModelScope.launch {
            _neckDeviceSN.collect { sn ->
                if (sn != null) {
                    // 获取当前连接的设备类型
                    val deviceType = _currentDeviceType.value
                    LogUtil.d(TAG, "设备序列号变化，保存到本地: $sn, 设备类型: $deviceType")

                    if (deviceType != DeviceType.None) {
                        updateDeviceConfigField(deviceType) { config ->
                            config.sn = sn
                        }
                    }
                }
            }
        }

        // 监听aiBack设备序列号变化并保存
        viewModelScope.launch {
            _backDeviceSN.collect { sn ->
                if (sn != null) {
                    // 获取当前连接的设备类型
                    val deviceType = _currentDeviceType.value
                    LogUtil.d(TAG, "设备序列号变化，保存到本地: $sn, 设备类型: $deviceType")

                    if (deviceType != DeviceType.None) {
                        updateDeviceConfigField(deviceType) { config ->
                            config.sn = sn
                        }
                    }
                }
            }
        }

        // 监听当前设备类型变化
        viewModelScope.launch {
            _currentDeviceType.collect { deviceType ->
                LogUtil.d(TAG, "当前设备类型变化: $deviceType")
                // 当设备类型变化时，可以触发其他操作
            }
        }

        // 监听震动模式状态变化并保存
        viewModelScope.launch {
            _neckIsVibrationModeEnabled.collect { isEnabled ->
                val deviceType = _currentDeviceType.value
                LogUtil.d(TAG, "震动模式状态变化，保存到本地: $isEnabled, 设备类型: $deviceType")

                if (deviceType != DeviceType.None) {
                    updateDeviceConfigField(deviceType) { config ->
                        config.isVibration = isEnabled
                    }
                }
            }
        }
    }

    // 保存整个设备配置
    fun saveDeviceConfig(config: DeviceConfig) {
        viewModelScope.launch {
            config.saveToLocal()
        }
    }

    // 更新设备振动设置
    fun updateVibrationSettings(
        deviceType: String,
        angle: Int
    ) {
        viewModelScope.launch {
            val updatedConfig = when (deviceType) {
                DeviceType.aiNeck.name -> {
                    neckDeviceConfig.value
                }

                DeviceType.aiBack.name -> {
                    backDeviceConfig.value
                }

                else -> {
                    DeviceConfig()
                }
            }.copy().apply {
                vibrationAngle = angle
                maxAngle = angle
                isCalibrated = true
            }
            saveDeviceConfig(updatedConfig)
        }
    }

    /**
     * 第一步校准 - 校准基准角度
     * 在 10s 的时间内，收集 **变化** 的角度数据，维护一个长度为 8 的角度队列。
     * 假设蓝牙通知频率约为 250ms/次，则 8 个 **不同** 的数据点大致覆盖时间窗口。
     * 如果队列满时，队列中角度的最大值和最小值的差 <= 20 度，则判定稳定，写入 0x0C 和 0x01。
     * @param deviceType 设备类型
     * @return 是否校准成功
     */
    suspend fun firstCalibrating(deviceType: String): Boolean {
        // 重置校准状态
        when (deviceType){
            DeviceType.aiNeck.name ->{
                _neckIsFirstCalibrated.value = false
                _neckIsSecondCalibrated.value = false
                isNeckSecondCalibratedSuccess = false
            }

            DeviceType.aiBack.name ->{
                _backIsFirstCalibrated.value = false
                _backIsSecondCalibrated.value = false
                isBackSecondCalibratedSuccess = false
            }
            else ->{

            }
        }

        return withContext(Dispatchers.IO) {
            LogUtil.d(TAG, "firstCalibrating, deviceType: $deviceType")
            val connection = getDeviceConnection(deviceType) ?: return@withContext false
            val angleQueue = ArrayDeque<Int>(8)
            var isStable = false

            LogUtil.d(TAG, "开始第一步校准：检测角度稳定性...")

            // Launch collection in a separate job to allow cancellation
            val collectionJob = launch(Dispatchers.IO) {
                when (deviceType) {
                    DeviceType.aiNeck.name -> _neckAngle
                    else  -> _backAngle
                }.filterNotNull() // Ignore null values first
                    .collect { currentAngle -> // This block only runs when a NEW angle arrives
                        LogUtil.d(
                            TAG, "callectionJob: 当前角度: $currentAngle"
                        )
                        angleQueue.addLast(currentAngle)
                        // Keep queue size at a maximum of 8
                        if (angleQueue.size > 8) {
                            angleQueue.removeFirst()
                        }

                        // Check for stability only when the queue is full with distinct values
                        if (angleQueue.size == 8) {
                            val maxAngle = angleQueue.maxOrNull()
                            val minAngle = angleQueue.minOrNull()

                            if (maxAngle != null && minAngle != null) {
                                val diff = maxAngle - minAngle
                                LogUtil.v(
                                    TAG,
                                    "角度队列 (更新后): ${angleQueue.joinToString()}, Max: $maxAngle, Min: $minAngle, Diff: $diff"
                                )
                                if (diff <= 20) {
                                    isStable = true
                                    LogUtil.d(TAG, "角度稳定条件满足 (差值 <= 20)")
                                    // Stability achieved, cancel this collection job
                                    this.cancel("Stability achieved")
                                }
                            }
                        }
                    }
            }

            // Wait for the collection job to complete (either by cancellation or timeout)
            try {
                withTimeout(30000L) {
                    collectionJob.join() // Wait for the job to finish
                }
                // If join completes without timeout, it means stability was achieved and job cancelled itself.
                // isStable should be true here if cancelled for stability.
                LogUtil.d(TAG, "稳定性检查成功完成")

            } catch (e: TimeoutCancellationException) {
                LogUtil.d(TAG, "校准基准角度：30秒超时，未达到稳定条件")
                isStable = false // Ensure stable is false on timeout
                collectionJob.cancel() // Explicitly cancel the job if it timed out
            } catch (e: Exception) {
                // Catch other potential exceptions during join/timeout
                if (e is kotlinx.coroutines.CancellationException && e.message == "Stability achieved") {
                    // This is the expected cancellation when stable, isStable is already true
                    LogUtil.d(TAG, "稳定性检查成功完成 (捕获特定取消)")
                } else {
                    LogUtil.e(TAG, "等待校准任务时发生意外错误", e)
                    isStable = false
                    collectionJob.cancel() // Cancel job on unexpected error
                }
            }

            // Proceed with writing commands only if stable
            if (isStable) {
                LogUtil.d(TAG, "校准基准角度：角度已稳定，开始写入命令...")
                // 向设备写入0x0C
                val writeSuccess1 = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID,
                    byteArrayOf(0x0C),
                    WRITE_TYPE_DEFAULT
                )
                if (writeSuccess1 == true) LogUtil.d(TAG, "写入 0x0C 成功") else LogUtil.e(
                    TAG,
                    "写入 0x0C 失败"
                )

                delay(500) // Wait for device processing

                // 向设备写入0x01
                val writeSuccess2 = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID,
                    byteArrayOf(0x01),
                    WRITE_TYPE_DEFAULT
                )
                if (writeSuccess2 == true) LogUtil.d(TAG, "写入 0x01 成功") else LogUtil.e(
                    TAG,
                    "写入 0x01 失败"
                )

                val success = writeSuccess1 == true && writeSuccess2 == true
                LogUtil.d(TAG, "校准基准角度：写入命令结果 ${if (success) "成功" else "失败"}")
                when (deviceType){
                    DeviceType.aiNeck.name ->{
                        _neckIsFirstCalibrated.value = success
                    }

                    DeviceType.aiBack.name ->{
                        _backIsFirstCalibrated.value = success
                    }
                    else ->{

                    }
                }
                return@withContext success
            } else {
                LogUtil.w(TAG, "校准基准角度：最终未达到稳定条件，校准失败")
                when (deviceType){
                    DeviceType.aiNeck.name ->{
                        _neckIsFirstCalibrated.value = false
                        LogUtil.w(TAG, "aiNeck第一步校准失败")
                    }

                    DeviceType.aiBack.name ->{
                        _backIsFirstCalibrated.value = false
                        LogUtil.w(TAG, "aiBack第一步校准失败")
                    }
                    else ->{
                        LogUtil.w(TAG, "未知设备类型第一步校准失败: $deviceType")
                    }
                }
                return@withContext false
            }
        }
    }

    /**
     * 第二步校准 - 设置最大角度
     * 在 10s 的时间内，收集 **变化** 的角度数据。
     * 如果角度值 **持续** 保持在 30 度以上达到 2 秒 (基于实际值变化的时间)，
     * 则判定成功，写入 0x02。
     * @param deviceType 设备类型
     * @return 是否校准成功
     */
    suspend fun secondCalibrating(deviceType: String): Boolean {
        // 重置第二步校准状态
        when (deviceType){
            DeviceType.aiNeck.name ->{
                _neckIsSecondCalibrated.value = false
                isNeckSecondCalibratedSuccess = false
            }

            DeviceType.aiBack.name ->{
                _backIsSecondCalibrated.value = false
                isBackSecondCalibratedSuccess = false
            }
            else ->{

            }
        }
        var isConsistentlyHigh = false

        return withContext(Dispatchers.IO) {
            val connection = getDeviceConnection(deviceType) ?: return@withContext false
            var startTimeAbove30: Long? = null // Track start time when angle > 30

            LogUtil.d(TAG, "开始第二步校准：检测角度是否持续 > 30 度...")

            // Launch collection in a separate job
            val collectionJob = launch(Dispatchers.IO) {
                when (deviceType) {
                    DeviceType.aiNeck.name -> _neckAngle
                    else  -> _backAngle
                }
                    .filterNotNull()
                    .collect { currentAngle ->
                        val currentTime = System.currentTimeMillis()
                        LogUtil.v(TAG, "第二步校准: 收到角度 $currentAngle")
                        val angle = Math.abs(currentAngle)
                        if (angle > 30) {
                            // If angle just went above 30 (or was already above), record/keep start time
                            if (startTimeAbove30 == null) {
                                startTimeAbove30 = currentTime
                                LogUtil.d(TAG, "角度首次超过 30 度于: $startTimeAbove30")
                            }

                            // Check if it has been consistently above 30 for 2 seconds
                            if (currentTime - (startTimeAbove30 ?: currentTime) >= 2000) {
                                isConsistentlyHigh = true
                                LogUtil.d(TAG, "角度持续 > 30 度已达 2 秒")
                                this.cancel("Condition met: Angle > 30 for 2s") // Cancel collection
                            }
                        } else {
                            // Angle dropped below or equals 30, reset the timer
                            if (startTimeAbove30 != null) {
                                LogUtil.d(TAG, "角度回落至 <= 30 度，重置计时器")
                            }
                            startTimeAbove30 = null
                        }
                    }
            }

            // Wait for the job to complete or timeout (10 seconds)
            try {
                withTimeout(10000L) { // Use 10 seconds timeout for this step
                    collectionJob.join()
                }
                LogUtil.d(TAG, "第二步校准：稳定性检查成功完成")
                // isConsistentlyHigh should be true if join completed due to cancellation
            } catch (e: TimeoutCancellationException) {
                LogUtil.d(TAG, "第二步校准：10秒超时，未满足持续角度条件")
                isConsistentlyHigh = false // Ensure flag is false on timeout
                collectionJob.cancel()
            } catch (e: Exception) {
                if (e is kotlinx.coroutines.CancellationException && e.message == "Condition met: Angle > 30 for 2s") {
                    LogUtil.d(TAG, "第二步校准：稳定性检查成功完成 (捕获特定取消)")
                    // isConsistentlyHigh is already true
                } else {
                    LogUtil.e(TAG, "等待第二步校准任务时发生意外错误", e)
                    isConsistentlyHigh = false
                    collectionJob.cancel()
                }
            }

            // Write command if condition was met
            if (isConsistentlyHigh) {
                LogUtil.d(TAG, "设置最大角度：角度持续大于30度，开始写入0x02")
                val writeSuccess = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID,
                    byteArrayOf(0x02),
                    WRITE_TYPE_DEFAULT
                )
                when (deviceType){
                    DeviceType.aiNeck.name ->{
                        isNeckSecondCalibratedSuccess = writeSuccess == true
                        _neckIsSecondCalibrated.value = isNeckSecondCalibratedSuccess

                        if (isNeckSecondCalibratedSuccess) {
                            val currentAngle = _neckAngle.value
                            LogUtil.d(TAG, "Neck设备保存当前角度 $currentAngle 为最大角度")
                            if (currentAngle != null) {
                                LogUtil.d(TAG, "Neck设备保存当前角度 $currentAngle 为最大角度")
                                updateDeviceConfigField(DeviceType.aiNeck) { config ->
                                    config.maxAngle = currentAngle.absoluteValue
                                    config.vibrationAngle = currentAngle.absoluteValue
                                    config.isCalibrated = true
                                }
                            }
                            _neckIsNeedCalibration.value = false
                        }
                    }

                    DeviceType.aiBack.name ->{
                        isBackSecondCalibratedSuccess = writeSuccess == true
                        _backIsSecondCalibrated.value = isBackSecondCalibratedSuccess
                        if (isBackSecondCalibratedSuccess) {
                            // 将当前的角度设置为设备的最大角度
                            val currentAngle = _backAngle.value
                            if (currentAngle != null) {
                                LogUtil.d(TAG, "Back设备保存当前角度 $currentAngle 为最大角度")
                                updateDeviceConfigField(DeviceType.aiBack) { config ->
                                    config.maxAngle = currentAngle.absoluteValue
                                    config.vibrationAngle = currentAngle.absoluteValue
                                    config.isCalibrated = true
                                }
                            }
                            _backIsNeedCalibration.value = false
                        }
                    }
                    else ->{

                    }
                }


                return@withContext isNeckSecondCalibratedSuccess ||isBackSecondCalibratedSuccess
            } else {
                LogUtil.w(TAG, "设置最大角度：最终未满足持续角度条件，校准失败")
                when (deviceType){
                    DeviceType.aiNeck.name -> {
                        _neckIsSecondCalibrated.value = false
                        LogUtil.w(TAG, "aiNeck第二步校准失败")
                    }
                    DeviceType.aiBack.name -> {
                        _backIsSecondCalibrated.value = false
                        LogUtil.w(TAG, "aiBack第二步校准失败")
                    }
                    else -> {
                        LogUtil.w(TAG, "未知设备类型第二步校准失败: $deviceType")
                    }
                }
                return@withContext false
            }
        }
    }


    /**
     * 重置校准状态
     * @param deviceType 设备类型
     */
    fun resetCalibrationState(deviceType: String) {
        LogUtil.d(TAG, "重置校准状态: $deviceType")
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                _neckIsFirstCalibrated.value = false
                _neckIsSecondCalibrated.value = false
                isNeckSecondCalibratedSuccess = false
                _neckIsNeedCalibration.value = true
            }
            DeviceType.aiBack.name -> {
                _backIsFirstCalibrated.value = false
                _backIsSecondCalibrated.value = false
                isBackSecondCalibratedSuccess = false
                _backIsNeedCalibration.value = true
            }
            else -> {
                LogUtil.w(TAG, "未知设备类型: $deviceType")
            }
        }
    }

    /**
     * 获取设备连接
     */
    private fun getDeviceConnection(deviceType: String): BluetoothConnection? {
        return try {
            val connection = bleService?.getConnectionByDeviceType(deviceType)
            if (connection == null) {
                LogUtil.e(TAG, "获取设备连接失败: 未找到连接")
            }
            connection
        } catch (e: Exception) {
            LogUtil.e(TAG, "获取设备连接失败", e)
            null
        }
    }

    /**
     * 连接到设备 - 优化：确保Service绑定和资源正确释放
     */
    fun connect(deviceAddress: String, initialDeviceType: DeviceType = DeviceType.None) {
        LogUtil.d(TAG, "连接到设备:$deviceAddress,deviceType:$initialDeviceType")
        
        // 重置设备状态变量和清除错误状态
        when (initialDeviceType) {
            DeviceType.aiNeck -> {
                _neckDeviceVersion.value = null
                _neckDeviceSN.value = null
                _neckBatteryLevel.value = null
                _neckAngle.value = null
                _neckMaxAngle.value = null
                _neckAngleTime.value = null
                _neckLocalAngle.value = null
                _neckLocalMaxAngle.value = null
                _neckLocalAngleTime.value = null
                _neckIsVibrationModeEnabled.value = false
                _neckPainRecords.value = emptyList()
                _neckIsFirstCalibrated.value = false
                _neckIsSecondCalibrated.value = false
                _neckDeviceConnecting.value = true
                _neckDeviceConnected.value = false
                
                // 重置aiNeck设备的错误和UI状态
                _neckConnectionError.value = null
                _neckDeviceUIState.value = DeviceUIState.Connecting
            }

            DeviceType.aiBack -> {
                _backDeviceVersion.value = null
                _backDeviceSN.value = null
                _backBatteryLevel.value = null
                _backAngle.value = null
                _backMaxAngle.value = null
                _backAngleTime.value = null
                _backLocalAngle.value = null
                _backLocalMaxAngle.value = null
                _backLocalAngleTime.value = null
                _backIsVibrationModeEnabled.value = false
                _backPainRecords.value = emptyList()
                _backIsFirstCalibrated.value = false
                _backIsSecondCalibrated.value = false
                _backDeviceConnecting.value = true
                _backDeviceConnected.value = false
                
                // 重置aiBack设备的错误和UI状态
                _backConnectionError.value = null
                _backDeviceUIState.value = DeviceUIState.Connecting
            }

            else -> {}
        }

        // 强制重置所有会阻塞新连接的状态 - 保留全局状态重置，以兼容旧代码
        _connectionError.value = null
        
        // 停止扫描并延迟连接，确保蓝牙协议栈稳定
        viewModelScope.launch {
            // 立即停止扫描
            stopScan()
            
            // 根据设备类型设置相应的UI状态
            when (initialDeviceType) {
                DeviceType.aiNeck -> _neckDeviceUIState.value = DeviceUIState.Connecting
                DeviceType.aiBack -> _backDeviceUIState.value = DeviceUIState.Connecting
                else -> {}
            }
            
            // 添加延迟让蓝牙协议栈稳定，提高第一次连接成功率
            LogUtil.d(TAG, "停止扫描后等待蓝牙协议栈稳定...")
            delay(1500) // 增加到1.5秒延迟
        }

        val intent = Intent(baseApplication, BleService::class.java)

        // 如果已有serviceConnection，先解绑
        cleanupServiceConnection()

        // 创建ServiceConnection对象，在Service连接成功后执行连接设备操作
        serviceConnection = object : ServiceConnection {
            override fun onServiceConnected(name: ComponentName, service: IBinder) {
                val binder = service as BleService.LocalBinder
                bleService = binder.getService()
                LogUtil.i(TAG, "BleService绑定成功，开始连接设备")

                // 在服务连接成功后，启动设备连接
                viewModelScope.launch(Dispatchers.IO) {
                    try {
                        // 添加额外延迟确保Service完全就绪
                        delay(500)
                        connectToDevice(deviceAddress, initialDeviceType)
                    } catch (e: Exception) {
                        updateConnectionError(
                            initialDeviceType,
                            ConnectionError.UnknownError(e.message ?: "Unknown error")
                        )
                        LogUtil.e(TAG, "连接设备过程中发生异常", e)

                        when (initialDeviceType) {
                            DeviceType.aiNeck -> _neckDeviceConnecting.value = false
                            DeviceType.aiBack -> _backDeviceConnecting.value = false
                            else -> {}
                        }

                        // 连接失败时解绑服务
                        cleanupServiceConnection()
                    }
                }
            }

            override fun onServiceDisconnected(p0: ComponentName?) {
                LogUtil.i(TAG, "BleService断开连接")
                bleService = null
                // 服务断开时重置连接状态
                viewModelScope.launch {
                    // 更新相应设备的UI状态
                    when (initialDeviceType) {
                        DeviceType.aiNeck -> {
                            _neckDeviceUIState.value = DeviceUIState.Error(R.string.error_service_not_initialized)
                            _neckDeviceConnecting.value = false
                            _neckDeviceConnected.value = false
                        }
                        DeviceType.aiBack -> {
                            _backDeviceUIState.value = DeviceUIState.Error(R.string.error_service_not_initialized)
                            _backDeviceConnecting.value = false
                            _backDeviceConnected.value = false
                        }
                        else -> {}
                    }

                    // 重置连接状态
                    when (_currentDeviceType.value) {
                        DeviceType.aiNeck -> {
                            _neckDeviceConnecting.value = false
                            _neckDeviceConnected.value = false
                        }

                        DeviceType.aiBack -> {
                            _backDeviceConnecting.value = false
                            _backDeviceConnected.value = false
                        }

                        else -> {}
                    }
                }
                serviceConnection = null
            }
        }

        // 绑定服务
        try {
            baseApplication.bindService(intent, serviceConnection!!, Context.BIND_AUTO_CREATE)
        } catch (e: Exception) {
            LogUtil.e(TAG, "绑定服务失败", e)
            
            // 更新设备特定的错误状态
            updateConnectionError(initialDeviceType, ConnectionError.ServiceNotInitialized)
            
            // 更新设备特定的UI状态
            when (initialDeviceType) {
                DeviceType.aiNeck -> _neckDeviceUIState.value = DeviceUIState.Error(R.string.error_service_not_initialized)
                DeviceType.aiBack -> _backDeviceUIState.value = DeviceUIState.Error(R.string.error_service_not_initialized)
                else -> {}
            }

            serviceConnection = null

            // 连接失败时重置连接状态
            viewModelScope.launch {
                when (initialDeviceType) {
                    DeviceType.aiNeck -> _neckDeviceConnecting.value = false
                    DeviceType.aiBack -> _backDeviceConnecting.value = false
                    else -> {}
                }
            }
        }
    }

    /**
     * 清理ServiceConnection
     */
    private fun cleanupServiceConnection() {
        serviceConnection?.let {
            try {
                baseApplication.unbindService(it)
                LogUtil.d(TAG, "成功解绑蓝牙服务")
            } catch (e: Exception) {
                LogUtil.e(TAG, "解绑服务失败", e)
            }
            serviceConnection = null
            bleService = null
        }
    }

    fun disconnect(deviceType: DeviceType) {
        when (deviceType) {
            DeviceType.aiNeck -> {
                LogUtil.i(TAG, "断开aiNeck连接")
                // 获取连接对象并进行安全断开
                val connection = getDeviceConnection(deviceType.name)
                if (connection != null) {
                    try {
                        connection.disconnect(true)
                    } catch (e: Exception) {
                        LogUtil.e(TAG, "断开aiNeck连接失败", e)
                    }
                }
                // 直接更新连接状态
                _neckDeviceConnected.value = false
                _neckDeviceConnecting.value = false
                
                // 更新设备特定的UI状态
                _neckDeviceUIState.value = DeviceUIState.Idle
                
                // 清除连接状态和错误状态，避免影响后续连接
                _neckConnectionState.value = null
                _neckConnectionError.value = null
            }

            DeviceType.aiBack -> {
                LogUtil.i(TAG, "断开aiBack连接")
                // 获取连接对象并进行安全断开
                val connection = getDeviceConnection(deviceType.name)
                if (connection != null) {
                    try {
                        connection.disconnect(true)
                    } catch (e: Exception) {
                        LogUtil.e(TAG, "断开aiBack连接失败", e)
                    }
                }
                // 直接更新连接状态
                _backDeviceConnected.value = false
                _backDeviceConnecting.value = false
                
                // 更新设备特定的UI状态
                _backDeviceUIState.value = DeviceUIState.Idle
                
                // 清除连接状态和错误状态，避免影响后续连接
                _backConnectionState.value = null
                _backConnectionError.value = null
            }

            else -> {}
        }
        cleanupServiceConnection()
    }

    /**
     * 停止扫描设备 - 优化：确保资源释放
     */
    @SuppressLint("MissingPermission")
    fun stopScan() {
        bluetoothUtil.stopScanDevice()
        _scanUiState.update {
            it.copy(isScanning = false)
        }
    }

    /**
     * 保存设备信息到本地
     */
    private fun saveDeviceInfoToLocal(address: String, deviceType: DeviceType) {
        if (deviceType == DeviceType.None) {
            LogUtil.w(TAG, "设备类型未知，无法保存设备信息")
            return
        }

        LogUtil.d(TAG, "保存设备信息到本地: $address, $deviceType")

        viewModelScope.launch {
            // 先获取现有配置或创建新配置
            val existingConfig = DeviceConfigManager.getDeviceConfig(deviceType)

            // 更新设备信息
            val updatedConfig = existingConfig.copy().apply {
                mac = address
                this.deviceType = deviceType
                // 如果是第一次连接，设置默认名称
                if (name.isEmpty()) {
                    name = getDefaultDeviceName(deviceType)
                }
            }

            // 保存更新后的配置
            DeviceConfigManager.saveDeviceConfig(updatedConfig)
            LogUtil.i(TAG, "设备信息已保存: $updatedConfig")
        }
    }

    /**
     * 获取设备默认名称
     */
    private fun getDefaultDeviceName(deviceType: DeviceType): String {
        return when (deviceType) {
            DeviceType.aiNeck -> "我的aiNeck"
            DeviceType.aiBack -> "我的aiBack"
            DeviceType.KneeJoint -> "我的膝关节传感器"
            DeviceType.ShoulderJoint -> "我的肩关节传感器"
            DeviceType.ElbowJoint -> "我的肘关节传感器"
            DeviceType.HipJoint -> "我的髋关节传感器"
            else -> "未知设备"
        }
    }

    /**
     * 启用所有可通知特征 (不包括本地角度，那个单独控制)
     */
    suspend fun enableAllNotifications(connection: BluetoothConnection) {
        withContext(Dispatchers.IO) {
            try {
                var successCount = 0
                var totalCount = 0
                val discoveredServices = connection?.getServices() ?: emptyList()

                // 识别关键服务
                val mainService = discoveredServices.find { it.uuid == DeviceUUID.serviceUUID }
                val batteryService =
                    discoveredServices.find { it.uuid == DeviceUUID.batteryServiceUUID }

                // 将服务按优先级排序并转换为UI模型
                val serviceModels = mutableListOf<ServiceModel>()

                // 1. 主服务优先显示
                mainService?.let {
                    serviceModels.add(
                        ServiceModel(
                            uuid = it.uuid,
                            name = "主服务 (数据通道)",
                            characteristics = it.characteristics.map { characteristic ->
                                ServiceModel.CharacteristicModel(
                                    uuid = characteristic.uuid,
                                    name = getCharacteristicName(characteristic.uuid),
                                    properties = characteristic.properties
                                )
                            }
                        ))
                }

                // 2. 电池服务
                batteryService?.let {
                    serviceModels.add(
                        ServiceModel(
                            uuid = it.uuid,
                            name = "电池服务",
                            characteristics = it.characteristics.map { characteristic ->
                                ServiceModel.CharacteristicModel(
                                    uuid = characteristic.uuid,
                                    name = getCharacteristicName(characteristic.uuid),
                                    properties = characteristic.properties
                                )
                            }
                        ))
                }

                // 遍历所有服务和特征
                serviceModels.forEach { service ->
                    service.characteristics.filter { it.uuid != DeviceUUID.getStepUUID }
                        .forEach { characteristic ->
                            if (characteristic.isNotifiable) {
                                totalCount++
                                LogUtil.d(
                                    TAG,
                                    "尝试启用通知: ${characteristic.name} (${characteristic.uuid})"
                                )

                                val success = bleService?.setCharacteristicNotification(
                                    connection,
                                    service.uuid,
                                    characteristic.uuid,
                                    true
                                )
                                delay(500) // 延迟500毫秒，确保通知已经完全建立
                                if (success == true) {
                                    successCount++
                                    LogUtil.d(
                                        TAG,
                                        "已启用通知: ${characteristic.name} (${characteristic.uuid})"
                                    )
                                    observeCharacteristic(
                                        connection,
                                        service.uuid,
                                        characteristic.uuid
                                    )
                                } else {
                                    LogUtil.e(
                                        TAG,
                                        "启用通知失败: ${characteristic.name} (${characteristic.uuid})"
                                    )
                                }
                            }
                        }
                }

                if (totalCount > 0) {
                    LogUtil.d(TAG, "通知启用完成: 成功 $successCount/$totalCount 个")

                    // 通知成功启用后，按顺序执行设备初始化操作
                    if (successCount > 0) {
                        // 按顺序执行设备初始化操作

                        delay(500) // 延迟500毫秒，确保通知已经完全建立
                        setDeviceType(connection)
                        delay(500)
                        setTime(connection)
                        delay(500)
                        getDeviceVersion(connection)
                        delay(500)
                        getDeviceSN(connection)
                        delay(500)
                        readBatteryLevel(connection.getDeviceType())
                    }
                } else {
                    LogUtil.d(TAG, "未找到需要启用的通知")
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "启用通知失败", e)
            }
        }
    }


    /**
     * 设置设备类型
     * @param deviceType 要设置的设备类型
     */
    private suspend fun setDeviceType(connection: BluetoothConnection): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                if (connection.getDeviceType() == DeviceType.None) {
                    LogUtil.e(TAG, "无法设置未知类型")
                    return@withContext false
                }

                val code: Short = when (connection.getDeviceType()) {
                    DeviceType.aiNeck -> 0x0001
                    DeviceType.aiBack -> 0x0002
                    DeviceType.KneeJoint -> 0x0001
                    DeviceType.ShoulderJoint -> 0x0001
                    DeviceType.ElbowJoint -> 0x0002
                    DeviceType.HipJoint -> 0x0002
                    else -> 0x0001
                }

                // 准备写入命令
                val commandBytes = ByteBuffer.allocate(2).order(java.nio.ByteOrder.BIG_ENDIAN)
                    .putShort(code)
                    .array()

                val writeSuccess = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID,
                    commandBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (writeSuccess == true) {
                    LogUtil.d(
                        TAG,
                        "已发送设置设备类型命令: ${connection.getDeviceType()} (0x${
                            bytesToHex(commandBytes)
                        })"
                    )
                    true
                } else {
                    LogUtil.e(TAG, "发送设置设备类型 (${connection.getDeviceType()}) 命令失败")
                    false
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "设置设备类型失败", e)
                false
            }
        }
    }

    /**
     * 设置设备时间
     */
    private suspend fun setTime(connection: BluetoothConnection): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                LogUtil.d(TAG, "开始设置设备时间...")
                val calendar = Calendar.getInstance()
                val year = calendar.get(java.util.Calendar.YEAR)
                val month = calendar.get(java.util.Calendar.MONTH) + 1
                val day = calendar.get(java.util.Calendar.DAY_OF_MONTH)
                val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
                val minute = calendar.get(java.util.Calendar.MINUTE)
                val second = calendar.get(java.util.Calendar.SECOND)

                LogUtil.d(TAG, "准备设置时间: $year 年$month 月$day 日 $hour:$minute:$second")

                // 构建时间字节数组
                val timeHex =
                    "${(year / 100).toHexStr()}${(year % 100).toHexStr()}${month.toHexStr()}${day.toHexStr()}${hour.toHexStr()}${minute.toHexStr()}${second.toHexStr()}"
                val timeBytes = BluetoothUtils.hexToByteArray(timeHex)
                LogUtil.d(TAG, "Setting time with hex: $timeHex, bytes: ${bytesToHex(timeBytes)}")

                // 写入特征值
                val success = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.setTimeUUID,
                    timeBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (success == true) {
                    LogUtil.i(TAG, "Set time command sent successfully.")
                    true
                } else {
                    LogUtil.e(TAG, "Failed to send set time command.")
                    false
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "Error setting time", e)
                false
            }
        }
    }

    /**
     * 08
     * 获取设备版本信息
     */
    private suspend fun getDeviceVersion(connection: BluetoothConnection) {
        return withContext(Dispatchers.IO) {
            try {
                LogUtil.d(TAG, "开始获取设备版本...")

                // 1. 先确保版本特征的通知已启用
//                ensureVersionNotificationsEnabled(connection)

                // 2. 发送获取版本命令
                val commandBytes = byteArrayOf(0x08)
                val commandResult = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID,
                    commandBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (commandResult == true) {
                    LogUtil.d(TAG, "已发送获取版本命令: 0x08")
                    // 3. 添加延迟后直接读取版本特征
//                    delay(300)
//                    val versionData = bleService?.readCharacteristic(
//                        connection,
//                        DeviceUUID.serviceUUID,
//                        DeviceUUID.getVersionUUID
//                    )

//                    if (versionData != null && versionData.isNotEmpty()) {
//                        LogUtil.d(TAG, "直接读取到版本数据: ${bytesToHex(versionData)}")
//                        processVersionData(versionData, connection)
//                        true
//                    } else {
//                        LogUtil.w(TAG, "未能直接读取到版本数据，等待通知回调...")
//                        false
//                    }
                } else {
                    LogUtil.e(TAG, "发送获取版本命令失败")
                    false
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "获取设备版本失败", e)
                false
            }
        }
    }
    /**
     * 08
     * 获取设备版本信息
     */
    private suspend fun getDeviceSN(connection: BluetoothConnection) {
        return withContext(Dispatchers.IO) {
            try {
                LogUtil.d(TAG, "开始获取设备版本...")

                // 1. 先确保版本特征的通知已启用
//                ensureVersionNotificationsEnabled(connection)

                // 2. 发送获取版本命令
                val commandBytes = byteArrayOf(0x0A)
                val commandResult = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID,
                    commandBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (commandResult == true) {
                    LogUtil.d(TAG, "已发送获取版本命令: 0x0A")
                    // 3. 添加延迟后直接读取版本特征
//                    delay(300)
//                    val versionData = bleService?.readCharacteristic(
//                        connection,
//                        DeviceUUID.serviceUUID,
//                        DeviceUUID.getVersionUUID
//                    )

//                    if (versionData != null && versionData.isNotEmpty()) {
//                        LogUtil.d(TAG, "直接读取到版本数据: ${bytesToHex(versionData)}")
//                        processVersionData(versionData, connection)
//                        true
//                    } else {
//                        LogUtil.w(TAG, "未能直接读取到版本数据，等待通知回调...")
//                        false
//                    }
                } else {
                    LogUtil.e(TAG, "发送获取SN命令失败")
                    false
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "获取设备SN失败", e)
                false
            }
        }
    }

    /**
     * 获取特征名称
     */
    private fun getCharacteristicName(uuid: UUID): String {
        return when (uuid) {
            // 主要数据特征
            DeviceUUID.getAngleUUID -> "角度数据"
            DeviceUUID.settingsUUID -> "设备设置"
            DeviceUUID.getPowerUUID -> "电池电量"
            DeviceUUID.getVersionUUID -> "设备版本"
            DeviceUUID.getStepUUID -> "步数数据"
            DeviceUUID.getPainUUID -> "疼痛记录"
            DeviceUUID.getLocalAngleUUID -> "本地角度数据"

            // 配置特征
            DeviceUUID.setMaxAngleUUID -> "最大偏转角设置"
            DeviceUUID.setTimeUUID -> "时间设置"
            DeviceUUID.setVibrationTimeUUID -> "振动间隔设置"
            DeviceUUID.descriptorUUID -> "配置描述符"

            // 固件更新特征
            DeviceUUID.updateControlUUID -> "固件更新控制"
            DeviceUUID.updatePackageUUID -> "固件更新包传输"

            else -> "未知特征 (${uuid.toString().substring(0, 8)}...)"
        }
    }

    /**
     * 观察特征值变化
     */
    private fun observeCharacteristic(
        connection: BluetoothConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d(
                    TAG,
                    "开始观察特征: ${getCharacteristicName(characteristicUuid)} (${characteristicUuid})"
                )

                bleService!!.observeCharacteristic(
                    connection,
                    serviceUuid,
                    characteristicUuid
                )
                    .collect { it ->
                        it?.let {
                            val hexString = bytesToHex(it)
                            LogUtil.d(
                                TAG,
                                "特征值变化 [${getCharacteristicName(characteristicUuid)}]: UUID=$characteristicUuid, 数据=$hexString"
                            )

                            // 处理特定的特征值
                            when (characteristicUuid) {
                                // 电池电量特征
                                DeviceUUID.getPowerUUID -> {
                                    processBatteryData(it, connection)
                                }
                                // 版本特征
                                // 序列号特征
                                DeviceUUID.getVersionUUID -> {
                                    processVersionData(it, connection)
                                }

                                // 角度特征
                                DeviceUUID.getAngleUUID -> {
                                    // 根据设备类型选择不同的状态流
                                    when (connection.getDeviceType()) {
                                        DeviceType.aiNeck -> {
                                            parseAndStoreAngleData(
                                                it,
                                                _neckAngleTime,
                                                _neckAngle,
                                                _neckMaxAngle,
                                                "Neck实时"
                                            )
                                            // 获取并保存角度数据到数据库，包含设备时间
                                            _neckAngle.value?.let { angle ->
                                                _neckAngleTime.value?.let { timeStr ->
                                                    saveAngle(DeviceType.aiNeck, angle, timeStr)
                                                }
                                            }
                                        }

                                        DeviceType.aiBack -> {
                                            parseAndStoreAngleData(
                                                it,
                                                _backAngleTime,
                                                _backAngle,
                                                _backMaxAngle,
                                                "Back实时"
                                            )
                                            // 获取并保存角度数据到数据库，包含设备时间
                                            _backAngle.value?.let { angle ->
                                                _backAngleTime.value?.let { timeStr ->
                                                    saveAngle(DeviceType.aiBack, angle, timeStr)
                                                }
                                            }
                                        }

                                        else -> {
                                            LogUtil.d(TAG, "未知设备类型的角度数据")
                                        }
                                    }
                                }
                                // 本地角度特征
                                DeviceUUID.getLocalAngleUUID -> {
                                    // 根据设备类型选择不同的状态流
                                    when (connection.getDeviceType()) {
                                        DeviceType.aiNeck -> {
                                            parseAndStoreAngleData(
                                                it,
                                                _neckLocalAngleTime,
                                                _neckLocalAngle,
                                                _neckLocalMaxAngle,
                                                "Neck本地"
                                            )
                                            // 获取并保存角度数据到数据库，包含设备时间
                                            _neckLocalAngle.value?.let { angle ->
                                                _neckLocalAngleTime.value?.let { timeStr ->
                                                    saveAngle(DeviceType.aiNeck, angle, timeStr)
                                                }
                                            }
                                        }

                                        DeviceType.aiBack -> {
                                            parseAndStoreAngleData(
                                                it,
                                                _backLocalAngleTime,
                                                _backLocalAngle,
                                                _backLocalMaxAngle,
                                                "Back本地"
                                            )
                                            // 获取并保存角度数据到数据库，包含设备时间
                                            _backLocalAngle.value?.let { angle ->
                                                _backLocalAngleTime.value?.let { timeStr ->
                                                    saveAngle(DeviceType.aiBack, angle, timeStr)
                                                }
                                            }
                                        }

                                        else -> {
                                            LogUtil.d(TAG, "未知设备类型的本地角度数据")
                                        }
                                    }
                                }

                                DeviceUUID.getPainUUID -> {
                                    when (connection.getDeviceType()) {
                                        DeviceType.aiNeck -> {
                                            parsePainData(it, _neckPainRecords)
                                        }

                                        DeviceType.aiBack -> {
                                            parsePainData(it, _backPainRecords)
                                        }

                                        else -> {
                                            LogUtil.d(TAG, "未知设备类型的疼痛数据")
                                        }
                                    }
                                }
                                // 其他特征...可以在此处添加
                            }
                        }
                    }
            } catch (e: Exception) {
                LogUtil.e(TAG, "观察特征值失败: $characteristicUuid", e)
            }
        }
    }

    /**
     * 解析并存储角度相关数据
     */
    private fun parseAndStoreAngleData(
        data: ByteArray,
        timeFlow: MutableStateFlow<String?>,
        angleFlow: MutableStateFlow<Int?>,
        maxAngleFlow: MutableStateFlow<Int?>,
        logPrefix: String
    ) {
        LogUtil.d(
            "AngleDebug",
            "Parsing $logPrefix data. Size: ${data.size}, Hex: ${bytesToHex(data)}"
        )

        // Year (2 bytes) + M(1) + D(1) + H(1) + M(1) + S(1) + Angle(1) + MaxAngle(1) + Function(1) = 10 bytes minimum
        if (data.size >= 10) { // Updated size check to 10
            LogUtil.d("AngleDebug", "Passed size check (>= 10).")
            try {
                val year = data[0].toInt() * 100 + data[1].toInt()
                val month = data[2].toInt()
                val day = data[3].toInt()
                val hour = data[4].toInt()
                val minute = data[5].toInt()
                val second = data[6].toInt()
                // Directly format the time string
                val formattedTime = String.format(
                    "%04d年%02d月%02d日 %02d:%02d:%02d",
                    year, month, day, hour, minute, second
                )
                timeFlow.value = formattedTime

                // 解析角度和最大角度 (从原始 data 数组的索引 7 和 8 读取)
                val angle =  data[7].toInt()
                val maxAngle = Math.abs(data[8].toInt())
                LogUtil.d(
                    "AngleDebug",
                    "Parsed angles from data[7] and data[8]. Angle: $angle, MaxAngle: $maxAngle"
                    + "Formatted time: $formattedTime"
                )
                // 更新角度和最大角度到StateFlow
                angleFlow.value = angle
                maxAngleFlow.value = maxAngle
                LogUtil.d(
                    TAG,
                    "$logPrefix 角度数据: 时间=$formattedTime, 当前角度=${angle}°, 最大角度=$maxAngle°"
                )

                // 更新通知，显示设备运行模式和角度
                val deviceType = if (logPrefix.contains("Neck")) DeviceType.aiNeck else DeviceType.aiBack
                val vibrationEnabled = if (deviceType == DeviceType.aiNeck) {
                    _neckIsVibrationModeEnabled.value
                } else {
                    _backIsVibrationModeEnabled.value
                }
                
                // 确定运行模式
                val operatingMode = if (vibrationEnabled) {
                    baseApplication.getString(R.string.vibration_mode)
                } else {
                    baseApplication.getString(R.string.silent_mode)
                }
                
                // 更新BleService通知
                bleService?.let { service ->
                    val deviceName = if (deviceType == DeviceType.aiNeck) {
                        neckDeviceConfig.value.name.ifEmpty { "aiNeck" }
                    } else {
                        backDeviceConfig.value.name.ifEmpty { "aiBack" }
                    }
                    
                    // 使用格式化字符串资源
                    val title = baseApplication.getString(
                        R.string.device_notification_format,
                        deviceName,
                        operatingMode
                    )
                    
                    val currentAngleText = baseApplication.getString(R.string.current_angle, angle)
                    val maxAngleText = baseApplication.getString(R.string.max_angle, maxAngle)
                    val content = baseApplication.getString(
                        R.string.angle_notification_format,
                        currentAngleText,
                        maxAngleText
                    )
                    
                    service.updateNotification(
                        deviceType = deviceType.name,
                        title = title,
                        content = content
                    )
                }

                // 不保存实时角度数据
            } catch (e: BufferUnderflowException) {
                LogUtil.e(
                    TAG,
                    "BufferUnderflowException during $logPrefix angle parsing. Data size: ${data.size}. Hex: ${
                        bytesToHex(data)
                    }",
                    e
                )
                timeFlow.value = null
                angleFlow.value = null
                maxAngleFlow.value = null
            } catch (e: Exception) {
                LogUtil.e(
                    TAG,
                    "Exception during $logPrefix angle parsing. Data size: ${data.size}. Hex: ${
                        bytesToHex(data)
                    }",
                    e
                )
                timeFlow.value = null // Clear on parsing failure
                angleFlow.value = null
                maxAngleFlow.value = null
            }
        } else {
            LogUtil.w(
                TAG,
                "Skipping $logPrefix angle parsing. Data size insufficient: ${data.size}. Hex: ${
                    bytesToHex(data)
                }"
            )
            timeFlow.value = null
            angleFlow.value = null
            maxAngleFlow.value = null
        }
    }

    /**
     * 解析疼痛记录数据
     */
    private fun parsePainData(data: ByteArray, painRecords: MutableStateFlow<List<PainRecord>>) {
        LogUtil.d("PainDebug", "Parsing pain data. Size: ${data.size}, Hex: ${bytesToHex(data)}")

        // Year (2 bytes) + M(1) + D(1) + H(1) + M(1) + S(1) + PainAngle(1) + MaxAngle(1) = 9 bytes minimum
        if (data.size >= 9) {
            LogUtil.d("PainDebug", "Passed size check (>= 9).")
            try {
                val year = data[0].toInt() * 100 + data[1].toInt()
                val month = data[2].toInt()
                val day = data[3].toInt()
                val hour = data[4].toInt()
                val minute = data[5].toInt()
                val second = data[6].toInt()
                val painAngle = data[7].toInt() and 0xFF
                val maxAngle = data[8].toInt() and 0xFF

                // Directly format the time string
                val formattedTime = String.format(
                    "%04d年%02d月%02d日 %02d:%02d:%02d",
                    year, month, day, hour, minute, second
                )

                LogUtil.d(
                    "PainDebug",
                    "Pain record: Time=$formattedTime, PainAngle=$painAngle°, MaxAngle=$maxAngle°"
                )

                // 创建新的疼痛记录
                val painRecord = PainRecord(
                    timestamp = formattedTime,
                    painAngle = painAngle,
                    maxAngle = maxAngle,
                    year = year,
                    month = month,
                    day = day,
                    hour = hour,
                    minute = minute,
                    second = second
                )

                // 将新记录添加到列表中（添加到开头以便最新记录显示在前面）
                painRecords.update { currentList ->
                    val newList = currentList.toMutableList()
                    newList.add(0, painRecord)
                    newList
                }

                // 最多保存最近50条记录
                if (painRecords.value.size > 50) {
                    painRecords.update { it.take(50) }
                }

            } catch (e: Exception) {
                LogUtil.e(
                    TAG,
                    "Exception during pain data parsing. Data size: ${data.size}. Hex: ${
                        bytesToHex(data)
                    }",
                    e
                )
            }
        } else {
            LogUtil.w(
                TAG,
                "Skipping pain data parsing. Data size insufficient: ${data.size}. Hex: ${
                    bytesToHex(data)
                }"
            )
        }
    }


    /**
     * 将int转化为16进制格式
     * formatNum: 16进制位数，默认2位数，不足用0补齐
     */

    private fun Int.toHexStr(formatNum: Int = 2): String {
        return String.format("%0${formatNum}X", this)
    }

    /**
     * 读取设备电量
     * 该方法手动触发读取设备当前电量
     * @param deviceType 设备类型
     */
    fun readBatteryLevel(deviceType: DeviceType) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val connection = getDeviceConnection(deviceType.name) ?: return@launch

                // 读取电池电量特征值
                val value = bleService?.readCharacteristic(
                    connection,
                    DeviceUUID.batteryServiceUUID,
                    DeviceUUID.getPowerUUID
                )
                value?.let {
                    if (it.isNotEmpty()) {
                        val batteryPercent = it[0].toInt() and 0xFF
                        LogUtil.d(TAG, "读取电池电量: $batteryPercent%")
                       when(deviceType){
                           DeviceType.aiNeck ->
                               _neckBatteryLevel.value = batteryPercent
                           DeviceType.aiBack -> _backBatteryLevel.value = batteryPercent
                           else -> {}
                       }
                    }
                }

            } catch (e: Exception) {
                LogUtil.e(TAG, "读取电量时发生错误", e)
            }
        }
    }

    /**
     * 更新设备配置的特定字段
     */
    private fun updateDeviceConfigField(
        deviceType: DeviceType,
        updateBlock: (DeviceConfig) -> Unit
    ) {
        if (deviceType == DeviceType.None) return

        viewModelScope.launch {
            // 确保从DataStore读取最新配置，而不是依赖可能过期的缓存
            val existingConfig = DeviceConfigManager.ensureLoaded(deviceType)

            // 创建配置副本并更新
            val updatedConfig = existingConfig.copy().apply {
                updateBlock(this)
            }

            // 保存更新后的配置
            DeviceConfigManager.saveDeviceConfig(updatedConfig)
            LogUtil.d(TAG, "设备配置已更新: $updatedConfig")
        }
    }

    /**
     * 06
     * 07
     * 切换设备震动模式 (开: 0x07, 关: 0x06)
     */
    fun toggleVibrationMode(deviceType: DeviceType, targetState: Boolean) {
        LogUtil.d(
            TAG,
            "切换设备震动模式 (开: 0x07, 关: 0x06),deviceType: $deviceType ,targetState: $targetState"
        )
        viewModelScope.launch {
            // 检查设备是否已连接
            if (bleService == null) {
                return@launch
            }
            val connection = bleService!!.getConnectionByDeviceType(deviceType.name)
            if (connection == null) {
                return@launch
            }

            val commandByte = if (targetState) 0x07.toByte() else 0x06.toByte()
            val commandAction = if (targetState) "开启" else "关闭"

            try {
                val commandBytes = byteArrayOf(commandByte)
                val writeSuccess = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID, // 写入 settingsUUID
                    commandBytes,
                    WRITE_TYPE_DEFAULT
                )
                if (writeSuccess == true) {
                    LogUtil.d(
                        TAG,
                        "已发送${commandAction}震动模式命令: 0x${bytesToHex(commandBytes)}"
                    )
                    // 根据设备类型更新不同的震动模式状态
                    when (deviceType) {
                        DeviceType.aiNeck -> _neckIsVibrationModeEnabled.value = targetState
                        DeviceType.aiBack -> _backIsVibrationModeEnabled.value = targetState
                        else -> {
                            // 其他设备类型处理
                        }
                    }

                    // 保存震动模式状态到设备配置
                    updateDeviceConfigField(deviceType) { config ->
                        config.isVibration = targetState
                    }
                } else {
                    LogUtil.e(TAG, "发送${commandAction}震动模式命令失败")
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "${commandAction}震动模式失败", e)
            }
        }
    }

    /**
     * 设置振动提醒角度
     * 通过setMaxAngleUUID特征向设备写入振动角度
     * @param deviceType 设备类型
     * @param angle 振动角度 (1-90度)
     */
    fun setVibrationAngle(deviceType: DeviceType, angle: Int) {
        if (angle < 1 || angle > 90) {
            LogUtil.e(TAG, "设置振动角度失败：角度值超出范围(1-90)：$angle")
            return
        }

        LogUtil.d(TAG, "设置振动角度: deviceType=$deviceType, angle=$angle")

        viewModelScope.launch {
            // 检查设备是否已连接
            if (bleService == null) {
                LogUtil.e(TAG, "设置振动角度失败：BLE服务未初始化")
                return@launch
            }

            val connection = bleService!!.getConnectionByDeviceType(deviceType.name)
            if (connection == null) {
                LogUtil.e(TAG, "设置振动角度失败：未找到设备连接")
                return@launch
            }

            try {
                // 将角度转换为字节数组，由于设备读出值比设置值小1度，这里加1度进行补偿
                val compensatedAngle = if (angle < 90) angle + 1 else angle
                val angleBytes = byteArrayOf(compensatedAngle.toByte())

                LogUtil.d(TAG, "写入设备的补偿角度值: $compensatedAngle°（原始设置值: $angle°）")

                // 向设备写入角度值
                val writeSuccess = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.setMaxAngleUUID,
                    angleBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (writeSuccess == true) {
                    LogUtil.d(TAG, "振动角度设置成功: $angle°（写入设备值: $compensatedAngle°）")

                    // 保存振动角度到设备配置（使用原始角度值，不是补偿值）
                    updateDeviceConfigField(deviceType) { config ->
                        config.vibrationAngle = angle
                        config.maxAngle = angle  // 同时更新最大角度
                    }

                    // 更新最大角度状态（使用原始角度值）
                    if (deviceType == DeviceType.aiNeck) {
                        _neckMaxAngle.value = angle
                    }
                } else {
                    LogUtil.e(TAG, "振动角度设置失败")
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "设置振动角度时发生异常", e)
            }
        }
    }

    /**
     * 设置振动提醒频率
     * @param deviceType 设备类型
     * @param frequency 振动频率，单位：秒 (1-60秒)
     */
    fun setVibrationFrequency(deviceType: DeviceType, frequency: Int) {
        if (frequency < 1 || frequency > 60) {
            LogUtil.e(TAG, "设置振动频率失败：频率值超出范围(1-60)：$frequency")
            return
        }

        LogUtil.d(TAG, "设置振动频率: deviceType=$deviceType, frequency=$frequency")

        viewModelScope.launch {
            // 检查设备是否已连接
            if (bleService == null) {
                LogUtil.e(TAG, "设置振动频率失败：BLE服务未初始化")
                return@launch
            }

            val connection = bleService!!.getConnectionByDeviceType(deviceType.name)
            if (connection == null) {
                LogUtil.e(TAG, "设置振动频率失败：未找到设备连接")
                return@launch
            }

            try {
                // 将频率转换为字节数组
                val frequencyBytes = byteArrayOf(frequency.toByte())

                // 向设备写入频率值
                val writeSuccess = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.setVibrationTimeUUID,
                    frequencyBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (writeSuccess == true) {
                    LogUtil.d(TAG, "振动频率设置成功: ${frequency}秒")

                    // 保存振动频率到设备配置
                    updateDeviceConfigField(deviceType) { config ->
                        config.vibrationFrequency = frequency
                    }
                } else {
                    LogUtil.e(TAG, "振动频率设置失败")
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "设置振动频率时发生异常", e)
            }
        }
    }

    /**
     * 保存角度数据到数据库
     * @param deviceType 设备类型
     * @param angle 角度值
     */
    fun saveAngle(deviceType: DeviceType, angle: Int) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // 获取用户UUID
                val userUuid = user.uuid
                
                // 使用当前时间戳（秒）
                val timestamp = System.currentTimeMillis() / 1000
                
                // 无需检查当前时间戳的有效性，因为当前时间一定是有效的

                // 创建角度数据实体
                val angleEntity = Angle(
                    uuid = userUuid,
                    angle = angle,
                    type = deviceType.name,
                    timestamp = timestamp
                )

                // 保存到数据库
                angleDao.add(angleEntity)

                LogUtil.d(TAG, "角度数据已保存到数据库: 设备类型=${deviceType.name}, 角度=$angle°")
            } catch (e: Exception) {
                LogUtil.e(TAG, "保存角度数据到数据库失败", e)
            }
        }
    }
    
    /**
     * 保存角度数据到数据库(带设备时间)
     * @param deviceType 设备类型
     * @param angle 角度值
     * @param timeStr 设备时间字符串，格式如："2023年02月15日 14:30:25"
     */
    fun saveAngle(deviceType: DeviceType, angle: Int, timeStr: String) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                // 获取用户UUID
                val userUuid = user.uuid
                
                // 解析时间字符串为时间戳
                val timestamp = parseTimeStringToTimestamp(timeStr)
                
                // 检查时间戳是否有效（非1970年初）
                // 这里定义一个合理的下限时间戳，例如2020年1月1日的时间戳：1577836800
                val minValidTimestamp = 1577836800L // 2020-01-01 00:00:00
                
                if (timestamp < minValidTimestamp) {
                    LogUtil.w(TAG, "检测到无效时间戳（1970年或太早的日期），不保存数据: $timeStr -> $timestamp")
                    return@launch
                }
                
                // 创建角度数据实体
                val angleEntity = Angle(
                    uuid = userUuid,
                    angle = angle,
                    type = deviceType.name,
                    timestamp = timestamp
                )

                // 保存到数据库
                angleDao.add(angleEntity)

                LogUtil.d(TAG, "角度数据(带设备时间)已保存到数据库: 设备类型=${deviceType.name}, 角度=$angle°, 时间=$timeStr")
            } catch (e: Exception) {
                LogUtil.e(TAG, "保存角度数据到数据库失败", e)
            }
        }
    }
    
    /**
     * 将时间字符串解析为Unix时间戳（秒）
     * @param timeStr 格式如："2023年02月15日 14:30:25"
     * @return 时间戳（秒）
     */
    private fun parseTimeStringToTimestamp(timeStr: String): Long {
        return try {
            // 解析时间字符串
            val pattern = """(\d{4})年(\d{2})月(\d{2})日 (\d{2}):(\d{2}):(\d{2})""".toRegex()
            val matchResult = pattern.find(timeStr)
            
            if (matchResult != null) {
                val (year, month, day, hour, minute, second) = matchResult.destructured
                
                // 创建Calendar实例并设置时间
                val calendar = Calendar.getInstance()
                calendar.set(
                    year.toInt(),
                    month.toInt() - 1, // Calendar月份从0开始
                    day.toInt(),
                    hour.toInt(),
                    minute.toInt(),
                    second.toInt()
                )
                
                // 返回秒级时间戳
                calendar.timeInMillis / 1000
            } else {
                // 如果解析失败，返回当前时间
                LogUtil.e(TAG, "解析时间字符串失败: $timeStr，使用当前时间")
                System.currentTimeMillis() / 1000
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "解析时间字符串异常", e)
            System.currentTimeMillis() / 1000
        }
    }

    /**
     * 断开所有连接并禁用自动重连（用于DFU更新）
     */
    fun disconnectForDfu(address: String, deviceType: DeviceType) {
        viewModelScope.launch {
            try {
                // 设置对应设备的UI状态为升级中
                when (deviceType) {
                    DeviceType.aiNeck -> {
                        _neckDeviceUIState.value = DeviceUIState.Upgrading
                        // 设置连接状态为DFU模式
                        _neckConnectionState.value = ConnectionState.DFU(address)
                    }
                    DeviceType.aiBack -> {
                        _backDeviceUIState.value = DeviceUIState.Upgrading
                        // 设置连接状态为DFU模式
                        _backConnectionState.value = ConnectionState.DFU(address)
                    }
                    else -> {}
                }
                

                // 获取连接并设置DFU模式标志
                bleService?.getConnectionByDeviceType(deviceType.name)?.let {
                    LogUtil.d(TAG, "设备即将进入DFU模式: ${address}")
                    it.setDfuMode(true)
                    it.disconnect()
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "断开连接失败", e)
                // 如果失败，恢复UI状态
                when (deviceType) {
                    DeviceType.aiNeck -> {
                        _neckDeviceUIState.value = DeviceUIState.Error(R.string.error_connection_failed)
                    }
                    DeviceType.aiBack -> {
                        _backDeviceUIState.value = DeviceUIState.Error(R.string.error_connection_failed)
                    }
                    else -> {}
                }
            }
        }
    }

    /**
     * 更新固件版本（断开连接后）
     * 此方法在启动DFU前会断开所有现有连接
     */
    fun updateDFU(address: String, deviceType: DeviceType) {
        LogUtil.d(TAG, "准备DFU更新，先断开现有连接")


        // 先断开现有连接并禁用自动重连
        disconnectForDfu(address, deviceType)

        // 延迟一下，确保断开完成
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                LogUtil.d(TAG, "开始DFU更新")
                val initiator = DfuServiceInitiator(address)
                    .setKeepBond(false)
                    .setUnsafeExperimentalButtonlessServiceInSecureDfuEnabled(true)
                    .setPrepareDataObjectDelay(300L)
                    .setZip(R.raw.ineck6_0_6)
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    DfuServiceInitiator.createDfuNotificationChannel(activity)
                }
                initiator.start(activity, DFUService::class.java)
            } catch (e: Exception) {
                LogUtil.e(TAG, "启动DFU更新失败", e)
                when (deviceType) {
                    DeviceType.aiNeck -> _neckDeviceUIState.value = DeviceUIState.UpgradeFailed
                    DeviceType.aiBack -> _backDeviceUIState.value = DeviceUIState.UpgradeFailed
                    else -> {}
                }
            }
        }, 500) // 延迟500ms
    }

    /**
     * 开始固件升级
     */
    fun startUpgrade(mac: String, deviceType: DeviceType) {
        // 立即设置UI状态为升级中，确保连接断开时不会触发错误UI
        when (deviceType) {
            DeviceType.aiNeck -> _neckDeviceUIState.value = DeviceUIState.Upgrading
            DeviceType.aiBack -> _backDeviceUIState.value = DeviceUIState.Upgrading
            else -> {}
        }
        // 执行DFU更新
        updateDFU(mac, deviceType)
    }

    /**
     * 设置是否启用自动重连
     */
    fun setAutoReconnect(enable: Boolean, deviceType: DeviceType) {
        viewModelScope.launch {
            try {
                // 获取当前连接
                val connection = bleService?.getConnectionByDeviceType(deviceType.name)
                connection?.let {
                    LogUtil.d(TAG, "设置自动重连: $enable")
                    it.setAutoReconnect(enable)
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "设置自动重连失败", e)
            }
        }
    }

    /**
     * 处理版本数据
     */
    private fun processVersionData(data: ByteArray, connection: BluetoothConnection) {
        try {
            if (data.size >= 2) {
                val byteBuffer = ByteBuffer.wrap(data)
                val deviceDFUVer = if (byteBuffer.getShort(0) < 0) {
                    Short.MAX_VALUE - (Short.MIN_VALUE - byteBuffer.getShort(0)) + 1
                } else {
                    byteBuffer.getShort(0).toInt()
                }

                val version = "v" + ((deviceDFUVer / 10).toString()
                    .replace("0", ".") + deviceDFUVer % 10).replace(
                    "..",
                    ".0"
                )

                LogUtil.d(TAG, "处理版本数据：$deviceDFUVer -> $version")
                if (version.contains(".")) {
                    // 根据设备类型设置不同的版本值
                    when (connection.getDeviceType()) {
                        DeviceType.aiNeck -> _neckDeviceVersion.value = version
                        DeviceType.aiBack -> _backDeviceVersion.value = version
                        else -> LogUtil.d(TAG, "未知设备类型的版本: $version")
                    }

                    // 保存版本到设备配置
                    val deviceType = connection.getDeviceType()
                    if (deviceType != DeviceType.None) {
                        updateDeviceConfigField(deviceType) { config ->
                            config.version = version
                        }
                    }
                }else {  // 序列号是字符串
                    processSNData(data, connection)
                 }
            } else {
                LogUtil.w(TAG, "版本数据长度不足: ${data.size}")
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "处理版本数据失败", e)
        }
    }

    /**
     * 处理序列号数据
     */
    private fun processSNData(data: ByteArray, connection: BluetoothConnection) {
        try {
            // 序列号应该是字符串格式
            val dataString = String(data, Charsets.UTF_8).trim()
            if (dataString.isNotEmpty()) {
                LogUtil.d(TAG, "处理序列号数据：$dataString")

                // 根据设备类型设置不同的序列号值
                when (connection.getDeviceType()) {
                    DeviceType.aiNeck -> _neckDeviceSN.value = dataString
                    DeviceType.aiBack -> _backDeviceSN.value = dataString
                    else -> LogUtil.d(TAG, "未知设备类型的序列号: $dataString")
                }

                // 保存序列号到设备配置
                val deviceType = connection.getDeviceType()
                if (deviceType != DeviceType.None) {
                    updateDeviceConfigField(deviceType) { config ->
                        config.sn = dataString
                    }
                }
            } else {
                LogUtil.w(TAG, "序列号数据为空")
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "处理序列号数据失败", e)
        }
    }

    /**
     * 处理电池电量数据
     */
    private fun processBatteryData(data: ByteArray, connection: BluetoothConnection) {
        try {
            if (data.isNotEmpty()) {
                val batteryPercent = data[0].toInt() and 0xFF
                LogUtil.d(TAG, "处理电池电量数据: $batteryPercent%")

                // 根据设备类型设置不同的电池电量值
                when (connection.getDeviceType()) {
                    DeviceType.aiNeck -> _neckBatteryLevel.value = batteryPercent
                    DeviceType.aiBack -> _backBatteryLevel.value = batteryPercent
                    else -> LogUtil.d(TAG, "未知设备类型的电池电量: $batteryPercent")
                }
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "处理电池电量数据失败", e)
        }
    }

    /**
     * 开始扫描设备
     */
    fun startScan(deviceType: DeviceType) {
        LogUtil.d(TAG, "开始扫描设备，类型: $deviceType")
        
        // 停止当前可能正在进行的扫描
        stopScan()
        
        // 清除之前的错误状态和连接状态，确保UI能正确进入扫描状态
        when (deviceType) {
            DeviceType.aiNeck -> {
                _neckConnectionError.value = null
                _neckConnectionState.value = null // 清除旧的连接状态
                LogUtil.d(TAG, "已清除aiNeck设备的错误状态和连接状态")
            }
            DeviceType.aiBack -> {
                _backConnectionError.value = null
                _backConnectionState.value = null // 清除旧的连接状态
                LogUtil.d(TAG, "已清除aiBack设备的错误状态和连接状态")
            }
            else -> {}
        }
        // 也清除全局错误状态以保持兼容性
        _connectionError.value = null
        
        // 延迟开始扫描，确保蓝牙协议栈稳定
        viewModelScope.launch {
            delay(200) // 给一点时间让状态清理完成
            
            try {
                bluetoothUtil.scanDevice()
                
                // 更新所有设备的UI状态，确保扫描状态正确传播
                when (deviceType) {
                    DeviceType.aiNeck -> {
                        _neckDeviceUIState.value = DeviceUIState.Scanning
                        LogUtil.d(TAG, "aiNeck设备UI状态已设置为扫描中")
                    }
                    DeviceType.aiBack -> {
                        _backDeviceUIState.value = DeviceUIState.Scanning
                        LogUtil.d(TAG, "aiBack设备UI状态已设置为扫描中")
                    }
                    else -> {}
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "启动扫描失败", e)
                when (deviceType) {
                    DeviceType.aiNeck -> {
                        _neckDeviceUIState.value = DeviceUIState.Error(R.string.error_bluetooth_not_available)
                    }
                    DeviceType.aiBack -> {
                        _backDeviceUIState.value = DeviceUIState.Error(R.string.error_bluetooth_not_available)
                    }
                    else -> {}
                }
            }
        }
    }

    /**
     * 固件升级完成
     */
    fun onUpgradeCompleted() {
        viewModelScope.launch {
            try {
                // 更新设备特定的UI状态
                when (_currentDeviceType.value) {
                    DeviceType.aiNeck -> _neckDeviceUIState.value = DeviceUIState.UpgradeSuccess
                    DeviceType.aiBack -> _backDeviceUIState.value = DeviceUIState.UpgradeSuccess
                    else -> {}
                }
                // 清除DFU相关状态
                clearDfuState()

                // 记录升级成功日志
                LogUtil.d(TAG, "固件升级成功，已清除DFU状态，用户可以手动重新连接设备")
            } catch (e: Exception) {
                LogUtil.e(TAG, "处理升级完成时出错", e)
            }
        }
    }

    /**
     * 清除DFU相关状态，为手动重连做准备
     */
    private fun clearDfuState() {
        // 重置连接状态
        _neckDeviceConnected.value = false
        _neckDeviceConnecting.value = false
        _backDeviceConnected.value = false
        _backDeviceConnecting.value = false

        // 清除错误状态
        _neckConnectionError.value = null
        _backConnectionError.value = null
        _connectionError.value = null

        // 重置蓝牙连接相关标志（如果任何连接还存在）
        try {
            val neckConn = bleService?.getConnectionByDeviceType(DeviceType.aiNeck.name)
            neckConn?.let {
                it.setDfuMode(false) // 关闭DFU模式
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "重置DFU标志时出错", e)
        }
    }

    /**
     * 在升级成功后重置UI为扫描状态，准备重新连接设备
     */
    fun resetForReconnect() {
        viewModelScope.launch {
            try {
                // 停止当前可能正在进行的扫描
                stopScan()

                // 先解绑服务确保资源释放
                cleanupServiceConnection()

                LogUtil.d(TAG, "已重置连接状态，开始扫描设备以便重新连接")
            } catch (e: Exception) {
                LogUtil.e(TAG, "重置连接状态失败", e)
            }
        }
    }

    /**
     * 固件升级失败
     */
    fun onUpgradeFailed() {
        viewModelScope.launch {
            // 更新设备特定的UI状态
            when (_currentDeviceType.value) {
                DeviceType.aiNeck -> _neckDeviceUIState.value = DeviceUIState.UpgradeFailed
                DeviceType.aiBack -> _backDeviceUIState.value = DeviceUIState.UpgradeFailed
                else -> {}
            }
            // 部分清除DFU状态，但保留足够信息以便重试
            try {
                // 重置连接状态，但保留升级设备信息以便重试
                _neckDeviceConnected.value = false
                _neckDeviceConnecting.value = false
                _backDeviceConnected.value = false
                _backDeviceConnecting.value = false

                // 清除错误状态
                _neckConnectionError.value = null
                _backConnectionError.value = null
                _connectionError.value = null

                LogUtil.d(TAG, "固件升级失败，已部分清除状态，允许重试")
            } catch (e: Exception) {
                LogUtil.e(TAG, "处理升级失败时出错", e)
            }
        }
    }

    companion object {
        private const val TAG = "DeviceViewModel"
    }

    // 确保ViewModel清理资源
    override fun onCleared() {
        super.onCleared()
        viewModelScope.launch {
            try {
                LogUtil.d(TAG, "ViewModel正在清理资源...")

                // 停止扫描
                if (_scanUiState.value.isScanning) {
                    stopScan()
                }

                // 断开所有连接
                try {
                    // 获取当前连接的设备并断开
                    if (_neckDeviceConnected.value || _neckDeviceConnecting.value) {
                        LogUtil.d(TAG, "断开aiNeck连接")
                        val neckConn = getDeviceConnection(DeviceType.aiNeck.name)
                        neckConn?.disconnect(true)
                    }

                    if (_backDeviceConnected.value || _backDeviceConnecting.value) {
                        LogUtil.d(TAG, "断开aiBack连接")
                        val backConn = getDeviceConnection(DeviceType.aiBack.name)
                        backConn?.disconnect(true)
                    }

                    // 重置连接状态
                    _neckDeviceConnected.value = false
                    _neckDeviceConnecting.value = false
                    _backDeviceConnected.value = false
                    _backDeviceConnecting.value = false
                } catch (e: Exception) {
                    LogUtil.e(TAG, "断开连接失败", e)
                }

                // 解绑服务
                cleanupServiceConnection()

                // 重置所有状态
                _connectionError.value = null
                _currentDeviceType.value = DeviceType.None

                LogUtil.d(TAG, "ViewModel资源清理完成")
            } catch (e: Exception) {
                LogUtil.e(TAG, "清理资源失败", e)
            }
        }
    }

    /**
     * 检查设备版本并判断是否需要升级
     * @param deviceAddress 设备MAC地址
     */
    private fun needDeviceVersion(deviceAddress: String): Boolean {
        val version = _neckDeviceVersion.value
        if (!version.isNullOrEmpty() && version != "v6.0.6") {
            LogUtil.d(TAG, "设备版本($version)不是V6.0.6，需要强制升级")
            // 保存要升级的设备MAC
            return true
        } else {
            LogUtil.d(TAG, "设备版本($version)验证通过，准备导航到主页")
            return false
        }
    }

    /**
     * 查找设备，发送震动指令
     * @param deviceType 设备类型
     * @param enable 是否开启查找（震动）
     */
    fun findDevice(deviceType: DeviceType, enable: Boolean) {
        // 保存当前状态的任务引用，便于取消
        val deviceFindingJob = when (deviceType) {
            DeviceType.aiNeck -> {
                // 更新状态
                _neckIsFindingDevice.value = enable
                if (!enable) {
                    // 如果是关闭查找，则取消任务
                    findingJobMap[deviceType.name]?.cancel()
                    findingJobMap.remove(deviceType.name)
                    return
                }
                // 继续执行开启查找的逻辑
                null
            }
            DeviceType.aiBack -> {
                // 更新状态
                _backIsFindingDevice.value = enable
                if (!enable) {
                    // 如果是关闭查找，则取消任务
                    findingJobMap[deviceType.name]?.cancel()
                    findingJobMap.remove(deviceType.name)
                    return
                }
                // 继续执行开启查找的逻辑
                null
            }
            else -> {
                LogUtil.w(TAG, "未支持的设备类型: $deviceType")
                return
            }
        }

        // 如果是启用查找功能
        if (enable) {
            val job = viewModelScope.launch {
                try {
                    LogUtil.d(TAG, "开始查找设备循环任务 ($deviceType)")
                    
                    // 检查设备是否已连接
                    if (bleService == null) {
                        LogUtil.e(TAG, "服务未初始化，无法查找设备")
                        return@launch
                    }
                    
                    val connection = bleService!!.getConnectionByDeviceType(deviceType.name)
                    if (connection == null) {
                        LogUtil.e(TAG, "未找到设备连接，无法查找设备")
                        return@launch
                    }
                    
                    // 循环发送震动命令，每500ms发送一次
                    while (isActive) {
                        sendVibrationCommand(connection, deviceType)
                        delay(1000) // 每500毫秒发送一次命令
                    }
                } catch (e: Exception) {
                    LogUtil.e(TAG, "查找设备循环任务出错", e)
                }
            }
            
            // 保存任务引用以便后续取消
            findingJobMap[deviceType.name] = job
        }
    }
    
    // 存储设备查找任务的Map
    private val findingJobMap = mutableMapOf<String, Job>()
    
    /**
     * 发送单次震动命令
     */
    private suspend fun sendVibrationCommand(connection: BluetoothConnection, deviceType: DeviceType) {
        try {
            // 固定发送0x05命令
            val commandByte = 0x05.toByte()
            val commandBytes = byteArrayOf(commandByte)
            
            val writeSuccess = bleService?.writeCharacteristic(
                connection,
                DeviceUUID.serviceUUID,
                DeviceUUID.settingsUUID,
                commandBytes,
                WRITE_TYPE_DEFAULT
            )
            
            if (writeSuccess == true) {
                LogUtil.d(TAG, "发送震动命令成功: 0x05")
            } else {
                LogUtil.e(TAG, "发送震动命令失败")
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "发送震动命令出错", e)
        }
    }

    /**
     * 设置设备名称
     * @param deviceType 设备类型
     * @param name 设备名称
     */
    fun setDeviceName(deviceType: DeviceType, name: String) {
        if (name.isBlank()) {
            LogUtil.e(TAG, "设置设备名称失败：名称不能为空")
            return
        }

        LogUtil.d(TAG, "设置设备名称: deviceType=$deviceType, name=$name")

        viewModelScope.launch {
            // 保存设备名称到设备配置
            updateDeviceConfigField(deviceType) { config ->
                config.name = name
            }
        }
    }

    /**
     * 设置振动强度
     * @param deviceType 设备类型
     * @param intensity 振动强度 (0-15)
     */
    fun setVibrationIntensity(deviceType: DeviceType, intensity: Int) {
        if (intensity < 0 || intensity > 15) {
            LogUtil.e(TAG, "设置振动强度失败：强度值超出范围(0-15)：$intensity")
            return
        }

        LogUtil.d(TAG, "设置振动强度: deviceType=$deviceType, intensity=$intensity")

        viewModelScope.launch {
            // 检查设备是否已连接
            if (bleService == null) {
                LogUtil.e(TAG, "设置振动强度失败：BLE服务未初始化")
                return@launch
            }

            val connection = bleService!!.getConnectionByDeviceType(deviceType.name)
            if (connection == null) {
                LogUtil.e(TAG, "设置振动强度失败：未找到设备连接")
                return@launch
            }

            try {
                // 将命令和强度转换为字节数组
                val intensityBytes = byteArrayOf(0xF0.toByte(), intensity.toByte())

                LogUtil.d(TAG, "写入设备的振动强度命令和值: 0xF0, $intensity")

                // 向设备写入强度值
                val writeSuccess = bleService?.writeCharacteristic(
                    connection,
                    DeviceUUID.serviceUUID,
                    DeviceUUID.settingsUUID, // 使用设置特征
                    intensityBytes,
                    WRITE_TYPE_DEFAULT
                )

                if (writeSuccess == true) {
                    LogUtil.d(TAG, "振动强度设置成功: $intensity")

                    // 保存振动强度到设备配置
                    updateDeviceConfigField(deviceType) { config ->
                        config.vibrationIntensity = intensity
                    }
                } else {
                    LogUtil.e(TAG, "振动强度设置失败")
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "设置振动强度时发生异常", e)
            }
        }
    }

    /**
     * 应用设备保存的设置
     * 在设备连接成功且无需升级时调用
     * @param deviceType 设备类型
     */
    private fun applyDeviceSettings(deviceType: DeviceType) {
        viewModelScope.launch {
            try {
                // 延迟一下，确保设备完全准备好接收命令
                delay(1000)
                
                LogUtil.d(TAG, "开始应用${deviceType.name}设备的保存设置")
                
                // 获取设备配置
                val config = when (deviceType) {
                    DeviceType.aiNeck -> neckDeviceConfig.value
                    DeviceType.aiBack -> backDeviceConfig.value
                    else -> {
                        LogUtil.w(TAG, "未知设备类型，跳过设置应用: $deviceType")
                        return@launch
                    }
                }
                
                LogUtil.d(TAG, "设备配置: 震动强度=${config.vibrationIntensity}, 震动角度=${config.vibrationAngle}, 震动频率=${config.vibrationFrequency}, 震动模式=${config.isVibration}")
                
                // 应用震动强度设置（如果有保存的值且不是默认值）
                if (config.vibrationIntensity > 0) {
                    LogUtil.d(TAG, "应用震动强度设置: ${config.vibrationIntensity}")
                    applyVibrationIntensitySetting(deviceType, config.vibrationIntensity)
                    delay(300) // 给设备处理时间
                }
                
                // 应用震动角度设置（如果有保存的值且设备已校准）
                if (config.vibrationAngle > 0 && config.isCalibrated) {
                    LogUtil.d(TAG, "应用震动角度设置: ${config.vibrationAngle}")
                    setVibrationAngle(deviceType, config.vibrationAngle)
                    delay(300) // 给设备处理时间
                }

                // 应用震动模式设置
                LogUtil.d(TAG, "应用震动模式设置: ${config.isVibration}")
                toggleVibrationMode(deviceType, config.isVibration)
                
                // 更新UI中的震动状态
                when (deviceType) {
                    DeviceType.aiNeck -> {
                        _neckIsVibrationModeEnabled.value = config.isVibration
                    }
                    DeviceType.aiBack -> {
                        _backIsVibrationModeEnabled.value = config.isVibration
                    }
                    else -> {}
                }
                
                LogUtil.d(TAG, "${deviceType.name}设备设置应用完成")
                
            } catch (e: Exception) {
                LogUtil.e(TAG, "应用设备设置时发生异常", e)
            }
        }
    }
    
    /**
     * 内部方法：应用震动强度设置（不保存到配置）
     * @param deviceType 设备类型
     * @param intensity 震动强度 (0-15)
     */
    private suspend fun applyVibrationIntensitySetting(deviceType: DeviceType, intensity: Int) {
        try {
            // 检查设备是否已连接
            if (bleService == null) {
                LogUtil.e(TAG, "应用振动强度失败：BLE服务未初始化")
                return
            }

            val connection = bleService!!.getConnectionByDeviceType(deviceType.name)
            if (connection == null) {
                LogUtil.e(TAG, "应用振动强度失败：未找到设备连接")
                return
            }

            // 将命令和强度转换为字节数组
            val intensityBytes = byteArrayOf(0xF0.toByte(), intensity.toByte())

            LogUtil.d(TAG, "应用保存的振动强度: 0xF0, $intensity")

            // 向设备写入强度值
            val writeSuccess = bleService?.writeCharacteristic(
                connection,
                DeviceUUID.serviceUUID,
                DeviceUUID.settingsUUID,
                intensityBytes,
                WRITE_TYPE_DEFAULT
            )

            if (writeSuccess == true) {
                LogUtil.d(TAG, "振动强度应用成功: $intensity")
            } else {
                LogUtil.e(TAG, "振动强度应用失败")
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "应用振动强度时发生异常", e)
        }
    }

    /**
     * 连接到设备 - 优化：增强错误处理与资源管理
     */
    suspend fun connectToDevice(
        address: String,
        deviceType: DeviceType = DeviceType.None,
        retryCount: Int = 0
    ) {
        withContext(Dispatchers.IO) {
            var connection: BluetoothConnection? = null

            try {
                LogUtil.d(TAG, "========== 开始连接设备 ==========")
                LogUtil.d(TAG, "设备地址: $address")
                LogUtil.d(TAG, "设备类型: $deviceType")
                LogUtil.d(TAG, "重试次数: $retryCount")
                LogUtil.d(TAG, "当前线程: ${Thread.currentThread().name}")
                
                // 检查蓝牙是否可用
                if (!bluetoothUtil.isBluetoothAvailable()) {
                    LogUtil.e(TAG, "蓝牙不可用，连接失败")
                    updateConnectionError(deviceType, ConnectionError.BluetoothNotAvailable)
                    return@withContext
                }
                LogUtil.d(TAG, "✓ 蓝牙可用性检查通过")

                // 检查服务是否已初始化
                val service = bleService
                if (service == null) {
                    LogUtil.e(TAG, "BLE服务未初始化，连接失败")
                    updateConnectionError(deviceType, ConnectionError.ServiceNotInitialized)
                    return@withContext
                }
                LogUtil.d(TAG, "✓ BLE服务检查通过")

                if (address.isEmpty()) {
                    LogUtil.e(TAG, "设备地址为空，连接失败")
                    updateConnectionError(deviceType, ConnectionError.InvalidAddress)
                    return@withContext
                }
                LogUtil.d(TAG, "✓ 设备地址有效性检查通过")

                val device = bluetoothUtil.getRemoteDevice(address)
                if (device == null) {
                    LogUtil.e(TAG, "无法获取远程设备，连接失败")
                    updateConnectionError(deviceType, ConnectionError.DeviceNotFound)
                    return@withContext
                }
                LogUtil.d(TAG, "✓ 远程设备获取成功")

                LogUtil.d(TAG, "开始创建连接，重试次数: $retryCount")

                // 创建连接
                connection = service.createConnection(device, true, deviceType)
                if (connection == null) {
                    updateConnectionError(deviceType, ConnectionError.ConnectionFailed)
                    return@withContext
                }

                // 监听连接状态变化
                val stateJob = launch {
                    // 从 service 的 connection 中收集连接状态
                    connection.connectionState.collectLatest {
                        when (deviceType) {
                            DeviceType.aiNeck -> {
                                _neckConnectionState.value = it
                            }

                            DeviceType.aiBack -> {
                                _backConnectionState.value = it
                            }

                            else -> {}
                        }
                    }
                }

                try {
                    // 增加超时时间到25秒，给设备更多时间响应
                    val result = withTimeout(25000L) {
                        connection.connect()
                    }

                    if (result == true) {
                        LogUtil.d(TAG, "设备连接成功，开始启用通知")
                        launch {
                            enableAllNotifications(connection)
                        }
                        saveDeviceInfoToLocal(address, deviceType)
                    } else {
                        stateJob.cancel()
                        
                        // 如果是第一次重试，尝试重新连接
                        if (retryCount < 1) {
                            LogUtil.w(TAG, "第一次连接失败，准备重试... 当前重试次数: $retryCount")
                            connection.disconnect(true)
                            delay(2000) // 等待2秒后重试
                            
                            connectToDevice(address, deviceType, retryCount + 1)
                            return@withContext
                        }
                        
                        updateConnectionError(deviceType, ConnectionError.ConnectionFailed)
                        // 确保断开连接并清理资源
                        connection.disconnect(true)
                    }
                } catch (e: TimeoutCancellationException) {
                    stateJob.cancel()
                    
                    // 如果是第一次重试，尝试重新连接
                    if (retryCount < 1) {
                        LogUtil.w(TAG, "连接超时，准备重试... 当前重试次数: $retryCount")
                        connection.disconnect(true)
                        delay(2000) // 等待2秒后重试
                        
                        connectToDevice(address, deviceType, retryCount + 1)
                        return@withContext
                    }
                    
                    updateConnectionError(deviceType, ConnectionError.ConnectionFailed)
                    LogUtil.e(TAG, "连接设备超时 (重试${retryCount}次后失败): $address")

                    // 确保断开连接并清理资源
                    connection.disconnect(true)
                } catch (e: Exception) {
                    stateJob.cancel()
                    
                    // 如果是第一次重试且不是严重错误，尝试重新连接
                    if (retryCount < 1 && !isSeriousError(e)) {
                        LogUtil.e(TAG, "连接异常，准备重试... 当前重试次数: $retryCount", e)
                        connection.disconnect(true)
                        delay(2000) // 等待2秒后重试
                        
                        connectToDevice(address, deviceType, retryCount + 1)
                        return@withContext
                    }
                    
                    updateConnectionError(deviceType, ConnectionError.UnknownError(e.message ?: "Unknown error"))
                    LogUtil.e(TAG, "连接设备失败 (重试${retryCount}次后失败): $address", e)

                    // 确保断开连接并清理资源
                    connection.disconnect(true)
                }
            } catch (e: Exception) {
                updateConnectionError(deviceType, ConnectionError.UnknownError(e.message ?: "Unknown error"))
                LogUtil.e(TAG, "创建连接过程中出错", e)

                // 确保连接被断开和清理
                connection?.disconnect(true)
            } finally {
                // 连接失败时重置状态
                if (getConnectionError(deviceType) != null) {
                    // 重置连接状态
                    when (deviceType) {
                        DeviceType.aiNeck -> {
                            _neckDeviceConnecting.value = false
                        }

                        DeviceType.aiBack -> {
                            _backDeviceConnecting.value = false
                        }

                        else -> {}
                    }
                }
            }
        }
    }
    
    /**
     * 判断是否为严重错误，严重错误不进行重试
     */
    private fun isSeriousError(e: Exception): Boolean {
        return when {
            e.message?.contains("SecurityException") == true -> true
            e.message?.contains("IllegalStateException") == true -> true
            e.message?.contains("OutOfMemoryError") == true -> true
            else -> false
        }
    }

    /**
     * 根据设备类型更新对应的错误状态
     */
    private fun updateConnectionError(deviceType: DeviceType, error: ConnectionError) {
        when (deviceType) {
            DeviceType.aiNeck -> {
                _neckConnectionError.value = error
                // 暂时也更新全局错误状态以保持兼容性
                _connectionError.value = error
            }
            DeviceType.aiBack -> {
                _backConnectionError.value = error
            }
            else -> {
                _connectionError.value = error
            }
        }
    }

    /**
     * 根据设备类型获取对应的错误状态
     */
    private fun getConnectionError(deviceType: DeviceType): ConnectionError? {
        return when (deviceType) {
            DeviceType.aiNeck -> _neckConnectionError.value
            DeviceType.aiBack -> _backConnectionError.value
            else -> _connectionError.value
        }
    }
}

/**
 * 蓝牙服务模型
 */
data class ServiceModel(
    val uuid: UUID,
    val name: String,
    val characteristics: List<CharacteristicModel> = emptyList()
) {
    /**
     * 蓝牙特征模型
     */
    data class CharacteristicModel(
        val uuid: UUID,
        val name: String,
        val properties: Int
    ) {
        // 是否可读
        val isReadable: Boolean
            get() = (properties and 2) != 0 // PROPERTY_READ = 2

        // 是否可写
        val isWritable: Boolean
            get() = (properties and 8) != 0 // PROPERTY_WRITE = 8

        // 是否支持通知
        val isNotifiable: Boolean
            get() = (properties and 16) != 0 // PROPERTY_NOTIFY = 16
    }
}

/**
 * 疼痛记录数据类
 */
data class PainRecord(
    val timestamp: String,  // 格式化后的时间字符串
    val painAngle: Int,     // 疼痛时的角度
    val maxAngle: Int,      // 疼痛时的最大角度
    val year: Int,
    val month: Int,
    val day: Int,
    val hour: Int,
    val minute: Int,
    val second: Int
)