package org.aihealth.ineck.viewmodel

import android.widget.Toast
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.google.gson.Gson
import kotlinx.coroutines.flow.MutableStateFlow
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.saveToLocal
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.view.preview.OrderListType
import org.aihealth.ineck.view.preview.VipBenefitObject
import org.aihealth.ineck.view.screen.membershipcenter.OrderData

class MemberShipCenterViewModel: BaseViewModel() {
    // 是否已阅读选中状态
    var isChecked by mutableStateOf(false)
    var dialogVisible by mutableStateOf(false)

    var vipBenefitsListWithSilver = mutableListOf<VipBenefitObject>().apply {
        add(
            VipBenefitObject(
                R.drawable.ic_practiced_first,
                activity.getString(R.string.the_new_lesson_is_practiced_first)
            )
        )
        add(
            VipBenefitObject(
                R.drawable.ic_vip_select_class,
                activity.getString(R.string.vip_select_class)
            )
        )
        add(
            VipBenefitObject(
                R.drawable.ic_24_hours_training,
                activity.getString(R.string._24_hours_training)
            )
        )
        add(
            VipBenefitObject(
                R.drawable.ic_member_benefits,
                activity.getString(R.string.member_benefits)
            )
        )
//    add(VipBenefitObject(R.drawable.ic_member_welfare_community,"会员福利社区"))
    }.toList()

    val orderDataListWithNewOne = mutableStateListOf<OrderData>().apply {
        add(
            OrderData(
                baseApplication.getString(R.string.trial_30),
                "0",
                "19"
            )
                .apply {
                    this.index = 0
                    this.isChoose = true
                })
        add(
            OrderData(
                baseApplication.getString(R.string.monthly_card_member),
                "18",
                "39"
            )
                .apply {
                    this.index = 1
                })
        add(
            OrderData(
                baseApplication.getString(R.string.annual_card_member),
                "88",
                "468"
            )
                .apply {
                    this.index = 2
                }
        )
        add(
            OrderData(
                baseApplication.getString(R.string.wearable_device_1_set_3_year_gold_card_membership),
                "599",
                "2088"
            )
                .apply {
                    this.index = 4
                })
    }
    var orderDataListWithOldOne = mutableStateListOf<OrderData>().apply {
        add(
            OrderData(
                baseApplication.getString(R.string.monthly_card_member),
                "18",
                "39"
            )
                .apply {
                    this.index = 1
                })
        add(
            OrderData(
                baseApplication.getString(R.string.annual_card_member),
                "88",
                "468"
            )
                .apply {
                    this.index = 2
                }
        )
        add(
            OrderData(
                baseApplication.getString(R.string.wearable_device_1_set_3_year_gold_card_membership),
                "599",
                "2088"
            )
                .apply {
                    this.index = 4
                })

    }

    fun order() {
        checkIsRead {
            if (currentOrderListType.value == OrderListType.Wearable) {
                startScreen(Screen.PaymentScreen.route + "${3}", false)
            } else {
                orderDataListWithNewOne.forEach {
                    if (it.isChoose) {
                        LogUtil.i("choose the ${it.orderName}")
                        if (it.index == 0) {
                            DialogUtil.showLoading()
                            apiService.postFreeTrial().enqueueBody {
                                apiService.getInfo().enqueueBody { response ->
                                    DialogUtil.hideLoading()
                                    if (response?.code == 1) {
                                        LogUtil.i("user: ${response.data.toJson()}")
                                        try {
                                            val gson = Gson()
                                            val info =
                                                gson.fromJson(response.data, User::class.java)
                                            LogUtil.i("user: $user")
                                            user = info
                                            user.saveToLocal()
                                            popScreen(Screen.Main.route)
                                        } catch (e: Exception) {
                                            DialogUtil.showToast(baseApplication.getString(R.string.online_data_error))
                                        }
                                    } else if (response?.code == 0) {
                                        startScreen(Screen.FirstUpdateData.route, true)
                                    } else {
                                        startScreen(Screen.Login.route, true)
                                    }
                                }
                            }

                        } else {
                            if (isInChina) {
                                startScreen(Screen.PaymentScreen.route + "${it.index}", false)

                            } else {
                                Toast.makeText(
                                    activity,
                                    baseApplication.getString(R.string.stay_tuned),
                                    Toast.LENGTH_SHORT
                                ).show()

                            }
                        }
                    }
                }
            }

        }
    }

    fun orderOlder() {
        checkIsRead {
            if (currentOrderListType.value == OrderListType.Wearable) {
                startScreen(Screen.PaymentScreen.route + "${3}", false)
            } else {
                orderDataListWithOldOne.forEach {
                    if (it.isChoose) {
                        LogUtil.i("choose the ${it.orderName}")
                        if (isInChina) {
                            startScreen(Screen.PaymentScreen.route + "${it.index}", false)

                        } else {
                            Toast.makeText(
                                activity,
                                baseApplication.getString(R.string.stay_tuned),
                                Toast.LENGTH_SHORT
                            ).show()

                        }
                    }
                }
            }

        }
    }

    fun clearCheck() {
        orderDataListWithNewOne.forEach {
            it.isChoose = false
        }
        orderDataListWithOldOne.forEach {
            it.isChoose = false
        }
    }

    private inline fun checkIsRead(
        content: () -> Unit
    ) {
        if (isChecked) {
            content()
        } else {
            dialogVisible = true
        }
    }

    var currentOrderListType = mutableStateOf(OrderListType.Vision)
    var renewal: MutableStateFlow<Boolean> = MutableStateFlow(false)
//    fun getClient(){
//        val client = BillingHelper(activity)
//        viewModelScope.launch {
//            client.startConnection(activity)
//            client.processPurchases {
//                LogUtil.e("productDetails: $it")
//                launch {
//                    it?.let {
//                        client.launchPurchaseFlow(activity,it)
//                    }
//                }
//
//            }
//        }
//    }


//    suspend fun processPurchases(): ProductDetailsResult {
//        val productList = listOf(
//            QueryProductDetailsParams.Product.newBuilder()
//                .setProductId("com.aihealth.re.membership.monthly")
//                .setProductType(BillingClient.ProductType.INAPP)
//                .build()
//        )
//        val params = QueryProductDetailsParams.newBuilder()
//        params.setProductList(productList)
//
//        // leverage queryProductDetails Kotlin extension function
//        val productDetailsResult = withContext(Dispatchers.IO) {
////            billingClient.queryProductDetails(params.build())
//        }
//        return productDetailsResult
//        // Process the result.
//    }
}