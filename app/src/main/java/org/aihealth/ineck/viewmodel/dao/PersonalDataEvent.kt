package org.aihealth.ineck.viewmodel.dao

import java.util.Calendar

interface PersonalDataEvent {

    /**
     * 拍照获取图片
     */
    fun getPictureFromCamera()

    /**
     * 从相册中选取图片
     */
    fun getPictureFromAlbum()

    /**
     * 修改姓名
     */
    fun setName(name: String)

    /**
     * 修改性别
     */
    fun setGender(gender: String)

    /**
     * 修改生日
     */
    fun setBirthday(calendar: Calendar)

    /**
     * 修改身高
     */
    fun setHeight(height: Double)

    /**
     * 修改体重
     */
    fun setWeight(weight: Double)


}