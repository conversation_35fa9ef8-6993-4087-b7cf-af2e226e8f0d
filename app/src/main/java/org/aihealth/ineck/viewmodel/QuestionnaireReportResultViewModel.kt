package org.aihealth.ineck.viewmodel

import org.aihealth.ineck.R
import org.aihealth.ineck.model.Answer
import org.aihealth.ineck.model.QuestionModel
import org.aihealth.ineck.util.localeResources

class QuestionnaireReportResultViewModel : BaseViewModel() {
    companion object {
        val questionList = mutableListOf<QuestionModel>().apply {
            add(
                QuestionModel(
                    id = "1",
                    type = "SCQ",
                    text = localeResources.getString(R.string.questionnaire_1),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_1_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_1_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_1_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_1_answer_4)),
                        Answer("E", localeResources.getString(R.string.questionnaire_1_answer_5))
                    )
                )
            )
            add(
                QuestionModel(
                    id = "2",
                    type = "MCQs",
                    text = localeResources.getString(R.string.questionnaire_2),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_2_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_2_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_2_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_2_answer_4)),
                        Answer("E", localeResources.getString(R.string.questionnaire_2_answer_5)),
                        Answer("F", localeResources.getString(R.string.questionnaire_2_answer_6))
                    )
                )
            )
            add(
                QuestionModel(
                    id = "3",
                    type = "SCQ",
                    text = localeResources.getString(R.string.questionnaire_3),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_3_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_3_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_3_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_3_answer_4)),
                    )
                )
            )
            add(
                QuestionModel(
                    id = "4",
                    type = "MCQs",
                    text = localeResources.getString(R.string.questionnaire_4),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_4_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_4_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_4_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_4_answer_4)),
                        Answer("E", localeResources.getString(R.string.questionnaire_4_answer_5)),
                        Answer("F", localeResources.getString(R.string.questionnaire_4_answer_6)),
                        Answer("G", localeResources.getString(R.string.questionnaire_4_answer_7))
                    )
                )
            )
            add(
                QuestionModel(
                    id = "5",
                    type = "MCQs",
                    text = localeResources.getString(R.string.questionnaire_5),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_5_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_5_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_5_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_5_answer_4)),
                        Answer("E", localeResources.getString(R.string.questionnaire_5_answer_5)),
                        Answer("F", localeResources.getString(R.string.questionnaire_5_answer_6)),
                        Answer("G", localeResources.getString(R.string.questionnaire_5_answer_7)),
                        Answer("H", localeResources.getString(R.string.questionnaire_5_answer_8)),
                        Answer("I", localeResources.getString(R.string.questionnaire_5_answer_9)),
                        Answer("J", localeResources.getString(R.string.questionnaire_5_answer_10))
                    )
                )
            )
            add(
                QuestionModel(
                    id = "6",
                    type = "MCQs",
                    text = localeResources.getString(R.string.questionnaire_6),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_6_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_6_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_6_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_6_answer_4)),
                        Answer("E", localeResources.getString(R.string.questionnaire_6_answer_5)),
                        Answer("F", localeResources.getString(R.string.questionnaire_6_answer_6)),
                    )
                )
            )
            add(
                QuestionModel(
                    id = "7",
                    type = "SCQ",
                    text = localeResources.getString(R.string.questionnaire_7),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_7_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_7_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_7_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_7_answer_4)),
                        Answer("E", localeResources.getString(R.string.questionnaire_7_answer_5)),
                    )
                )
            )
            add(
                QuestionModel(
                    id = "8",
                    type = "SCQ",
                    text = localeResources.getString(R.string.questionnaire_8),
                    answer = listOf(
                        Answer("A", localeResources.getString(R.string.questionnaire_8_answer_1)),
                        Answer("B", localeResources.getString(R.string.questionnaire_8_answer_2)),
                        Answer("C", localeResources.getString(R.string.questionnaire_8_answer_3)),
                        Answer("D", localeResources.getString(R.string.questionnaire_8_answer_4)),
                    )
                )
            )


        }

    }
}