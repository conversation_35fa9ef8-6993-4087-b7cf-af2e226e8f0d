package org.aihealth.ineck.viewmodel.dao

import kotlinx.coroutines.flow.MutableStateFlow

abstract class MyScreenEvent{

    /**
     *  退出登录提示对话框显示状态
     */
    val isVisibleLogoutTipDialog: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /**
     * 点击退出登录按钮
     */
    abstract fun onLogoutClick()

    /**
     *  退出注销登录提示对话框显示状态
     */
    abstract fun changeIsVisibleLogoutTipDialog(state: Boolean)

}