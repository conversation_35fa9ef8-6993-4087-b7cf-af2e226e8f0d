package org.aihealth.ineck.viewmodel.impl

import androidx.lifecycle.viewModelScope
import com.auth0.android.authentication.AuthenticationException
import com.auth0.android.callback.Callback
import com.auth0.android.provider.WebAuthProvider
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.auth0Account
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.util.AuthingUtil
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.token
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.dao.MyScreenEvent
import org.aihealth.ineck.viewmodel.user
import org.aihealth.ineck.util.clearAllUserData
import org.aihealth.ineck.util.StepModuleManager

class MyScreenEventImpl(
    private val viewModel: MainViewModel
) : MyScreenEvent() {
    override fun onLogoutClick() {
        DialogUtil.showLoading()
        LogUtil.i(localeResources.configuration.locales.toLanguageTags())
        if (isInChina) {
            authingLogout()
        } else {
            LogUtil.i("Logout begin")
            auth0Logout()
        }

    }

    private fun auth0Logout() {
        val endTime = System.currentTimeMillis() / 1000
        viewModel.postTimeStamp()
        activity.addTimeStamp(endTime)
        viewModel.getTimeStamp()
        val context = MainActivity.getInstance()
        
        LogUtil.i("🚪 MyScreenEventImpl: ========== 开始处理用户登出 ==========")
        
        // 用户登出，自动关闭步数模块
        LogUtil.i("🚪 MyScreenEventImpl: 开始处理步数模块登出逻辑")
        StepModuleManager.onUserLogout()
        
        // 清空所有用户数据（用户信息、步数数据、设备token等）
        LogUtil.i("🚪 MyScreenEventImpl: 开始清空用户数据")
        clearAllUserData()
        
        userSP.edit().clear().apply()
        user = User()
        WebAuthProvider
            .logout(auth0Account)
            .withScheme(localeResources.getString(R.string.com_auth0_scheme))
            .start(context, object : Callback<Void?, AuthenticationException> {
                override fun onSuccess(result: Void?) {
                    // The user has been logged out!
                    LogUtil.i("✅ MyScreenEventImpl: Auth0登出成功")
                    LogUtil.i("🚪 MyScreenEventImpl: 跳转到登录界面")
                    startScreen(Screen.Login.route, true)
                    MainViewModel.pageIndex = 0
                    DialogUtil.hideLoading()
                    LogUtil.i("🚪 MyScreenEventImpl: ========== 登出处理完成 ==========")
                }

                override fun onFailure(error: AuthenticationException) {
                    // Something went wrong!
                    LogUtil.d("Logout error!!!")
                    startScreen(Screen.Login.route, true)
                    DialogUtil.hideLoading()
                }
            })


    }

    private fun authingLogout() {
        viewModel.viewModelScope.launch {
            val isLogout = AuthingUtil.logout(token)
            if (isLogout) {

                val endTime = System.currentTimeMillis() / 1000
                viewModel.postTimeStamp()
                activity.addTimeStamp(endTime)
                
                LogUtil.i("🚪 MyScreenEventImpl: ========== 开始处理用户登出(中国) ==========")
                
                // 用户登出，自动关闭步数模块
                LogUtil.i("🚪 MyScreenEventImpl: 开始处理步数模块登出逻辑")
                StepModuleManager.onUserLogout()
                
                // 清空所有用户数据（用户信息、步数数据、设备token等）
                LogUtil.i("🚪 MyScreenEventImpl: 开始清空用户数据")
                clearAllUserData()
                
                userSP.edit().clear().apply()
                user = User()
                DialogUtil.hideLoading()
                LogUtil.i("✅ MyScreenEventImpl: 中国登出成功，跳转到登录界面")
                startScreen(Screen.Login.route, true)
                MainViewModel.pageIndex = 0
                LogUtil.i("🚪 MyScreenEventImpl: ========== 登出处理完成 ==========")
            }
        }
    }

    override fun changeIsVisibleLogoutTipDialog(state: Boolean) {
        this.isVisibleLogoutTipDialog.update { state }
    }

}