package org.aihealth.ineck.viewmodel

import android.annotation.SuppressLint
import com.google.gson.Gson
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.GoodItem
import org.aihealth.ineck.model.MemberShipLevel
import org.aihealth.ineck.model.PayWayItem
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.WeChatPayService
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.saveToLocal
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.wxapi.WechatCallback
import org.aihealth.ineck.wxapi.WechatPayResult

class PaymentViewModel: BaseViewModel() {

    private var memberShipLevelList = mutableListOf<MemberShipLevel>()
        .apply {
            add(MemberShipLevel("silver_1m"))
            add(MemberShipLevel("silver_1y"))
//            add(MemberShipLevel("silver_2y"))
            add(MemberShipLevel("gold_3y"))
        }
    var payWayList = mutableListOf<PayWayItem>()
        .apply {
            add(
                PayWayItem(R.drawable.ic_payment_wechat, activity.getString(R.string.wechat_pay))
                .apply {
                    isCheck = false
                })
        }
    var goodList = mutableListOf<GoodItem>()
        .apply {
            add(GoodItem(R.drawable.ic_payment_sliver, activity.getString(R.string.sliver_30_day),"￥18","￥39"))
            add(GoodItem(R.drawable.ic_payment_sliver, activity.getString(R.string.sliver_12_month),"￥88","￥468"))
//            add(GoodItem(R.drawable.ic_payment_sliver, activity.getString(R.string.sliver_2_year),"￥168","￥936"))
            add(GoodItem(R.drawable.ic_payment_device, activity.getString(R.string.aiSpine_device),"1","￥599"))
            add(GoodItem(R.drawable.ic_payment_glod_vip, activity.getString(R.string.gold_3_year),"￥0","199"))
        }
    @SuppressLint("SuspiciousIndentation")
    fun payWayClick(index:Int){
        LogUtil.i("payWayClick: $index")
        var count = 0
        payWayList.forEachIndexed { _, payWayItem ->
            if(payWayItem.isCheck){
                count++
                if(payWayItem.payName == activity.getString(R.string.wechat_pay)){
                    val memberShipLevel = memberShipLevelList[index-1]
                        apiService.postWechatPay(
                            memberShipLevel
                        ).enqueueBody {
                            LogUtil.i("payWayClick"+it.toString())
                            if(it?.code == 1){
                                val weChatPayService = WeChatPayService()
                                val preId = it.data.preId
                                val nonceStr = it.data.nonceStr
                                val timeStamp = it.data.timeStamp
                                val sign = it.data.sign
                                val outTradeNo = it.data.outTradeNo
                                weChatPayService.payFromWX(
                                    prepayId = preId,
                                    nonceStr = nonceStr,
                                    timeStamp = timeStamp,
                                    sign = sign,
                                    object : WechatCallback {
                                        override fun call(result: WechatPayResult) {
                                            LogUtil.i("wechatCallback: $result")
//                                            DialogUtil.showToast("PayCallback: $result")
                                            when (result.errCode) {
                                                0 -> {
                                                    apiService.getWechatBill(
                                                        outTradeNo = outTradeNo
                                                    ).enqueueBody { res->
                                                        LogUtil.i("getWechatBill"+res.toString())
                                                        if(res?.code == 1 && res.data.paymentStatus){
                                                            apiService.getInfo().enqueueBody { response ->
                                                                DialogUtil.hideLoading()
                                                                if (response?.code == 1) {
                                                                    LogUtil.i("user: ${response.data.toJson()}")
                                                                    try {
                                                                        val gson = Gson()
                                                                        val info = gson.fromJson(
                                                                            response.data,
                                                                            User::class.java
                                                                        )
                                                                        LogUtil.i("user: $user")
                                                                        user = info
                                                                        user.saveToLocal()
                                                                        popScreen(Screen.Main.route)
                                                                    } catch (e: Exception) {
                                                                        DialogUtil.showToast(
                                                                            baseApplication.getString(
                                                                                R.string.online_data_error
                                                                            )
                                                                        )
                                                                    }
                                                                } else if (response?.code == 0) {
                                                                    startScreen(
                                                                        Screen.FirstUpdateData.route,
                                                                        true
                                                                    )
                                                                } else {
                                                                    startScreen(
                                                                        Screen.Login.route,
                                                                        true
                                                                    )
                                                                }
                                                            }
                                                        } else {
                                                            DialogUtil.showToast(
                                                                baseApplication.getString(
                                                                    R.string.pay_failed
                                                                )
                                                            )
                                                        }
                                                    }
                                                }
                                                -2 -> {
                                                    DialogUtil.showToast(
                                                        baseApplication.getString(
                                                            R.string.pay_cancel
                                                        )
                                                    )
                                                }
                                                else -> {
                                                    DialogUtil.showToast(
                                                        baseApplication.getString(
                                                            R.string.pay_failed
                                                        )
                                                    )
                                                }
                                            }
                                        }
                                    }
                                )
                            }
                        }
//                    viewModelScope.launch(Dispatchers.IO) {
//                        var weChatPayService = WeChatPayService()
//                        weChatPayService.init(activity)
//                        weChatPayService.prepayOrder()
////                        weChatPayService.payWX()
//                        var memberShipLevel = memberShipLevelList[index-1]
////                        apiService.postWechatPay(
////                            memberShipLevel
////                        ).enqueueBody {
////                            LogUtil.i("payWayClick"+it.toString())
////                            if(it?.code == 1){
////                                val gson = Gson()
////                                val preId = it.data?.get("prepay_id")?.asString
////                                weChatPayService.payWX(prepayId = preId!!)
////                            }
////                            else {
////
////                            }
////                        }
//                    }


//                    weChatPayService.pay()
                }
            }
        }
        if(count == 0){
            DialogUtil.showToast(
                baseApplication.getString(
                    R.string.please_select_payment_method
                )
            )
        }

    }

}

