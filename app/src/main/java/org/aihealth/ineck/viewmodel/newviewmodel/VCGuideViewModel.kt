package org.aihealth.ineck.viewmodel.newviewmodel

import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import org.aihealth.ineck.model.Queue
import org.aihealth.ineck.viewmodel.dao.AngleWithTimestamp


class VCGuideViewModel(
    context: Context
) : ViewModel() {
    /* 当前引导检测页执行状态 */
    private val _currentVCGuideProcessState: MutableStateFlow<VCGuideProcessState> =
        MutableStateFlow(VCGuideProcessState.PreviousGuidePage)

    val currentVCGuideProcessState = _currentVCGuideProcessState

    fun changeGuideDetectProcessState(newState: VCGuideProcessState) {
        _currentVCGuideProcessState.value = newState
    }

    /* 播报静音状态 */
    private val _isMuteState: MutableStateFlow<Boolean> = MutableStateFlow(false)

    val isMuteState = _isMuteState

    fun changeCurrentMuteState(newState: Boolean) {
        this._isMuteState.update { newState }
    }

    /** 引导检测结果 */
    private val _detectedResult: MutableStateFlow<VCDetectingResult> =
        MutableStateFlow(VCDetectingResult.None)

    val detectedResult = _detectedResult

    fun changeDetectedResult(newState: VCDetectingResult) {
        _detectedResult.update { newState }
    }

    /** 检测过程中面部角度队列 */
    var angleWithTimestampQueueForDetecting = mutableStateOf(Queue<AngleWithTimestamp>())

    /** 检测过程中 测量开始时间戳 - 从有效数据开始计算 */
    var startTimestampForEffectDetecting = MutableStateFlow(0L)

    fun writeDetectedResult(result: VCDetectingResult) {
        this.detectedResult.update { result }
    }

    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by mutableStateOf(false)
}


sealed class VCDetectingResult {
    data object None : VCDetectingResult()
    data class DetectingSuccess(val result: Any) : VCDetectingResult()
    data class DetectingError(val error: VCDetectedError) : VCDetectingResult()
}

/**
 *  引导检测过程状态
 */
sealed class VCGuideProcessState {
    /* 引导前提示页面 */
    data object PreviousGuidePage : VCGuideProcessState() {
        fun toPageNumber(): Int {
            return 0
        }
    }

    /* 检测运行中页面 */
    data object DetectingPage : VCGuideProcessState() {
        fun toPageNumber(): Int {
            return 1
        }
    }

    /* 检测结束页 */
    data object DetectedPage : VCGuideProcessState() {
        fun toPageNumber(): Int {
            return 2
        }
    }

    fun toStateType(pageNumber: Int): VCGuideProcessState {
        return when (pageNumber) {
            0 -> PreviousGuidePage
            1 -> DetectingPage
            2 -> DetectedPage
            else -> PreviousGuidePage
        }
    }

}

sealed class VCDetectedError {
    data class Error(val error: String) : VCDetectedError()
    data class NoObject(val error: String = "NoObject") : VCDetectedError()
    data class Timeout(val error: String = "TimeOut") : VCDetectedError()
    data class Cancel(val error: String = "Cancel") : VCDetectedError()
}