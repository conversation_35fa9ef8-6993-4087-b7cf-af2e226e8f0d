package org.aihealth.ineck.viewmodel

import com.google.gson.Gson
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import org.aihealth.ineck.R
import org.aihealth.ineck.model.vitalsigns.*
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.*
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState
import org.aihealth.ineck.viewmodel.dao.HistoryVitalSignsEvent
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.TimeZone

/**
 *  生命体征历史记录视图模型
 *  @param  vitalSignsState 生命体征提交对话框显示状态
 *  @param  isCelsius   显示体温历史记录数据是否采用摄氏度单位
 */
class HistoryVitalSignsViewModel(
    vitalSignsState: VitalSignPostDialogVisibleState,
    isCelsius: <PERSON><PERSON><PERSON>,
    isMmolL: <PERSON><PERSON><PERSON>,
) : BaseViewModel(), HistoryVitalSignsEvent {

    /**
     *  上传生命体征数据底部抽屉显示标识
     */
    val vitalSignPostDialogVisibleState: MutableStateFlow<VitalSignPostDialogVisibleState> =
        MutableStateFlow(VitalSignPostDialogVisibleState.NONE)

    /* 生命体征数据历史集合 */
    var resultList =
        MutableStateFlow<VitalSignHistoryResultState>(VitalSignHistoryResultState.Loading)

    /* 是否采用摄氏度作为体温历史记录显示单位 */
    val isCelsius = MutableStateFlow(isCelsius)

    /* 生命体征卡片血糖测量值测量单位是否采用 毫摩尔/升(mmol/L) */
    val isMmolL = MutableStateFlow(isMmolL)

    init {
        loadVitalSignHistoryData(vitalSignsState = vitalSignsState)
    }

    fun changeVitalSignPostDialogVisibleState(state: VitalSignPostDialogVisibleState) {
        this.vitalSignPostDialogVisibleState.update { state }
    }

    /* 请求加载生命体征历史数据 */
    override fun loadVitalSignHistoryData(vitalSignsState: VitalSignPostDialogVisibleState) {
        /* 更新结果集的状态为Loading */
        resultList.update { VitalSignHistoryResultState.Loading }

        val calendar = Calendar.getInstance(TimeZone.getTimeZone("UTC"))

        // Format for ISO-8601 UTC timestamps
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'")
        dateFormat.timeZone = TimeZone.getTimeZone("UTC")

        // Set endDate to the current time
        val endDate = calendar.clone() as Calendar

        // Copy the endDate and subtract 3 months to get startDate
        val startDate = calendar.clone() as Calendar
        startDate.add(Calendar.MONTH, -3)

        // Format the start and end times
        val startTime = dateFormat.format(startDate.time)

        endDate.add(Calendar.DATE, 1) // Add one day to end date
        val endTime = dateFormat.format(endDate.time)

        when (vitalSignsState) {
            VitalSignPostDialogVisibleState.NONE -> { /* Nothing */
            }

            VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> {
                /* 请求心率历史数据 */
                apiService.getHistoryOfHeartRate(
                    fromDate = startTime,
                    toDate = endTime
                ).enqueueBody { response ->
                    if (response!!.code == -1) {
                        this.resultList.update {
                            VitalSignHistoryResultState.Success(listOf())
                        }
                    } else {
                        try {
                            LogUtil.i("心率历史数据：${response.data}")
                            val data: List<HeartRateHistoryUnit> = Gson().fromJson(
                                response.data.toString(),
                                Array<HeartRateHistoryUnit>::class.java
                            ).toList()
                            this.resultList.update {
                                VitalSignHistoryResultState.Success(data)
                            }
                        } catch (e: Exception) {
                            LogUtil.d("_chen", "请求心率历史数据, 响应发生错误")
                            this.resultList.update {
                                VitalSignHistoryResultState.Failure(e)
                            }
                        }
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> {
                /* 请求体温历史数据 */
                apiService.getHistoryOfBodyTemperature(
                    fromDate = startTime,
                    toDate = endTime
                ).enqueueBody { response ->
                    LogUtil.i("BodyTemperature:${response.toString()}")
                    if (response!!.code == -1) {
                        this.resultList.update {
                            VitalSignHistoryResultState.Success(listOf())
                        }
                    } else {
                        try {
                            val data: List<BodyTemperatureHistoryUnit> = Gson().fromJson(
                                response.data.toString(),
                                Array<BodyTemperatureHistoryUnit>::class.java
                            ).toList()
                            this.resultList.update {
                                VitalSignHistoryResultState.Success(data)
                            }
                        } catch (e: Exception) {
                            LogUtil.d("_chen", "请求体温历史数据, 响应发生错误")
                            this.resultList.update {
                                VitalSignHistoryResultState.Failure(e)
                            }
                        }
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> {
                /* 请求血氧饱和度历史数据 */
                apiService.getHistoryOfBloodOxygen(
                    fromDate = startTime,
                    toDate = endTime
                ).enqueueBody { response ->
                    LogUtil.i("BloodOxygen:${response.toString()}")
                    if (response!!.code == -1) {
                        this.resultList.update {
                            VitalSignHistoryResultState.Success(listOf())
                        }
                    } else {
                        try {
                            val data: List<BloodOxygenHistoryUnit> = Gson().fromJson(
                                response.data.toString(),
                                Array<BloodOxygenHistoryUnit>::class.java
                            ).toList()
                            this.resultList.update {
                                VitalSignHistoryResultState.Success(data)
                            }
                        } catch (e: Exception) {
                            LogUtil.d("_chen", "请求血氧饱和度历史数据, 响应发生错误")
                            this.resultList.update {
                                VitalSignHistoryResultState.Failure(e)
                            }
                        }
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> {
                /* 请求血压历史数据 */
                apiService.getHistoryOfBloodPressure(
                    fromDate = startTime,
                    toDate = endTime
                ).enqueueBody { response ->
                    // 当code 为 -1 时 代表没有数据
                    if (response!!.code == -1) {
                        this.resultList.update {
                            VitalSignHistoryResultState.Success(listOf())
                        }
                    } else {
                        try {
                            val data: List<BloodPressureHistoryUnit> = Gson().fromJson(
                                response.data.toString(),
                                Array<BloodPressureHistoryUnit>::class.java
                            ).toList()
                            this.resultList.update {
                                VitalSignHistoryResultState.Success(data)
                            }
                        } catch (e: Exception) {
                            LogUtil.d("_chen", "请求血压历史数据, 响应发生错误")
                            this.resultList.update {
                                VitalSignHistoryResultState.Failure(e)
                            }
                        }
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> {
                /* 请求血糖历史数据 */
                apiService.getHistoryOfBloodGlucose(
                    fromDate = startTime,
                    toDate = endTime
                ).enqueueBody { response ->
                    if (response!!.code == -1) {
                        this.resultList.update {
                            VitalSignHistoryResultState.Success(listOf())
                        }
                    } else {
                        try {
                            val data: List<BloodGlucoseHistoryUnit> = Gson().fromJson(
                                response.data.toString(),
                                Array<BloodGlucoseHistoryUnit>::class.java
                            ).toList()
                            this.resultList.update {
                                VitalSignHistoryResultState.Success(data)
                            }
                        } catch (e: Exception) {
                            LogUtil.d("_chen", "请求血糖历史数据, 响应发生错误")
                            this.resultList.update {
                                VitalSignHistoryResultState.Failure(e)
                            }
                        }
                    }
                }
            }
        }
    }

    fun postVitalSignData(
        data: String,
        toastEvent: (Boolean, Int) -> Unit,
    ) {
        when (this.vitalSignPostDialogVisibleState.value) {
            VitalSignPostDialogVisibleState.NONE -> {
                /* Nothing */
            }

            VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> {
                /* 上传心率数据逻辑 */
                apiService.postHeartRate(
                    body = HeartRate(value = data.toInt())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.heart_rate)
                        } else {
                            toastEvent(false, R.string.heart_rate)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.heart_rate)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> {
                /* 上传体温数据逻辑 */
                apiService.postBodyTemperature(
                    body = BodyTemperature(temperature = data.toFloat())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> {
                /* 上传血氧数据逻辑 */
                apiService.postBloodOxygen(
                    body = BloodOxygen(saturation = data.toFloat())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> {
                val (diastolic, systolic) = data.split("/")
                /* 上传血压数据逻辑 */
                apiService.postBloodPressure(
                    body = BloodPressure(diastolic = diastolic.toInt(), systolic = systolic.toInt())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> {
                /* 上传血糖数据逻辑 */
                apiService.postBloodGlucose(
                    body = BloodGlucose(level = data.toFloat())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.blood_glucose)
                        } else {
                            toastEvent(false, R.string.blood_glucose)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.blood_glucose)
                    }
                }
            }
        }
    }
    fun changeIsCelsiusState() {
        this.isCelsius.update { !isCelsius.value }
    }

    fun changeIsMmolL() {
        this.isMmolL.update { !isMmolL.value }
    }
}

/**
 *  生命体征历史记录结果加载状态
 */
sealed class VitalSignHistoryResultState {
    data object Loading : VitalSignHistoryResultState()
    class Failure(err: Exception) : VitalSignHistoryResultState()
    class Success(val data: List<VitalSigns>) : VitalSignHistoryResultState()
}