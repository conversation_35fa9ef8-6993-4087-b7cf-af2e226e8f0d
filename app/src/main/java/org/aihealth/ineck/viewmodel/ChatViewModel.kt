package org.aihealth.ineck.viewmodel

import android.Manifest
import android.app.Activity
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.JsonParser
import io.socket.client.IO
import io.socket.client.Socket
import io.socket.emitter.Emitter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.model.chat.CallDurationContent
import org.aihealth.ineck.model.chat.ChatMessageEntity
import org.aihealth.ineck.model.chat.ChatSession
import org.aihealth.ineck.model.chat.ChatSmsEntity
import org.aihealth.ineck.model.chat.FollowContent
import org.aihealth.ineck.model.chat.MeetContent
import org.aihealth.ineck.model.chat.MessageType
import org.aihealth.ineck.model.chat.PhoneRequestContent
import org.aihealth.ineck.model.chat.ReportContent
import org.aihealth.ineck.model.chat.getFollowContent
import org.aihealth.ineck.model.chat.getMeetContent
import org.aihealth.ineck.model.chat.getPhoneRequestContent
import org.aihealth.ineck.model.improvement.ChatItem
import org.aihealth.ineck.model.rtm.UseTimeMeta
import org.aihealth.ineck.model.rtm.UseTimeType
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.service.CallService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil.parseIsoDateTimeToTimestamp
import org.json.JSONObject
import java.net.URISyntaxException
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

class ChatViewModel : BaseViewModel() {

    // Socket.IO 相关
    private var socket: Socket? = null
    private var isSocketConnected = false
    private val _isConnected = MutableStateFlow(false)
    val isConnected: StateFlow<Boolean> = _isConnected
    private var roomId = ""

    // 当前聊天会话的用户ID
    private val _currentUserId = MutableStateFlow<String?>(null)
    val currentUserId: StateFlow<String?> = _currentUserId

    // 当前聊天会话的用户名
    private val _currentUserName = MutableStateFlow<String?>(null)
    val currentUserName: StateFlow<String?> = _currentUserName

    // 聊天会话数据
    private val _chatSession = MutableStateFlow<ChatSession?>(null)
    val chatSession: StateFlow<ChatSession?> = _chatSession

    // 临时会话（在API返回前使用）
    private val _tempSession = MutableStateFlow<ChatSession?>(null)

    // 聊天列表数据
    private val _chatItems = MutableStateFlow<List<ChatItem>>(emptyList())
    val chatItems: StateFlow<List<ChatItem>> = _chatItems

    // 聊天列表加载状态
    private val _isChatListLoading = MutableStateFlow(false)
    val isChatListLoading: StateFlow<Boolean> = _isChatListLoading

    // 消息列表
    private val _chatMessageList = MutableStateFlow<List<ChatMessageEntity>>(emptyList())
    val chatMessagesList: StateFlow<List<ChatMessageEntity>> = _chatMessageList

    // 消息是否已加载完成
    private val _isMessagesLoaded = MutableStateFlow(false)
    val isMessagesLoaded: StateFlow<Boolean> = _isMessagesLoaded

    // Call service connection
    private var callService: CallService? = null
    private var callServiceBound = false

    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            try {
                val binder = service as CallService.CallBinder
                callService = binder.getService()
                callServiceBound = true

                // Set callback to update UI while call is in progress
                callService?.setDurationCallback { duration ->
                    // This can be used to update UI in real-time if needed
                    LogUtil.d("Call in progress, duration: $duration seconds")
                }

                LogUtil.d("Successfully connected to call service")
            } catch (e: Exception) {
                LogUtil.e("Error connecting to call service: ${e.message}")
                callServiceBound = false
                callService = null
            }
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            LogUtil.d("Call service disconnected")
            callServiceBound = false
            callService = null
        }
    }

    // Add these variables near the top of the class
    private var callStarted = false
    private var callPhoneNumber = ""
    private var callStartTimestamp = 0L


    // 初始化Socket连接
    private fun initSocket() {
        if (socket != null) {
            // 如果Socket已存在但断开了，尝试重新连接
            if (!isSocketConnected) {
                socket?.connect()
                LogUtil.d("Socket.IO - 尝试重新连接现有Socket")
            }
            return
        }

        try {
            val options = IO.Options()
            // 设置认证信息等
            options.reconnection = true
            options.reconnectionAttempts = 10
            options.reconnectionDelay = 1000

            // 连接到Socket.IO服务器
            socket = when (isInChina) {
                true -> IO.socket("ws://52.83.183.229:4000", options)
                else -> IO.socket("ws://54.172.111.218:4000", options)
            }
            // 设置连接事件监听
            socket?.on(Socket.EVENT_CONNECT, onConnect)
            socket?.on(Socket.EVENT_DISCONNECT, onDisconnect)
            socket?.on(Socket.EVENT_CONNECT_ERROR, onConnectError)

            // 监听接收消息事件
            socket?.on(SocketIOEvent.JOIN_OR_LEAVE_ROOM_CALLBACK, message)
            socket?.on(SocketIOEvent.RECEIVE_MESSAGE, receiveMessageCallback)

            // 建立连接
            socket?.connect()

            LogUtil.d("Socket.IO - 初始化Socket连接")
        } catch (e: URISyntaxException) {
            LogUtil.e("Socket.IO - 初始化Socket失败: ${e.message}")
        }
    }

    /**
     * 公开方法，供UI层调用以确保Socket已初始化
     */
    fun initSocketIfNeeded() {
        LogUtil.d("Socket.IO - 检查并初始化Socket连接")
        initSocket()
    }

    /**
     * 公开方法，强制重新加入聊天室
     */
    fun joinRoomIfConnected() {
        if (isSocketConnected && _currentUserId.value != null) {
            LogUtil.d("Socket.IO - 强制重新加入聊天室")
            joinChatRoom()
        } else {
            LogUtil.d("Socket.IO - 无法加入聊天室: 连接状态=$isSocketConnected, 当前用户ID=${_currentUserId.value}")
        }
    }

    // 连接成功事件处理
    private val onConnect = Emitter.Listener {
        _isConnected.value = true
        isSocketConnected = true
        LogUtil.d("Socket.IO - 连接成功")

        // 连接成功后，注册当前用户
        viewModelScope.launch {
            joinChatRoom()
        }
    }

    // 断开连接事件处理
    private val onDisconnect = Emitter.Listener {
        _isConnected.value = false
        isSocketConnected = false
        LogUtil.d("Socket.IO - 连接断开")
    }

    // 连接错误事件处理
    private val onConnectError = Emitter.Listener {
        _isConnected.value = false
        isSocketConnected = false
        LogUtil.e("Socket.IO - 连接错误: ${it.firstOrNull()}")
    }


    // 接收聊天室加入回调和历史消息
    private val message = Emitter.Listener { args ->
        viewModelScope.launch {
            try {
                if (args.isNotEmpty() && args[0] is JSONObject) {
                    val data = args[0] as JSONObject
                    LogUtil.d("Socket.IO - 收到聊天室回调: $data")
                    // 获取聊天室ID
                    roomId = data.optString("room", "")
                    if (roomId.isNotEmpty()) {
                        LogUtil.d("Socket.IO - 成功加入聊天室: $roomId")
                    }
                    val msg = data.optJSONObject("msg")
                    // 检查是否为消息更新通知
                    if (msg != null) {
                        LogUtil.d("Socket.IO - 消息更新: 消息=$msg")
                        try {
                            // 处理消息更新（例如更新会议卡片）
                            handleMessageUpdate(msg)
                            return@launch
                        } catch (e: Exception) {
                            LogUtil.e("Socket.IO - 处理消息更新失败: ${e.message}")
                        }
                    }
                    // 处理历史消息
                    if (data.has("message")) {
                        try {
                            val historyArray = data.getJSONArray("message")
                            LogUtil.d("Socket.IO - 收到历史消息: ${historyArray.length()}条")

                            // 历史消息列表
                            val messageList = mutableListOf<ChatMessageEntity>()

                            for (i in 0 until historyArray.length()) {
                                try {
                                    val msgObj = historyArray.getJSONObject(i)
                                    val fromUserId = msgObj.getString("from_user")
                                    val toUserId = msgObj.getString("to_user")
                                    val messageContent = msgObj.getString("msg")
                                    val status = msgObj.getInt("status")
                                    val dateTimeStr = msgObj.getString("date_time")
                                    val msgId = msgObj.optLong("msg_id", 0L)

                                    // 解析ISO 8601格式的日期时间
                                    val timestamp = parseIsoDateTimeToTimestamp(dateTimeStr)

                                    // 判断消息类型（发送/接收）
                                    val isSentByMe = fromUserId == user.uuid
                                    val messageType =
                                        if (isSentByMe) MessageType.SEND else MessageType.RECEIVE

                                    // 创建ChatSmsEntity
                                    val chatEntity = ChatMessageEntity(
                                        fromUserUid = fromUserId,
                                        toUserUid = toUserId,
                                        message = messageContent,
                                        dateTime = timestamp,
                                        messageId = msgId,
                                        status = status,
                                        messageType = messageType
                                    )

                                    messageList.add(chatEntity)
                                } catch (e: Exception) {

                                }

                            }
                            // 按时间排序并更新UI
                            updateMessages(messageList.sortedByDescending { it.dateTime })

                        } catch (e: Exception) {
                            LogUtil.e("Socket.IO - 解析历史消息失败: ${e.message}")
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtil.e("Socket.IO - 处理加入聊天室回调异常: ${e.message}")
            }
        }
    }

    // 处理消息更新（例如会议请求被接受）
    private fun handleMessageUpdate(newMsg: JSONObject) {
        try {
            val msgId = newMsg.getLong("msg_id")
            // 从当前消息列表中查找对应的消息
            val currentMessages = _chatMessageList.value.toMutableList()
            val messageIndex = currentMessages.indexOfFirst { it.messageId == msgId }

            if (messageIndex >= 0) {
                // 找到匹配的消息，更新它的内容
                val existingMessage = currentMessages[messageIndex]
                val updatedMessage = existingMessage.copy(
                    message = newMsg.optString("msg")
                )
                currentMessages[messageIndex] = updatedMessage
                _chatMessageList.value = currentMessages
                LogUtil.d("Socket.IO - 成功更新消息: msgId=$msgId")
            } else {
                LogUtil.d("Socket.IO - 未找到需要更新的消息: msgId=$msgId")
            }
        } catch (e: Exception) {
            LogUtil.e("Socket.IO - 处理消息更新失败: ${e.message}")
        }
    }

    // 接收实时消息回调
    private val receiveMessageCallback = Emitter.Listener { args ->
        viewModelScope.launch {
            try {
                if (args.isNotEmpty() && args[0] is JSONObject) {
                    val data = args[0] as JSONObject
                    val jsonObject = data.getJSONObject("msg")
                    LogUtil.d("Socket.IO - 收到实时消息-msg: $jsonObject")
                    // 解析新的消息格式: {"msg":"消息内容","sender":"发送者ID","msg_time":"2025-03-21T13:59:21Z"}
                    val fromUserId = jsonObject.optString("from_user", "")
                    val messageContent = jsonObject.optString("msg", "")
                    val dateTimeStr = jsonObject.optString("date_time", "")
                    val toUuid = jsonObject.optString("to_uuid", "")
                    val msgId = jsonObject.optLong("msg_id", 0L)
                    val status = jsonObject.optInt("status", 0)
                    // 解析ISO 8601格式的日期时间
                    val timestamp = if (dateTimeStr.isNotEmpty()) {
                        parseIsoDateTimeToTimestamp(dateTimeStr)
                    } else {
                        System.currentTimeMillis()
                    }

                    // 判断这个消息是否与当前聊天有关 (发送者是当前聊天对象或当前用户)
                    val currentUserId = _currentUserId.value
                    // 判断消息类型（发送/接收）
                    val isSentByMe = fromUserId == user.uuid

                    // 如果是我发送的消息，不要重复添加 (避免双重渲染)
                    // 因为我们已经在sendSocketMessage中添加了本地消息
                    val messageType = if (isSentByMe) {
                        MessageType.SEND
                    } else {
                        MessageType.RECEIVE
                    }
                    // 创建ChatSmsEntity
                    val chatEntity = ChatMessageEntity(
                        fromUserUid = fromUserId,
                        toUserUid = toUuid,
                        message = messageContent,
                        messageType = messageType,
                        dateTime = timestamp,
                        status = status,
                        messageId = msgId
                    )

                    // 将新消息添加到现有列表并更新UI
                    val currentMessages = _chatMessageList.value.toMutableList()

                    // 检查是否已存在相同消息，避免重复
                    val existingMsg = currentMessages.find {
                        it.messageId == chatEntity.messageId
                    }

                    if (existingMsg == null) {
                        currentMessages.add(0, chatEntity) // 添加到列表顶部
                        _chatMessageList.value =
                            currentMessages.sortedByDescending { it.dateTime }
                    }
                }
            } catch (e: Exception) {
                LogUtil.e("Socket.IO - 处理实时消息异常: ${e.message}")
            }
        }
    }

    // 发送消息到Socket服务器
    private fun sendSocketMessage(message: String) {
        val providerUuid = _currentUserId.value ?: return
        if (isSocketConnected) {
            try {
                // 创建符合服务器格式的消息对象
                val socketMsg = JSONObject().apply {
                    put("sender_id", user.uuid)
                    put("receiver_id", providerUuid)
                    put("message", message)
                    put("status", 0) // 未读状态
                }

                // 发送消息到服务器
                socket?.emit(SocketIOEvent.SEND_MESSAGE, socketMsg)
                LogUtil.d("Socket.IO - 发送消息: $socketMsg")
            } catch (e: Exception) {
                LogUtil.e("Socket.IO - 发送消息失败: ${e.message}")
            }
        }
    }

    /**
     * 发送消息 - 更新为使用Socket.IO
     */
    fun sendMessage(message: String, onComplete: () -> Unit = {}) {
        viewModelScope.launch {
            try {
                // 通过Socket.IO发送消息
                sendSocketMessage(message)
                // 消息发送后回调
                onComplete()
            } catch (e: Exception) {
                LogUtil.e("Socket.IO - 发送消息异常: ${e.message}")
            }
        }
    }

    /**
     * 发送报告 - 更新为使用Socket.IO
     */
    fun sendReport(
        reportContent: ReportContent,
        onComplete: () -> Unit = {}
    ) {
        viewModelScope.launch {
            try {
                val gson = Gson()
                val reportMsg = gson.toJson(reportContent)

                // 通过Socket.IO发送报告消息
                sendSocketMessage(reportMsg)
                onComplete()
            } catch (e: Exception) {
                LogUtil.e("Exception when sending report: ${e.message}")
            }
        }
    }

    /**
     * 发送电话记录
     */
    fun sendCallRecord(callRecordContent: CallDurationContent) {
        viewModelScope.launch {
            try {
                val gson = Gson()
                val callDurationJson = gson.toJson(callRecordContent)
                sendSocketMessage(callDurationJson)
            } catch (e: Exception) {
                LogUtil.e("Exception when call record: ${e.message}")
            }
        }

    }

    // 清除当前聊天会话数据 - 修改以离开Socket.IO聊天室
    fun clearCurrentChat() {
        // 离开当前聊天室
        val currentUuid = _currentUserId.value
        if (currentUuid != null && isSocketConnected) {
            try {
                val leaveData = JSONObject().apply {
                    put("user_id", user.uuid)
                    put("room_id", roomId)
                }
                socket?.emit(SocketIOEvent.LEAVE_ROOM, leaveData)
                LogUtil.d("Socket.IO - 离开聊天室: sender=${user.uuid}, receiver=$currentUuid, room=$roomId")

                // 注意：不要在这里断开Socket连接，只是离开聊天室
                // 这样当用户重新进入聊天时可以复用连接
            } catch (e: Exception) {
                LogUtil.e("Socket.IO - 离开聊天室失败: ${e.message}")
            }
        }

        // 清除当前聊天数据
        _currentUserId.value = null
        _currentUserName.value = null
        _chatSession.value = null
        _tempSession.value = null
        _chatMessageList.value = emptyList()
        _isMessagesLoaded.value = false  // 重置消息加载状态
        roomId = ""
    }

    // 注册当前用户到Socket服务器并加入聊天室
    private fun joinChatRoom() {
        val userId = user.uuid
        val providerId = _currentUserId.value  // 修改这里，使用currentUserId而不是chatSession

        LogUtil.d("Socket.IO - 尝试加入聊天室: userId=$userId, providerId=$providerId, isSocketConnected=$isSocketConnected")

        if (userId.isNotEmpty() && providerId != null && isSocketConnected) {
            try {
                val roomData = JSONObject().apply {
                    put("sender_id", userId)
                    put("receiver_id", providerId)
                }
                socket?.emit(SocketIOEvent.JOIN_ROOM, roomData)
                LogUtil.d("Socket.IO - 发送加入聊天室请求: sender=$userId, receiver=$providerId")
            } catch (e: Exception) {
                LogUtil.e("Socket.IO - 加入聊天室失败: ${e.message}")
            }
        } else {
            LogUtil.e("Socket.IO - 无法加入聊天室: 条件不满足")
        }
    }

    /**
     * 获取聊天列表数据
     */
    fun fetchChatList() {
        if (_isChatListLoading.value) return

        _isChatListLoading.value = true
        viewModelScope.launch {
            apiService.getChatList().enqueue(
                onResponse = { _, response ->
                    _isChatListLoading.value = false
                    response?.apply {
                        if (code == 1) {
                            val filteredList = data.filter {
                                !it.uuid.isNullOrEmpty() &&
                                        !it.lastMessage.isNullOrEmpty()
                            }.sortedByDescending { it.time }
                            _chatItems.value = filteredList
                        }
                    }
                },
                onFailure = { _, throwable ->
                    _isChatListLoading.value = false
                    LogUtil.d("getChatList failure: ${throwable.message}")
                }
            )
        }
    }

    override fun onCleared() {
        // 在ViewModel销毁时断开Socket连接
        disconnectSocket()
        super.onCleared()
    }

    // 断开Socket连接
    private fun disconnectSocket() {
        socket?.off(Socket.EVENT_CONNECT, onConnect)
        socket?.off(Socket.EVENT_DISCONNECT, onDisconnect)
        socket?.off(Socket.EVENT_CONNECT_ERROR, onConnectError)
        socket?.off(SocketIOEvent.JOIN_OR_LEAVE_ROOM_CALLBACK, message)
        socket?.off(SocketIOEvent.RECEIVE_MESSAGE, receiveMessageCallback)
        socket?.disconnect()
        socket = null
        isSocketConnected = false
        _isConnected.value = false
        LogUtil.d("Socket.IO - 断开Socket连接")
    }

    // 设置当前聊天的用户 - 修改以添加Socket.IO初始化
    /**
     * @param uuid:医生的ID
     * @param name:医生的名称
     * @param avatar:医生的头像
     */
    fun setChatUser(uuid: String, name: String, avatar: String) {
        LogUtil.d("ChatViewModel - 设置聊天用户: $uuid, $name")

        // 如果是同一个用户，不需要重新设置
        if (uuid == _currentUserId.value) {
            LogUtil.d("ChatViewModel - 已经是当前聊天用户，无需重新设置")
            return
        }

        // 如果当前已有聊天，先离开当前聊天室
        if (_currentUserId.value != null) {
            clearCurrentChat()
        }

        _currentUserId.value = uuid
        _currentUserName.value = name

        // 创建临时会话
        _tempSession.value = ChatSession(
            providerUUID = uuid,
            avatar = avatar,
            name = name,
            message = "",
            messageType = MessageType.RECEIVE,
            createBy = System.currentTimeMillis()
        )

        // 加载用户信息
        loadUserInfo(uuid)

        // 确保Socket已初始化并连接
        initSocketIfNeeded()

        // 如果Socket已连接，立即尝试加入聊天室
        if (isSocketConnected) {
            joinChatRoom()
        }
        // 如果Socket未连接，会在onConnect回调中尝试加入聊天室
    }

    // 获取聊天会话，如果API尚未返回则使用临时会话
    fun getCurrentSession(): ChatSession? {
        return _chatSession.value ?: _tempSession.value
    }

    // 从API加载用户信息
    /**
     * @param uuid:医生的ID
     */
    private fun loadUserInfo(uuid: String) {
        viewModelScope.launch {
            apiService.getUserInfoWithoutToken(uuid)
                .enqueueBody { response ->
                    if (response?.code == 1) {
                        try {
                            val gson = Gson()
                            val userInfo = gson.fromJson(response.data, User::class.java)
                            LogUtil.i("ChatViewModel loadUserInfo: ${userInfo.name}, avatar: ${userInfo.photo}")

                            // 用获取到的用户信息创建完整的ChatSession
                            val newSession = ChatSession(
                                providerUUID = uuid,
                                avatar = userInfo.photo, // 使用API获取的头像
                                name = userInfo.name,    // 使用API获取的名称
                                message = "",
                                messageType = MessageType.RECEIVE,
                                phone = userInfo.phone,
                                createBy = System.currentTimeMillis()
                            )
                            _chatSession.value = newSession
                        } catch (e: Exception) {
                            LogUtil.e("Error parsing user info: ${e.message}")
                            // 如果解析失败，使用临时会话
                            _chatSession.value = _tempSession.value
                        }
                    } else {
                        LogUtil.e("Failed to get user info: ${response?.msg}")
                        // 如果API请求失败，使用临时会话
                        _chatSession.value = _tempSession.value
                    }
                }
        }
    }


    // 避免闪烁的消息更新方法
    private fun updateMessages(newMessages: List<ChatMessageEntity>) {
        _chatMessageList.value = newMessages
        _isMessagesLoaded.value = true
    }

    /**
     * 标记消息为已读
     */
    fun markMessagesAsRead() {
        val toUserUuid = _currentUserId.value ?: return

        viewModelScope.launch {
            try {
                // 从ApiService的实现看，patchChatData方法只需要rootUuid参数
                // 而在默认情况下，rootUuid会使用当前用户的uuid，to_user参数会在服务端处理
                // 所以这里只需要调用patchChatData方法即可
                val body = com.google.gson.JsonObject().apply {
                    addProperty("from_uuid", toUserUuid)
                }
                apiService.patchChatData(
                    body = body
                ).enqueue(
                    onResponse = { _, response ->
                        response?.let {
                            if (it.code == 1) {
                                fetchChatList()

                                LogUtil.i("Messages marked as read successfully for user: $toUserUuid")
                            } else {
                                LogUtil.e("Failed to mark messages as read: ${it.msg}")
                            }
                        }
                    },
                    onFailure = { _, throwable ->
                        LogUtil.e("Error marking messages as read: ${throwable.message}")
                    }
                )
            } catch (e: Exception) {
                LogUtil.e("Exception when marking messages as read: ${e.message}")
            }
        }
    }

    /**
     * 一键已读
     */
    suspend fun markAllMessagesAsRead() {
        withContext(Dispatchers.IO) {
            try {
                apiService.markAllMessagesAsRead(
                ).enqueue(
                    onResponse = { _, response ->
                        response?.let {
                            if (it.code == 1) {
                                fetchChatList()
                            } else {
                            }
                        }
                    },
                    onFailure = { _, throwable ->
                        LogUtil.e("Error marking messages as read: ${throwable.message}")
                    }
                )
            } catch (e: Exception) {
                LogUtil.e("Exception when marking messages as read: ${e.message}")
            }
        }

    }

    /**
     * 处理接受关注请求
     * @param followContent 原始关注内容
     * @param chatSmsEntity 包含消息创建时间和用户ID的聊天消息实体
     * @param onSuccess 成功时的回调
     * @param onFailure 失败时的回调
     */
    fun modifyFollowRequest(
        followContent: FollowContent,
        chatSmsEntity: ChatSmsEntity,
        onSuccess: () -> Unit,
        onFailure: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                // 更新followContent的isAccepted状态
                val gson = Gson()
                val updatedMsg = gson.toJson(followContent)

                // 创建JsonObject来构建请求体
                val jsonObject = JSONObject().apply {
                    put("from_user", chatSmsEntity.fromUserUid)
                    put("to_user", user.uuid)
                    // 原始消息
                    put("msg", chatSmsEntity.message)
                    // 新消息 - 包含更新后的MeetContent
                    put("new_msg", updatedMsg)
                }
                LogUtil.d("Socket.IO update request: $jsonObject")
                socket?.emit(SocketIOEvent.MODIFY_MESSAGE, jsonObject)
                onSuccess()

            } catch (e: Exception) {
                LogUtil.e("Exception when accepting follow request: ${e.message}")
                onFailure("error")
            }
        }
    }

    fun acceptFollow(
        message: ChatMessageEntity
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d("socket ${message}")
                message.getFollowContent()?.let {
                    LogUtil.d("socket ${it}")
                    val newFollow = it.copy(
                        isAccepted = 1
                    )
                    modifyFollow(message.messageId, newFollow)
                }
            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }

    }

    fun rejectFollow(
        message: ChatMessageEntity
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d("socket ${message}")
                message.getFollowContent()?.let {
                    LogUtil.d("socket ${it}")
                    val newFollow = it.copy(
                        isAccepted = 2
                    )
                    modifyFollow(message.messageId, newFollow)
                }
            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }

    }

    fun modifyFollow(
        messageId: Long,
        followContent: FollowContent
    ) {
        viewModelScope.launch {
            try {
                val gson = Gson()
                val updatedMsg = gson.toJson(followContent)

                val jsonObject = JSONObject().apply {
                    put("msg_id", messageId)
                    put("new_msg", updatedMsg)
                }
                LogUtil.d("Socket.IO update request: $jsonObject")
                socket?.emit(SocketIOEvent.MODIFY_MESSAGE, jsonObject)

            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }
    }


    fun acceptMeet(
        message: ChatMessageEntity
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d("socket ${message}")
                message.getMeetContent()?.let {
                    LogUtil.d("socket ${it}")
                    val newMeet = it.copy(
                        isAccepted = 1
                    )
                    modifyMeet(message.messageId, newMeet)
                }
            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }

    }

    fun rejectMeet(
        message: ChatMessageEntity
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d("socket ${message}")
                message.getMeetContent()?.let {
                    LogUtil.d("socket ${it}")
                    val newMeet = it.copy(
                        isAccepted = 2
                    )
                    modifyMeet(message.messageId, newMeet)
                }
            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }

    }

    fun modifyMeet(
        messageId: Long,
        meetContent: MeetContent
    ) {
        viewModelScope.launch {
            try {
                val gson = Gson()
                val updatedMsg = gson.toJson(meetContent)

                val jsonObject = JSONObject().apply {
                    put("msg_id", messageId)
                    put("new_msg", updatedMsg)
                }
                LogUtil.d("Socket.IO update request: $jsonObject")
                socket?.emit(SocketIOEvent.MODIFY_MESSAGE, jsonObject)

            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }
    }

    fun acceptPhoneRequest(
        message: ChatMessageEntity,
        phone: String,
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d("socket ${message}")
                message.getPhoneRequestContent()?.let {
                    LogUtil.d("socket ${it}")
                    val newPhoneRequestContent = it.copy(
                        isAccepted = 1,
                        phone = phone
                    )
                    modifyPhoneRequest(message.messageId, newPhoneRequestContent)
                }
            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }

    }

    fun rejectPhoneRequest(
        message: ChatMessageEntity
    ) {
        viewModelScope.launch {
            try {
                LogUtil.d("socket ${message}")
                message.getPhoneRequestContent()?.let {
                    LogUtil.d("socket ${it}")
                    val newPhoneRequestContent = it.copy(
                        isAccepted = 2
                    )
                    modifyPhoneRequest(message.messageId, newPhoneRequestContent)
                }
            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }

    }

    fun modifyPhoneRequest(
        messageId: Long,
        phoneRequestContent: PhoneRequestContent
    ) {
        viewModelScope.launch {
            try {
                val gson = Gson()
                val updatedMsg = gson.toJson(phoneRequestContent)

                val jsonObject = JSONObject().apply {
                    put("msg_id", messageId)
                    put("new_msg", updatedMsg)
                }
                LogUtil.d("Socket.IO update request: $jsonObject")
                socket?.emit(SocketIOEvent.MODIFY_MESSAGE, jsonObject)

            } catch (e: Exception) {
                LogUtil.e("socket ${e.message}")
            }
        }
    }

    /**
     * 处理接受会议请求
     * @param meetContent 原始会议内容
     * @param chatSmsEntity 包含消息创建时间和用户ID的聊天消息实体
     * @param onSuccess 成功时的回调
     * @param onFailure 失败时的回调
     */
    fun modifyMeetRequest(
        meetContent: MeetContent,
        chatSmsEntity: ChatSmsEntity,
        onSuccess: () -> Unit,
        onFailure: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                val gson = Gson()
                val updatedMsg = gson.toJson(meetContent)

                // 创建JsonObject来构建请求体
                val jsonObject = JSONObject().apply {
                    // 原始消息
                    put("msg_id", chatSmsEntity.message)
                    // 新消息 - 包含更新后的MeetContent
                    put("new_msg", updatedMsg)
                }

                // 记录发送请求的数据
                LogUtil.d("Socket.IO update request: $jsonObject")
                socket?.emit(SocketIOEvent.MODIFY_MESSAGE, jsonObject)
                onSuccess()
            } catch (e: Exception) {
                LogUtil.e("Exception when accepting meet request: ${e.message}")
                onFailure("error")
            }
        }
    }

    /**
     * Make a direct phone call with permission check
     */
    fun makePhoneCall(phoneNumber: String) {
        if (phoneNumber.isEmpty()) {
            DialogUtil.showToast(activity.getString(R.string.no_phone))
            return
        }

        // Check for CALL_PHONE permission
        if (ContextCompat.checkSelfPermission(activity, Manifest.permission.CALL_PHONE)
            != PackageManager.PERMISSION_GRANTED
        ) {
            // Request permission
            ActivityCompat.requestPermissions(
                activity as Activity,
                arrayOf(Manifest.permission.CALL_PHONE),
                CALL_PERMISSION_REQUEST_CODE
            )
            // Show rationale
            DialogUtil.showToast(activity.getString(R.string.permission_call_phone_rationale))
            return
        }

        // 记录通话相关信息
        callStarted = true
        callPhoneNumber = phoneNumber
        callStartTimestamp = System.currentTimeMillis()

        // Start tracking call duration before making the call
        startCallTracking()

        // Make the call directly
        try {
            val intent = Intent(Intent.ACTION_CALL).apply {
                data = Uri.parse("tel:$phoneNumber")
                // Add flag to return to our app after call ends
                addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            activity.startActivity(intent)

            LogUtil.d("Initiating phone call to: $phoneNumber at timestamp: $callStartTimestamp")
        } catch (e: Exception) {
            LogUtil.e("Error making phone call: ${e.message}")
            DialogUtil.showToast(activity.getString(R.string.dialer_error))
            resetCallState()
            stopCallTracking()
        }
    }

    /**
     * 重置通话状态
     */
    private fun resetCallState() {
        callStarted = false
        callPhoneNumber = ""
        callStartTimestamp = 0
    }

    /**
     * Start tracking call duration
     */
    fun startCallTracking() {
        // Start the service for tracking before binding, pass the call start timestamp
        CallService.startService(activity, callStartTimestamp)
        LogUtil.d("Call tracking service started with timestamp: $callStartTimestamp")

        // Only bind to the service if we're not already bound
        if (!callServiceBound) {
            // Bind to the service
            try {
                val intent = Intent(activity, CallService::class.java)
                callServiceBound =
                    activity.bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
                LogUtil.d("Binding to call service, result: $callServiceBound")
                
                // 设置超时检查，如果5秒内没有绑定成功，记录警告
                Handler(Looper.getMainLooper()).postDelayed({
                    if (!callServiceBound || callService == null) {
                        LogUtil.w("Service binding timeout - service may not be available for precise timing")
                        // 不影响通话流程，只是计时可能不够精确
                    }
                }, 5000)
                
            } catch (e: Exception) {
                LogUtil.e("Failed to bind to call service: ${e.message}")
                callServiceBound = false
                // 即使绑定失败，服务仍可能在运行，通过静态变量获取时长
            }
        } else {
            LogUtil.d("Already bound to call service, skipping bind")
        }
    }

    /**
     * Stop tracking call duration and clean up resources
     */
    fun stopCallTracking(): Int {
        // 获取服务记录的时长
        var duration = 0

        if (callServiceBound && callService != null) {
            try {
                // 先从当前服务实例获取最新时长
                duration = callService?.getCurrentDuration() ?: 0
                // 然后停止服务
                val finalDuration = callService?.stopTracking() ?: 0
                // 使用更大的值作为最终时长
                duration = maxOf(duration, finalDuration)
                LogUtil.d("Call tracking stopped with service bound, current: $duration, final: $finalDuration")

                // 解绑服务
                try {
                    activity.unbindService(serviceConnection)
                    LogUtil.d("Successfully unbound from call service")
                } catch (e: Exception) {
                    LogUtil.e("Error unbinding from call service: ${e.message}")
                }
            } catch (e: Exception) {
                LogUtil.e("Error stopping call service: ${e.message}")
                // 如果服务出错，使用静态保存的时长
                duration = CallService.lastTrackedDuration
            } finally {
                callServiceBound = false
                callService = null
            }
        } else {
            // 如果服务未绑定，从静态值获取时长
            duration = CallService.lastTrackedDuration
            LogUtil.d("Call tracking stopped with service not bound, using saved duration: $duration seconds")
        }

        // 停止服务
        try {
            CallService.stopService(activity)
        } catch (e: Exception) {
            LogUtil.e("Error stopping call service: ${e.message}")
        }

        LogUtil.d("Call tracking stopped completely, final duration: $duration seconds")
        return duration
    }

    /**
     * 应用最小计费时长的业务逻辑
     * @param actualDuration 实际通话时长
     * @return 应用最小计费时长后的时长
     */
    private fun applyMinimumBillingDuration(actualDuration: Int): Int {
        val minimumDuration = 1 // 最小计费时长1秒
        val billingDuration = if (actualDuration > 0) {
            // 如果实际有时长，使用实际时长（不强制最小值，让真实通话时长生效）
            actualDuration
        } else {
            // 只有在实际时长为0时才应用最小计费时长
            minimumDuration
        }
        
        if (billingDuration != actualDuration) {
            LogUtil.d("Applied minimum billing duration - actual: ${actualDuration}s, billed: ${billingDuration}s")
        }
        
        return billingDuration
    }

    /**
     * Check if we need to handle call completion (if this is a resumption after a call)
     */
    fun handlePossibleCallCompletion(): Boolean {
        // 计算从通话开始到现在的时间
        val callDuration = if (callStartTimestamp > 0) {
            ((System.currentTimeMillis() - callStartTimestamp) / 1000).toInt()
        } else {
            0
        }
        
        LogUtil.d("Checking call completion - started=$callStarted, callDuration=$callDuration seconds")
        
        // 如果通话已开始，需要更智能地判断是否真的结束了
        // 增加时间阈值，避免在电话应用启动瞬间就停止计时
        if (callStarted && callDuration >= 3) { // 从1秒增加到3秒
            // 获取服务实际记录的时长
            var actualDuration = 0
            
            if (callServiceBound && callService != null) {
                // 如果服务还在运行，获取当前时长
                actualDuration = callService?.getCurrentDuration() ?: CallService.lastTrackedDuration
                LogUtil.d("Got actual duration from running service: $actualDuration seconds")
            } else {
                // 服务未绑定，使用静态保存的时长
                actualDuration = CallService.lastTrackedDuration
                LogUtil.d("Got actual duration from static storage: $actualDuration seconds")
            }
            
            // 如果服务时长仍为0，使用计算的时长作为备选
            if (actualDuration <= 0) {
                actualDuration = callDuration
                LogUtil.d("Using calculated actual duration: $actualDuration seconds")
            }
            
            // 应用最小计费时长业务逻辑
            val billingDuration = applyMinimumBillingDuration(actualDuration)
            
            // 判断是否接听（基于实际时长，不是计费时长）
            val wasAnswered = actualDuration >= CALL_ANSWERED_THRESHOLD
            
            val providerId = _currentUserId.value  // 使用currentUserId
            
            if (providerId != null) {
                // 发送通话记录到服务器，使用计费时长
                sendCallRecordToServer(wasAnswered, callStartTimestamp, billingDuration)
            }
            
            LogUtil.d("Call completed - actual: ${actualDuration}s, billing: ${billingDuration}s, answered: $wasAnswered")
            
            // 重置通话状态
            resetCallState()
            
            // 停止追踪并获取最终时长
            val finalDuration = stopCallTracking()
            
            LogUtil.d("Call completion cleanup complete, final service duration: $finalDuration seconds")
            
            return true
        }
        
        LogUtil.d("Call duration too short (${callDuration}s) or no call started, not handling - this might be call app launching")
        return false
    }
    
    /**
     * 强制结束通话追踪 - 用于用户直接挂断电话的情况
     * 此方法不依赖于通话时长判断
     */
    fun forceEndCallTracking() {
        if (!callStarted) {
            LogUtil.d("No call in progress, ignoring force end request")
            return
        }
        
        LogUtil.d("Force ending call tracking")
        
        val callDuration = if (callStartTimestamp > 0) {
            ((System.currentTimeMillis() - callStartTimestamp) / 1000).toInt()
        } else {
            0
        }
        
        // 获取服务记录的实际时长
        var actualDuration = 0
        
        if (callServiceBound && callService != null) {
            // 如果服务还在运行，获取当前时长
            actualDuration = callService?.getCurrentDuration() ?: CallService.lastTrackedDuration
            LogUtil.d("Force end - got actual duration from running service: $actualDuration seconds")
        } else {
            // 服务未绑定，使用静态保存的时长
            actualDuration = CallService.lastTrackedDuration
            LogUtil.d("Force end - got actual duration from static storage: $actualDuration seconds")
        }
        
        // 如果服务时长仍为0，使用计算的时长作为备选
        if (actualDuration <= 0) {
            actualDuration = callDuration
            LogUtil.d("Force end - using calculated actual duration: $actualDuration seconds")
        }
        
        // 应用最小计费时长业务逻辑
        val billingDuration = applyMinimumBillingDuration(actualDuration)
        
        // 不管时长如何，都当作未接通处理
        val wasAnswered = false
        
        // 发送记录到服务器，使用计费时长
        val providerId = _currentUserId.value
        if (providerId != null) {
            // 发送通话记录到服务器
            sendCallRecordToServer(wasAnswered, callStartTimestamp, billingDuration)
        }
        
        LogUtil.d("Call forcibly ended - actual: ${actualDuration}s, billing: ${billingDuration}s, answered: false")
        
        // 重置通话状态
        resetCallState()
        
        // 停止追踪并获取最终时长
        val finalDuration = stopCallTracking()
        
        LogUtil.d("Call forcibly ended cleanup complete, final service duration: $finalDuration seconds")
    }
    
    /**
     * 获取通话开始时间戳，用于外部检测通话持续时间
     */
    fun getCallStartTimestamp(): Long {
        return callStartTimestamp
    }
    
    /**
     * 获取Service运行时间（秒）
     */
    fun getServiceRunningTime(): Int {
        return if (callStartTimestamp > 0) {
            ((System.currentTimeMillis() - callStartTimestamp) / 1000).toInt()
        } else {
            0
        }
    }
    
    /**
     * 判断是否有通话正在进行
     */
    fun isCallInProgress(): Boolean {
        return callStarted
    }
    
    /**
     * 向服务器发送通话记录
     * @param wasAnswered 是否接听
     * @param startTimestamp 开始时间戳
     * @param durationInSeconds 通话时长（秒）
     */
    private fun sendCallRecordToServer(wasAnswered: Boolean, startTimestamp: Long, durationInSeconds: Int) {
        try {
            val currentSession = getCurrentSession() ?: return
            
            // 计算结束时间和通话时长
            val startTimeSeconds = startTimestamp / 1000
            val endTimeSeconds = startTimeSeconds + durationInSeconds
            val providerId = _currentUserId.value  // 修改这里，使用currentUserId而不是chatSession

            if (providerId != null) {
                // 发送Socket消息
                sendCallRecord(
                    CallDurationContent(
                        duration = durationInSeconds.toLong(),
                        userId = user.uuid,
                        requesterId = providerId,
                        time = formatSecondToDefaultDateStringWithout(startTimeSeconds),
                    )
                )
                
                // 格式化时间为ISO格式
                val startTimeFormatted = formatSecondToDefaultDateStringWithout(startTimeSeconds)
                val endTimeFormatted = formatSecondToDefaultDateStringWithout(endTimeSeconds)
                
                // 根据是否接听确定通话类型
                val callType = if (wasAnswered) {
                    UseTimeType.USER_CALl_CONNECT.value
                } else {
                    UseTimeType.USER_CALl_UNCONNECT.value
                }

                // 创建通话记录对象 - 注意：根据需求，fromUser是医生ID，toUser是用户ID
                val useTimeMeta = UseTimeMeta(
                    fromUser = currentSession.providerUUID,  // 医生ID
                    toUser = user.uuid,                     // 用户ID
                    startTime = startTimeFormatted,         // 已格式化的开始时间字符串
                    endTime = endTimeFormatted,             // 已格式化的结束时间字符串
                    timeSpent = durationInSeconds,          // 通话时长（秒）
                    type = callType
                )
                
                // 转换为JsonObject
                val gson = Gson()
                val jsonString = gson.toJson(useTimeMeta)
                val jsonObject = JsonParser.parseString(jsonString).asJsonObject
                
                LogUtil.d("Sending call record to server: $jsonObject")
                
                // 调用API发送记录
                viewModelScope.launch {
                    try {
                        apiService.postRTMRecordingTime(body = jsonObject).enqueue(
                            onResponse = { _, response ->
                                response?.let {
                                    if (it.code == 1) {
                                        LogUtil.d("Call record sent successfully")
                                    } else {
                                        LogUtil.e("Failed to send call record: ${it.msg}")
                                    }
                                }
                            },
                            onFailure = { _, throwable ->
                                LogUtil.e("Error sending call record: ${throwable.message}")
                            }
                        )
                    } catch (e: Exception) {
                        LogUtil.e("Exception when sending call record: ${e.message}")
                    }
                }
            }
            
        } catch (e: Exception) {
            LogUtil.e("Error preparing call record: ${e.message}")
        }
    }
    fun formatSecondToDefaultDateStringWithout(second: Long): String {
        val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US)
        sdf.timeZone = TimeZone.getTimeZone("UTC")
        return sdf.format(Date(second * 1000L))
    }
    /**
     * 判断上次通话是否被接听
     * 可以在UI中调用此方法显示相应状态
     */
    fun wasLastCallAnswered(): Boolean {
        val duration = CallService.lastTrackedDuration
        return duration >= CALL_ANSWERED_THRESHOLD
    }


    companion object {
        private const val CALL_PERMISSION_REQUEST_CODE = 101

        // 通话时长超过此阈值（秒）认为对方已接听
        private const val CALL_ANSWERED_THRESHOLD = 10
    }
}

object SocketIOEvent {
    const val JOIN_ROOM = "join"
    const val JOIN_OR_LEAVE_ROOM_CALLBACK = "message"
    const val SEND_MESSAGE = "send_message"
    const val RECEIVE_MESSAGE = "receive_message"
    const val LEAVE_ROOM = "leave"
    const val MODIFY_MESSAGE = "patch"
    const val ERROR = "error"
}