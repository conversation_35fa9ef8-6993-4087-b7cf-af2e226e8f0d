package org.aihealth.ineck.viewmodel

import android.content.Context
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.model.AccelerometerParam
import org.aihealth.ineck.model.Queue
import org.aihealth.ineck.model.toList
import org.aihealth.ineck.sensor.AccelerometerSensor
import org.aihealth.ineck.sensor.GravitySensor
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.viewmodel.dao.VCGuideEvent
import org.aihealth.ineck.viewmodel.dao.VCGuideProcessState
import org.aihealth.ineck.viewmodel.dao.getTimestampNow
import org.aihealth.ineck.viewmodel.dao.maxDiff

class AibackVCGuideViewModel(
    context: Context
) : VCGuideEvent() {

    init {
        gravitySensor = GravitySensor(context = context)
        accelerometerSensor = AccelerometerSensor(context = context)
    }

    override fun changeGuideDetectProcessState(newState: VCGuideProcessState) {
        this.currentVCGuideProcessState.update { newState }
    }

    override suspend fun startDetectingTimer() {
        viewModelScope.launch {
            repeat(3) {
                delay(1000)
                countTime.update { value -> value - 1 }
            }
            delay(1000)
            changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
        }
    }

    override fun gravityStartListening() {
        gravitySensor.startListening()
        gravitySensor.setOnSensorValuesChangedListener { values ->
            verticalGravity.update { values[1] }
        }
    }

    override fun gravityStopListening() {
        gravitySensor.stopListening()
        verticalGravity.update { 0f }
    }

    override fun accelerometerStartListener() {
        accelerometerSensor.startListening()
        accelerometerSensor.setOnSensorValuesChangedListener { values ->
            viewModelScope.launch {
                val x = values[0]
                val y = values[1]
                val z = values[2]
                accelerometerQueue.value.enqueue(AccelerometerParam(x, y, z))
            }
            viewModelScope.launch {
                isDeviceStatic.update {
                    accelerometerQueue.value.toList().map { it.x }.maxDiff() < 0.5f &&
                            accelerometerQueue.value.toList().map { it.y }.maxDiff() < 0.5f &&
                            accelerometerQueue.value.toList().map { it.z }.maxDiff() < 0.5f
                }
            }
        }
    }

    override fun accelerometerStopListener() {
        accelerometerSensor.stopListening()
        isDeviceStatic.update { true }
    }

    override suspend fun verifyVerticalDetection(calibrationTime: Int, duration: Long) {
        var times = 1
        while (times <= calibrationTime) {
            /* 设定前后符合范围的倾角在20°之内 */
            LogUtil.i("VCGuideViewModel", "verticalGravity.value: ${verticalGravity.value}")
            if (verticalGravity.value > 9.8f * 0.658f) {
                if (times < calibrationTime) {
                    finishedVerticalDetectedState.update { false }
                    delay(duration)
                } else {
                    finishedVerticalDetectedState.update { true }
                    break
                }
                times += 1
            } else {
                finishedVerticalDetectedState.update { false }
                break
            }
        }
    }

    override fun triggerDetectingCountDown() {
        viewModelScope.launch {
            (5 downTo -1).forEach { countdown ->
                delay(1000)
                if (countdown == -1) {
                    /* 若进入开始校准状态，则更新当前时间戳 */
                    <EMAIL> {
                        getTimestampNow()
                    }
                }
                <EMAIL> { countdown }
            }
        }
    }

    override fun writeDetectedResult(result: DetectedResult) {
        this.detectedResult.update { result }
    }

    override fun changeCurrentMuteState(newState: Boolean) {
        this.isMuteState.update { newState }
    }

    override fun clearAllState() {
        /** 重力矢量状态 */
        this.verticalGravity = MutableStateFlow(0f)
        /* 加速度参数队列 */
        this.accelerometerQueue = mutableStateOf(Queue(30))

        /* 当前引导检测页执行状态 */
        this.currentVCGuideProcessState.update { VCGuideProcessState.PreviousGuidePage }

        /** 检测中页 倒计时变量 */
        this.countTime.update { 5 }
        /** 检测过程中 测量开始时间戳 - 从有效数据开始计算 */
        this.startTimestampForEffectDetecting.update { 0L }
        /** 垂直效果完成状态 */
        this.finishedVerticalDetectedState.update { false }

        /** 校准前各状态允许 */
        /* 设备静止状态 */
        this.isDeviceStatic.update { false }
        /*  能否捕捉到胯部 */
        this.capturedPose.update { false }
        /*清除当前背部弯曲角度*/
        this.bodyTiltAngle.update { 0f }
        /* 清除直立姿态*/
        this.verticalPose.update { false }

        /* 综合状态 */
        this.totalState.update { false }
        /** 引导检测结果 */
        this.detectedResult.update { DetectedResult.None }
    }

}