package org.aihealth.ineck.viewmodel.impl

import android.annotation.SuppressLint
import android.graphics.Canvas
import android.graphics.LinearGradient
import android.graphics.Paint
import android.graphics.Shader
import android.provider.MediaStore
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.dp
import androidx.core.graphics.createBitmap
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.AnglesHandle
import org.aihealth.ineck.model.angles.BackPain
import org.aihealth.ineck.model.angles.LineChart
import org.aihealth.ineck.model.angles.Mean
import org.aihealth.ineck.model.angles.NeckPain
import org.aihealth.ineck.model.angles.RecordScales
import org.aihealth.ineck.model.angles.SummaryValue
import org.aihealth.ineck.model.angles.ToEmail
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.network.convertTimestampToFormattedDate
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.timeInSecond
import org.aihealth.ineck.util.toPx
import org.aihealth.ineck.view.custom.CaptureController
import org.aihealth.ineck.view.custom.DayCalendarState
import org.aihealth.ineck.view.custom.MonthCalendarState
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshState
import org.aihealth.ineck.viewmodel.MainViewModel
import java.util.Calendar
import java.util.Date
import java.util.GregorianCalendar

@SuppressLint("SimpleDateFormat")
class ReportScreenEventImpl(
    private val viewModel: MainViewModel
) {
    // 刷新状态
    var refreshState = AIHRefreshState(false)

    // 日，月，周页面Index
    var periodIndex by mutableIntStateOf(0)

    // 当天日期
    var today = getDefaultDate()

    // 日报告日历状态
    val dayCalendarState = DayCalendarState(today)

    /** 周报告月历选择显示信号 */
    val isWeekMonthCalendarVisible = MutableStateFlow(false)

    /** 周报告月历回到被选日期月份信号 */
    val isBackToSelectCalendarSignal = MutableStateFlow(true)

    // 月报告日历状态
    val monthCalendarState = MonthCalendarState(today)


    // 月报告时间显示
    var monthCalendarTime by mutableStateOf(Date())

    // 月报告月份
    var monthCalendarMonth by mutableIntStateOf(1)

    // 改进版本的数据存储
    var monthRecordsMap by mutableStateOf<Map<String, org.aihealth.ineck.model.angles.Record>>(
        emptyMap()
    )
    var monthSeveritiesMap by mutableStateOf<Map<String, org.aihealth.ineck.model.angles.Severity>>(
        emptyMap()
    )

    // 日报告角度数据
    var dayAnglesData by mutableStateOf(AnglesHandle())

    // 周报告角度数据
    var weekAnglesData by mutableStateOf(AnglesHandle())

    // 日报告表单记录数据
    var dayRecordScales by mutableStateOf(RecordScales())

    // 周报告表单记录数据
    var weekRecordScales by mutableStateOf(RecordScales())

    // 周报告脊椎列表
    var weekMeanList = mutableStateListOf<Mean>()

    // 月报告ODI和 PROMIS数据
    var monthLineChart by mutableStateOf(LineChart())

    // 月报告体姿监测数据
    var monthAnglesHandle by mutableStateOf(AnglesHandle())

    // 月报告ODI和PROMIS数据
    var monthRecordScales by mutableStateOf(RecordScales())

    // 发送报告、生成图片dialogvisible
    var sendEmailDialogVisible by mutableStateOf(false)

    // 发送报告邮箱
    var email by mutableStateOf("")

    // 截图Controller
    val captureController = CaptureController()

    var isHasConnected by mutableStateOf(
        when (viewModel.homeScreen.currentDeviceType) {
            DeviceType.aiNeck -> true
            DeviceType.aiBack -> true
            DeviceType.KneeJoint -> true
            DeviceType.HipJoint -> true
            DeviceType.ShoulderJoint -> true
            DeviceType.ElbowJoint -> true
            else -> false
        }
    )

    init {

        viewModel.viewModelScope.launch {
//            monthCalendarState.currentData.records.clear()
//            monthCalendarState.currentData.records.addAll(monthCalendarState.getRandomRecords())
//            monthCalendarState.currentData.severities.addAll(monthCalendarState.getRandomSeverites())
//
//            monthCalendarState.preData.records.clear()
//            monthCalendarState.preData.records.addAll(monthCalendarState.getRandomRecords())
//            monthCalendarState.preData.severities.addAll(monthCalendarState.getRandomSeverites())
//
//            monthCalendarState.nextData.records.clear()
//            monthCalendarState.nextData.records.addAll(monthCalendarState.getRandomRecords())
//            monthCalendarState.nextData.severities.addAll(monthCalendarState.getRandomSeverites())
        }

        monthCalendarState.onDisplayTimeChanged {
            monthCalendarMonth = monthCalendarState.month
            val calendar =
                GregorianCalendar(monthCalendarState.year, monthCalendarState.month - 1, 1)
            val startTime = calendar.timeInSecond
            val endCalendar = calendar.clone() as Calendar
            val endTime = endCalendar.apply { roll(Calendar.DATE, -1) }.timeInSecond
            monthCalendarTime = calendar.time
            if (isHasConnected) {
                if (monthCalendarState.currentData.severities.isEmpty()) {
                    apiService.getAnglesMonthly(
                        type = viewModel.homeScreen.currentDeviceType.apiName,
                        from_date = startTime,
                        to_date = endTime
                    ).enqueueBody { response ->
                        response?.data?.let {
                            monthCalendarState.currentData.severities.clear()
                            monthCalendarState.currentData.severities.addAll(it)
                            // 转换为改进版本的数据格式
                            monthSeveritiesMap = convertSeveritiesToMap(
                                it,
                                monthCalendarState.year,
                                monthCalendarState.month
                            )
                        }
                    }
                }

                apiService.getAnglesHandle(
                    type = viewModel.homeScreen.currentDeviceType.apiName,
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    response?.data?.let {
                        monthAnglesHandle = it
                    }
                }
            }
            if (monthCalendarState.currentData.records.isEmpty()) {
                apiService.getRecord(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    response?.data?.let {
                        monthCalendarState.currentData.records.clear()
                        monthCalendarState.currentData.records.addAll(it)
                        // 转换为改进版本的数据格式
                        monthRecordsMap = convertRecordsToMap(
                            it,
                            monthCalendarState.year,
                            monthCalendarState.month
                        )
                    }
                }
            }
            apiService.getLineChart(
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthLineChart = it
                }
            }
            apiService.getRecordScales(
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthRecordScales = it.apply {
                        if (back_pain.leg == -1) {
                            back_pain = BackPain()
                        }
                        if (neck_pain.neck == -1) {
                            neck_pain = NeckPain()
                        }
                        if (promis.mean == -1) {
                            promis = SummaryValue(80, 80, 80)
                        }
                        if (odi.mean == -1) {
                            odi = SummaryValue()
                        }
                    }
                }
            }
        }
    }

    /**
     * 当日报告日历选择时间改变时
     */
    suspend fun onDaySelectedChanged(currentDeviceType: DeviceType) {
        val calendar = dayCalendarState.selectedDay.clone() as Calendar
        var startTime = calendar.timeInSecond
        val endTime = calendar.apply { add(Calendar.DATE, 1) }.timeInSecond

        apiService.getAnglesHandle(
            type = currentDeviceType.apiName,
            from_date = startTime,
            to_date = endTime
        ).enqueueBody { response ->
            response?.data?.let {
                dayAnglesData = it
            }
        }

        apiService.getRecordScales(
            from_date = startTime,
            to_date = endTime
        ).enqueueBody { response ->
            response?.data?.let {
                dayRecordScales = it.apply {
                    if (back_pain.leg == -1) {
                        back_pain = BackPain()
                    }
                    if (neck_pain.neck == -1) {
                        neck_pain = NeckPain()
                    }
                    if (promis.mean == -1) {
                        promis = SummaryValue(80, 80, 80)
                    }
                    if (odi.mean == -1) {
                        odi = SummaryValue()
                    }
                    when (currentDeviceType) {
                        DeviceType.aiNeck, DeviceType.aiNeckCV -> {
                            apiService.getTargetNeckNeural(
                                from_date = convertTimestampToFormattedDate(startTime.toLong()),
                                to_date = convertTimestampToFormattedDate(endTime.toLong())
                            ).enqueueBody {
                                it?.data?.let { result ->
                                    dayRecordScales = RecordScales().apply {
                                        if (result.numb != -1) neck_pain =
                                            neck_pain.copy(numb = result.numb)
                                        if (result.balance != -1) neck_pain =
                                            neck_pain.copy(balance = result.balance)
                                        if (result.muscle != -1) neck_pain =
                                            neck_pain.copy(muscle = result.muscle)
                                        this.promis = SummaryValue().copy(
                                            dayRecordScales.promis.max,
                                            dayRecordScales.promis.mean,
                                            dayRecordScales.promis.min
                                        )
                                        this.odi = SummaryValue().copy(
                                            dayRecordScales.odi.max,
                                            dayRecordScales.odi.mean,
                                            dayRecordScales.odi.min
                                        )
                                    }
                                }
                                apiService.getTargetNeckPain(
                                    from_date = convertTimestampToFormattedDate(startTime.toLong()),
                                    to_date = convertTimestampToFormattedDate(endTime.toLong())
                                ).enqueueBody {
                                    it?.data?.let { result ->
                                        dayRecordScales = RecordScales().apply {
                                            if (result.neck != -1) neck_pain =
                                                neck_pain.copy(neck = result.neck)
                                            if (result.hand != -1) neck_pain =
                                                neck_pain.copy(hand = result.hand)
                                            neck_pain = neck_pain.copy(
                                                muscle = dayRecordScales.neck_pain.muscle,
                                                balance = dayRecordScales.neck_pain.balance,
                                                numb = dayRecordScales.neck_pain.numb,
                                            )
                                            this.promis = dayRecordScales.promis
                                            this.odi = SummaryValue().copy(
                                                dayRecordScales.odi.max,
                                                dayRecordScales.odi.mean,
                                                dayRecordScales.odi.min
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        DeviceType.aiBack, DeviceType.aiBackCV -> {
                            apiService.getTargetBackNeural(
                                from_date = convertTimestampToFormattedDate(startTime.toLong()),
                                to_date = convertTimestampToFormattedDate(endTime.toLong())
                            ).enqueueBody {
                                it?.data?.let { result ->
                                    dayRecordScales = RecordScales().apply {
                                        if (result.numb != -1) back_pain =
                                            back_pain.copy(numb = result.numb)
                                        if (result.balance != -1) back_pain =
                                            back_pain.copy(balance = result.balance)
                                        if (result.muscle != -1) back_pain =
                                            back_pain.copy(muscle = result.muscle)
                                        this.promis = SummaryValue().copy(
                                            dayRecordScales.promis.max,
                                            dayRecordScales.promis.mean,
                                            dayRecordScales.promis.min
                                        )
                                        this.odi = SummaryValue().copy(
                                            dayRecordScales.odi.max,
                                            dayRecordScales.odi.mean,
                                            dayRecordScales.odi.min
                                        )
                                    }
                                }
                                apiService.getTargetBackPain(
                                    from_date = convertTimestampToFormattedDate(startTime.toLong()),
                                    to_date = convertTimestampToFormattedDate(endTime.toLong())
                                ).enqueueBody {
                                    it?.data?.let { result ->
                                        dayRecordScales = RecordScales().apply {
                                            if (result.leg != -1) back_pain =
                                                back_pain.copy(leg = result.leg)
                                            if (result.back != -1) back_pain =
                                                back_pain.copy(back = result.back)
                                            back_pain = back_pain.copy(
                                                muscle = dayRecordScales.neck_pain.muscle,
                                                balance = dayRecordScales.neck_pain.balance,
                                                numb = dayRecordScales.neck_pain.numb,
                                            )
                                            this.promis = dayRecordScales.promis
                                            this.odi = SummaryValue().copy(
                                                dayRecordScales.odi.max,
                                                dayRecordScales.odi.mean,
                                                dayRecordScales.odi.min
                                            )
                                        }
                                    }
                                }
                            }
                        }

                        else -> {}
                    }

                }
            }
        }
        calendar.date -= 7
        startTime = calendar.timeInSecond

        apiService.getAnglesHandle(
            type = currentDeviceType.apiName,
            from_date = startTime,
            to_date = endTime
        ).enqueueBody { response ->
            response?.data?.let {
                weekAnglesData = it
            }
        }

        apiService.getAnglesRefactor(
            type = currentDeviceType.apiName,
            from_date = startTime,
            to_date = endTime
        ).enqueueBody {
            it?.mean?.let { list ->
                weekMeanList.clear()
                weekMeanList.addAll(list)
            }
        }

        apiService.getRecordScales(
            from_date = startTime,
            to_date = endTime
        ).enqueueBody { response ->
            response?.data?.let {
                weekRecordScales = it.apply {
                    if (back_pain.leg == -1) {
                        back_pain = BackPain()
                    }
                    if (neck_pain.neck == -1) {
                        neck_pain = NeckPain()
                    }
                    if (promis.mean == -1) {
                        promis = SummaryValue(80, 80, 80)
                    }
                    if (odi.mean == -1) {
                        odi = SummaryValue()
                    }
                }
            }
        }

    }

    /**
     * 当页面刷新时
     */
    suspend fun onRefresh(currentDeviceType: DeviceType) {
        if (periodIndex == 2) {
            val calendar =
                GregorianCalendar(monthCalendarState.year, monthCalendarState.month - 1, 1)
            val startTime = calendar.timeInSecond
            val endTime = calendar.apply { roll(Calendar.DATE, -1) }.timeInSecond

            apiService.getAnglesMonthly(
                type = currentDeviceType.apiName,
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthCalendarState.currentData.severities.clear()
                    monthCalendarState.currentData.severities.addAll(it)
                    // 转换为改进版本的数据格式
                    monthSeveritiesMap = convertSeveritiesToMap(
                        it,
                        monthCalendarState.year,
                        monthCalendarState.month
                    )
                }
            }
            apiService.getAnglesHandle(
                type = currentDeviceType.apiName,
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthAnglesHandle = it
                }
            }
            apiService.getRecord(
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthCalendarState.currentData.records.clear()
                    monthCalendarState.currentData.records.addAll(it)
                    // 转换为改进版本的数据格式
                    monthRecordsMap =
                        convertRecordsToMap(it, monthCalendarState.year, monthCalendarState.month)
                }
            }
            apiService.getLineChart(
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthLineChart = it
                }
            }
            apiService.getRecordScales(
                from_date = startTime,
                to_date = endTime
            ).enqueueBody { response ->
                response?.data?.let {
                    monthRecordScales = it
                    if (dayRecordScales.back_pain.leg == -1) {
                        dayRecordScales.back_pain = BackPain()
                    }
                    if (dayRecordScales.neck_pain.neck == -1) {
                        dayRecordScales.neck_pain = NeckPain()
                    }
                }
            }
        } else {
            onDaySelectedChanged(currentDeviceType)
        }
        refreshState.refreshing = false
    }

    private fun isValidEmail(email: String): Boolean {
        val emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$".toRegex()
        return email.matches(emailRegex)
    }

    /**
     * 发送报告到邮箱
     */
    fun sendToEmail(
        onResult: (Boolean) -> Unit
    ) {
        if (email.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.email_account_cannot_be_empty))
            return
        }
        if (!isValidEmail(email)) {
            DialogUtil.showToast(localeResources.getString(R.string.email_account_format_error))
            return
        }
        val calendar = dayCalendarState.selectedDay.clone() as Calendar
        val endTime = calendar.timeInSecond
        val startTime = calendar.apply {
            when (periodIndex) {
                0 -> add(Calendar.DATE, -1)
                1 -> add(Calendar.DATE, -7)
                2 -> add(Calendar.MONTH, -1)
            }
        }.timeInSecond
        apiService.sendReportToEmail(
            from_date = startTime,
            to_date = endTime,
            toEmail = ToEmail(email)
        ).enqueueBody {
            if (it?.data == true) {
                DialogUtil.showToast(localeResources.getString(R.string.report_sent_successfully))
            } else {
                DialogUtil.showToast(localeResources.getString(R.string.failed_to_send_report))
            }
            onResult(it?.data == true)
        }
    }

    /**
     * 一键生成图片
     */
    fun createReportImage() {
        val bitmap = captureController.getBitmap()
        val mBitmap = createBitmap(bitmap.width, bitmap.height)
        val canvas = Canvas(mBitmap)
        val shaderHeight = 527.dp.toPx()
        val mColors = intArrayOf(0XFF3654CD.toInt(), 0XFF3F6CE3.toInt(), 0XFF789AF7.toInt())
        val paint = Paint().apply { color = 0XFFF1F1F1.toInt() }
        val linearGradient =
            LinearGradient(0F, 0F, 0F, shaderHeight, mColors, null, Shader.TileMode.MIRROR)
        canvas.drawRect(0F, 0F, bitmap.width.toFloat(), shaderHeight, Paint().apply {
            shader =
                linearGradient
        })
        canvas.drawRect(
            0F, shaderHeight, bitmap.width.toFloat(),
            bitmap.height.toFloat(), paint
        )
        canvas.drawBitmap(bitmap, 0F, 0F, paint)
        MediaStore.Images.Media.insertImage(
            activity.contentResolver,
            mBitmap,
            localeResources.getString(R.string.report),
            "description"
        )
        DialogUtil.showToast(localeResources.getString(R.string.save_report_image_success))
    }

    /**
     * 将List<Record>转换为Map<String, Record>格式
     * 用于改进的月日历组件
     */
    private fun convertRecordsToMap(
        records: List<org.aihealth.ineck.model.angles.Record>,
        year: Int,
        month: Int
    ): Map<String, org.aihealth.ineck.model.angles.Record> {
        return records.mapIndexed { index, record ->
            "$year-$month-${index + 1}" to record
        }.toMap()
    }

    /**
     * 将List<Severity>转换为Map<String, Severity>格式
     * 用于改进的月日历组件
     */
    private fun convertSeveritiesToMap(
        severities: List<org.aihealth.ineck.model.angles.Severity>,
        year: Int,
        month: Int
    ): Map<String, org.aihealth.ineck.model.angles.Severity> {
        return severities.mapIndexed { index, severity ->
            "$year-$month-${index + 1}" to severity
        }.toMap()
    }
}