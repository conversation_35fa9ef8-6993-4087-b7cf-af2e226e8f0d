package org.aihealth.ineck.viewmodel.dao

import android.bluetooth.BluetoothDevice
import android.content.Context
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

abstract class DeviceScreenEvent{



    /**
     * 点击连接设备按钮
     */
    abstract fun onConnectDeviceClick(deviceType: String)

    /**
     * 搜索附近设备
     * return List<Device>(周围所有设备集合)
     */
    abstract fun onScanDevice(
        onScanResult: (device: BluetoothDevice) -> Unit,
        onTimeOut: () ->Unit
    )

    var updateDialogVisible by mutableStateOf(false)

    /**
     * 连接设备
     * 连接成功执行onSuccess方法
     */
    abstract fun onConnectedDevice(
        device: BluetoothDevice,
        deviceType: String
    )

    /**
     * 校准抬头
     * return Boolean(true:校准成功,false: 校准失败)
     */
    abstract suspend fun onHeadUpCalibrating(): Boolean

    /**
     * 校准弯曲颈部
     * return Boolean(true:校准成功,false: 校准失败)
     */
    abstract suspend fun onBendNeckCalibrating(): Boolean

    /**
     * 设置振动偏移角度
     */
    abstract fun setVibrationAngle(angle: Int)

    /**
     * 设置振动间隔
     */
    abstract fun setVibrationFrequency(frequency: Int)

    /**
     * 设置寻找设备
     */
    abstract fun setFindDevice(enabled: Boolean)

    /**
     * 设置设备模式（干预/监测）
     */
    abstract fun setVibration(enabled: Boolean)

    /**
     *  初始化重力传感器
     */
    abstract fun initSensor(context: Context)

    /**
     *  重力传感器开始监听
     */
    abstract fun gravityStartListening()

    /**
     *  重力传感器停止监听
     */
    abstract fun gravityStopListening()
}