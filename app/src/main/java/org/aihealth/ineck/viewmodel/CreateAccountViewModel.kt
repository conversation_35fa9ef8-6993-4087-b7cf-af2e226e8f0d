package org.aihealth.ineck.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.model.angles.Gender
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.saveToLocal
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.time_UTC
import org.aihealth.ineck.viewmodel.dao.CreateAccountScreenEvent

class CreateAccountViewModel : BaseViewModel(), CreateAccountScreenEvent {
    var gender by mutableStateOf(Gender.M)
    var name by mutableStateOf("")
    var height by mutableDoubleStateOf(0.0)
    var weight by mutableDoubleStateOf(0.0)
    var birthdate by mutableStateOf(getDefaultDate())

    override fun onNextClick() {
        if (name.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.the_real_name_cannot_be_empty))
            return
        }
        val zeroDouble = 0.0
        if (height.equals(zeroDouble)) {
            DialogUtil.showToast(localeResources.getString(R.string.height_cannot_be_0))
            return
        }
        if (weight.equals(zeroDouble)) {
            DialogUtil.showToast(localeResources.getString(R.string.weight_cannot_be_0))
            return
        }
        DialogUtil.showLoading()
        user.name = name
        user.gender = gender.name
        user.height = height
        user.weight = weight
        user.birthdate = birthdate.time_UTC
        if (isInChina) {
            createAccountWithAuthing()
        } else {
            createAccountWithAuth0()
        }


    }

    private fun createAccountWithAuth0() {
        apiService.postInfo(user).enqueueBody { response ->
            if (response?.code == 1) {
                user.saveToLocal()
                DialogUtil.hideLoading()
                startScreen(Screen.Main.route, true)
            } else if (response?.code == 0) {
                startScreen(Screen.FirstUpdateData.route, true)
            } else {
                DialogUtil.showToast { response?.msg }
            }

        }
    }

    private fun createAccountWithAuthing() {
        apiService.postInfo(user).enqueueBody {
            user.saveToLocal()
            DialogUtil.hideLoading()
            startScreen(Screen.Main.route, true)
        }
    }

    fun uploadData(data: User) {
        if (data.name.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.the_real_name_cannot_be_empty))
            return
        }
        val zeroDouble = 0.0
        if (data.height.equals(zeroDouble)) {
            DialogUtil.showToast(localeResources.getString(R.string.height_cannot_be_0))
            return
        }
        if (data.weight.equals(zeroDouble)) {
            DialogUtil.showToast(localeResources.getString(R.string.weight_cannot_be_0))
            return
        }
        if (data.birthdate.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.please_select_birthdate))
            return
        }
        user.name = data.name
        user.gender = data.gender
        user.height = data.height
        user.weight = data.weight
        user.birthdate = data.birthdate
        user.preferences.heightUnit = data.preferences.heightUnit
        user.preferences.weightUnit = data.preferences.weightUnit
        DialogUtil.showLoading()
        apiService.postInfo(user).enqueueBody { response ->
            if (response?.code == 1) {
                user.saveToLocal()
                DialogUtil.hideLoading()
                startScreen(Screen.Main.route, true)
            } else if (response?.code == 0) {
                DialogUtil.hideLoading()
                startScreen(Screen.FirstUpdateData.route, true)
            } else {
                DialogUtil.hideLoading()
                DialogUtil.showToast { response?.msg }
            }
        }
    }
}