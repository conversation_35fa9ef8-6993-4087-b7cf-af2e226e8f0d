package org.aihealth.ineck.viewmodel

import android.graphics.Bitmap
import android.media.MediaFormat
import androidx.annotation.OptIn
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.media3.common.Format
import androidx.media3.common.MediaItem
import androidx.media3.common.MediaMetadata
import androidx.media3.common.MimeTypes
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.offline.DownloadRequest
import androidx.media3.exoplayer.video.VideoFrameMetadataListener
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.aihealth.ineck.activity
import org.aihealth.ineck.analyzer.CaptureImageAnalyzer
import org.aihealth.ineck.model.ExerciseResult
import org.aihealth.ineck.model.FeelingResult
import org.aihealth.ineck.model.MediaItemTag
import org.aihealth.ineck.model.improvement.ImproveProgram
import org.aihealth.ineck.model.improvement.ImprovementDetailType
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.model.improvement.ProgramModel
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.util.videoUtils.VideoDownloadUtil
import org.aihealth.ineck.view.screen.exercise.FeelingRecordData
import org.aihealth.ineck.view.screen.exercise.PhotoTrainingData
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import kotlin.math.abs
import kotlin.math.min

/**
 *  改善详情页视图模型
 */
class ImproveDetailViewModel : ViewModel() {

    var materialId: Int = 0

    var pageIndex by mutableIntStateOf(0)

    var startTime = 0L
    var endTime = 0L

    var comparisonSteps = 5

    /** 当前改善页加载项目方案内容数据列表缓存 */
    var improveProgramDetail = ImproveProgram(
        1,
        "Video",
        "https://myaih.net/material/image/get/12",
        32,
        isDevicesRequired = false,
        isMembershipRequired = false,
        title = "\\u810a\\u67f1\\u534f\\u4f1a\\u63a8\\u8350\\uff1a\\u9888\\u690e\\u653e\\u677e\\u7597\\u6cd5",
        subtitle = "",
        description = "45\\u79d2\\u653e\\u677e\\u80a9\\u9888\\uff0c\\u9888\\u524d\\u4f38\\uff0c\\u7ec3\\u5929\\u9e45\\u9888\\uff0c\\u63d0\\u5347\\u6c14\\u8d28\\u3001\\u91ca\\u653e\\u80a9\\u9888\\u80cc\\u90e8\\u75b2\\u52b3\\uff0c\\u9002\\u7528\\u957f\\u671f\\u4f4e\\u5934\\u4f0f\\u6848\\u4eba\\u7fa4",
        frequency = 128
    )

    /**
     *  视频校准引导的测量结果，该数据用于带入拟合方程估算设备角度
     */
    var detectResultValueForFitted: Float = 0f

    /**
     *  视频检测模式启动前校准引导页返回结果
     */
    val detectResultState: MutableStateFlow<DetectedResult> = MutableStateFlow(DetectedResult.None)


    /**
     * 获取改善方案详情
     */
    fun getImproveProgramDetailData(mainViewModel: MainViewModel) {
        improveProgramDetail = mainViewModel.improvementScreen.getExerciseItemData(materialId)
        val dur = improveProgramDetail.duration / comparisonSteps
        viewModelScope.launch(Dispatchers.IO) {
            duration.clear()
            durationList.clear()
            MLKitCompareModel.init(comparisonSteps)
            duration.add(dur * 1_000_000L)
            durationList.add(duration[0] - 1_000_000L)
            durationList.add(duration[0] - 500_000L)
            durationList.add(duration[0])
            durationList.add(duration[0] + 500_000L)
            durationList.add(duration[0] + 1_000_000L)
            for (i in 1 until comparisonSteps) {
                duration.add(duration[i - 1] + dur * 1_000_000L)
                durationList.add(duration[i] - 1_000_000L)
                durationList.add(duration[i] - 500_000L)
                durationList.add(duration[i])
                durationList.add(duration[i] + 500_000L)
                durationList.add(duration[i] + 1_000_000L)
            }
            LogUtil.d("analyze steps:${comparisonSteps} dur:${dur} duration: ${duration.toJson()}\n\r durationList: ${durationList.toJson()}")
        }
    }

    val programList = MutableStateFlow<ImprovementProgramsData?>(
        ImprovementProgramsData(
            2, listOf(
                ImproveProgram(
                    1,
                    "Video",
                    "https://myaih.net/material/image/get/12",
                    32,
                    false,
                    false,
                    "颈椎改善练习1",
                    "颈椎改善练习1",
                    "颈椎改善练习1",
                    100
                ),
                ImproveProgram(
                    2,
                    "Image",
                    "https://myaih.net/material/image/get/3",
                    1800,
                    false,
                    false,
                    "颈椎改善练习2",
                    "颈椎改善练习2",
                    "颈椎改善练习2",
                    100
                )
            )
        )
    )

    var videoStartList = mutableListOf(0L)
    val sectionData = mutableStateOf(ProgramModel(0, 0, "", emptyList()))

    // 当前步数
    val count = mutableStateOf(0)
    val score = mutableStateOf(100)
    val duration = mutableListOf<Long>(4_000_000L)
    val durationList = mutableListOf<Long>()
    val videoEulerDic: MutableMap<Int, List<Double>> = mutableMapOf()
    val valueEulerDic: MutableMap<Int, List<Double>> = mutableMapOf()
    val hasFinishedScore: MutableStateFlow<Boolean> = MutableStateFlow(false)
    var photoFinished = false
    var videoFinished = false
    val hasPostEvaluation = MutableStateFlow<Boolean>(false)

    /** 获取改善项目方案详情数据 */
    fun getImprovementProgramDetailData() {
        LogUtil.i("getImprovementProgramDetailData origin")
        apiService.getImprovementProgramDetailData(materialId)
            .enqueueBody { response ->
                try {
                    if (response!!.code == 1) {
                        /* 若成功请求到数据 */
                        viewModelScope.launch {
                            sectionData.value = response.data
                            //                        LogUtil.i("improveProgramDetail.type ${improveProgramDetail.type} && ${ImprovementDetailType.IMAGE.type}")
                            if (improveProgramDetail.type == ImprovementDetailType.VIDEO.type) {
                                LogUtil.i("videoStartList: init")
                                //                                extractFrames()

                                videoStartList.clear()
                                videoStartList.add(0L)
                                sectionData.value.sections.forEachIndexed { _, section ->
                                    videoStartList.add(videoStartList.last() + section.duration)
                                }
                                LogUtil.i("videoStartList: ${videoStartList.toJson()}")
                            } else if (improveProgramDetail.type == ImprovementDetailType.IMAGE.type) {
                                dataList.clear()
                                sectionData.value.sections.forEachIndexed { index, section ->
                                    dataList.add(
                                        PhotoTrainingData(
                                            index = index,
                                            title = section.title,
                                            description = section.subtitle,
                                            action = section.description,
                                            photo = section.resource,
                                        )
                                    )
                                }
                            }
                        }
                    } else {
//                            /* 响应不正常的情况 */
//                            this.improvementDataState.update {
//                                ImprovementProgramsLoadState.Failure(error = Exception(), message = "data of response is null")
//                            }
                    }
                } catch (e: Exception) {
                    /* 出现了response出现了空指针的情况 */
//                        this.improvementDataState.update {
//                            ImprovementProgramsLoadState.Failure(error = e, message = "request error")
//                        }
                }
            }
    }

    fun getImprovementProgramDataTwo(mainViewModel: MainViewModel) {
        programList.value = mainViewModel.improvementScreen.getExerciseItemDataTow()
    }

    fun postUserExerciseData() {
        endTime = System.currentTimeMillis()

        //用的时间
        val second = (endTime - startTime) / 1000
        //至少100秒
        if (second >= 100) {
            score.value = 100
        } else {
            val baseScore = 5 * 9.0
            val addScore = 100.0 - baseScore
            val baseSeconds = 5 * 3.0
            val addBaseSecond = 100.0 - baseSeconds
            score.value = (baseScore + (addScore / addBaseSecond) * (second).toDouble()).toInt()
        }
        LogUtil.i("score ${score.value}, cost time ${second},startTime ${startTime},endTime ${endTime}")
        val body = ExerciseResult(
            materialId,
            score.value,
            improveProgramDetail.duration,
        )
        apiService.postImprovementProgramUserExerciseData(body).enqueueBody {
            LogUtil.i("upload")
        }
    }

    fun postUserFeelingData(feeling: FeelingRecordData) {
        val body = FeelingResult(
            materialId,
            feeling.feeling,
            feeling.comment,
        )
        apiService.patchImprovementProgramUserExerciseData(body).enqueueBody {
            LogUtil.i("upload")
            hasPostEvaluation.value = true
        }
    }

    var mediaItems: List<MediaItem> = mutableListOf()
    var downloadMediaItems: List<MediaItem> = mutableListOf()
    var id = MutableStateFlow(0)
    var volume = MutableStateFlow(0f)
    var isPlaySub = MutableStateFlow(false)

    var playerIsPlaying by mutableStateOf(false)

    var totalDuration by mutableLongStateOf(1L)

    var nowPlayTime = 0L
    var currentTime by mutableLongStateOf(0L)

    var finishedDialogVisible by mutableStateOf(false)

    var finishedPhotoDialogVisible by mutableStateOf(false)
    val listener = object : Player.Listener {
        override fun onEvents(
            player: Player,
            events: Player.Events
        ) {
            super.onEvents(player, events)
            when (player.playbackState) {
                Player.STATE_IDLE -> {
                    LogUtil.i("STATE_IDLE")
                }

                Player.STATE_READY -> {
                    totalDuration = player.duration.coerceAtLeast(1L)
                    currentTime = player.currentPosition.coerceAtLeast(0L)
                }

                Player.STATE_ENDED -> {
                    if (player.duration > 0 && player.currentPosition >= player.duration) {
                        finishedDialogVisible = true
                        LogUtil.i("STATE_ENDED finishedDialogVisible: $finishedDialogVisible")
                    }
                }

                else -> {
                }
            }

        }

        override fun onIsPlayingChanged(isPlaying: Boolean) {
            playerIsPlaying = isPlaying
        }

        override fun onMediaItemTransition(mediaItem: MediaItem?, reason: Int) {
            super.onMediaItemTransition(mediaItem, reason)
            when (reason) {
                // 自动播放
                Player.MEDIA_ITEM_TRANSITION_REASON_AUTO -> {
                    id.value += 1
                }

                Player.MEDIA_ITEM_TRANSITION_REASON_REPEAT -> {

                }

                Player.MEDIA_ITEM_TRANSITION_REASON_PLAYLIST_CHANGED -> {

                }

                else -> {
                }
            }
        }
    }

    @OptIn(UnstableApi::class)
    val videoFrameMetadataListener = object : VideoFrameMetadataListener {
        override fun onVideoFrameAboutToBeRendered(
            presentationTimeUs: Long,
            releaseTimeNs: Long,
            format: Format,
            mediaFormat: MediaFormat?
        ) {
            duration.forEachIndexed { index, it ->
                if (presentationTimeUs == it * 1000000L) {
                    count.value += 1
//                    imageAnalyzer.triggerCapture()
//                    savePeopleFace(count.value)
                    if (count.value == 7) {
//                        imageAnalyzer.close()
//                        LogUtil.i("analyze start analyze photo")
//                        analyzerPicture(activity.getExternalFilesDir(null)?.absolutePath + "/${materialId}_face")
                    }
                }
            }
        }
    }

    val imageAnalyzer = CaptureImageAnalyzer { x, y, z ->
        LogUtil.i("analyze the angleX:${x},angleY:${y},angleZ:${z}")
        valueEulerDic.put(
            count.value,
            listOf(x.toDouble(), y.toDouble(), z.toDouble())
        )
        if (valueEulerDic.size == duration.size && videoEulerDic.size == duration.size) {
            score.value =
                MLKitCompareModel.compareDataDicToResultScore(videoEulerDic, valueEulerDic).toInt()
            hasFinishedScore.value = true
            LogUtil.i("analyze :$score, valueEulerDic:$valueEulerDic, videoEulerDic:$videoEulerDic")
        }
    }

    val isVisiblePlayList: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /**
     * 设置视频播放源
     */
    @UnstableApi
    fun setMediaList() {
        mediaItems = listOf()
        LogUtil.i("sectionData: $sectionData")
        // 加载分集
        viewModelScope.launch {
            if (!isPlaySub.value) {
                val mediaItem =
                    MediaItem.Builder()
                        .setUri(sectionData.value.video.toUri())
                        .setMimeType(MimeTypes.VIDEO_MP4)
                        .setMediaMetadata(
                            MediaMetadata.Builder().setTitle(
                                improveProgramDetail.title
                            ).build()
                        )
                        .setTag(
                            MediaItemTag(
                                improveProgramDetail.duration, improveProgramDetail.title
                            )
                        )
                        .build()
                mediaItems = mediaItems.plus(mediaItem)
                VideoDownloadUtil.getDownloadTracker(activity)
                    .toggleDownloadDialogHelper(activity, mediaItem)
            } else {
                if (sectionData.value.sections.isEmpty()) {
                    val mediaItem =
                        MediaItem.Builder()
                            .setUri(sectionData.value.video.toUri())
                            .setMimeType(MimeTypes.VIDEO_MP4)
                            .setMediaMetadata(
                                MediaMetadata.Builder().setTitle(
                                    improveProgramDetail.title
                                ).build()
                            )
                            .setTag(
                                MediaItemTag(
                                    improveProgramDetail.duration, improveProgramDetail.title
                                )
                            )
                            .build()
                    mediaItems = mediaItems.plus(mediaItem)
                    VideoDownloadUtil.getDownloadTracker(activity)
                        .toggleDownloadDialogHelper(activity, mediaItem)

                } else {
                    sectionData.value
                        .sections.let { section ->
                            section.forEachIndexed { index, it ->
                                LogUtil.i("section: $it")
                                val mediaItem = MediaItem.Builder()
                                    .setUri(it.resource.toUri())
                                    .setMimeType(MimeTypes.VIDEO_MP4)
                                    .setMediaMetadata(
                                        MediaMetadata.Builder().setTitle(it.title + index).build()
                                    )
                                    .setTag(
                                        MediaItemTag(it.duration, it.title)
                                    )
                                    .build()
                                mediaItems = mediaItems.plus(mediaItem)
                                VideoDownloadUtil.getDownloadTracker(activity)
                                    .toggleDownloadDialogHelper(activity, mediaItem)
                            }
                        }
                }
            }
            downloadMediaItems = maybeSetDownloadProperties(mediaItems)

        }
    }

    @OptIn(UnstableApi::class)
    fun setMediaSample() {
        val dur = 32 / comparisonSteps
        viewModelScope.launch(Dispatchers.IO) {
            duration.clear()
            durationList.clear()
            MLKitCompareModel.init(comparisonSteps)
            duration.add(dur * 1_000_000L)
            durationList.add(duration[0] - 1_000_000L)
            durationList.add(duration[0] - 500_000L)
            durationList.add(duration[0])
            durationList.add(duration[0] + 500_000L)
            durationList.add(duration[0] + 1_000_000L)
            for (i in 1 until comparisonSteps) {
                duration.add(duration[i - 1] + dur * 1_000_000L)
                durationList.add(duration[i] - 1_000_000L)
                durationList.add(duration[i] - 500_000L)
                durationList.add(duration[i])
                durationList.add(duration[i] + 500_000L)
                durationList.add(duration[i] + 1_000_000L)
            }
            LogUtil.d("analyze steps:${comparisonSteps} dur:${dur} duration: ${duration.toJson()}\n\r durationList: ${durationList.toJson()}")
        }
        mediaItems = listOf()
        viewModelScope.launch {
            val mediaItemOne = MediaItem
                .Builder()
                .setUri("https://myaih.net/material/video/get/24")
                .setMimeType("video/mp4")
                .setMediaMetadata(
                    MediaMetadata.Builder().setTitle(
                        "\\u810a\\u67f1\\u534f\\u4f1a\\u63a8\\u8350\\uff1a\\u9888\\u690e\\u653e\\u677e\\u7597\\u6cd5"
                    ).build()
                )
                .setTag(
                    MediaItemTag(
                        32,
                        "\\u529e\\u516c\\u5fc5\\u5907\\u00b75\\u5206\\u949f\\u6539\\u5584\\u9888\\u524d\\u503e"
                    )
                )
                .build()
            mediaItems = mediaItems.plus(mediaItemOne)
            VideoDownloadUtil.getDownloadTracker(activity)
                .toggleDownloadDialogHelper(activity, mediaItemOne)
            downloadMediaItems = maybeSetDownloadProperties(mediaItems)
        }
    }

    @UnstableApi
    fun maybeSetDownloadProperties(items: List<MediaItem>): List<MediaItem> {
        val item = mutableListOf<MediaItem>()
        items.forEachIndexed { _, it ->
            val downloadRequest: DownloadRequest? = VideoDownloadUtil.getDownloadTracker(activity)
                .getDownloadRequest(it.localConfiguration?.uri)
            if (downloadRequest == null) {
                item.add(it)
            } else {
                val builder = it.buildUpon()
                    .setMediaId(downloadRequest.id)
                    .setUri(downloadRequest.uri)
                    .setCustomCacheKey(downloadRequest.customCacheKey)
                    .setMimeType(downloadRequest.mimeType)
                    .setStreamKeys(downloadRequest.streamKeys)

                val drmConfiguration = it.localConfiguration!!.drmConfiguration
                if (drmConfiguration != null) {
                    builder.setDrmConfiguration(
                        drmConfiguration.buildUpon().setKeySetId(downloadRequest.keySetId).build()
                    )
                }
                val downloadMediaItem = builder.build()
                LogUtil.i("downloadMediaItem: ${downloadMediaItem.toJson()}")
                item.add(downloadMediaItem)
            }
        }
        return item
    }

    @UnstableApi
    fun maybeSetDownloadProperty(item: MediaItem): MediaItem {
        val downloadRequest: DownloadRequest? = VideoDownloadUtil.getDownloadTracker(activity)
            .getDownloadRequest(item.localConfiguration?.uri)
        if (downloadRequest == null) {
            return item
        } else {
            val builder = item.buildUpon()
                .setMediaId(downloadRequest.id)
                .setUri(downloadRequest.uri)
                .setCustomCacheKey(downloadRequest.customCacheKey)
                .setMimeType(downloadRequest.mimeType)
                .setStreamKeys(downloadRequest.streamKeys)

            val drmConfiguration = item.localConfiguration!!.drmConfiguration
            if (drmConfiguration != null) {
                builder.setDrmConfiguration(
                    drmConfiguration.buildUpon().setKeySetId(downloadRequest.keySetId).build()
                )
            }
            return builder.build()
        }
    }

    fun clearPlayerHistory() {
        playerIsPlaying = false
        totalDuration = 1L
        currentTime = 0L
    }

    fun playVideo(
        isPlaySubVide: Boolean,
        theVideoIndex: Int,
    ) {
        clearPlayerHistory()
        id.value = theVideoIndex
        if (isPlaySubVide) {
            isPlaySub.value = true
        } else {
            isPlaySub.value = false
        }
    }

    val dataList = mutableListOf<PhotoTrainingData>().apply {
        add(
            PhotoTrainingData(
                index = 1,
                title = "动作一：等长运动",
                description = "坐立或站立缩颈",
                action = "以 \"中立 \"的头部姿势坐立或站着缩颈，保持3秒。",
                photo = "https://myaih.net/material/image/get/7"
            )
        )
        add(
            PhotoTrainingData(
                index = 2,
                title = "动作二：等长抗阻训练",
                description = "手掌抵住头部运动",
                action = "将手掌按在额头上，用颈部肌肉抵抗。保持3秒。",
                photo = "https://myaih.net/material/image/get/7"
            )
        )
        add(
            PhotoTrainingData(
                index = 3,
                title = "动作三：等长抗阻训练",
                description = "手掌抵住头部运动",
                action = "将手掌按在后脑勺上，用颈部肌肉抵抗。保持3秒。",
                photo = "https://myaih.net/material/image/get/7"
            )
        )
        add(
            PhotoTrainingData(
                index = 4,
                title = "动作四：等长抗阻训练",
                description = "手掌抵住头部运动",
                action = "将手掌按在头侧，用颈部肌肉抵抗。保持3秒。",
                photo = "https://myaih.net/material/image/get/7"
            )
        )
        add(
            PhotoTrainingData(
                index = 5,
                title = "动作五：肩胛骨回缩",
                description = "弯曲肘部，挤压肩胛骨",
                action = "站直并在躯干两侧弯曲肘部，将肩胛骨挤压在一起后，保持3秒。",
                photo = "https://myaih.net/material/image/get/7"
            )
        )
    }


    /**
     * 获取视频关键帧
     */
    suspend fun getVideoFrame(bitmap: Bitmap, index: Long) {
        val outputDirectory =
            (activity.getExternalFilesDir(null)?.absolutePath + "/$materialId") ?: ""
        val logFile = File(outputDirectory)
        if (!logFile.exists()) {
            logFile.mkdirs()
        }
        val outputName = "$outputDirectory/$index.png"
        var out: FileOutputStream? = null
        try {
            out = withContext(Dispatchers.IO) {
                FileOutputStream(outputName)
            }
            // Compress Bitmap to PNG format image
            bitmap.compress(Bitmap.CompressFormat.PNG, 100, out)
        } catch (e: Exception) {
            e.printStackTrace()
        } finally {
            try {
                out?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }


    }

    /**
     * 分析视频数据
     */
    fun analyzerVideo() {
        viewModelScope.launch(Dispatchers.IO) {
            val outputDirectory =
                (activity.getExternalFilesDir(null)?.absolutePath + "/$materialId") ?: ""
            val photoFile = File(outputDirectory)
            if (!photoFile.exists()) {
                return@launch
            }
            LogUtil.i("analyze video file is exist")
            val files = photoFile.listFiles()
            val detector = FaceDetection.getClient(
                FaceDetectorOptions.Builder()
                    .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
                    .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
                    .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
                    .build()
            )
            files?.forEachIndexed { index, file ->
                val image: InputImage
                try {
                    val t = file.path.substringAfterLast("/").substringBefore(".").toInt()
                    image = InputImage.fromFilePath(activity, file.toUri())
                    detector.process(image)
                        .addOnSuccessListener { faces ->
                            if (faces.isEmpty()) {
                                LogUtil.i("analyze video face is empty")
                            } else {
                                for (face in faces) {
                                    videoEulerDic.put(
                                        (t / 100_000),
                                        listOf(
                                            face.headEulerAngleX.toDouble(),
                                            face.headEulerAngleY.toDouble(),
                                            face.headEulerAngleZ.toDouble(),
                                        )
                                    )
                                }
                                LogUtil.i("analyze video face x:${faces[0].headEulerAngleX},y:${-faces[0].headEulerAngleY},z:${faces[0].headEulerAngleZ}")
                            }
                        }
                        .addOnFailureListener { e ->
                            LogUtil.i("analyze video face error: $e")
                        }
                        .addOnCompleteListener {
                            if (index == files.size - 1) {
                                LogUtil.d("analyze the videoEulerDic is $videoEulerDic and size:${videoEulerDic.size}")
                                LogUtil.d("analyze the valueEulerDic is $valueEulerDic and size:${valueEulerDic.size}")
                                videoFinished = true
                                LogUtil.d("analyze video finished")
                                calculateScore()

                            }
                        }
                } catch (e: IOException) {
                    LogUtil.i("analyze video face error: $e")
                }
            }

        }

    }


    /**
     * 分析摄像头数据
     */
    fun analyzerPicture() {
        viewModelScope.launch(Dispatchers.IO) {
            val dirPath =
                (activity.getExternalFilesDir(null)?.absolutePath + "/${materialId}_face") ?: ""
            val photoFile = File(dirPath)
            LogUtil.i("analyze file")
            if (!photoFile.exists()) {
                return@launch
            }
            LogUtil.i("face file is exist")
            val files = photoFile.listFiles()
            val detector = FaceDetection.getClient(
                FaceDetectorOptions.Builder()
                    .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
                    .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
                    .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
                    .build()
            )
            files?.forEachIndexed { index, file ->
                LogUtil.i("analyze file name is ${file.path}")
                try {
                    val t = file.path.substringAfterLast("/").substringBefore(".").toInt()
                        ?: (index + 1)
                    val image: InputImage
                    image = InputImage.fromFilePath(activity, file.toUri())
                    detector.process(image)
                        .addOnSuccessListener { faces ->
                            if (faces.isEmpty()) {
                                LogUtil.i("analyze video face is empty")
                            } else {
                                for (face in faces) {
                                    valueEulerDic.put(
                                        (t / 100_000),
                                        listOf(
                                            face.headEulerAngleX.toDouble(),
                                            -(face.headEulerAngleY.toDouble() - detectResultValueForFitted),
                                            face.headEulerAngleZ.toDouble(),
                                        )
                                    )
                                }
                                LogUtil.i("analyze camera face x:${faces[0].headEulerAngleX},y:${faces[0].headEulerAngleY},z:${faces[0].headEulerAngleZ}")
                            }
                        }
                        .addOnFailureListener { e ->
                            LogUtil.i("analyze video face error: $e")
                        }
                        .addOnCompleteListener {
                            if (index == files.size - 1) {
                                LogUtil.d("analyze the videoEulerDic is $videoEulerDic and size:${videoEulerDic.size}")
                                LogUtil.d("analyze the valueEulerDic is $valueEulerDic and size:${valueEulerDic.size}")
                                photoFinished = true
                                LogUtil.d("analyze photo finished")
                                calculateScore()

                            }
                        }


                } catch (e: IOException) {
                    e.printStackTrace()
                    LogUtil.i("analyze video face error: $e")
                }
            }
        }

    }

    /**
     * 删除图片
     */
    fun deletePhoto(dirPath: String) {
        viewModelScope.launch(Dispatchers.IO) {
            val directory = File(dirPath)
            val images = directory.listFiles { _, name ->
                name.endsWith(".png") || name.endsWith(".jpg") || name.endsWith(
                    ".jpeg"
                )
            }
            images?.forEach { file ->
                val deleted = file.delete()
                if (deleted) {
                    LogUtil.i("analyze  ${file.name} was deleted successfully.")
                } else {
                    LogUtil.i("Failed to delete ${file.name}.")
                }
            }
        }
    }
    fun deleteVideoPhoto() {
        viewModelScope.launch(Dispatchers.IO) {
            val outputDirectory =
                (activity.getExternalFilesDir(null)?.absolutePath + "/${improveProgramDetail.materialId}")
                    ?: ""
            val photoFile = File(outputDirectory)
            if (photoFile.exists()) {
                LogUtil.i("删除文件:$outputDirectory")
                deletePhoto(outputDirectory)
            }
        }
    }

    fun deleteCameraPhoto() {
        viewModelScope.launch(Dispatchers.IO) {
            val outputDirectory =
                (activity.getExternalFilesDir(null)?.absolutePath + "/${improveProgramDetail.materialId}_face")
                    ?: ""
            val photoFile = File(outputDirectory)
            if (photoFile.exists()) {
                LogUtil.i("删除文件:$outputDirectory")
                deletePhoto(outputDirectory)
            }
        }
    }


    /**
     * 获取相机图片
     */
    suspend fun savePeopleFace(imageCapture: ImageCapture, index: Long) {
        withContext(Dispatchers.IO) {
            val outputDirectory =
                (activity.getExternalFilesDir(null)?.absolutePath + "/${materialId}_face") ?: ""
            val logFile = File(outputDirectory)
            if (!logFile.exists()) {
                logFile.mkdirs()
                LogUtil.i("face create file")
            }
            val photoFile = File(
                outputDirectory,
                "$index.png"
            )
            LogUtil.i("analyze start get one photo")
            val outputOptions = ImageCapture.OutputFileOptions.Builder(photoFile).build()
            imageCapture.takePicture(
                outputOptions,
                ContextCompat.getMainExecutor(activity),
                object : ImageCapture.OnImageSavedCallback {
                    override fun onError(exc: ImageCaptureException) {
                        LogUtil.d("analyze Photo capture failed: ${exc.message}")
                    }

                    override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                        //                    val savedUri = Uri.fromFile(photoFile)
                        LogUtil.i("analyze get one photo")

                    }
                }
            )
        }
    }

    /**
     * 计算得分
     */
    private fun calculateScore() {
        LogUtil.i("analyze calculateScore start,photoFinished:$photoFinished,videoFinished:$videoFinished")
        if (photoFinished && videoFinished) {
            score.value =
                MLKitCompareModel.compareDataDicToResultScore(videoEulerDic, valueEulerDic).toInt()
            hasFinishedScore.value = true
            photoFinished = false
            videoFinished = false
            LogUtil.d("analyze calculateScore finish, score:$score,hasFinishedScore:$hasFinishedScore,photoFinished:$photoFinished,videoFinished:$videoFinished")

        }

    }

    /**
     * 搜索是否存在图片
     */
    private fun isExistPhoto(): Boolean {
        val photoFile = File(
            activity.getExternalFilesDir(null)?.absolutePath + "/$materialId"
        )
        if (!photoFile.exists()) {
            return false
        } else {
            val files = photoFile.listFiles()
            if (files != null) {
                if (files.size == 7) {
                    return true
                }
            }
            return false
        }
    }

    fun checkCameraPermission(): Boolean {
        return ActivityResultUtils.checkPermissions(
            arrayOf(android.Manifest.permission.CAMERA),
        )
    }

}

object MLKitCompareModel {
    // 步骤数
    var steps = 7

    // 单个步骤最大得分
    var stepScore = 14

    var finalScore = 0.0

    fun init(steps: Int) {
        this.steps = steps
        stepScore = 100 / steps
        finalScore = 0.0
    }

    private fun compareOneValueToResultScore(videoPoint: Point, cameraPoint: Point): Double {
        val x1 = videoPoint.x
        val x2 = videoPoint.y
        val x3 = videoPoint.z

        val y1 = cameraPoint.x
        val y2 = cameraPoint.y
        val y3 = cameraPoint.z

        val deltaX = abs(x1 - y1)
        val deltaY = abs(x2 - y2)
        val deltaZ = abs(x3 - y3)

        val videoSum = abs(x1) + abs(x2) + abs(x3)
        val deltaSum = deltaX + deltaY + deltaZ

        /** 偏差值 */
        val deviationValue = deltaSum / videoSum

        /** 最多将这个步骤的分数扣完 */
        val score = (1.0 - min(deviationValue, 0.5)) * stepScore
        LogUtil.i(
            "analyze compareOneValueToResultScore score:${
                score.toString().format("%.2f")
            } deviationValue:${deviationValue.toString().format("%.2f")} videoSum:${
                videoSum.toString().format("%.2f")
            } deltaSum:${deltaSum.toString().format("%.2f")} videoPoint:${
                videoPoint.toString().format("%.2f")
            } cameraPoint:${cameraPoint.toString().format("%.2f")}"
        )
        return score

    }

    fun compareDataDicToResultScore(
        videoDic: Map<Int, List<Double>>,
        cameraDic: Map<Int, List<Double>>
    ): Double {
        finalScore = 0.0
        videoDic.forEach { t, u ->
            val videoPoint = Point(u[0], u[1], u[2])
            val singleSoreArray = mutableListOf<Double>()
            cameraDic.forEach { t1, u1 ->
                if (t - 5 == t1 || t - 10 == t1 || t == t1 || t + 5 == t1 || t + 10 == t1) {
                    val cameraPoint = Point(u1[0], u1[1], u1[2])
                    val score = compareOneValueToResultScore(videoPoint, cameraPoint)
                    singleSoreArray.add(score)
                }
            }
            LogUtil.d("analyze compareDataDicToResultScore singleSoreArray:${singleSoreArray.toJson()}")
            finalScore += singleSoreArray.maxOrNull() ?: 0.0
        }
        return finalScore
    }

    private data class Point(val x: Double, val y: Double, val z: Double) {
        override fun toString(): String {
            return "x:$x,y:$y,z:$z"
        }
    }
}
