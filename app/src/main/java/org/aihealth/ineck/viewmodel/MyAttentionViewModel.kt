package org.aihealth.ineck.viewmodel

import android.Manifest
import android.content.Intent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.JsonElement
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.model.angles.Follow
import org.aihealth.ineck.model.angles.ProviderSearchResult
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.qrcode.QRCodeActivity
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.dialog.AIHTipButtonDialog
import org.aihealth.ineck.viewmodel.dao.MyAttentionEvent
import java.net.URLDecoder
import java.nio.charset.StandardCharsets

class MyAttentionViewModel : BaseViewModel(), MyAttentionEvent {
    var list = mutableStateListOf<Follow>()
    var sendReportDialogVisible by mutableStateOf(false)
    var selectFollow: Follow? = null

    var searchData = mutableStateListOf<Follow>()

    // 分页相关状态
    // 分页相关状态
    private val _currentPage = MutableStateFlow(1)
    private val _isLoading = MutableStateFlow(false)
    private val _hasMoreData = MutableStateFlow(true)
    val isLoading: StateFlow<Boolean> = _isLoading
    val hasMoreData: StateFlow<Boolean> = _hasMoreData
    private val pageSize = 20
    // 添加总文章数量状态
    private val _totalArticleCount = MutableStateFlow(0)

    var totalPages by mutableStateOf(1)
    var currentSearchText by mutableStateOf("")

    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by mutableStateOf(false)

    var theClickType by mutableIntStateOf(0)

    var scanUUID = ""
    var userInfoName = ""

    /**
     * 扫码成功后确认是否添加关注对话框visible
     */
    var addAttentionDialogVisible by mutableStateOf(false)


    override fun sendReport(viewModel: MainViewModel) {
        viewModel.reportScreen.email = selectFollow?.email ?: ""
        viewModel.reportScreen.sendEmailDialogVisible = true
        MainViewModel.pageIndex = 3
        popScreen(Screen.Main.route)
    }

    override fun addFollow(uuid: String) {
        apiService.addFollow(
            followingId = uuid
        ).enqueue(
            onResponse = { _, response ->
                if (response!!.code == 1) {
                    DialogUtil.showToast(baseApplication.getString(R.string.addattention_success))
                    startScreen(Screen.MyAttention.route, true)
                } else {
                    DialogUtil.showToast(response.msg)
                }
            },
            onFailure = { _, throwable ->
                DialogUtil.hideLoading()
                DialogUtil.showToast(throwable.message.toString())
            })
    }

    override fun deleteFollow() {
        selectFollow?.uuid?.let {
            apiService.deleteFollow(following_id = it).enqueueBody {
                list.remove(selectFollow)
            }
        }
    }

    fun search(text: String,refresh: Boolean = true) {
        if (_isLoading.value) return
        // 重置分页状态
        if (refresh) {
            _currentPage.value = 1
            currentSearchText = text
            _hasMoreData.value = true
            searchData.clear()
        }
        _isLoading.value = true
        viewModelScope.launch {
            apiService.searchProviderList(currentSearchText, _currentPage.value, pageSize).enqueue (
                onResponse = { _, response ->
                    _isLoading.value = false
                    response?.let {
                        if (it.code == 1) {
                            val l = mutableStateListOf<Follow>()

                            val result = parseSearchResult(it.data)
                            result?.let {
                                if (it.providers.isEmpty()){
                                    _hasMoreData.value = false
                                }else {
                                    it.providers.forEach { provider ->
                                        if (list.contains(provider)) {
                                            provider.followed = true
                                        }
                                        l.add(provider)
                                    }
                                    searchData.addAll(l.sortedBy { it.followed })
                                    _hasMoreData.value = it.providers.size >= pageSize
                                }

                            }
                        }
                    } ?: run {
                        _hasMoreData.value = false
                    }
                },
                onFailure = { _, throwable ->
                    _isLoading.value = false
                    _hasMoreData.value = false
                }
            )
        }

    }

    private fun parseSearchResult(data: Any):ProviderSearchResult? {
        return try {
            val gson = Gson()
            when (data) {
                is JsonElement -> {
                    gson.fromJson(data, ProviderSearchResult::class.java)
                }
                else -> {
                    gson.fromJson(gson.toJson(data), ProviderSearchResult::class.java)
                }
            }
        }catch (e: Exception){
            LogUtil.e("Error parsing search data: ${e.message}")
            null
        }
    }

    /**
     * 加载更多数据
     */
    fun loadMore() {
        if (!_hasMoreData.value || _isLoading.value || currentSearchText.isEmpty()) return
        _currentPage.value++
        search(currentSearchText,false)
    }

    fun onScanClick() {
        ActivityResultUtils.requestPermissions(
            permissions = arrayOf(Manifest.permission.CAMERA),
            onAllGranted = {
                ActivityResultUtils.startActivity(
                    intent = Intent(activity, QRCodeActivity::class.java)
                ) { _: Int, data: Intent? ->
                    data?.let {
                        val qrcodeResult = it.getStringExtra("qrcode") ?: ""
                        LogUtil.i("qrcodeResult:${qrcodeResult}")
                        if (qrcodeResult.contains("provideruuid:")) {
                            LogUtil.d("mytag", "qrcodeResult: $qrcodeResult")
                            scanUUID = if ("provideruuid:".length == qrcodeResult.length) {
                                ""
                            } else {
                                qrcodeResult.substring("provideruuid:".length)
                            }

                            DialogUtil.showLoading()
                            viewModelScope.launch {
                                apiService.getUserInfoWithoutToken(scanUUID)
                                    .enqueueBody { response ->
                                        DialogUtil.hideLoading()
                                        if (response?.code == 1) {
                                            try {
                                                val gson = Gson()
                                                val info =
                                                    gson.fromJson(response.data, User::class.java)
                                                userInfoName = info.name
                                                addAttentionDialogVisible = true
                                            } catch (e: Exception) {
                                                DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                            }
                                        } else {
                                            DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                        }
                                    }
                            }
                        } else if (qrcodeResult.contains("https://medical.aihnet.com/follow?uuid=")) {
                            DialogUtil.showLoading()
                            val encodedUuid = qrcodeResult.substringAfter("uuid=")
                            val decodedUuid =
                                URLDecoder.decode(encodedUuid, StandardCharsets.UTF_8.toString())
                            scanUUID = decodedUuid
                            LogUtil.d("mytag", "qrcodeResult: $qrcodeResult,scanUUID: $scanUUID")
                            viewModelScope.launch {
                                apiService.getUserInfoWithoutToken(scanUUID)
                                    .enqueueBody { response ->
                                        DialogUtil.hideLoading()
                                        if (response?.code == 1) {
                                            try {
                                                val gson = Gson()
                                                val info =
                                                    gson.fromJson(response.data, User::class.java)
                                                userInfoName = info.name
                                                addAttentionDialogVisible = true
                                            } catch (e: Exception) {
                                                DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                            }
                                        } else {
                                            DialogUtil.showToast(response?.msg ?: "")
                                        }
                                    }
                            }
                        } else {
                            DialogUtil.showToast(localeResources.getString(R.string.incorrect_format_of_QR_code))
                        }
                    }
                }
            },
            onProhibited = {
                if (SPUtil.getBoolean(SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION, true)) {
                    SPUtil.putBoolean(SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION, false)
                } else {
                    DialogUtil.showToast {
                        AIHTipButtonDialog(
                            text = localeResources.getString(R.string.permission_denied),
                            buttonText = localeResources.getString(R.string.to_authorization),
                            onClick = {
                                APPUtil.goAPPDetail()
                            }
                        ) {
                            DialogUtil.hideToast()
                        }
                    }
                }
            }
        )
    }

    fun addFollowInSearch(uuid: String) {
        apiService.addFollow(
            followingId = uuid
        ).enqueue(
            onResponse = { _, response ->
                if (response!!.code == 1) {
                    DialogUtil.showToast(baseApplication.getString(R.string.addattention_success))
                    val search = searchData.find { it.uuid == uuid }
                    LogUtil.i("search:$search")
                    searchData.remove(search)
                    LogUtil.i("searchData:$searchData")
                    searchData.also {
                        search?.followed = true
                        search?.let {
                            searchData.add(it)
                        }
                    }
                    DialogUtil.hideLoading()

                } else {
                    DialogUtil.hideLoading()
                    DialogUtil.showToast(response.msg)
                }
            },
            onFailure = { _, throwable ->
                DialogUtil.hideLoading()
                DialogUtil.showToast(throwable.message.toString())
            })
    }
}