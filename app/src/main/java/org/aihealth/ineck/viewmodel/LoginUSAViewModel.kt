package org.aihealth.ineck.viewmodel

import android.util.Base64
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.auth0.android.authentication.AuthenticationAPIClient
import com.auth0.android.authentication.AuthenticationException
import com.auth0.android.callback.Callback
import com.auth0.android.provider.WebAuthProvider
import com.auth0.android.result.Credentials
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.suspendCancellableCoroutine
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.auth0Account
import org.aihealth.ineck.auth0ApiClient
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.StepModuleManager
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.util.token
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.viewmodel.dao.LoginENScreenEvent
import org.json.JSONObject
import kotlin.coroutines.resume


/**
 * 国外服务器登入界面的ViewModel
 */
class LoginUSAViewModel : BaseViewModel(), LoginENScreenEvent {

    // 是否已阅读选中状态
    private val _isReadChecked = MutableStateFlow(false)
    val isReadChecked: StateFlow<Boolean> = _isReadChecked

    // 未点击阅读需要弹出的对话框显示状态
    private val _dialogVisible = MutableStateFlow(false)
    val dialogVisible: StateFlow<Boolean> = _dialogVisible

    // 邮箱验证对话框显示状态
    private val _emailVerificationDialogVisible = MutableStateFlow(false)
    val emailVerificationDialogVisible: StateFlow<Boolean> = _emailVerificationDialogVisible

    // 当前需要验证的邮箱地址
    private val _emailToVerify = MutableStateFlow("")
    val emailToVerify: StateFlow<String> = _emailToVerify

    // 登录邮箱号码
    var emailLogin by mutableStateOf("")

    // 登录密码
    var passwordLogin by mutableStateOf("")

    // 注册邮箱号码
    var emailSignUp by mutableStateOf("")

    // 注册邮箱密码
    var passwordSignUp by mutableStateOf("")

    // 确认密码
    var passwordConfirm by mutableStateOf("")

    // 密码等级
    var passwordLevel by mutableIntStateOf(0)

    // 密码与确认密码是否一致
    var isRule by mutableStateOf(false)

    // 登录手机号
    var phoneLogin by mutableStateOf("")

    // 注册验证码
    private var codeSignUp by mutableStateOf("")

    // 登录发送验证码剩余时间 默认0秒可发
    var timeSignUp by mutableIntStateOf(0)

    private inline fun checkIsRead(
        content: () -> Unit
    ) {
        if (_isReadChecked.value) {
            content()
        } else {
            _dialogVisible.value = true
        }
    }

    fun setIsReadChecked(value: Boolean) {
        _isReadChecked.value = value
    }

    fun setDialogVisible(value: Boolean) {
        _dialogVisible.value = value
    }

    fun changeIsReadChecked() {
        _isReadChecked.value = !_isReadChecked.value
    }

    fun setEmailVerificationDialogVisible(value: Boolean) {
        _emailVerificationDialogVisible.value = value
    }

    fun setEmailToVerify(email: String) {
        _emailToVerify.value = email
    }

    /**
     * 检查是否需要显示协议对话框
     */
    fun checkAndShowAgreementDialog() {
        if (SPUtil.getBoolean(SPConstant.IS_FIRST_READ_AGREEMENT, true)) {
            LogUtil.i("first private term for USA")
            SPUtil.putBoolean(SPConstant.IS_FIRST_READ_AGREEMENT, false)
            _dialogVisible.value = true
        }
    }


    override fun emailLoginClick() {
        checkIsRead {
            if (emailLogin.isBlank()) {
                DialogUtil.showToast(localeResources.getString(R.string.email_account_cannot_be_empty))
                return
            }
            if (passwordLogin.isBlank()) {
                DialogUtil.showToast(localeResources.getString(R.string.email_password_cannot_be_empty))
                return
            }
            DialogUtil.showLoading()
            loginAuth0(emailLogin, passwordLogin)
        }

    }

    override fun sendCodeClick() {
    }

    override fun signUpClick() {
        if (emailSignUp.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.email_account_cannot_be_empty))
            return
        }
        if (passwordSignUp.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.email_password_cannot_be_empty))
            return
        }
        if (passwordSignUp.length < 8) {
            DialogUtil.showToast(localeResources.getString(R.string.password_not_same))
            return
        }
        if (!isRule) {
            if (passwordSignUp.trim().length < 8) {
                DialogUtil.showLongToast(baseApplication.getString(R.string.password_least_8))
                return
            }
            var count = 0
            if (passwordSignUp.contains(Regex("[a-z]"))) {
                count++
            }
            if (passwordSignUp.contains(Regex("[A-Z]"))) {
                count++
            }
            if (passwordSignUp.contains("!") || passwordSignUp.contains(
                    "@"
                )
                || passwordSignUp.contains("#") || passwordSignUp.contains(
                    "$"
                )
                || passwordSignUp.contains("%") || passwordSignUp.contains(
                    "^"
                )
                || passwordSignUp.contains("&") || passwordSignUp.contains(
                    "*"
                )
            ) {
                count++
            }
            if (count < 3) {
                DialogUtil.showLongToast(baseApplication.getString(R.string.password_limit))
                return
            }
            return
        }
        if (passwordConfirm != passwordSignUp) {
            DialogUtil.showToast(localeResources.getString(R.string.password_not_same))
            return
        }

        checkIsRead {
            auth0SignUpByEmail()
        }
    }

    fun confirmPassword() {
        passwordLevel = 0
        if (passwordSignUp.trim().length >= 8) {
            if (passwordSignUp.contains(Regex("[0-9]"))) {
                passwordLevel++
            }
            if (passwordSignUp.contains(Regex("[a-z]"))) {
                passwordLevel++
            }
            if (passwordSignUp.contains(Regex("[A-Z]"))) {
                passwordLevel++
            }
            if (passwordSignUp.contains("!") || passwordSignUp.contains(
                    "@"
                )
                || passwordSignUp.contains("#") || passwordSignUp.contains(
                    "$"
                )
                || passwordSignUp.contains("%") || passwordSignUp.contains(
                    "^"
                )
                || passwordSignUp.contains("&") || passwordSignUp.contains(
                    "*"
                )
            ) {
                passwordLevel++
            }
            isRule = passwordLevel >= 3
            passwordLevel = 1
            if (passwordSignUp.contains("!") || passwordSignUp.contains(
                    "@"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.contains("#") || passwordSignUp.contains(
                    "$"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.contains("%") || passwordSignUp.contains(
                    "^"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.contains("&") || passwordSignUp.contains(
                    "*"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.trim().length >= 12) {
                passwordLevel++
            }
            LogUtil.i("passwordLevel: $passwordLevel")
        } else {
            isRule = false
            passwordLevel = 0
        }
    }

    suspend fun forgetPassword(email: String): Result<Unit> = suspendCancellableCoroutine { continuation ->
        auth0ApiClient.resetPassword(email, "Username-Password-Authentication")
            .start(object : Callback<Void?, AuthenticationException> {
                override fun onFailure(error: AuthenticationException) {
                    DialogUtil.showLongToast(error.getDescription())
                    continuation.resume(Result.failure(error))
                }

                override fun onSuccess(result: Void?) {
                    DialogUtil.showToast(localeResources.getString(R.string.reset_password_success))
                    continuation.resume(Result.success(Unit))
                }
            })
    }

    /**
     * 重新发送验证邮件
     */
    suspend fun resendVerificationEmail(email: String): Result<Unit> = suspendCancellableCoroutine { continuation ->
        apiService.sendVerificationEmail(email)
            .enqueue({ _, throwable ->
                // 请求失败
                LogUtil.e("resendVerificationEmail", "Failed to send verification email: ${throwable.message}")
                DialogUtil.showLongToast("Failed to send verification email")
                continuation.resume(Result.failure(throwable))
            }, { _, response ->
                // 请求成功
                if (response?.code == 1) {
                    DialogUtil.showToast("Verification email sent successfully")
                    continuation.resume(Result.success(Unit))
                } else {
                    val errorMsg =  "Failed to send verification email"
                    DialogUtil.showLongToast(errorMsg)
                    continuation.resume(Result.failure(Exception(errorMsg)))
                }
            })
    }

    /**
     * auth0 注册
     */
    private fun auth0SignUpByEmail() {
        DialogUtil.showLoading()
        auth0ApiClient.signUp(
            emailSignUp, passwordSignUp, emailSignUp, "Username-Password-Authentication"
        ).validateClaims().start(object : Callback<Credentials, AuthenticationException> {
            override fun onSuccess(result: Credentials) {
                LogUtil.i("Registration success: ${result}")
                // 注册成功后，不再自动登录，而是设置邮箱地址并提示用户验证邮箱
                emailLogin = emailSignUp  // 将注册邮箱设置为登录邮箱
                DialogUtil.hideLoading()
                popScreen(Screen.Login.route)
                DialogUtil.showLongToast(localeResources.getString(R.string.registration_success_message))
            }

            override fun onFailure(error: AuthenticationException) {
                DialogUtil.hideLoading()
                val errorDescription = error.getDescription()
                // 检查是否是邮箱验证错误
                if (errorDescription.contains("Please verify your email address first", ignoreCase = true)) {
                    emailLogin = emailSignUp  // 将注册邮箱设置为登录邮箱
                    DialogUtil.hideLoading()
                    finish()
                    DialogUtil.showLongToast(localeResources.getString(R.string.registration_success_message))
                } else {
                    // 其他错误显示toast
                    DialogUtil.showToast(errorDescription)
                }
            }
        })
    }


    override fun googleLoginClick() {
        checkIsRead {
            DialogUtil.showLoading()
            loginAuh0FromGoogle()
        }
    }

    /**
     * facebook login
     */
    override fun facebookLoginClick() {
        checkIsRead {
            LogUtil.i("facebook login button click")
            WebAuthProvider.login(auth0Account)
                .withConnection("facebook")
                .withScheme(localeResources.getString(R.string.com_auth0_scheme))
                .withScope("openid profile email read:current_user update:current_user_metadata")
                .withAudience("https://${localeResources.getString(R.string.com_auth0_domain)}/api/v2/")
                .start(MainActivity.getInstance(), auth0Callback)
        }

    }


    override fun wechatLoginClick() {
    }


    /**
     * login with email and password
     */
    fun loginAuth0(email: String, password: String) {
        LogUtil.i("login with auth")
        user.email = email
        val auth0APIClient = AuthenticationAPIClient(auth0Account)
        auth0APIClient.login(email, password)
            .validateClaims()
            .setGrantType("http://auth0.com/oauth/grant-type/password-realm")
            .setRealm("Username-Password-Authentication")
            .setScope("openid profile email read:current_user update:current_user_metadata")
            .setAudience("https://ineck.auth0.com/api/v2/").start(auth0Callback)

    }

    private fun loginAuh0FromGoogle() {
        LogUtil.i("login with auth google")
        WebAuthProvider.login(auth0Account).withConnection("google-oauth2")
            .withScheme(localeResources.getString(R.string.com_auth0_scheme))
            .withScope("openid profile email read:current_user update:current_user_metadata phone")
            .withAudience("https://${localeResources.getString(R.string.com_auth0_domain)}/api/v2/")
            .withParameters(mapOf("prompt" to "select_account"))
            .start(MainActivity.getInstance(), auth0Callback)
    }

    private val auth0Callback = object : Callback<Credentials, AuthenticationException> {
        override fun onFailure(error: AuthenticationException) {
            // Failure! Check the exception for details
            LogUtil.d("auth0",error.toJson())
            
            val errorDescription = error.getDescription()
            // 检查是否是邮箱验证错误
            if (errorDescription.contains("Please verify your email address first", ignoreCase = true)) {
                // 显示邮箱验证对话框
                val currentEmail = user.email.ifEmpty { emailLogin }
                setEmailToVerify(currentEmail)
                setEmailVerificationDialogVisible(true)
            } else {
                // 其他错误显示toast
                DialogUtil.showToast(errorDescription)
            }
            DialogUtil.hideLoading()
        }

        override fun onSuccess(result: Credentials) {
            LogUtil.i("🔐 LoginUSAViewModel: ========== 用户登录成功 ==========")
            LogUtil.i("Credentials:${result.idToken}")
            
            extractUserInfoFromToken(result.idToken)
            token = result.accessToken
            result.user.let {
                user.uuid = it.getId() ?: ""
            }
            userSP.edit().putString(SPConstant.TOKEN, token)
                .putString("uuid", user.uuid).apply()
            
            LogUtil.i("🔐 LoginUSAViewModel: 用户信息保存完成，开始处理步数模块")
            // 用户登录成功，检查并自动启用步数模块
            StepModuleManager.onUserLogin()
            
            LogUtil.i("🔐 LoginUSAViewModel: 准备跳转到主界面")
            DialogUtil.hideLoading()
            startScreen(Screen.Main.route, true)
            LogUtil.i("🔐 LoginUSAViewModel: ========== 登录处理完成 ==========")
        }
    }

    fun extractUserInfoFromToken(token: String) {
        return try {
            val parts = token.split(".")
            if (parts.size < 2) {
                // Invalid token format
                return
            }
            val payload = parts[1]
            val decodedPayloadBytes = Base64.decode(payload, Base64.DEFAULT)
            val decodedPayloadString = String(decodedPayloadBytes)
            val jsonObject = JSONObject(decodedPayloadString)

            val nickname = jsonObject.optString("nickname", null)
            val email = jsonObject.optString("email", null)
            val picture = jsonObject.optString("picture", null)
            user.name = nickname
            user.email = email
            user.photo = picture
        } catch (e: Exception) {
            return
        }
    }

}