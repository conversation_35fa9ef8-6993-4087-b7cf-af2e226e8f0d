package org.aihealth.ineck.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.modelmsg.SendAuth
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.aihealth.ineck.BaseApplication
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.util.AuthingUtil
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.StepModuleManager
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.token
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.viewmodel.dao.LoginZHScreenEvent
import java.util.Locale

/**
 * 国内服务器登入界面的ViewModel
 */
class LoginChinaViewModel : BaseViewModel(), LoginZHScreenEvent {
    // 是否已阅读选中状态
    private val _isReadChecked = MutableStateFlow(false)
    val isReadChecked: StateFlow<Boolean> = _isReadChecked

    // 未点击阅读需要弹出的对话框显示状态
    private val _dialogVisible = MutableStateFlow(false)
    val dialogVisible: StateFlow<Boolean> = _dialogVisible


    // 是否手机号登录
    var isPhoneLogin by mutableStateOf(true)

    // 登录手机号
    var phoneLogin by mutableStateOf("")

    // 登录验证码
    var codeLogin by mutableStateOf("")

    // 登录发送验证码剩余时间 默认0秒可发
    var timeLogin by mutableIntStateOf(0)

    // 登录邮箱号码
    var emailLogin by mutableStateOf("")

    // 登录密码
    var passwordLogin by mutableStateOf("")

    // 注册邮箱号码
    var emailSignUp by mutableStateOf("")

    // 注册邮箱密码
    var passwordSignUp by mutableStateOf("")

    // 确认密码
    var passwordConfirm by mutableStateOf("")

    // 密码等级
    var passwordLevel by mutableIntStateOf(0)

    // 密码与确认密码是否一致
    var isRule by mutableStateOf(false)

    var forgetEmail by mutableStateOf("")
    var forgetPassword by mutableStateOf("")
    var checkForgetPassword by mutableStateOf("")
    var forgetCode by mutableStateOf("")
    var forgetTimeLogin by mutableIntStateOf(0)

    init {
        //微信登录回调
        BaseApplication.instance.processWechatStatusChanged = {
            if (BaseResp.ErrCode.ERR_OK === it.errCode) {
                loginWechat(it as SendAuth.Resp)
            } else if (BaseResp.ErrCode.ERR_USER_CANCEL === it.errCode) {
                //取消
            } else {

            }
        }
    }
    private inline fun checkIsRead(
        content: () -> Unit
    ) {
        if (_isReadChecked.value) {
            content()
        } else {
            _dialogVisible.value = true
        }
    }

    fun setIsReadChecked(value: Boolean) {
        _isReadChecked.value = value
    }

    fun setDialogVisible(value: Boolean) {
        _dialogVisible.value = value
    }

    fun changeIsReadChecked() {
        _isReadChecked.value = !_isReadChecked.value
    }

    override fun phoneLoginClick() {
        checkIsRead {
            if (phoneLogin.length < 11) {
                DialogUtil.showToast(localeResources.getString(R.string.write_correct_phone))
                return@checkIsRead
            }
            if (codeLogin.length < 4) {
                DialogUtil.showToast(localeResources.getString(R.string.wrong_verification_code))
                return@checkIsRead
            }
            DialogUtil.showLoading()
            viewModelScope.launch {
                val response = AuthingUtil.loginWithPhone(phoneLogin, codeLogin)
                response?.let {
                    if (response.statusCode == 200) {
                        response.data?.let {
                            token = AuthingUtil.modifyJWT(
                                response.data.idToken,
                                "e26ede8d9a6d89256ffd5f194924fcd8"
                            )
                            user.phone = phoneLogin
                            userSP.edit().putString(SPConstant.TOKEN, token).apply()
                            
                            LogUtil.i("🔐 LoginChinaViewModel: 手机登录成功，开始处理步数模块")
                            // 用户登录成功，检查并自动启用步数模块
                            StepModuleManager.onUserLogin()
                            
                            LogUtil.i("🔐 LoginChinaViewModel: 准备跳转到主界面")
                            startScreen(Screen.Main.route, true)
                        }
                    } else if (response.apiCode == 2026) {
                        DialogUtil.showToast(baseApplication.getString(R.string.email_registered))
                    } else {
                        DialogUtil.showToast(response.message)
                    }
                }
            }
        }
    }

    /**
     * 发送验证码，由 authing 提供
     */
    override fun sendCodeClick() {
        if (currentLocale == Locale.CHINESE && phoneLogin.length < 11 || currentLocale == Locale.ENGLISH && phoneLogin.length < 10) {
            DialogUtil.showToast(localeResources.getString(R.string.write_correct_phone))
            return
        }
        if (timeLogin == 0) {
            LogUtil.d("sendCodeClick")
            viewModelScope.launch {
                val response = AuthingUtil.sendSMS(phoneLogin)
                response?.let {
                    if (response.statusCode == 200) {
                        timeLogin = 59
                        // 倒计时
                        launch {
                            while (timeLogin > 0) {
                                delay(1000)
                                timeLogin -= 1
                            }
                        }
                    } else {
                        DialogUtil.showToast(response.message)
                    }
                }
            }
        }

    }

    fun sendForgetPasswordCode(){
        if (forgetTimeLogin == 0){
            viewModelScope.launch{
                val response=   AuthingUtil.sendEmail(forgetEmail,"reset_password")
                response?.let {
                    if (response.statusCode == 200) {
                        forgetTimeLogin = 299
                        // 倒计时
                        launch {
                            while (forgetTimeLogin > 0) {
                                delay(1000)
                                forgetTimeLogin -= 1
                            }
                        }
                    } else {
                        DialogUtil.showToast(response.message)
                    }
                }
            }
        }
    }
    fun resetPassword(){

        viewModelScope.launch{
            val response = AuthingUtil.resetPassword(forgetEmail,forgetCode,checkForgetPassword)
            LogUtil.i("resetPassword:$response")
            if (response == null){
                DialogUtil.showToast(activity.getString(R.string.try_again))
            }else {
                if (response.statusCode == 200){
                    DialogUtil.showToast(baseApplication.getString(R.string.reset_password_success_with_password))
                    popScreen(Screen.Login.route)
                }else{
                    DialogUtil.showToast(activity.getString(R.string.wrong_verification_code))
                }

            }
        }
    }

    fun changeSignUpWay() {
        isPhoneLogin = !isPhoneLogin
    }

    /**
     * authing 通过邮箱注册，不发送验证码
     */
    private fun authingSignUpByEmail() {
        DialogUtil.showLoading()
        viewModelScope.launch {
            val response = AuthingUtil.signUpByEmail(emailSignUp, passwordSignUp)
            DialogUtil.hideLoading()
            response?.let {
                if (response.statusCode == 200) {
                    response.data?.let {
                        emailLogin = emailSignUp
                        user.email = emailLogin
                        popScreen(Screen.Login.route)
                    }
                } else if (response.apiCode == 2026) {
                    DialogUtil.showToast(baseApplication.getString(R.string.email_registered))
                } else {
                    DialogUtil.showToast(response.message)
                }
            }
        }
    }

    /**
     * authing 通过邮箱登入
     */
    fun authingLoginByEmail() {

        checkIsRead {
            viewModelScope.launch {
                val response = AuthingUtil.loginWithEmail(emailLogin, passwordLogin)
                response?.let {
                    if (response.statusCode == 200) {
                        response.data?.let {
                            token = AuthingUtil.modifyJWT(
                                response.data.idToken,
                                "e26ede8d9a6d89256ffd5f194924fcd8"
                            )
                            user.email = emailLogin
                            userSP.edit().putString(SPConstant.TOKEN, token).apply()
                            
                            LogUtil.i("🔐 LoginChinaViewModel: 邮箱登录成功，开始处理步数模块")
                            // 用户登录成功，检查并自动启用步数模块
                            StepModuleManager.onUserLogin()
                            
                            LogUtil.i("🔐 LoginChinaViewModel: 准备跳转到主界面")
                            startScreen(Screen.Main.route, true)
                        }
                    } else if (response.apiCode == 2026) {
                        DialogUtil.showToast(baseApplication.getString(R.string.email_registered))
                    } else {
                        DialogUtil.showToast(response.message)
                    }
                }

            }
        }
    }

    /**
     * 微信登入跳转，Wechat 由 authing 提供
     */
    override fun wechatLoginClick() {
        checkIsRead {
            val req = SendAuth.Req()
            req.scope = "snsapi_userinfo" // 只能填 snsapi_userinfo
            req.state = "AIHealth"
            BaseApplication.instance.wxapi.sendReq(req)

//            val wechat = Wechat()
//            wechat.login(baseApplication, loginCallBack)
        }

    }
    /**
     * 微信登录
     *
     * https://developers.weixin.qq.com/doc/oplatform/Mobile_App/WeChat_Login/Development_Guide.html
     */
    fun loginWechat(resp: SendAuth.Resp) {
        LogUtil.e("wechat:${resp.code}")
        viewModelScope.launch {
            val response = AuthingUtil.loginByWechat(resp.code)
            response?.let {
                if (response.statusCode == 200) {
                    response.data?.let {
                        token = AuthingUtil.modifyJWT(
                            response.data.idToken,
                            "e26ede8d9a6d89256ffd5f194924fcd8"
                        )
                        userSP.edit().putString(SPConstant.TOKEN, token).apply()
                        startScreen(Screen.Main.route, true)
                    }
                } else if (response.apiCode == 2026) {
                    DialogUtil.showToast(baseApplication.getString(R.string.email_registered))
                } else {
                    DialogUtil.showToast(response.message)
                }
            }
        }


    }


    fun confirmPassword() {
        passwordLevel = 0
        if (passwordSignUp.trim().length >= 8) {
            if (passwordSignUp.contains(Regex("[0-9]"))) {
                passwordLevel++
            }
            if (passwordSignUp.contains(Regex("[a-z]"))) {
                passwordLevel++
            }
            if (passwordSignUp.contains(Regex("[A-Z]"))) {
                passwordLevel++
            }
            if (passwordSignUp.contains("!") || passwordSignUp.contains(
                    "@"
                )
                || passwordSignUp.contains("#") || passwordSignUp.contains(
                    "$"
                )
                || passwordSignUp.contains("%") || passwordSignUp.contains(
                    "^"
                )
                || passwordSignUp.contains("&") || passwordSignUp.contains(
                    "*"
                )
            ) {
                passwordLevel++
            }
            isRule = passwordLevel >= 3
            passwordLevel = 1
            if (passwordSignUp.contains("!") || passwordSignUp.contains(
                    "@"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.contains("#") || passwordSignUp.contains(
                    "$"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.contains("%") || passwordSignUp.contains(
                    "^"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.contains("&") || passwordSignUp.contains(
                    "*"
                )
            ) {
                passwordLevel++
            }
            if (passwordSignUp.trim().length >= 12) {
                passwordLevel++
            }
            LogUtil.i("passwordLevel: $passwordLevel")
        } else {
            isRule = false
            passwordLevel = 0
        }
    }

    fun signUpClick() {
        if (emailSignUp.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.email_account_cannot_be_empty))
            return
        }
        if (passwordSignUp.isBlank()) {
            DialogUtil.showToast(localeResources.getString(R.string.email_password_cannot_be_empty))
            return
        }
        if (passwordSignUp.length < 8) {
            DialogUtil.showToast(localeResources.getString(R.string.password_not_same))
            return
        }
        if (!isRule) {
            if (passwordSignUp.trim().length < 8) {
                DialogUtil.showLongToast(
                    baseApplication.getString(R.string.password_least_8)
                )
                return
            }
            var count = 0
            if (passwordSignUp.contains(Regex("[a-z]"))) {
                count++
            }
            if (passwordSignUp.contains(Regex("[A-Z]"))) {
                count++
            }
            if (passwordSignUp.contains("!") || passwordSignUp.contains(
                    "@"
                )
                || passwordSignUp.contains("#") || passwordSignUp.contains(
                    "$"
                )
                || passwordSignUp.contains("%") || passwordSignUp.contains(
                    "^"
                )
                || passwordSignUp.contains("&") || passwordSignUp.contains(
                    "*"
                )
            ) {
                count++
            }
            if (count < 3) {
                DialogUtil.showLongToast(baseApplication.getString(R.string.password_limit))
                return
            }
            return
        }
        if (passwordConfirm != passwordSignUp) {
            DialogUtil.showToast(localeResources.getString(R.string.password_not_same))
            return
        }

        checkIsRead {
            authingSignUpByEmail()
        }
    }
}