package org.aihealth.ineck.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.bluetooth.BLEScanner
import org.aihealth.ineck.database
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.model.SPConstant.DOCTOR_UUID
import org.aihealth.ineck.model.Utilization
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
// StepService 已移除，使用HybridStepTracker替代
import org.aihealth.ineck.timeStampDao
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.viewmodel.impl.DeviceScreenEventImpl
import org.aihealth.ineck.viewmodel.impl.HomeScreenEventImpl
import org.aihealth.ineck.viewmodel.impl.ImprovementEventImpl
import org.aihealth.ineck.viewmodel.impl.MyScreenEventImpl
import org.aihealth.ineck.viewmodel.impl.ReportScreenEventImpl

class MainViewModel : BaseViewModel() {


    companion object {
        const val TAG = "MainViewModel"
        var onlineTime by mutableLongStateOf(0L)
        var exerciseTime by mutableLongStateOf(0L)
        val endTime = System.currentTimeMillis() / 1000
        var pageIndex by mutableIntStateOf(0)
        var trailTimeLimit by mutableLongStateOf(100 * 60 * 60L)
    }

    var deviceScreen = DeviceScreenEventImpl(this)
    var homeScreen = HomeScreenEventImpl(this)
    var improvementScreen = ImprovementEventImpl(this)
    var reportScreen = ReportScreenEventImpl(this)
    var myScreen = MyScreenEventImpl(this)
    var questionnaireReportScreen = QuestionnaireReportViewModel(this)
    private val bleScanner = BLEScanner(activity)

    var unreadMessageCount by mutableIntStateOf(0)

    val angleDao by lazy {
        database.angleDao()
    }

    var useLocalData by mutableIntStateOf(
        // 从SharedPreferences中读取保存的状态，默认为1（禁用）
        userSP.getInt(SPConstant.USE_LOCAL_STEP_DATA, 1)
    )

    var deviceStep by mutableStateOf(userSP.getString(SPConstant.DEVICE_STEP, "5000").toString())

    /**
     * 是否有过弹窗
     */
    var isShowTailMembershipDialog by mutableStateOf(false)
    var isShowTailMembershipFinishedDialog by mutableStateOf(false)

    /**
     * 导航到固件升级页面
     */
    fun navigateToFirmwareUpgrade() {
        LogUtil.d(TAG, "导航到固件升级页面")
        // 显示更新对话框
        deviceScreen.updateDialogVisible = true
    }

    init {
        viewModelScope.launch {
            getTimeStamp()
            val endTime = System.currentTimeMillis() / 1000
            activity.addTimeStamp(endTime)
            postTimeStamp()

            startUnreadMessageCountTimer()
        }
    }

    private fun startUnreadMessageCountTimer() {
        flow {
            while (true) {
                emit(Unit)
                delay(30000)
            }
        }
        .flowOn(Dispatchers.Default)
        .cancellable()
        .onEach {
            getTheUnreadMessageCount()
        }
        .launchIn(viewModelScope)
    }

    fun postAngle(deviceType: DeviceType) {
        val angleList = angleDao.find(user.uuid, deviceType.name)
        if (angleList.isNotEmpty()) {
            NetWork.postAngle(deviceType, angleList) {
                angleDao.clear(user.uuid, deviceType.name, angleList.last().timestamp)
            }
        }
    }

    /**
     * 上传用户在线时长
     */
    fun postTimeStamp() {
        if (user.uuid.isEmpty()) {
            return
        }
        val endTime = System.currentTimeMillis() / 1000
        activity.updateEndTimeStamp(endTime)

        val timeStampList = timeStampDao.getTimeStampByUidWithEnd(user.uuid)
        if (timeStampList.isEmpty()) {
            return
        }
        LogUtil.i("the upload time:${timeStampDao.getTotalTimeSpentByUid(user.uuid)}")
        timeStampList.forEach { timeStamp ->
            NetWork.postTimeStamp(timeStamp) {
                timeStampDao.deleteTimeStamp(timeStamp)
            }
        }

    }

    fun getTimeStamp() {
        if (user.uuid.isEmpty()) {
            return
        }
        apiService.getUserUtilizationTime().enqueueBody { response ->
            LogUtil.i("response body: ${response?.data}")
            if (response?.code == 1) {
                if (response.msg == "Success") {
                    exerciseTime = response.data.exerciseDuration
                    onlineTime =
                        response.data.usageDuration + timeStampDao.getTotalTimeSpentByUid(user.uuid)
                    if (onlineTime < 0) {
                        apiService.postUserUtilizationTime(
                            Utilization(
                                startTime = "0",
                                endTime = "0",
                                timeSpent = -onlineTime,
                            )
                        ).enqueueBody { it ->
                            LogUtil.i("response body: $it")
                        }
                        onlineTime = 0
                    }
                    if (onlineTime > 1706868282000L) {
                        apiService.postUserUtilizationTime(
                            Utilization(
                                startTime = "0",
                                endTime = "0",
                                timeSpent = onlineTime / 1000 - onlineTime,
                            )
                        ).enqueueBody { it ->
                            LogUtil.i("response body: $it")
                        }
                        onlineTime /= 1000
                    }
                    LogUtil.i(
                        "get the Time online:${response.data}，" +
                                ",the time outline:${timeStampDao.getTotalTimeSpentByUid(user.uuid)}" +
                                "the totalTime:${onlineTime}"
                    )
                } else {
                    onlineTime = timeStampDao.getTotalTimeSpentByUid(user.uuid)
                    LogUtil.i(
                        "No Time online,the time outline:${
                            timeStampDao.getTotalTimeSpentByUid(
                                user.uuid
                            )
                        }"
                    )
                }
            } else {
                LogUtil.d(("get time stamp failure" + response?.data))
            }
        }
    }


    override fun onCleared() {
        LogUtil.d("mytag", "onCleared: ")
        deviceScreen.bluetoothUtil.close()
        super.onCleared()

    }

    fun addFollow(uuid: String) {
        apiService.addFollow(
            followingId = uuid
        ).enqueue(
            onResponse = { _, response ->
                SPUtil.putString(DOCTOR_UUID, "")
                if (response!!.code == 1) {
                    DialogUtil.showToast(baseApplication.getString(R.string.addattention_success))
                } else {
                    DialogUtil.showToast(response.msg)
                }
            },
            onFailure = { _, throwable ->
                DialogUtil.showToast(throwable.message.toString())
            })

    }

    fun getTheUnreadMessageCount() {
        apiService.getUnreadChatCount().enqueue(
            onResponse = { _, response ->
                if (response?.code == 1 && response.data != null) {
                    unreadMessageCount = response.data
                    LogUtil.i("未读消息数: $unreadMessageCount")
                }
            },
            onFailure = { _, throwable ->
                LogUtil.e("获取未读消息数失败: ${throwable.message}")
            }
        )
    }

}

var user by mutableStateOf(User())