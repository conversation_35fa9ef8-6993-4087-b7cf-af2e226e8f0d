package org.aihealth.ineck.viewmodel

import kotlinx.coroutines.flow.MutableStateFlow
import org.aihealth.ineck.model.AiNeckQuestionnaireHistoryModel
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshState

class QuestionnaireResultViewModel : BaseViewModel() {
    /* 刷新状态 */
    val refreshState = AIHRefreshState(false)

    val aiNeckDataModeState: MutableStateFlow<QuestionnaireResultLoadState> =
        MutableStateFlow(QuestionnaireResultLoadState.Loading)
    val aiBackDataModeState: MutableStateFlow<QuestionnaireResultLoadState> =
        MutableStateFlow(QuestionnaireResultLoadState.Loading)
}

enum class QuestionnaireResultTabType {
    AiNeck,
    AiBack,
}

sealed class QuestionnaireResultLoadState() {
    object Loading : QuestionnaireResultLoadState()
    data class Success(val data: List<AiNeckQuestionnaireHistoryModel>) :
        QuestionnaireResultLoadState()

    data class Error(val error: Throwable) : QuestionnaireResultLoadState()
}