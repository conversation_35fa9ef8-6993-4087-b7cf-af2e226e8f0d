package org.aihealth.ineck.viewmodel

import org.aihealth.ineck.model.CervicalVertebraeResult
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil

class QuestionnaireReportViewModel (
    private val viewModel: MainViewModel
) : BaseViewModel() {

    var cervicalVertebraeResult = CervicalVertebraeResult(
        1,
        "0:0",
        "健康",
    )
    var nowDate = TimeUtil.getCurrentDate()
    fun getCervicalVertebraeResult() {
        apiService.getCervicalVertebraeResult().enqueueBody { res ->
            LogUtil.i("getCervicalVertebrae ${res.toString()}")
            if (res!= null &&res.code == 1){
                cervicalVertebraeResult = res.data
            }

        }
    }

    fun getLumbarVertebraeResult() {
        apiService.getLumbarVertebraeResult().enqueueBody { res ->
            LogUtil.i("getCervicalVertebrae ${res.toString()}")
            if (res != null && res.code == 1) {
                cervicalVertebraeResult  =res.data
            }

        }
    }
}