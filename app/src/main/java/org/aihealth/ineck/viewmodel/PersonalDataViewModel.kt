package org.aihealth.ineck.viewmodel

import android.Manifest
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.MediaStore
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.google.gson.Gson
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import org.aihealth.ineck.BaseApplication
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.putDouble
import org.aihealth.ineck.util.saveToLocal
import org.aihealth.ineck.util.time_UTC
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.dialog.AIHTipButtonDialog
import org.aihealth.ineck.view.screen.info.HeightUnit
import org.aihealth.ineck.view.screen.info.WeightUnit
import org.json.JSONObject
import java.io.File
import java.io.IOException
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone


class PersonalDataViewModel : BaseViewModel() {
    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by mutableStateOf(false)
    fun getPictureFromCamera() {
        ActivityResultUtils.requestPermissions(
            permissions = arrayOf(Manifest.permission.CAMERA),
            onAllGranted = {
                val intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)
                val contentValues = ContentValues(2)
                contentValues.put(MediaStore.Images.Media.DISPLAY_NAME, "AIHealth_camera")
                contentValues.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg")
                val uri = activity.contentResolver.insert(
                    MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                    contentValues
                )
                intent.putExtra(MediaStore.EXTRA_OUTPUT, uri)
                ActivityResultUtils.startActivity(intent) { resultCode, _ -> }
            },
            onProhibited = {
                DialogUtil.showToast {
                    AIHTipButtonDialog(
                        text = localeResources.getString(R.string.permission_denied),
                        buttonText = localeResources.getString(R.string.to_authorization),
                        onClick = {
                            APPUtil.goAPPDetail()
                        }
                    ) {
                        DialogUtil.hideToast()
                    }
                }
            }
        )
    }
    fun getPictureFromAlbum() {
        ActivityResultUtils.openDocument(arrayOf("image/*")) {
            it?.let {
                DialogUtil.showLoading()
                val file = createFilePart(it,BaseApplication.instance)
                apiService.uploadImage(file)
                    .enqueue(
                        onResponse = { _, response ->
                            response?.code?.let {
                                if (response.code == 1){
                                    apiService.getInfo().enqueueBody { response ->
                                        DialogUtil.hideLoading()
                                        if (response?.code == 1) {
                                            LogUtil.i("user: ${response.data.toJson()}")
                                            try {
                                                val gson = Gson()
                                                val info = gson.fromJson(response.data, User::class.java)
                                                LogUtil.i("user: $user")
                                                user = info
                                                user.saveToLocal()
                                            } catch (e: Exception) {
                                                DialogUtil.showToast(baseApplication.getString(R.string.online_data_error))
                                            }
                                        }
                                    }
                                }
                                else {
                                    DialogUtil.hideLoading()
                                    DialogUtil.showToast(response.msg)
                                }
                            }
                        },
                        onFailure = { _, throwable ->
                            DialogUtil.hideLoading()
                            DialogUtil.showToast(BaseApplication.instance.getString(R.string.data_upload_failed))
                            LogUtil.d("getAiNeckDetailHistory failure: ${throwable.message}")
                        }
                    )
            }
        }
    }

    fun setName(name: String) {
        user = user.copy().apply { this.name = name }
        userSP.edit().putString("name", name).apply()
        apiService.updateInfo(
            body = hashMapOf(Pair("name", name))
        ).enqueueBody {}
    }

    fun setGender(gender: String) {
        LogUtil.i("gender: $gender")
        user = user.copy().apply { this.gender = gender }
        userSP.edit().putString("gender", gender).apply()
        apiService.updateInfo(
            body = hashMapOf(Pair("gender", gender))
        ).enqueueBody {}
    }

    fun setBirthday(calendar: Calendar) {
        val body = JSONObject()
        val birthday = calendar.time_UTC
        user = user.copy().apply { this.birthdate = birthday }
        userSP.edit().putString("birthdate", birthday).apply()
        userSP.edit().putInt("age", user.age).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair("age", user.age),
                Pair("birthdate", birthday)
            )
        ).enqueueBody {}
    }

    fun setBirthdate(birthdate: Long) {
        val body = JSONObject()
        val birthday = longToFormattedString(birthdate)
        val calculatedAge = calculateAge(birthdate)
        user = user.copy().apply {
            this.birthdate = birthday
            this.age = calculatedAge
        }
        userSP.edit().putString("birthdate", birthday).apply()
        userSP.edit().putInt("age", calculatedAge).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair("age", calculatedAge),
                Pair("birthdate", birthday)
            )
        ).enqueueBody {
        }

    }

    fun setMobileNumber(phone: String) {
        user = user.copy().apply { this.phone = phone }
        userSP.edit().putString("phone", phone).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair("phone", phone)
            )
        ).enqueueBody {
        }

    }

    fun setEmail(email: String) {
        user = user.copy().apply { this.email = email }
        userSP.edit().putString("email", email).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair("email", email)
            )
        ).enqueueBody {
        }
    }


    fun longToFormattedString(timeInMillis: Long): String {
        val date = Date(timeInMillis)
        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        format.timeZone = TimeZone.getTimeZone("UTC")
        return format.format(date)
    }

    private fun calculateAge(birthdateMillis: Long): Int {
        val birthCalendar = Calendar.getInstance().apply {
            timeInMillis = birthdateMillis
        }
        val currentCalendar = Calendar.getInstance()

        var age = currentCalendar.get(Calendar.YEAR) - birthCalendar.get(Calendar.YEAR)

        // 如果还没到生日，年龄减1
        if (currentCalendar.get(Calendar.DAY_OF_YEAR) < birthCalendar.get(Calendar.DAY_OF_YEAR)) {
            age--
        }

        return maxOf(0, age) // 确保年龄不为负数
    }


    fun setHeight(height: Double) {
        user = user.copy().apply {
            this.height = height
            this.preferences.heightUnit = preferences.heightUnit
        }
        userSP.edit().putDouble("height", height).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair(
                    "preferences",
                    user.preferences
                ),
                Pair(
                    "height",
                    height
                )
            )
        ).enqueueBody {
        }
    }

    fun setWeight(weight: Double) {
        user = user.copy().apply {
            this.weight = weight
            this.preferences.weightUnit = preferences.weightUnit
        }
        userSP.edit().putDouble("weight", weight).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair(
                    "preferences",
                    user.preferences
                ),
                Pair(
                    "weight",
                    weight
                )
            )
        ).enqueueBody {}
    }

    fun setHeight(unit: HeightUnit) {
        val p = user.preferences.copy().apply { this.heightUnit = unit.unit }
        user = user.copy().apply {
            this.height = unit.value + 0.0000000001
            this.preferences.heightUnit = p.heightUnit
        }
        LogUtil.i("heightString: $unit,P:$p,user:${user.preferences}")
        userSP.edit().putDouble("height", unit.value).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair(
                    "preferences",
                    p
                ),
                Pair(
                    "height",
                    unit.value + 0.0000000001
                )
            )
        ).enqueueBody {}
    }

    fun setWeight(unit: WeightUnit) {
        user = user.copy().apply {
            this.weight = unit.value
            this.preferences.weightUnit = unit.unit
        }
        userSP.edit().putDouble("weight", unit.value).apply()
        apiService.updateInfo(
            body = hashMapOf(
                Pair(
                    "preferences",
                    user.preferences
                ),
                Pair(
                    "weight",
                    unit.value
                )
            )
        ).enqueueBody {}
    }

    fun getFileFromUri(context: Context, uri: Uri): File {
        val inputStream = context.contentResolver.openInputStream(uri)
            ?: throw IOException("Cannot open input stream from URI")
        val file = File(context.cacheDir, "temp_image.jpg")
        file.outputStream().use { output ->
            inputStream.copyTo(output)
        }
        return file
    }

    private fun createFilePart(uri: Uri, context: Context): MultipartBody.Part {
        val filePath = getRealPathFromURI(context, uri) ?: throw IOException("Invalid file path")
        val file = File(filePath)

        val requestBody = file.asRequestBody("image/jpeg".toMediaTypeOrNull())
        return MultipartBody.Part.createFormData("file", file.name, requestBody)
    }
    private fun getRealPathFromURI(context: Context, uri: Uri): String? {
        val cursor = context.contentResolver.query(uri, null, null, null, null)
        cursor?.use {
            if (it.moveToFirst()) {
                val index = it.getColumnIndex(MediaStore.Images.Media.DATA)
                if (index != -1) {
                    return it.getString(index)
                }
            }
        }
        val inputStream = context.contentResolver.openInputStream(uri)
        val file = File(context.cacheDir, "temp_image.jpg")
        file.outputStream().use { output ->
            inputStream?.copyTo(output)
        }
        return file.absolutePath
    }
}