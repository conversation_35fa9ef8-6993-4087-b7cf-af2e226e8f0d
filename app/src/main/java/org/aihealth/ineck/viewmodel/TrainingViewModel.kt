package org.aihealth.ineck.viewmodel

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue

class TrainingViewModel(
    uriString : String,
    imageString : String
): BaseViewModel() {

    private val TAG = "TrainingViewModel"
    val uriString = uriString
    val imageString = imageString
    var isLoadVideoDown by mutableStateOf(false)


}