package org.aihealth.ineck.viewmodel.impl

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import android.view.Gravity
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.bluetooth.BLEClientState
import org.aihealth.ineck.bluetooth.BluetoothService
import org.aihealth.ineck.bluetooth.BluetoothUtil
import org.aihealth.ineck.bluetooth.ScanUiState
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.NotificationConstants
import org.aihealth.ineck.model.angles.Angle
import org.aihealth.ineck.sensor.GravitySensor
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.NotificationUtils
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.dao.DeviceScreenEvent
import org.aihealth.ineck.viewmodel.user
import kotlin.math.absoluteValue

class DeviceScreenEventImpl(
    private val viewModel: MainViewModel
) : DeviceScreenEvent() {

    private val _uiState = MutableStateFlow<ConnectUiState>(ConnectUiState.None)
    val uiState: StateFlow<ConnectUiState> = _uiState
    var mac: String = ""
    var kneeAngleMax = 0
    var kneeAngleMin = 0
    private val deviceVersion = "6.0.6"

    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by mutableStateOf(false)

    var deviceTypeChosen: DeviceType = DeviceType.None

    // aiNeck
    private var neckConnection: ServiceConnection? = null

    // aiBack
    private var backConnection: ServiceConnection? = null

    // kneeJoint
    private var kneeConnection: ServiceConnection? = null

    // elbowJoint
    private var elbowConnection: ServiceConnection? = null

    // shoulderJoint
    private var shoulderConnection: ServiceConnection? = null

    // hipJoint
    private var hipConnection: ServiceConnection? = null

    private var messenger: Messenger? = null

    // BluetoothService 实例和 Messenger
    private var bluetoothService: BluetoothService? = null

    // aiNeck aiBack
    private var bound = false
    private var hasSingleConnected = false

    // aiBack
    private var backBound = false
    private var hasBackConnected = false

    // kneeJoint
    private var kneeBound = false
    private var hasKneeConnected = false

    // elbowJoint
    private var elbowBound = false
    private var hasElbowConnected = false

    // shoulderJoint
    private var shoulderBound = false
    private var hasShoulderConnected = false

    // hipJoint
    private var hipBound = false
    private var hasHipConnected = false

    // aiNeck
    private val _singleDeviceState = MutableStateFlow(BLEClientState())
    val singleDeviceState = _singleDeviceState.asStateFlow()

    // aiBack
    private val _backDeviceState = MutableStateFlow(BLEClientState())
    val backDeviceState = _backDeviceState.asStateFlow()

    // elbowJoint
    private val _elbowDeviceState = MutableStateFlow(BLEClientState())
    val elbowDeviceState = _elbowDeviceState.asStateFlow()

    // kneeJoint
    private val _kneeDeviceState = MutableStateFlow(BLEClientState())
    val kneeDeviceState = _kneeDeviceState.asStateFlow()
    private var _kneeExercise = MutableStateFlow(Pair(0, 0))
    val kneeExercise = _kneeExercise.asStateFlow()

    // shoulderJoint
    private val _shoulderDeviceState = MutableStateFlow(BLEClientState())
    val shoulderDeviceState = _shoulderDeviceState.asStateFlow()

    // hipJoint
    private val _hipDeviceState = MutableStateFlow(BLEClientState())
    val hipDeviceState = _hipDeviceState.asStateFlow()

    /**
     * 蓝牙操作工具类
     */
    var bluetoothUtil: BluetoothUtil = BluetoothUtil()

    // aiNeck
    var aiNeckDeviceConfig: DeviceConfig = DeviceConfig()
//        DeviceConfig.loadByLocal(DeviceType.aiNeck.name)

    // aiBack
    var aiBackDeviceConfig: DeviceConfig =  DeviceConfig()
//    DeviceConfig.loadByLocal(DeviceType.aiBack.name)

    // kneeJoint
    var kneeJointDeviceConfig: DeviceConfig = DeviceConfig()
//    DeviceConfig.loadByLocal(DeviceType.KneeJoint.name)

    // elbowJoint
    var elbowJointDeviceConfig: DeviceConfig =  DeviceConfig()
//        DeviceConfig.loadByLocal(DeviceType.ElbowJoint.name)

    // shoulderJoint
    var shoulderJointDeviceConfig: DeviceConfig =  DeviceConfig()
//        DeviceConfig.loadByLocal(DeviceType.ShoulderJoint.name)

    // hipJoint
    val hipJointDeviceConfig: DeviceConfig =  DeviceConfig()
//    DeviceConfig.loadByLocal(DeviceType.HipJoint.name)

    // 控制 MatchingDeviceSheet 的显示
    var showMatchingDeviceSheet by mutableStateOf(false)
    var currentMatchingDeviceType by mutableStateOf("")

    private val scope = CoroutineScope(Dispatchers.IO + Job())

    /* 重力传感器对象 */
    private lateinit var gravitySensor: GravitySensor

    /* 垂直重力矢量 */
    var verticalGravity = MutableStateFlow(0f)

    var isSecondCalibratedSuccess by mutableStateOf(false)
    private var calibrating = false
    private var primaryAngle = 0
    private var maxAngle = 0
    private val _scanUiState = MutableStateFlow(ScanUiState())
    val scanUiState = _scanUiState.stateIn(
        scope, SharingStarted.WhileSubscribed(5000), ScanUiState()
    )

    init {
        scope.launch {
            singleDeviceState.collectLatest {
                it.isDeviceConnected?.let { connected ->
                    waitScope?.cancel()
                    if (connected) {
                        it.activeDevice?.address?.let { it1 ->
                            if (aiNeckDeviceConfig.mac.isEmpty()) {
                                LogUtil.i("saveDeviceMessage no mac")
                                saveDeviceMessage(
                                    it1,
                                    it.version ?: "",
                                    it.serialNumber ?: "",
                                    it.deviceType.name,
                                )
                            } else if (aiNeckDeviceConfig.mac.isNotEmpty() && aiNeckDeviceConfig.mac != it1) {
                                LogUtil.i("saveDeviceMessage has diff mac")
                                saveDeviceMessage(
                                    it1,
                                    it.version ?: "",
                                    it.serialNumber ?: "",
                                    it.deviceType.name
                                )
                            } else if ((aiNeckDeviceConfig.sn.isEmpty() && !it.serialNumber.isNullOrEmpty()) ||
                                (!it.serialNumber.isNullOrEmpty() && it.serialNumber != aiNeckDeviceConfig.sn) ||
                                (!it.version.isNullOrEmpty() && it.version != aiNeckDeviceConfig.version)
                            ) {
                                LogUtil.i("saveDeviceMessage sn is empty")
                                saveDeviceMessage(
                                    it1,
                                    it.version ?: "",
                                    it.serialNumber ?: "",
                                    it.deviceType.name
                                )
                            }

                        }
                        if (!hasSingleConnected) {
                            if (it.version.isNullOrEmpty()) {
                            } else if (!it.version.contains(deviceVersion) ) {
                                viewModel.deviceScreen.updateDialogVisible = true
                            } else {
                                hasSingleConnected = true
                                _uiState.value = ConnectUiState.ConnectingSuccess
                                LogUtil.d("连接成功 ")
//                                getPower(DeviceType.aiNeck.name)
                            }
                        }
                    } else {
                        if (hasSingleConnected) {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.device_disconnect,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        } else {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.connect_device_error,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        }
                    }
                }
            }
        }
        scope.launch {
            backDeviceState.collectLatest {
                it.isDeviceConnected?.let { connected ->
                    waitScope?.cancel()
                    if (connected) {
                        it.activeDevice?.address?.let { it1 ->
                            if (aiBackDeviceConfig.mac.isEmpty()) {
                                saveDeviceMessage(
                                    it1,
                                    it.version ?: "",
                                    it.serialNumber ?: "",
                                    it.deviceType.name
                                )
                            } else if (aiBackDeviceConfig.mac.isNotEmpty() && aiBackDeviceConfig.mac != it1) {
                                saveDeviceMessage(
                                    it1,
                                    it.version ?: "",
                                    it.serialNumber ?: "",
                                    it.deviceType.name
                                )
                            } else if ((aiBackDeviceConfig.sn.isEmpty() && !it.serialNumber.isNullOrEmpty()) ||
                                (!it.serialNumber.isNullOrEmpty() && it.serialNumber != aiBackDeviceConfig.sn) ||
                                (!it.version.isNullOrEmpty() && it.version != deviceVersion)
                            ) {
                                LogUtil.i("saveDeviceMessage sn is empty")
                                saveDeviceMessage(
                                    it1,
                                    it.version ?: "",
                                    it.serialNumber ?: "",
                                    it.deviceType.name
                                )
                            }
                        }
                        if (!hasBackConnected) {
                            if (it.version == null) {
                            } else if (!it.version.contains(deviceVersion)) {
                                viewModel.deviceScreen.updateDialogVisible = true
                            } else {
                                hasBackConnected = true
                                _uiState.value = ConnectUiState.ConnectingSuccess
                                LogUtil.i("backDeviceState$connected")
//                                getPower(DeviceType.aiBack.name)
                            }
                        }
                    } else {
                        if (hasBackConnected) {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.device_disconnect,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        } else {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.connect_device_error,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        }
                    }
                }
            }
        }
        /*
        scope.launch {
            hipDeviceState.collectLatest {
                it.isDeviceConnected?.let { connected ->
                    waitScope?.cancel()
                    if (connected) {
                        if (!hasHipConnected) {
                            hasHipConnected = true
                            _uiState.value = ConnectUiState.ConnectingSuccess
                        }
                        it.activeDevice?.address?.let { it1 ->
                            saveDeviceMessage(
                                it1,
                                it.deviceType.name
                            )
                        }
                        getPower(DeviceType.HipJoint.name)
                    } else {
                        if (hasHipConnected) {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.device_disconnect,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        } else {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.connect_device_error,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        }
                    }
                }
            }
        }
        scope.launch {
            elbowDeviceState.collectLatest {
                it.isDeviceConnected?.let { connected ->
                    waitScope?.cancel()
                    if (connected) {
                        if (!hasElbowConnected) {
                            hasElbowConnected = true
                            _uiState.value = ConnectUiState.ConnectingSuccess
                        }
                        it.activeDevice?.address?.let { it1 ->
                            saveDeviceMessage(
                                it1,
                                it.deviceType.name
                            )
                        }
                        getPower(DeviceType.ElbowJoint.name)
                    } else {
                        if (hasElbowConnected) {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.device_disconnect,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        } else {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.connect_device_error,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        }
                    }
                }
            }
        }
        scope.launch {
            shoulderDeviceState.collectLatest {
                it.isDeviceConnected?.let { connected ->
                    waitScope?.cancel()
                    if (connected) {
                        if (!hasShoulderConnected) {
                            hasShoulderConnected = true
                            _uiState.value = ConnectUiState.ConnectingSuccess
                        }
                        it.activeDevice?.address?.let { it1 ->
                            saveDeviceMessage(
                                it1,
                                it.deviceType.name
                            )
                        }
                        getPower(DeviceType.ShoulderJoint.name)
                    } else {
                        if (hasShoulderConnected) {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.device_disconnect,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        } else {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.connect_device_error,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        }
                    }
                }
            }
        }
        scope.launch {
            kneeDeviceState.collectLatest {
                it.isDeviceConnected?.let { connected ->
                    waitScope?.cancel()
                    if (connected) {
                        if (!hasKneeConnected) {
                            hasKneeConnected = true
                            _uiState.value = ConnectUiState.ConnectingSuccess
                        }
                        it.activeDevice?.address?.let { it1 ->
                            saveDeviceMessage(
                                it1,
                                it.deviceType.name
                            )
                        }
                        getPower(DeviceType.KneeJoint.name)
                    } else {
                        if (hasKneeConnected) {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.device_disconnect,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        } else {
                            _uiState.value = ConnectUiState.ConnectingError(
                                visibility = true,
                                res = R.string.connect_device_error,
                                address = it.activeDevice?.address,
                                deviceType = it.deviceType.name
                            )
                        }
                    }
                }
            }
        }
        */
        scope.launch {
            bluetoothUtil.foundDevices.collect { devices ->
                _scanUiState.update {
                    it.copy(
                        foundDevices = devices
                    )
                }
            }
        }
        scope.launch {
            bluetoothUtil.isScanning.collect { isScanning ->
                _scanUiState.update { it.copy(isScanning = isScanning) }
            }
        }
    }

    private val incomingHandler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                BluetoothService.MSG_SEND_DATA -> {
                    val data: BLEClientState? = msg.data.getParcelable("data")
                    LogUtil.i("state$data")
                    data?.let {
                        scope.launch {
                            if (it.angle != null) {
                                scope.launch(Dispatchers.IO) {
                                    viewModel.angleDao.add(
                                        Angle(
                                            uuid = user.uuid,
                                            angle = it.angle,
                                            type = it.deviceType.name,
                                            timestamp = System.currentTimeMillis() / 1000
                                        )
                                    )
                                }
                            }
                            if (it.historyAngle != null) {
                                scope.launch(Dispatchers.IO) {
                                    viewModel.angleDao.add(
                                        Angle(
                                            uuid = user.uuid,
                                            angle = it.historyAngle.first,
                                            type = it.deviceType.name,
                                            timestamp = (it.historyAngle.second.div(1000))
                                        )
                                    )
                                }
                            }
                        }
                        scope.launch {
                            when (it.deviceType) {

                                DeviceType.ElbowJoint -> {
                                    _elbowDeviceState.value = it
                                }

                                DeviceType.KneeJoint -> {
                                    _kneeDeviceState.value = it
                                }

                                DeviceType.ShoulderJoint -> {
                                    _shoulderDeviceState.value = it
                                }

                                DeviceType.HipJoint -> {
                                    _hipDeviceState.value = it
                                }

                                else -> {}
                            }
                        }
                    }
                }

                else -> super.handleMessage(msg)
            }
        }
    }

    private val replyMessenger = Messenger(incomingHandler)

    private fun registerClient() {
        LogUtil.i("registerClient")
        val msg = Message.obtain(null, BluetoothService.MSG_REGISTER_CLIENT)
        msg.replyTo = replyMessenger
        try {
            messenger?.send(msg)
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    private fun unregisterClient() {
        val msg = Message.obtain(null, BluetoothService.MSG_UNREGISTER_CLIENT)
        msg.replyTo = replyMessenger
        try {
            messenger?.send(msg)
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
    }

    fun startService() {
        // 启动BluetoothService服务
        val intent = Intent(activity, BluetoothService::class.java)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            activity.startForegroundService(intent)
        } else {
            activity.startService(intent)
        }
    }

    private var neckDeviceJob: Job? = null
    private var backDeviceJob: Job? = null

    /**
     * 连接设备
     */
    fun connectGatt(device: BLEClientState) {
        startService()
        val intent = Intent(activity, BluetoothService::class.java)
        when (device.deviceType) {
            DeviceType.aiNeck -> {
                neckConnection = object : ServiceConnection {
                    override fun onServiceConnected(
                        className: ComponentName,
                        service: IBinder
                    ) {
                        val binder = service as BluetoothService.LocalBinder
                        bluetoothService = binder.getService()
                        neckDeviceJob?.cancel()
                        bluetoothService?.let {
                            it.setNeckActiveDevice(device)
                            it.connectNeckActiveDevice()
                        }

                        messenger = binder.getMessenger()  // 获取 Messenger
                        bound = true
                        neckDeviceJob = scope.launch {
                            bluetoothService?.singleDeviceState?.collectLatest {
                                LogUtil.i("blue data get ${it}")
                                _singleDeviceState.value = it
                                if (it.isDeviceConnected != null && it.isDeviceConnected) {
                                    bluetoothService?.notificationBuild?.setContentText(
                                        localeResources.getString(
                                            R.string.your_current_angle,
                                            "AiNeck",
                                            it.angle
                                        )
                                    )
                                } else {
                                    bluetoothService?.notificationBuild?.setContentText(
                                        localeResources.getString(
                                            R.string.device_disconnect,
                                            "AiNeck"
                                        )
                                    )
                                }
                                bluetoothService?.notificationBuild?.let { it1 ->
                                    NotificationUtils.sendNotification(
                                        NotificationConstants.BLUETOOTH_NOTIFY_ID,
                                        it1
                                    )
                                }
                                if (it.angle != null && it.angle != 0) {
                                    launch(Dispatchers.IO) {
                                        viewModel.angleDao.add(
                                            Angle(
                                                uuid = user.uuid,
                                                angle = it.angle,
                                                type = it.deviceType.name,
                                                timestamp = System.currentTimeMillis() / 1000
                                            )
                                        )
                                    }
                                }

                                if (it.historyAngle != null) {
                                    launch(Dispatchers.IO) {
                                        viewModel.angleDao.add(
                                            Angle(
                                                uuid = user.uuid,
                                                angle = it.historyAngle.first,
                                                type = it.deviceType.name,
                                                timestamp = (it.historyAngle.second.div(1000))
                                            )
                                        )
                                    }
                                }

                            }
                        }
//                            sendMessage(device)
                        registerClient()
                        LogUtil.i("service connected")
                    }

                    override fun onServiceDisconnected(className: ComponentName) {
                        neckDeviceJob?.cancel()
                        messenger = null
                        bound = false
                        unregisterClient()
                        LogUtil.i("service disconnected")
                    }
                }
                activity.bindService(intent, neckConnection!!, Context.BIND_AUTO_CREATE)
            }

            DeviceType.aiBack -> {
                backConnection = object : ServiceConnection {
                    override fun onServiceConnected(
                        className: ComponentName,
                        service: IBinder
                    ) {
                        val binder = service as BluetoothService.LocalBinder
                        bluetoothService = binder.getService()
                        backDeviceJob?.cancel()
                        bluetoothService?.let {
                            it.setBackActiveDevice(device)
                            it.connectBackActiveDevice()
                        }
                        messenger = binder.getMessenger()  // 获取 Messenger
                        backBound = true
                        backDeviceJob = scope.launch {
                            bluetoothService?.backDeviceState?.collectLatest {
                                LogUtil.i("blue data get ${it}")
                                _backDeviceState.value = it
                                if (it.angle != null && it.angle != 0) {
                                    launch(Dispatchers.IO) {
                                        viewModel.angleDao.add(
                                            Angle(
                                                uuid = user.uuid,
                                                angle = it.angle,
                                                type = it.deviceType.name,
                                                timestamp = System.currentTimeMillis() / 1000
                                            )
                                        )
                                    }
                                }

                                if (it.historyAngle != null) {
                                    launch(Dispatchers.IO) {
                                        viewModel.angleDao.add(
                                            Angle(
                                                uuid = user.uuid,
                                                angle = it.historyAngle.first,
                                                type = it.deviceType.name,
                                                timestamp = (it.historyAngle.second.div(1000))
                                            )
                                        )
                                    }
                                }
                                if (it.isDeviceConnected != null && it.isDeviceConnected) {
                                    bluetoothService?.notificationBuild?.setContentText(
                                        localeResources.getString(
                                            R.string.your_current_angle,
                                            "AiBack",
                                            it.angle
                                        )
                                    )
                                } else {
                                    bluetoothService?.notificationBuild?.setContentText(
                                        localeResources.getString(
                                            R.string.device_disconnect,
                                            "AiBack"
                                        )
                                    )
                                }
                                bluetoothService?.notificationBuild?.let { it1 ->
                                    NotificationUtils.sendNotification(
                                        NotificationConstants.BLUETOOTH_NOTIFY_ID,
                                        it1
                                    )
                                }
                            }
                        }
                        registerClient()
                        LogUtil.i("service connected")
                    }

                    override fun onServiceDisconnected(className: ComponentName) {
                        backDeviceJob?.cancel()
                        messenger = null
                        backBound = false
                        unregisterClient()
                        LogUtil.i("service disconnected")
                    }
                }

                activity.bindService(intent, backConnection!!, Context.BIND_AUTO_CREATE)
            }

            DeviceType.KneeJoint -> {
                if (kneeConnection != null) {
                    sendMessage(device)
                } else {
                    kneeConnection = object : ServiceConnection {
                        override fun onServiceConnected(
                            className: ComponentName,
                            service: IBinder
                        ) {
                            messenger = Messenger(service)
                            kneeBound = true
                            sendMessage(device)
                            registerClient()
                        }

                        override fun onServiceDisconnected(className: ComponentName) {
                            messenger = null
                            kneeBound = false
                            unregisterClient()
                        }
                    }
                }
                activity.bindService(intent, kneeConnection!!, Context.BIND_AUTO_CREATE)
            }

            DeviceType.ElbowJoint -> {
                if (elbowConnection != null) {
                    sendMessage(device)
                } else {
                    elbowConnection = object : ServiceConnection {
                        override fun onServiceConnected(
                            className: ComponentName,
                            service: IBinder
                        ) {
                            messenger = Messenger(service)
                            elbowBound = true
                            sendMessage(device)
                            registerClient()
                        }

                        override fun onServiceDisconnected(className: ComponentName) {
                            messenger = null
                            elbowBound = false
                            unregisterClient()
                        }
                    }
                }
                activity.bindService(intent, elbowConnection!!, Context.BIND_AUTO_CREATE)
            }

            DeviceType.ShoulderJoint -> {
                if (shoulderConnection != null) {
                    sendMessage(device)
                } else {
                    shoulderConnection = object : ServiceConnection {
                        override fun onServiceConnected(
                            className: ComponentName,
                            service: IBinder
                        ) {
                            messenger = Messenger(service)
                            shoulderBound = true
                            sendMessage(device)
                            registerClient()
                        }

                        override fun onServiceDisconnected(className: ComponentName) {
                            messenger = null
                            shoulderBound = false
                            unregisterClient()
                        }
                    }
                }
                activity.bindService(intent, shoulderConnection!!, Context.BIND_AUTO_CREATE)
            }

            DeviceType.HipJoint -> {
                if (hipConnection != null) {
                    sendMessage(device)
                } else {
                    hipConnection = object : ServiceConnection {
                        override fun onServiceConnected(
                            className: ComponentName,
                            service: IBinder
                        ) {
                            messenger = Messenger(service)
                            hipBound = true
                            sendMessage(device)
                            registerClient()
                        }

                        override fun onServiceDisconnected(className: ComponentName) {
                            messenger = null
                            hipBound = false
                            unregisterClient()
                        }
                    }
                }
                activity.bindService(intent, hipConnection!!, Context.BIND_AUTO_CREATE)
            }

            else -> {}

        }

    }

    private var waitScope: Job? = null
    fun connectRemoteGatt(mac: String, deviceType: String) {
        // Device does not support Bluetooth
        bluetoothUtil.checkBluetoothEnable {
            val device: BluetoothDevice? = bluetoothUtil.getRemoteDevice(mac)
            if (device == null) {
                DialogUtil.showToast(activity.getString(R.string.not_find_device), Gravity.CENTER)
                return@checkBluetoothEnable
            }
            // 开始连接
            _uiState.value = ConnectUiState.Connecting
            connectGatt(
                BLEClientState(
                    activeDevice = device,
                    deviceType = DeviceType.valueOf(deviceType)
                )
            )
            waitScope = viewModel.viewModelScope.launch(exceptionHandler) {
                delay(12000)
                if (_uiState.value == ConnectUiState.Connecting) {
                    _uiState.value = ConnectUiState.ConnectingError(
                        visibility = true,
                        res = R.string.connect_device_timeout,
                        address = mac,
                        deviceType = deviceType
                    )
                }

            }
        }
    }

//    private fun getDataFromDevice(mac: String, deviceType: String) {
//        when (deviceType) {
//            DeviceType.aiNeck.name -> {
//                scope.launch {
//                    val time = System.currentTimeMillis()
//                    var lt = time
//                    while (lt < time + 9000) {
//                        if (singleDeviceState.value.isDeviceConnected) {
//                            setDeviceType(deviceType)
//                            getPower(deviceType)
//                            if (aiNeckDeviceConfig.mac == mac) {
//                                saveDeviceMessage(mac, deviceType)
//                                DialogUtil.hideLoading()
//                                MainViewModel.pageIndex = 1
//                                popScreen(Screen.Main.route)
//                                viewModel.homeScreen.recalibrationDialogVisible = true
//                            } else {
//                                saveDeviceMessage(mac, deviceType)
//                                DialogUtil.hideLoading()
//                                startScreen(Screen.FormatDevice.route, true)
//                            }
//                            return@launch
//                        }
//                        lt = System.currentTimeMillis()
//                    }
//                    DialogUtil.hideLoading()
//                    DialogUtil.showToast(
//                        activity.getString(R.string.connect_device_error),
//                        Gravity.CENTER
//                    )
//                }
//            }
//
//            DeviceType.aiBack.name -> {
//                scope.launch {
//                    val time = System.currentTimeMillis()
//                    var lt = time
//                    while (lt < time + 9000) {
//                        if (backDeviceState.value.isDeviceConnected) {
//                            setDeviceType(deviceType)
//                            getPower(deviceType)
//                            if (aiBackDeviceConfig.mac == mac) {
//                                saveDeviceMessage(mac, deviceType)
//                                DialogUtil.hideLoading()
//                                MainViewModel.pageIndex = 1
//                                popScreen(Screen.Main.route)
//                                viewModel.homeScreen.recalibrationDialogVisible = true
//                            } else {
//                                saveDeviceMessage(mac, deviceType)
//                                DialogUtil.hideLoading()
//                                startScreen(Screen.FormatDevice.route, true)
//                            }
//                            return@launch
//                        }
//                        lt = System.currentTimeMillis()
//                    }
//                    DialogUtil.hideLoading()
//                    DialogUtil.showToast(
//                        activity.getString(R.string.connect_device_error),
//                        Gravity.CENTER
//                    )
//                }
//            }
//
//            DeviceType.KneeJoint.name -> {
//                scope.launch {
//                    val time = System.currentTimeMillis()
//                    var lt = time
//                    while (lt < time + 9000) {
//                        if (kneeDeviceState.value.isDeviceConnected) {
//                            setDeviceType(deviceType)
//                            getPower(deviceType)
//                            if (kneeJointDeviceConfig.mac == mac) {
//                                saveDeviceMessage(mac, deviceType)
//                                DialogUtil.hideLoading()
//                                MainViewModel.pageIndex = 1
//                                popScreen(Screen.Main.route)
//                                viewModel.homeScreen.recalibrationDialogVisible = true
//                            } else {
//                                saveDeviceMessage(mac, deviceType)
//                                DialogUtil.hideLoading()
//                                startScreen(Screen.FormatDevice.route, true)
//                            }
//                            return@launch
//                        }
//                        lt = System.currentTimeMillis()
//                    }
//                    DialogUtil.hideLoading()
//                    DialogUtil.showToast(
//                        activity.getString(R.string.connect_device_error),
//                        Gravity.CENTER
//                    )
//                }
//            }
//
//            DeviceType.ElbowJoint.name -> {
//                scope.launch {
//                    val time = System.currentTimeMillis()
//                    var lt = time
//                    while (lt < time + 9000) {
//                        if (elbowDeviceState.value.isDeviceConnected) {
//                            setDeviceType(deviceType)
//                            getPower(deviceType)
//                            if (elbowJointDeviceConfig.mac == mac) {
//                                saveDeviceMessage(mac, deviceType)
//
//                                DialogUtil.hideLoading()
//                                MainViewModel.pageIndex = 1
//                                popScreen(Screen.Main.route)
//                                viewModel.homeScreen.recalibrationDialogVisible = true
//                            } else {
//                                saveDeviceMessage(mac, deviceType)
//
//                                DialogUtil.hideLoading()
//                                startScreen(Screen.FormatDevice.route, true)
//                            }
//                            return@launch
//                        }
//                        lt = System.currentTimeMillis()
//                    }
//                    DialogUtil.hideLoading()
//                    DialogUtil.showToast(
//                        activity.getString(R.string.connect_device_error),
//                        Gravity.CENTER
//                    )
//                }
//            }
//
//            DeviceType.ShoulderJoint.name -> {
//                scope.launch {
//                    val time = System.currentTimeMillis()
//                    var lt = time
//                    while (lt < time + 9000) {
//                        if (shoulderDeviceState.value.isDeviceConnected) {
//                            setDeviceType(deviceType)
//                            getPower(deviceType)
//                            if (shoulderJointDeviceConfig.mac == mac) {
//                                saveDeviceMessage(mac, deviceType)
//
//                                DialogUtil.hideLoading()
//                                MainViewModel.pageIndex = 1
//                                popScreen(Screen.Main.route)
//                                viewModel.homeScreen.recalibrationDialogVisible = true
//                            } else {
//                                saveDeviceMessage(mac, deviceType)
//
//                                DialogUtil.hideLoading()
//                                startScreen(Screen.FormatDevice.route, true)
//                            }
//                            return@launch
//                        }
//                        lt = System.currentTimeMillis()
//                    }
//                    DialogUtil.hideLoading()
//                    DialogUtil.showToast(
//                        activity.getString(R.string.connect_device_error),
//                        Gravity.CENTER
//                    )
//                }
//            }
//
//            DeviceType.HipJoint.name -> {
//                scope.launch {
//                    val time = System.currentTimeMillis()
//                    var lt = time
//                    while (lt < time + 9000) {
//                        if (hipDeviceState.value.isDeviceConnected) {
//                            setDeviceType(deviceType)
//                            getPower(deviceType)
//                            if (hipJointDeviceConfig.mac == mac) {
//                                saveDeviceMessage(mac, deviceType)
//
//                                DialogUtil.hideLoading()
//                                MainViewModel.pageIndex = 1
//                                popScreen(Screen.Main.route)
//                                viewModel.homeScreen.recalibrationDialogVisible = true
//                            } else {
//                                saveDeviceMessage(mac, deviceType)
//
//                                DialogUtil.hideLoading()
//                                startScreen(Screen.FormatDevice.route, true)
//                            }
//                            return@launch
//                        }
//                        lt = System.currentTimeMillis()
//                    }
//                    DialogUtil.hideLoading()
//                    DialogUtil.showToast(
//                        activity.getString(R.string.connect_device_error),
//                        Gravity.CENTER
//                    )
//                }
//            }
//
//            else -> {}
//
//        }
//
//    }

    /**
     *  Set the device running model
     */
    private fun setDeviceType(deviceType: String) {
        sendCodeMessage(
            deviceType,
            "${deviceType}.setDeviceType.${deviceType}"
        )
    }

    /**
     * Save the device message
     */
    private fun saveDeviceMessage(mac: String, version: String, sn: String, deviceType: String) {
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                aiNeckDeviceConfig.let {
                    it.deviceType = DeviceType.valueOf(deviceType)
                    it.mac = mac
                    it.version = version
                    it.sn = sn
                    it.saveToLocal()
                }
                LogUtil.i("saveDeviceMessage${aiNeckDeviceConfig}")
            }

            DeviceType.aiBack.name -> {
                aiBackDeviceConfig.let {
                    it.deviceType = DeviceType.valueOf(deviceType)
                    it.mac = mac
                    it.saveToLocal()
                }
            }

            DeviceType.KneeJoint.name -> {
                kneeJointDeviceConfig.let {
                    it.deviceType = DeviceType.valueOf(deviceType)
                    it.mac = mac
                    it.saveToLocal()
                }
            }

            DeviceType.ElbowJoint.name -> {
                elbowJointDeviceConfig.let {
                    it.deviceType = DeviceType.valueOf(deviceType)
                    it.mac = mac
                    it.saveToLocal()
                }
            }

            DeviceType.ShoulderJoint.name -> {
                shoulderJointDeviceConfig.let {
                    it.deviceType = DeviceType.valueOf(deviceType)
                    it.mac = mac
                    it.saveToLocal()
                }
            }

            DeviceType.HipJoint.name -> {
                hipJointDeviceConfig.let {
                    it.deviceType = DeviceType.valueOf(deviceType)
                    it.mac = mac
                    it.saveToLocal()
                }
            }

            else -> {}
        }
    }

    private fun sendMessage(device: BLEClientState) {
        when (device.deviceType) {
            DeviceType.aiNeck -> {
                if (bound) {
                    LogUtil.i("BluetoothUtil", "put devices${device.deviceType}")
                    val msg = Message.obtain(null, BluetoothService.MSG_DEVICE).apply {
                        data = Bundle().apply {
                            putParcelable(BluetoothService.KEY_DEVICE, device)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.aiBack -> {
                if (backBound) {
                    LogUtil.i("BluetoothUtil", "put devices${device.deviceType}")
                    val msg = Message.obtain(null, BluetoothService.MSG_DEVICE).apply {
                        data = Bundle().apply {
                            putParcelable(BluetoothService.KEY_DEVICE, device)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.KneeJoint -> {
                if (kneeBound) {
                    LogUtil.i("BluetoothUtil", "put devices${device.deviceType}")
                    val msg = Message.obtain(null, BluetoothService.MSG_DEVICE).apply {
                        data = Bundle().apply {
                            putParcelable(BluetoothService.KEY_DEVICE, device)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.ElbowJoint -> {
                if (elbowBound) {
                    LogUtil.i("BluetoothUtil", "put devices${device.deviceType}")
                    val msg = Message.obtain(null, BluetoothService.MSG_DEVICE).apply {
                        data = Bundle().apply {
                            putParcelable(BluetoothService.KEY_DEVICE, device)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.ShoulderJoint -> {
                if (shoulderBound) {
                    LogUtil.i("BluetoothUtil", "put devices${device.deviceType}")
                    val msg = Message.obtain(null, BluetoothService.MSG_DEVICE).apply {
                        data = Bundle().apply {
                            putParcelable(BluetoothService.KEY_DEVICE, device)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.HipJoint -> {
                if (hipBound) {
                    LogUtil.i("BluetoothUtil", "put devices${device.deviceType}")
                    val msg = Message.obtain(null, BluetoothService.MSG_DEVICE).apply {
                        data = Bundle().apply {
                            putParcelable(BluetoothService.KEY_DEVICE, device)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            else -> {}
        }

    }

    private fun sendCodeMessage(deviceType: String, code: String) {
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                if (bound) {
                    LogUtil.i("send fun code")
                    val msg = Message.obtain(null, BluetoothService.MSG_CODE).apply {
                        data = Bundle().apply {
                            putString(BluetoothService.KEY_CODE, code)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.aiBack.name -> {
                if (backBound) {
                    LogUtil.i("send fun code")
                    val msg = Message.obtain(null, BluetoothService.MSG_CODE).apply {
                        data = Bundle().apply {
                            putString(BluetoothService.KEY_CODE, code)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.ElbowJoint.name -> {
                if (elbowBound) {
                    LogUtil.i("send fun code ${code}")
                    val msg = Message.obtain(null, BluetoothService.MSG_CODE).apply {
                        data = Bundle().apply {
                            putString(BluetoothService.KEY_CODE, code)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.KneeJoint.name -> {
                if (kneeBound) {
                    LogUtil.i("send fun code")
                    val msg = Message.obtain(null, BluetoothService.MSG_CODE).apply {
                        data = Bundle().apply {
                            putString(BluetoothService.KEY_CODE, code)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.HipJoint.name -> {
                if (hipBound) {
                    LogUtil.i("send fun code")
                    val msg = Message.obtain(null, BluetoothService.MSG_CODE).apply {
                        data = Bundle().apply {
                            putString(BluetoothService.KEY_CODE, code)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            DeviceType.ShoulderJoint.name -> {
                if (shoulderBound) {
                    LogUtil.i("send fun code")
                    val msg = Message.obtain(null, BluetoothService.MSG_CODE).apply {
                        data = Bundle().apply {
                            putString(BluetoothService.KEY_CODE, code)
                        }
                    }
                    try {
                        messenger?.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }

            else -> {}
        }
    }

    override suspend fun onHeadUpCalibrating(): Boolean {
        delay(1000)
//        sendCodeMessage(".onHeadUpCalibrating.1")
        delay(1000)
        return singleDeviceState.value.isDeviceConnected ?: false

//        delay(2000)
//        return bluetoothUtil.deviceRepository?.let {
//            it.startFirstCalibration()
//            true
//        }?:false
    }

    suspend fun onHeadUpCalibrating(deviceType: String): Boolean {
        delay(3000)
        sendCodeMessage(deviceType, "${deviceType}.onHeadUpCalibrating.1")
        // 不再在这里设置maxAngle，只在第二阶段校准完成时才设置
        delay(1000)
        return when (deviceType) {
            DeviceType.aiNeck.name -> singleDeviceState.value.isDeviceConnected ?: false
            DeviceType.aiBack.name -> backDeviceState.value.isDeviceConnected ?: false
            DeviceType.ElbowJoint.name -> elbowDeviceState.value.isDeviceConnected ?: false
            DeviceType.KneeJoint.name -> kneeDeviceState.value.isDeviceConnected ?: false
            DeviceType.ShoulderJoint.name -> shoulderDeviceState.value.isDeviceConnected ?: false
            DeviceType.HipJoint.name -> hipDeviceState.value.isDeviceConnected ?: false
            else -> false
        }
    }

    override suspend fun onBendNeckCalibrating(): Boolean {
        var num = 0
        calibrating = true
        while (true) {
            if (calibrating) {
                this.primaryAngle = singleDeviceState.value.angle ?: 0
                this.maxAngle = singleDeviceState.value.maxAngle ?: 0
            }
            if (primaryAngle.absoluteValue >= 15) {
                isSecondCalibratedSuccess = true
                if (num++ >= 20) {
                    break
                }
            } else {
                isSecondCalibratedSuccess = false
                num = 0
            }
            delay(100)
        }
//        sendCodeMessage("onBendNeckCalibrating.2")
        delay(500)
        aiNeckDeviceConfig.vibrationAngle = maxAngle.absoluteValue
        calibrating = false
        setVibration(aiNeckDeviceConfig.isVibration)
        return singleDeviceState.value.isDeviceConnected ?: false
//        return bluetoothUtil.deviceRepository?.let {
//            it.startSecondCalibration()
//            delay(500)
//            deviceConfig.vibrationAngle = maxAngle.absoluteValue
//            calibrating = false
//            setVibration(deviceConfig.isVibration)
//            true
//        }?:false
    }

    suspend fun onBendNeckCalibrating(deviceType: String): Boolean {
        var num = 0
        calibrating = true
        while (true) {
            if (calibrating) {
                when (deviceType) {
                    DeviceType.aiNeck.name -> {
                        this.primaryAngle = singleDeviceState.value.angle ?: 0
                        this.maxAngle = singleDeviceState.value.maxAngle ?: 0
                    }

                    DeviceType.aiBack.name -> {
                        this.primaryAngle = backDeviceState.value.angle ?: 0
                        this.maxAngle = backDeviceState.value.maxAngle ?: 0
                    }

                    DeviceType.ElbowJoint.name -> {
                        this.primaryAngle = elbowDeviceState.value.angle ?: 0
                        this.maxAngle = elbowDeviceState.value.maxAngle ?: 0
                    }

                    DeviceType.KneeJoint.name -> {
                        this.primaryAngle = kneeDeviceState.value.angle ?: 0
                        this.maxAngle = kneeDeviceState.value.maxAngle ?: 0
                    }

                    DeviceType.ShoulderJoint.name -> {
                        this.primaryAngle = shoulderDeviceState.value.angle ?: 0
                        this.maxAngle = shoulderDeviceState.value.maxAngle ?: 0
                    }

                    DeviceType.HipJoint.name -> {
                        this.primaryAngle = hipDeviceState.value.angle ?: 0
                        this.maxAngle = hipDeviceState.value.maxAngle ?: 0
                    }

                    else -> {}
                }
            }
            if (primaryAngle.absoluteValue >= 5) {
                isSecondCalibratedSuccess = true
                if (num++ >= 20) {
                    break
                }
            } else {
                isSecondCalibratedSuccess = false
                num = 0
            }
            delay(100)
        }
        sendCodeMessage(deviceType, "${deviceType}.onBendNeckCalibrating.2")
        delay(1000)
        
        // 保存校准角度值（仅在校准完成后）
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                aiNeckDeviceConfig.saveCalibrationAngle(maxAngle.absoluteValue)
                aiNeckDeviceConfig.vibrationAngle = maxAngle.absoluteValue
            }
            DeviceType.aiBack.name -> {
                aiBackDeviceConfig.saveCalibrationAngle(maxAngle.absoluteValue)
                aiBackDeviceConfig.vibrationAngle = maxAngle.absoluteValue
            }
            DeviceType.ElbowJoint.name -> {
                elbowJointDeviceConfig.saveCalibrationAngle(maxAngle.absoluteValue)
                elbowJointDeviceConfig.vibrationAngle = maxAngle.absoluteValue
            }
            DeviceType.KneeJoint.name -> {
                kneeJointDeviceConfig.saveCalibrationAngle(maxAngle.absoluteValue)
                kneeJointDeviceConfig.vibrationAngle = maxAngle.absoluteValue
            }
            DeviceType.ShoulderJoint.name -> {
                shoulderJointDeviceConfig.saveCalibrationAngle(maxAngle.absoluteValue)
                shoulderJointDeviceConfig.vibrationAngle = maxAngle.absoluteValue
            }
            DeviceType.HipJoint.name -> {
                hipJointDeviceConfig.saveCalibrationAngle(maxAngle.absoluteValue)
                hipJointDeviceConfig.vibrationAngle = maxAngle.absoluteValue
            }
        }
        
        calibrating = false
        setVibration(aiNeckDeviceConfig.isVibration)
        return when (deviceType) {
            DeviceType.aiNeck.name -> singleDeviceState.value.isDeviceConnected ?: false
            DeviceType.aiBack.name -> backDeviceState.value.isDeviceConnected ?: false
            DeviceType.ElbowJoint.name -> elbowDeviceState.value.isDeviceConnected ?: false
            DeviceType.KneeJoint.name -> kneeDeviceState.value.isDeviceConnected ?: false
            DeviceType.ShoulderJoint.name -> shoulderDeviceState.value.isDeviceConnected ?: false
            DeviceType.HipJoint.name -> hipDeviceState.value.isDeviceConnected ?: false
            else -> false
        }
    }

    //    override fun setVibrationAngle(angle: Int) {
//        bluetoothUtil.deviceRepository?.let {
//            it.setVibrationAngle(angle)
//            it.setVibration(deviceConfig.isVibration)
//            deviceConfig.vibrationAngle = angle
//            deviceConfig.saveToLocal()
//        }
//    }

    //    override fun setVibrationFrequency(frequency: Int) {
//        bluetoothUtil.deviceRepository?.let {
//            it.setVibrationFrequency(frequency)
//            it.setVibration(deviceConfig.isVibration)
//            deviceConfig.vibrationFrequency = frequency
//            deviceConfig.saveToLocal()
//        }
//    }
    @OptIn(ExperimentalStdlibApi::class)
    override fun setVibrationAngle(angle: Int) {
//        sendCodeMessage(
//            DeviceType.aiNeck.name, "setVibrationAngle.${angle.toHexString()}"
//        )
        setVibration(aiNeckDeviceConfig.isVibration)
        aiNeckDeviceConfig.vibrationAngle = angle
        aiNeckDeviceConfig.saveToLocal()

    }

    @OptIn(ExperimentalStdlibApi::class)
    fun setVibrationAngle(deviceType: String, angle: Int) {
        sendCodeMessage(deviceType, "${deviceType}.setVibrationAngle.${angle.toHexString()}")
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                setVibration(deviceType, aiNeckDeviceConfig.isVibration)
                aiNeckDeviceConfig.vibrationAngle = angle
                aiNeckDeviceConfig.saveToLocal()
            }

            DeviceType.aiBack.name -> {
                setVibration(deviceType, aiBackDeviceConfig.isVibration)
                aiBackDeviceConfig.vibrationAngle = angle
                aiBackDeviceConfig.saveToLocal()
            }

            DeviceType.ElbowJoint.name -> {
                setVibration(deviceType, elbowJointDeviceConfig.isVibration)
                elbowJointDeviceConfig.vibrationAngle = angle
                elbowJointDeviceConfig.saveToLocal()
            }

            DeviceType.KneeJoint.name -> {
                setVibration(deviceType, kneeJointDeviceConfig.isVibration)
                kneeJointDeviceConfig.vibrationAngle = angle
                kneeJointDeviceConfig.saveToLocal()
            }

            DeviceType.ShoulderJoint.name -> {
                setVibration(deviceType, shoulderJointDeviceConfig.isVibration)
                shoulderJointDeviceConfig.vibrationAngle = angle
                shoulderJointDeviceConfig.saveToLocal()
            }

            DeviceType.HipJoint.name -> {
                setVibration(deviceType, hipJointDeviceConfig.isVibration)
                hipJointDeviceConfig.vibrationAngle = angle
                hipJointDeviceConfig.saveToLocal()
            }

            else -> {}
        }
    }

    override fun setVibrationFrequency(frequency: Int) {
//        sendCodeMessage(DeviceType.aiNeck.name, "setVibrationFrequency.${frequency.toHexString()}")
        setVibration(aiNeckDeviceConfig.isVibration)
        aiNeckDeviceConfig.vibrationFrequency = frequency
        aiNeckDeviceConfig.saveToLocal()

    }

    @OptIn(ExperimentalStdlibApi::class)
    fun setVibrationFrequency(deviceType: String, frequency: Int) {
        sendCodeMessage(
            deviceType,
            "${deviceType}.setVibrationFrequency.${frequency.toHexString()}"
        )
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                setVibration(deviceType, aiNeckDeviceConfig.isVibration)
                aiNeckDeviceConfig.vibrationFrequency = frequency
                aiNeckDeviceConfig.saveToLocal()
            }

            DeviceType.aiBack.name -> {
                setVibration(deviceType, aiBackDeviceConfig.isVibration)
                aiBackDeviceConfig.vibrationFrequency = frequency
                aiBackDeviceConfig.saveToLocal()
            }

            DeviceType.ElbowJoint.name -> {
                setVibration(deviceType, elbowJointDeviceConfig.isVibration)
                elbowJointDeviceConfig.vibrationFrequency = frequency
                elbowJointDeviceConfig.saveToLocal()
            }

            DeviceType.KneeJoint.name -> {
                setVibration(deviceType, kneeJointDeviceConfig.isVibration)
                kneeJointDeviceConfig.vibrationFrequency = frequency
                kneeJointDeviceConfig.saveToLocal()
            }

            DeviceType.ShoulderJoint.name -> {
                setVibration(deviceType, shoulderJointDeviceConfig.isVibration)
                shoulderJointDeviceConfig.vibrationFrequency = frequency
                shoulderJointDeviceConfig.saveToLocal()
            }

            DeviceType.HipJoint.name -> {
                setVibration(deviceType, hipJointDeviceConfig.isVibration)
                hipJointDeviceConfig.vibrationFrequency = frequency
                hipJointDeviceConfig.saveToLocal()
            }

            else -> {}
        }
    }

    override fun setFindDevice(enabled: Boolean) {
//        vibrate(enabled)
//        bluetoothUtil.deviceRepository?.let {
//            vibrate(it, enabled)
//            deviceConfig.findDevice = enabled
//        }
    }


    override fun setVibration(enabled: Boolean) {
//        sendCodeMessage(
//            "setVibration.$enabled"
//        )
        aiNeckDeviceConfig.isVibration = enabled
        aiNeckDeviceConfig.saveToLocal()
//        bluetoothUtil.deviceRepository?.let {
//            it.setVibration(enabled)
//            deviceConfig.isVibration = enabled
//            deviceConfig.saveToLocal()
//        }
    }

    fun setVibration(deviceType: String, enabled: Boolean) {
        sendCodeMessage(deviceType, "${deviceType}.setVibration.$enabled")
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                aiNeckDeviceConfig.isVibration = enabled
                aiNeckDeviceConfig.saveToLocal()
            }

            DeviceType.aiBack.name -> {
                aiBackDeviceConfig.isVibration = enabled
                aiBackDeviceConfig.saveToLocal()
            }

            DeviceType.ElbowJoint.name -> {
                elbowJointDeviceConfig.isVibration = enabled
                elbowJointDeviceConfig.saveToLocal()
            }

            DeviceType.KneeJoint.name -> {
                kneeJointDeviceConfig.isVibration = enabled
                kneeJointDeviceConfig.saveToLocal()
            }

            DeviceType.ShoulderJoint.name -> {
                shoulderJointDeviceConfig.isVibration = enabled
                shoulderJointDeviceConfig.saveToLocal()
            }

            DeviceType.HipJoint.name -> {
                hipJointDeviceConfig.isVibration = enabled
                hipJointDeviceConfig.saveToLocal()
            }

            else -> {}
        }
//        bluetoothUtil.deviceRepository?.let {
//            it.setVibration(enabled)
//            deviceConfig.isVibration = enabled
//            deviceConfig.saveToLocal()
//        }
    }

    override fun onConnectDeviceClick(deviceType: String) {
        bluetoothUtil.checkBluetoothEnable {
            // 显示 MatchingDeviceSheet
            currentMatchingDeviceType = deviceType
            showMatchingDeviceSheet = true
        }
    }
    
    // 连接设备的回调方法
    fun onDeviceConnected(mac: String) {
        showMatchingDeviceSheet = false
        
        // 连接设备
        val device = bluetoothUtil.getRemoteDevice(mac)
        if (device != null) {
            onConnectedDevice(device, currentMatchingDeviceType)
        }
    }


    @SuppressLint("MissingPermission")
    fun onDisconnectDeviceClick(deviceType: String) {
//        powerScope?.cancel()
        vibrationScore?.cancel()
//        sendCodeMessage(deviceType, "${deviceType}.onDisconnectDeviceClick.${deviceType}")
        when (deviceType) {
            DeviceType.aiBack.name -> {
                backConnection?.let {
                    scope.launch {
                        bluetoothService?.backConnection?.value?.disconnect()
                        bluetoothService?.backConnection?.update { null }
                    }
                }
            }

            else -> {
                neckConnection?.let {
                    scope.launch {
                        bluetoothService?.singleConnection?.value?.disconnect()
                        bluetoothService?.singleConnection?.update { null }
                    }

                }
            }
        }

    }

    fun checkPermissions(
        onAllGranted: () -> Unit,
        notAllGranted: () -> Unit
    ) {
        bluetoothUtil.checkPermissions(
            onAllGranted = {
                onAllGranted()
            },
            notAllGranted = {
                notAllGranted()
            }
        )
    }


    override fun onScanDevice(
        onScanResult: (device: BluetoothDevice) -> Unit,
        onTimeOut: () -> Unit
    ) {
        bluetoothUtil.scanDevice(
            onScanResult = onScanResult,
            onTimeOut = onTimeOut
        )
    }

    fun onScanDevice() {
        bluetoothUtil.scanDevice()
    }

    override fun onConnectedDevice(
        device: BluetoothDevice,
        deviceType: String
    ) {

        DialogUtil.showLoading()
        val device1: BluetoothDevice? = bluetoothUtil.getRemoteDevice(device.address)
        val deviceState = BLEClientState(
            deviceType = DeviceType.valueOf(deviceType),
            activeDevice = device1
        )
        LogUtil.i("deviceState:${deviceState}")
        connectGatt(deviceState)
//        getDataFromDevice(device.address, deviceType)
        // 100ms更新一次界面
//        var lastUpdateTime = 0L
//        var lastSaveTime = 0L
//        bluetoothUtil.connectGatt(device) { oldStatus, newStatus ->
//            DialogUtil.hideLoading()
//            if (oldStatus == BluetoothGatt.GATT_SUCCESS) {
//                if (newStatus == BluetoothProfile.STATE_CONNECTED) {
//                    viewModel.reportScreen.isHasConnected = true
//                    userSP.edit().putBoolean(SPConstant.IS_HAS_CONNECTED, true).apply()
//                    deviceConfig.deviceType = DeviceType.valueOf(deviceType)
//                    viewModel.homeScreen.currentDeviceType = DeviceType.valueOf(deviceType)
//                    bluetoothUtil.deviceRepository?.let { it ->
//                        it.getVersion { version ->
//                            LogUtil.d("mytag", "getVersion: $version")
//                            deviceConfig.version = version
//                            if (version == "6.0.6") {
//                                it.setDeviceType(deviceConfig.deviceType)
//                                it.setCurrentTime()
//                                it.getLocalAngle { angle, timeStamp ->
//                                    angleDao.add(
//                                        Angle(
//                                            0,
//                                            user.uuid,
//                                            angle,
//                                            DeviceConfig.getHistoryDeviceType().name,
//                                            timeStamp / 1000
//                                        )
//                                    )
//                                }
//                                getPower(it)
//                                it.getSN {
//                                    LogUtil.d("mytag", "getSN: $it")
//                                    deviceConfig.s_n = it
//                                    if (userSP.getBoolean(deviceConfig.s_n, false)) {
//                                        MainViewModel.pageIndex = 1
//                                        popScreen(Screen.Main.route)
//                                        viewModel.homeScreen.recalibrationDialogVisible = true
//                                    } else {
//                                        startScreen(Screen.FormatDevice.route, true)
//                                    }
//                                }
//                                it.setOnAngleChangedListener { primaryAngle, angle, maxAngle, currentTime ->
//                                    // 若在校准中
//                                    if (calibrating) {
//                                        this.primaryAngle = primaryAngle
//                                        this.maxAngle = maxAngle.toInt()
//                                    }
//
//                                    // 距离上一次数据更新超过100ms， 符合角度刷新更新要求，更新角度
//                                    if (currentTime - lastUpdateTime > 100) {
//                                        lastUpdateTime = currentTime
//                                        animateToAngle(angle)
//                                    }
//                                    // 间隔10s 将数据保存到数据库
//                                    if (currentTime - lastSaveTime >= 10000) {
//                                        lastSaveTime = currentTime
//                                        angleDao.add(
//                                            Angle(
//                                                0,
//                                                user.uuid,
//                                                angle,
//                                                deviceConfig.deviceType.name,
//                                                currentTime / 1000
//                                            )
//                                        )
//                                    }
//                                }
//                            } else {
//                                updateDialogVisible = true
//                            }
//                        }
//                    }
//
//                } else {
//                    // 断开连接取消角度动画协程
//                    animateScope?.cancel()
//                    // 断开连接取消电量获取协程
//                    powerScope?.cancel()
//                    vibrationScrop?.cancel()
//                    bluetoothUtil.close()
//                    deviceConfig.deviceType = DeviceType.None
//                    deviceConfig.angle = 0
////                    DialogUtil.showToast("蓝牙连接失败，请重新连接")
//                }
//            } else {
//                // 断开连接取消角度动画协程
//                animateScope?.cancel()
//                // 断开连接取消电量获取协程
//                powerScope?.cancel()
//                vibrationScrop?.cancel()
//                bluetoothUtil.close()
//                deviceConfig.deviceType = DeviceType.None
//                deviceConfig.angle = 0
////                DialogUtil.showToast("蓝牙断开连接")
//
//            }
//
//        }
    }

    override fun initSensor(context: Context) {
        gravitySensor = GravitySensor(context = context)
    }

    override fun gravityStartListening() {
        gravitySensor.startListening()
        gravitySensor.setOnSensorValuesChangedListener { value ->
            verticalGravity.update { value[1] }
        }
    }

    override fun gravityStopListening() {
        gravitySensor.stopListening()
        verticalGravity.update { 0f }
    }

// 刷新角度协程
//    private var animateScope: Job? = null
//    private fun animateToAngle(newAngle: Int) {
//
//        animateScope?.cancel()
//        animateScope = viewModel.viewModelScope.launch {
//            while (deviceConfig.angle > newAngle) {
//                deviceConfig.angle -= 1
//                delay(30L)
//            }
//            while (deviceConfig.angle < newAngle) {
//                deviceConfig.angle += 1
//                delay(30L)
//            }
//        }
//    }

    //使用协程每2分钟获取一次电量
    /**
    private var powerScope: Job? = null
    private fun getPower(deviceType: String) {
        if (powerScope == null) {
            powerScope = viewModel.viewModelScope.launch(exceptionHandler) {
                while (true) {
                    sendCodeMessage(deviceType, "${deviceType}.power.1")
                    delay(120000)
                }
            }
        }
    }
    */

    //    private fun getPower(deviceRepository: DeviceRepository) {
//        powerScope = viewModel.viewModelScope.launch {
//            while (true) {
//                deviceRepository.getPower {
//                    deviceConfig.power = it
//                }
//                delay(120000)
//            }
//        }
//    }
    val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        LogUtil.e("CoroutineException Caught $exception")
    }
    private var vibrationScore: Job? = null
    fun vibrate(deviceType: String, enabled: Boolean) {
        if (enabled) {
            vibrationScore = viewModel.viewModelScope.launch(exceptionHandler) {
                while (true) {
                    sendCodeMessage(deviceType, "${deviceType}.vibrate.1")
                    when (deviceType) {
                        DeviceType.aiNeck.name -> {
                            delay(aiNeckDeviceConfig.vibrationFrequency * 1000L)
                        }

                        DeviceType.aiBack.name -> {
                            delay(aiBackDeviceConfig.vibrationFrequency * 1000L)
                        }

                        DeviceType.KneeJoint.name -> {
                            delay(kneeJointDeviceConfig.vibrationFrequency * 1000L)
                        }

                        DeviceType.ElbowJoint.name -> {
                            delay(elbowJointDeviceConfig.vibrationFrequency * 1000L)
                        }

                        DeviceType.ShoulderJoint.name -> {
                            delay(shoulderJointDeviceConfig.vibrationFrequency * 1000L)
                        }

                        DeviceType.HipJoint.name -> {
                            delay(hipJointDeviceConfig.vibrationFrequency * 1000L)
                        }

                        else -> {}
                    }
                }
            }
        } else {
            vibrationScore?.cancel()
        }
    }

    //    private fun vibrate(deviceRepository: DeviceRepository, enabled: Boolean) {
//        if (enabled) {
//            vibrationScrop = viewModel.viewModelScope.launch {
//                while (true) {
//                    deviceRepository.vibrate()
//                    delay(deviceConfig.vibrationFrequency * 1000L)
//                }
//            }
//        } else {
//            vibrationScrop?.cancel()
//        }
//    }
    fun setKneeExerciseOne() {
        val value = _kneeExercise.value.first + 1
        _kneeExercise.value = Pair(value, _kneeExercise.value.second)
        LogUtil.i("knee exercise value:${value}")
    }

    fun setKneeExerciseTwo() {
        val value = _kneeExercise.value.second + 1
        _kneeExercise.value = Pair(_kneeExercise.value.first, value)
        LogUtil.i("knee exercise value:${value}")
    }

    fun resetUiState(deviceType: String) {
        _uiState.value = ConnectUiState.None
    }

    fun clearConnectedData(deviceType: String) {
        when (deviceType) {
            DeviceType.aiNeck.name -> {
                hasSingleConnected = false
            }

            DeviceType.aiBack.name -> {
                hasBackConnected = false
            }

            DeviceType.ElbowJoint.name -> {
                hasElbowConnected = false
            }

            DeviceType.KneeJoint.name -> {
                hasKneeConnected = false
            }

            DeviceType.ShoulderJoint.name -> {
                hasShoulderConnected = false
            }

            DeviceType.HipJoint.name -> {
                hasHipConnected = false
            }

            else -> {}
        }
        LogUtil.i("clearConnectedData:${deviceType} isConnected:${hasSingleConnected}")
    }

    fun recordKneeMaxAngle() {
        kneeAngleMax = kneeDeviceState.value.angle ?: 0
    }

    fun recordKneeMinAngle() {
        kneeAngleMin = kneeDeviceState.value.angle ?: 0
    }

    @SuppressLint("MissingPermission")
    fun stopScanDevice() {
        bluetoothUtil.stopScanDevice()
    }
}

/**
 * 登录界面状态
 */
sealed interface ConnectUiState {
    data object ConnectingSuccess : ConnectUiState

    data class ConnectingError(
        val visibility: Boolean = false,
        val res: Int? = null,
        val address: String? = null,
        val deviceType: String? = null,
        val throwable: Throwable? = null,
    ) : ConnectUiState

    data object Connecting : ConnectUiState
    data object None : ConnectUiState
}