package org.aihealth.ineck.viewmodel.dao

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import org.aihealth.ineck.model.AccelerometerParam
import org.aihealth.ineck.model.Queue
import org.aihealth.ineck.sensor.AccelerometerSensor
import org.aihealth.ineck.sensor.GravitySensor
import org.aihealth.ineck.viewmodel.DetectedResult
import java.util.Calendar

abstract class VCGuideEvent : ViewModel(){

    /* 重力传感器 */
    lateinit var gravitySensor: GravitySensor
    /** 加速度传感器 */
    lateinit var accelerometerSensor: AccelerometerSensor

    /** 重力矢量状态 */
    var verticalGravity = MutableStateFlow(0f)
    /* 加速度参数队列 */
    var accelerometerQueue = mutableStateOf(Queue<AccelerometerParam>(30))

    /* 当前引导检测页执行状态 */
    val currentVCGuideProcessState: MutableStateFlow<VCGuideProcessState> =
        MutableStateFlow(VCGuideProcessState.PreviousGuidePage)

    /** 检测中页 倒计时变量 */
    val countTime: MutableStateFlow<Int> = MutableStateFlow(5)
    /** 检测过程中 测量开始时间戳 - 从有效数据开始计算 */
    var startTimestampForEffectDetecting = MutableStateFlow( 0L )
    /** 检测过程中面部角度队列 */
    var angleWithTimestampQueueForDetecting = mutableStateOf(Queue<AngleWithTimestamp>())
    /** 垂直效果完成状态 */
    val finishedVerticalDetectedState: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /** 校准前各状态允许 */
    /* 设备静止状态 */
    val isDeviceStatic: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /** 能否捕捉到脸的状态 */
    val isCapturedFace: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /** 直立平视状态 */
    val isSightLineHorizontal: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /** 是否捕捉到姿势 */
    val capturedPose: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /** 直立姿态 */
    val verticalPose: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /** 综合状态 */
    val totalState: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /* 播报静音状态 */
    val isMuteState: MutableStateFlow<Boolean> = MutableStateFlow(true)

    /* 面部检测欧拉角数据 */
    val headEulerAngleX: MutableStateFlow<Float> = MutableStateFlow(0f)
    val headEulerAngleY: MutableStateFlow<Float> = MutableStateFlow(0f)
    val headEulerAngleZ: MutableStateFlow<Float> = MutableStateFlow(0f)

    /** 背部倾斜角度 */
    val bodyTiltAngle: MutableStateFlow<Float> = MutableStateFlow(0f)

    /** 引导检测结果 */
    val detectedResult: MutableStateFlow<DetectedResult> = MutableStateFlow(DetectedResult.None)

    /**
     *  变更当前引导检测页状态
     */
    abstract fun changeGuideDetectProcessState(newState: VCGuideProcessState)

    /** 预检测页向检测中页状态的过渡方法，倒计时开始 */
    abstract suspend fun startDetectingTimer()

    /**
     *  重力传感器开始监听
     */
    abstract fun gravityStartListening()

    /**
     *  重力传感器停止监听
     */
    abstract fun gravityStopListening()

    /**
     *  加速度传感器开始监听
     */
    abstract fun accelerometerStartListener()

    /**
     *  加速度传感器停止监听
     */
    abstract fun accelerometerStopListener()

    /**
     *  验证设备处于垂直状态：
     *  [calibrationTime]ms取一重力矢量的值，若([duration] * [calibrationTime])ms内该矢量值均处于垂直要求范围之内，
     *  则变更设备垂直步骤状态为完成
     *  @param  calibrationTime 验证次数
     *  @param  duration    验证间隔时长
     */
    abstract suspend fun verifyVerticalDetection(calibrationTime: Int, duration: Long)

    /**
     *  触发检测中状态倒计时
     */
    abstract fun triggerDetectingCountDown()

    /**
     *  填写检测结果
     */
    abstract fun writeDetectedResult(result: DetectedResult)

    /**
     *  变更当前静音状态
     */
    abstract fun changeCurrentMuteState(newState: Boolean)

    /**
     *  清空所有状态
     */
    abstract fun clearAllState()
}

/**
 *  引导检测过程状态
 */
sealed class VCGuideProcessState {
    /* 引导前提示页面 */
    data object PreviousGuidePage: VCGuideProcessState() {
        private fun toPageNumber(): Int {
            return 0
        }
    }
    /* 检测前预校准页 */
    data object PreviousDetectPage: VCGuideProcessState() {
        private fun toPageNumber(): Int {
            return 1
        }
    }
    /* 检测运行中页面 */
    data object DetectingPage: VCGuideProcessState() {
        private fun toPageNumber(): Int {
            return 2
        }
    }

    fun toStateType(pageNumber: Int) : VCGuideProcessState{
        return when(pageNumber) {
            0 -> PreviousGuidePage
            1 -> PreviousDetectPage
            2 -> DetectingPage
            else -> PreviousGuidePage
        }
    }
}

/**
 *  求整形列表中最大的差值
 *  @return 列表中最大的差值
 */
fun List<Float>.maxDiff(): Float {
    var min = this[0]
    var max = this[0]
    for (i in 1 until this.size) {
        val num = this[i]
        min = minOf(min, num)
        max = maxOf(max, num)
    }
    return max - min
}

/**
 *  监测角度与其获取的时间戳
 *  @param  angle   监测角度
 *  @param  timestamp   获取角度时间戳
 */
data class AngleWithTimestamp(
    val angle: Float,
    val timestamp: Long
)

/**
 *  携带时间戳的角度队列， 对比入队
 *  @param  newAngle    新角度
 */
internal fun Queue<AngleWithTimestamp>.compareEnqueue(
    newAngle: AngleWithTimestamp,
) {
    val nowTimeStamp = newAngle.timestamp
    this.enqueue(newAngle)
    while (nowTimeStamp - this.elements[0].timestamp > 5000L) {
        this.elements.removeAt(0)
    }
}

/**
 *  判断校准角度是否符合偏差
 *  @param  range   指定偏差范围
 */
internal fun Queue<AngleWithTimestamp>.verifyDiff(range : Float) : Boolean {
    return this.elements.map { it.angle }.maxDiff() <= range
}

/**
 *  获得现在时间戳
 */
internal fun getTimestampNow() : Long {
    return Calendar.getInstance().timeInMillis
}

/**
 *  监测角度与其获取的时间戳
 *  @param  angleX   监测角度
 *  @param  angleY   监测角度
 *  @param  angleZ   监测角度
 *  @param  timestamp   获取角度时间戳
 */
data class TripleAngleWithTimestamp(
    val angleX: Float,
    val angleY: Float,
    val angleZ: Float,
    val timestamp: Long
)

/**
 *  携带时间戳的角度队列， 对比入队
 *  @param  newAngle    新角度
 */
internal fun Queue<TripleAngleWithTimestamp>.compareEnqueue(
    newAngle: TripleAngleWithTimestamp,
) {
    val nowTimeStamp = newAngle.timestamp
    this.enqueue(newAngle)
    while (nowTimeStamp - this.elements[0].timestamp > 5000L) {
        this.elements.removeAt(0)
    }
}

/**
 *  判断校准角度是否符合偏差
 *  @param  range   指定偏差范围
 */
internal fun Queue<TripleAngleWithTimestamp>.verifyTripleDiff(range: Float): Boolean {
    return this.elements.map {
        it.angleX
    }.maxDiff() < range
}