package org.aihealth.ineck.viewmodel

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import org.aihealth.ineck.model.angles.BackNeuralHistoryItem
import org.aihealth.ineck.model.angles.BackPainHistoryItem
import org.aihealth.ineck.model.angles.NeckNeuralHistoryItem
import org.aihealth.ineck.model.angles.NeckPainHistoryItem
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.copy
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.year
import org.aihealth.ineck.view.custom.PainTypeState
import org.aihealth.ineck.viewmodel.dao.HistoryPainEvent
import java.util.Calendar

/**
 *  疼痛 ViewModel
 */
class HistoryPainViewModel: BaseViewModel(), HistoryPainEvent {

    /* 颈部疼痛数据列表 */
    var neckPainHistoryList = MutableStateFlow<List<NeckPainHistoryItem>>(emptyList())

    /* 颈部神经数据列表 */
    var neckNeuralHistoryList = MutableStateFlow<List<NeckNeuralHistoryItem>>(emptyList())

    /* 背部疼痛数据列表 */
    var backPainHistoryList = MutableStateFlow<List<BackPainHistoryItem>>(emptyList())

    /* 颈部疼痛数据列表 */
    var backNeuralHistoryList = MutableStateFlow<List<BackNeuralHistoryItem>>(emptyList())

    /**
     *  请求疼痛历史记录
     *  @param  painType 疼痛类型
     */
    override fun loadPainHistory(painType: PainTypeState) {
        /* 设定查询范围为一个月内 */
        val endDate = getDefaultDate()
        val end = endDate.clone() as Calendar
        end.add(Calendar.DATE, 1)
        val startDate = endDate.copy().apply { add(Calendar.MONTH, -1) }
        val startTime = "${startDate.year}-${startDate.month + 1}-${startDate.date}T00:00:00.000000000-00:00"
        val endTime = "${end.year}-${end.month + 1}-${end.date}T00:00:00.000000000-00:00"
        when (painType) {
            PainTypeState.NECK_PAIN -> {
                apiService.getNeckPainHistory(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    try {
                        neckPainHistoryList.update { response!!.data.reversed() }
                    } catch (e: Throwable) {
                        LogUtil.d(
                            "_chen",
                            "请求颈部疼痛历史数据, 响应发生错误!\n${e.printStackTrace()}"
                        )
                    }
                }
            }
            PainTypeState.BACK_PAIN -> {
                apiService.getBackPainHistory(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    try {
                        backPainHistoryList.update { response!!.data.reversed() }
                    } catch (e: Throwable) {
                        LogUtil.d(
                            "_chen",
                            "请求颈部疼痛历史数据, 响应发生错误!\n${e.printStackTrace()}"
                        )
                    }
                }
            }

            PainTypeState.NECK_NEURAL -> {
                apiService.getNeckNeuralHistory(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    try {
                        neckNeuralHistoryList.update { response!!.data.reversed() }
                    } catch (e: Throwable) {
                        LogUtil.d(
                            "_chen",
                            "请求颈部疼痛历史数据, 响应发生错误!\n${e.printStackTrace()}"
                        )
                    }
                }
            }

            PainTypeState.BACK_NEURAL -> {
                apiService.getBackNeuralHistory(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    try {
                        backNeuralHistoryList.update { response!!.data.reversed() }
                    } catch (e: Throwable) {
                        LogUtil.d(
                            "_chen",
                            "请求颈部疼痛历史数据, 响应发生错误!\n${e.printStackTrace()}"
                        )
                    }
                }
            }

            else -> {

            }
        }
    }
}

