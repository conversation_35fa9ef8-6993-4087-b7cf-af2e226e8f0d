package org.aihealth.ineck.viewmodel.dao

import android.Manifest
import android.content.Intent
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.Code
import org.aihealth.ineck.model.CodeScanData
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.qrcode.QRCodeActivity
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.saveToLocal
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.viewmodel.BaseViewModel
import org.aihealth.ineck.viewmodel.user

class RedemptionCodeViewModel: BaseViewModel() {

    var redeemCode by mutableStateOf("")
    /**
     * 扫码成功后确认是否使用兑换码对话框visible
     */
    var useRedemptionCodeDialogVisible by mutableStateOf(false)

    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by mutableStateOf(false)
    fun redeemCode(){
        if(redeemCode.trim().length!=8){
            DialogUtil.showToast("无效码,请重新输入")
            return
        }
        DialogUtil.showLoading()
        LogUtil.i("redeemCode $redeemCode")
        apiService.postRedemptionCodeActivate(
            body = Code(redeemCode),
        ).enqueueBody{response ->
            redeemCode = ""
            DialogUtil.hideLoading()
            LogUtil.i("response body: ${response?.data}")
            if (response?.code == 1){
                if(response.msg == "Success"){
                    DialogUtil.showToast("兑换成功")
                    apiService.getInfo().enqueueBody { response ->
                        DialogUtil.hideLoading()
                        if (response?.code == 1) {
                            LogUtil.i("user: ${response.data.toJson()}")
                            try {
                                val gson = Gson()
                                val info = gson.fromJson(response.data, User::class.java)
                                LogUtil.i("user: $user")
                                user = info
                                user.saveToLocal()
                                popScreen(Screen.Main.route)
                            } catch (e: Exception) {
                                DialogUtil.showToast(baseApplication.getString(R.string.online_data_error))
                            }
                        } else if (response?.code == 0) {
                            startScreen(Screen.FirstUpdateData.route, true)
                        } else {
                            startScreen(Screen.Login.route, true)
                        }
                    }
                }else{
                    val gson = Gson()
                    val data = response.data

                    val description= gson.fromJson(data,CodeError::class.java)
                    DialogUtil.showToast("$description.description")
                }

            }else{
                DialogUtil.showToast("请联系管理员")
            }
        }
    }

    fun onScanClick() {
        ActivityResultUtils.requestPermissions(
            permissions = arrayOf(Manifest.permission.CAMERA),
            onAllGranted = {
                ActivityResultUtils.startActivity(
                    intent = Intent(activity, QRCodeActivity::class.java)
                ){ _: Int, data: Intent? ->
                    data?.let {
                        val qrcodeResult = it.getStringExtra("qrcode")?:""
                        LogUtil.i("qrcodeResult:${qrcodeResult}")
                        if(qrcodeResult.contains("code")) {
                            LogUtil.d("mytag", "qrcodeResult: $qrcodeResult")
                            val gson = Gson()
                            val code = gson.fromJson(qrcodeResult, CodeScanData::class.java )
                            redeemCode = code.code
                            useRedemptionCodeDialogVisible = true
                        }
                        else {
                            DialogUtil.showToast(localeResources.getString(R.string.incorrect_format_of_QR_code))
                        }
                    }
                }
            }
        )
    }

}
private data class CodeError(
    @SerializedName("description")
    var description:String
)