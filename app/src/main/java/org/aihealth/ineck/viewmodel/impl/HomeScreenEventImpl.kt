package org.aihealth.ineck.viewmodel.impl

import android.Manifest
import android.content.Intent
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.model.vitalsigns.BloodGlucose
import org.aihealth.ineck.model.vitalsigns.BloodOxygen
import org.aihealth.ineck.model.vitalsigns.BloodPressure
import org.aihealth.ineck.model.vitalsigns.BodyTemperature
import org.aihealth.ineck.model.vitalsigns.HeartRate
import org.aihealth.ineck.model.vitalsigns.VitalSignGroupLoadingState
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.qrcode.QRCodeActivity
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.copy
import org.aihealth.ineck.util.fixUTCDate
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState
import org.aihealth.ineck.view.dialog.AIHTipButtonDialog
import org.aihealth.ineck.view.screen.home.VitalSignsModuleState
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.dao.HomeScreenEvent
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import java.util.Calendar
import kotlin.math.abs
import kotlin.math.pow
import kotlin.math.roundToInt
import kotlin.math.sqrt

class HomeScreenEventImpl(
    private val viewModel: MainViewModel
) : HomeScreenEvent() {

    var scanUUID = ""
    var userInfoName = ""

    init {
        viewModel.viewModelScope.apply {
            launch {
                loadVitalSignData()
            }
        }
    }

    /** 浮动窗口初始化锚定状态 */
    val initFloatingWindowAnchorState = MutableStateFlow(true)

    /** 视频监测流 - 含背压策略 */
    val angleDebounceCVFlow = this.angleVC.onEach { delay(250) }

    /** 视频监测浮动窗口的锚点 x、y坐标  (初始化坐标是经过测算推断浮窗基座的左上角的绝对坐标位置) */
    val anchorXOfFloatingWindow = MutableStateFlow(0f)
    val anchorYOfFloatingWindow = MutableStateFlow(0f)

    // 用来保存角度数据流
    private val _angleData = MutableStateFlow<List<Float>>(emptyList())

    // 用来显示处理后的角度数据
    private val _processedAngle = MutableStateFlow(0)
    val processedAngle: StateFlow<Int> = _processedAngle
    // 更新角度数据
    fun updateAngle(angle: Float) {
        _angleData.value = _angleData.value + angle
        processAngleData()
    }

    // 处理角度数据：丢弃异常值，计算平均数等
    private fun processAngleData() {
        val validData = _angleData.value.filter { isValidData(it) }

        // 保留最新的20个数据点
        val recentData = validData.takeLast(10)

        // 计算均值和标准差
        val mean = recentData.average()
        val stdDev = sqrt(recentData.map { (it - mean).pow(2) }.average())

        // 丢弃偏差过大的数据点
        val filteredData = recentData.filter { abs(it - mean) <= 3 * stdDev }

        // 计算处理后的平均值并更新
        _processedAngle.value = filteredData.average().roundToInt()
        LogUtil.i("angleValue, _processedAngle:${_processedAngle.value}")
    }

    // 检查数据是否有效
    private fun isValidData(value: Float): Boolean {
        return value in -90f..90f // 你可以根据实际情况调整这个范围
    }
    // 刷新角度协程
    private var animateScope: Job? = null

    override fun onScanClick() {
        ActivityResultUtils.requestPermissions(
            permissions = arrayOf(Manifest.permission.CAMERA),
            onAllGranted = {
                ActivityResultUtils.startActivity(
                    intent = Intent(activity, QRCodeActivity::class.java)
                ) { _: Int, data: Intent? ->
                    data?.let {
                        val qrcodeResult = it.getStringExtra("qrcode") ?: ""
                        LogUtil.i("qrcodeResult:${qrcodeResult}")
                        if (qrcodeResult.contains("provideruuid:")) {
                            LogUtil.d("mytag", "qrcodeResult: $qrcodeResult")
                            scanUUID = if ("provideruuid:".length == qrcodeResult.length) {
                                ""
                            } else {
                                qrcodeResult.substring("provideruuid:".length)
                            }

                            DialogUtil.showLoading()
                            viewModel.viewModelScope.launch {
                                apiService.getUserInfoWithoutToken(scanUUID)
                                    .enqueueBody { response ->
                                        DialogUtil.hideLoading()
                                        if (response?.code == 1) {
                                            try {
                                                val gson = Gson()
                                                val info =
                                                    gson.fromJson(response.data, User::class.java)
                                                userInfoName = info.name
                                                addAttentionDialogVisible = true
                                            } catch (e: Exception) {
                                                DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                            }
                                        } else {
                                            DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                        }
                                    }
                            }
                        } else if (qrcodeResult.contains("https://medical.aihnet.com/follow?uuid=")) {
                            DialogUtil.showLoading()
                            val encodedUuid = qrcodeResult.substringAfter("uuid=")
                            val decodedUuid =
                                URLDecoder.decode(encodedUuid, StandardCharsets.UTF_8.toString())
                            scanUUID = decodedUuid
                            LogUtil.d("mytag", "qrcodeResult: $qrcodeResult,scanUUID: $scanUUID")
                            viewModel.viewModelScope.launch {
                                apiService.getUserInfoWithoutToken(scanUUID)
                                    .enqueueBody { response ->
                                        DialogUtil.hideLoading()
                                        if (response?.code == 1) {
                                            try {
                                                val gson = Gson()
                                                val info =
                                                    gson.fromJson(response.data, User::class.java)
                                                userInfoName = info.name
                                                addAttentionDialogVisible = true
                                            } catch (e: Exception) {
                                                DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                            }
                                        } else {
                                            DialogUtil.showToast(response?.msg ?: "")
                                        }
                                    }
                            }
                        } else {
                            DialogUtil.showToast(localeResources.getString(R.string.incorrect_format_of_QR_code))
                        }
                    }
                }
            },
            onProhibited = {
                if (SPUtil.getBoolean(SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION, true)) {
                    SPUtil.putBoolean(SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION, false)
                } else {
                    DialogUtil.showToast {
                        AIHTipButtonDialog(
                            text = localeResources.getString(R.string.permission_denied),
                            buttonText = localeResources.getString(R.string.to_authorization),
                            onClick = {
                                APPUtil.goAPPDetail()
                            }
                        ) {
                            DialogUtil.hideToast()
                        }
                    }
                }
            }
        )
    }

    override fun toggleDeviceType(deviceType: DeviceType) {
        if (deviceType == currentDeviceType) {
            return
        }
        
        // 先关闭当前的相机扫描状态
        if (isCameraScanModel.value) {
            clearCameraVideoResource() // 使用已有的资源清理方法
        }
        
        if (viewModel.deviceScreen.aiNeckDeviceConfig.deviceType != DeviceType.None) {
            viewModel.deviceScreen.aiNeckDeviceConfig.deviceType = deviceType
            viewModel.deviceScreen.bluetoothUtil.deviceRepository?.setDeviceType(deviceType)
        }
        currentDeviceType = deviceType
    }

    override fun addFollow() {
        apiService.addFollow(
            followingId = scanUUID
        ).enqueue(
            onResponse = { _, response ->
                if (response!!.code == 1) {
                    DialogUtil.showToast(baseApplication.getString(R.string.addattention_success))
                } else {
                    DialogUtil.showToast(response.msg)
                }
            },
            onFailure = { _, throwable ->
                DialogUtil.showToast(throwable.message.toString())
            })
    }

    override fun loadVitalSignData() {
        val date = getDefaultDate()
        val nextDate = date.copy().apply {
            add(Calendar.DATE, 1)
        }
        val (startTime,endTime) = fixUTCDate(date, nextDate)
        /* 对所有的生命体征数据接口进行异步的请求 */
        apiService.getVitalSigns(
            fromDate = startTime,
            toDate = endTime
        ).enqueue(
            onResponse = { _, response ->
                LogUtil.i("response: $response")
                try {
                    /* 成功读到数据，赋值为成功状态，即使接收到的生命体征状态各参数为-1 */
                    this.vitalSignsData.update { VitalSignGroupLoadingState.Success(response!!.data) }
                } catch (e: Exception) {
                    /* 响应判空，赋值为失败状态 */
                    this.vitalSignsData.update {
                        VitalSignGroupLoadingState.Failure(
                            error = e,
                            msg = ""
                        )
                    }
                }
            },
            onFailure = { _, throwable ->
                LogUtil.i("response: $throwable")
                /* 网络请求失败，赋值为失败状态 */
                this.vitalSignsData.update {
                    VitalSignGroupLoadingState.Failure(
                        error = throwable.message?.let { Exception(it) } ?: Exception(),
                        msg = ""
                    )
                }
            }
        )
    }

    override fun postVitalSignData(
        data: String,
        toastEvent: (Boolean, Int) -> Unit,
    ) {
        when (this.vitalSignPostDialogVisibleState.value) {
            VitalSignPostDialogVisibleState.NONE -> {
                /* Nothing */
            }

            VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> {
                /* 上传心率数据逻辑 */
                apiService.postHeartRate(
                    body = HeartRate(value = data.toInt())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.heart_rate)
                        } else {
                            toastEvent(false, R.string.heart_rate)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.heart_rate)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> {
                /* 上传体温数据逻辑 */
                apiService.postBodyTemperature(
                    body = BodyTemperature(temperature = data.toFloat())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> {
                /* 上传血氧数据逻辑 */
                apiService.postBloodOxygen(
                    body = BloodOxygen(saturation = data.toFloat())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> {
                val (diastolic, systolic) = data.split("/")
                /* 上传血压数据逻辑 */
                apiService.postBloodPressure(
                    body = BloodPressure(diastolic = diastolic.toInt(), systolic = systolic.toInt())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }

            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> {
                /* 上传血糖数据逻辑 */
                apiService.postBloodGlucose(
                    body = BloodGlucose(level = data.toFloat())
                ).enqueueBody { response ->
                    try {
                        if (response!!.code == 1) {
                            toastEvent(true, R.string.body_temperature)
                        } else {
                            toastEvent(false, R.string.body_temperature)
                        }
                    } catch (e: Exception) {
                        toastEvent(false, R.string.body_temperature)
                    }
                }
            }
        }
    }

    fun changeVitalSignsModuleStateVisibleState(state: VitalSignsModuleState) {
        this.vitalSignsModuleVisibleState.update { state }
    }

    override fun changeVitalSignPostDialogVisibleState(state: VitalSignPostDialogVisibleState) {
        this.vitalSignPostDialogVisibleState.update { state }
    }

    override fun onChangeCameraScanModel(isScan: Boolean) {
        this.isCameraScanModel.update { isScan }
    }

    override fun onChangeAngleCV(newAngle: Int) {
        this.angleVC.update { newAngle }
    }


    /** 视频检测带动画效果的角度变更 */
    fun animateToAngleCV(newAngle: Int) {
        animateScope?.cancel()
        animateScope = viewModel.viewModelScope.launch {
//            LogUtil.d("_chenx", "angleVC.value: ${angleVC.value} | newAngle: $newAngle | ${if (angleVC.value > newAngle) "↓" else "↑"}")
            while (angleVC.value > newAngle) {
                onChangeAngleCV(angleVC.value - 1)
                delay(30L)
            }
            while (angleVC.value < newAngle) {
                onChangeAngleCV(angleVC.value + 1)
                delay(30L)
            }
        }
    }

    /**
     *  视频监测资源回收
     */
    fun clearCameraVideoResource() {
        this.onChangeCameraScanModel(isScan = false)
        this.animateScope?.cancel()             // 关闭时关闭视频检测角度的动画协程
        this.angleVC.update { 0 }
    }

    /**
     *  视频检测 校准拟合方程
     *  @param  x   视频检测单次收集数据角度
     *  @param  x0  校准成功获得的校准数据
     *  @return 设备角度的估计值
     */
    fun fittedFunctionForVC(x: Float, x0: Float): Double {
        val calArgument = x - x0
        val a = 0.000203608124796145
        val b = 0.0303301565742188
        val c = 186587835889043
        return a * calArgument.pow(3) + b * calArgument.pow(2) + c * calArgument
    }

    /**
     *  视频检测 校准拟合方程
     *  @param  x   视频检测单次收集数据角度
     *  @param  x0  校准成功获得的校准数据
     *  @return 设备角度的估计值
     */
    fun fittedFunctionForVC2(x: Float, x0: Float): Double {
        val calArgument = x - x0
        val a = 0.0001971016
        val b = 0.03066
        val c = 1.90631

        return a * calArgument.pow(3) + b * calArgument.pow(2) + c * calArgument
    }
}