package org.aihealth.ineck.viewmodel.dao

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import kotlinx.coroutines.flow.MutableStateFlow
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.vitalsigns.VitalSignGroupLoadingState
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState
import org.aihealth.ineck.view.screen.home.VitalSignsModuleState
import org.aihealth.ineck.viewmodel.DetectedResult

abstract class HomeScreenEvent {

    /**
     * 当前页面选择的设备类型(aiNeck,aiBack...)
     */
    var currentDeviceType by mutableStateOf(DeviceType.aiNeckCV)

    /**
     * 是否重新校准对话框visible
     */
    var recalibrationDialogVisible by mutableStateOf(false)

    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by mutableStateOf(false)

    /**
     * 是否显示进入颈椎检测
     */
    var showEnterAiNeckDetect by mutableStateOf(false)

    var theClickType by mutableIntStateOf(0)

    /**
     * 上次上传角度时间
     */
    var lastUpdateTime: Long = 0L

    /**
     * 扫码成功后确认是否添加关注对话框visible
     */
    var addAttentionDialogVisible by mutableStateOf(false)


    /**
     *  服务端生命体征数据
     */
    val vitalSignsData: MutableStateFlow<VitalSignGroupLoadingState> =
        MutableStateFlow(VitalSignGroupLoadingState.Loading)

    /**
     *  上传生命体征数据底部抽屉显示标识
     */
    val vitalSignPostDialogVisibleState: MutableStateFlow<VitalSignPostDialogVisibleState> =
        MutableStateFlow(VitalSignPostDialogVisibleState.NONE)

    /**
     * new vitalSignModuleVisible
     */
    val vitalSignsModuleVisibleState: MutableStateFlow<VitalSignsModuleState> =
        MutableStateFlow(VitalSignsModuleState.HIDDEN)


    /**
     *  视频检测模式启动前引导校准页触发状态
     */
    val isBootVCGuideScreen = mutableStateOf(false)


    /**
     *  视频检测模式启动前校准引导页返回结果响应对话框显示状态
     */
    val isShowVCGuideDetectResultDialog = mutableStateOf(false)

    val isShowQuestionnaireVCGuideDetectResultDialog = mutableStateOf(false)
    /**
     *  视频检测模式启动前校准引导页返回结果
     */
    val detectResultState: MutableStateFlow<DetectedResult> = MutableStateFlow(DetectedResult.None)

    /**
     *  视频监测模式启动标识
     */
    val isCameraScanModel: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /**
     *  视频监测模式下是否捕获到人脸
     */
    val isCaptureFace: MutableStateFlow<Boolean> = MutableStateFlow(false)

    /**
     *  视频监测角度
     */
    val angleVC: MutableStateFlow<Int> = MutableStateFlow(0)


    /**
     *  视频校准引导的测量结果，该数据用于带入拟合方程估算设备角度
     */
    var detectResultValueForFitted: Float = 0f

    /**
     * 点击扫一扫
     */
    abstract fun onScanClick()

    /**
     * 切换设备模式
     */
    abstract fun toggleDeviceType(deviceType: DeviceType)

    /**
     * 添加关注
     */
    abstract fun addFollow()

    /**
     *  加载生命体征数据
     */
    abstract fun loadVitalSignData()

    /**
     *  上传生命体征数据
     */
    abstract fun postVitalSignData(data: String, toastEvent: (Boolean, Int) -> Unit)

    /**
     *  改变上传生命体征数据底部抽屉显示标识状态
     */
    abstract fun changeVitalSignPostDialogVisibleState(state: VitalSignPostDialogVisibleState)

    /**
     *  变更视频监测模式启动标识
     */
    abstract fun onChangeCameraScanModel(isScan: Boolean)

    /**
     *  变更视频监测的角度值
     */
    abstract fun onChangeAngleCV(newAngle: Int)

}