package org.aihealth.ineck.viewmodel.impl

import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.update
import org.aihealth.ineck.model.improvement.ImproveProgram
import org.aihealth.ineck.model.improvement.ImproveProgramUserExercise
import org.aihealth.ineck.model.improvement.ImproveProgramUserExerciseLoadState
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.model.improvement.ImprovementProgramsLoadState
import org.aihealth.ineck.model.improvement.toImprovementDetailModel
import org.aihealth.ineck.model.meet.MeetingListLoadState
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.copy
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.year
import org.aihealth.ineck.view.directions.ImprovementDetailDirections
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.dao.ImprovementEvent
import java.util.Calendar

/** 改善页视图模型 */
class ImprovementEventImpl(
    viewModel: MainViewModel
) : ImprovementEvent() {

    // 新的 androidx.compose.foundation.pager.PagerState，用于替代父类中的 accompanist PagerState
    private var _newPagerState: PagerState? = null
    
    // 提供访问新 PagerState 的方法
    fun getNewPagerState(): PagerState? = _newPagerState
    
    // 设置新 PagerState 的方法
    fun setNewPagerState(pagerState: PagerState) {
        _newPagerState = pagerState
    }

    // 使用新 PagerState 进行页面跳转的方法
    suspend fun animateScrollToPageWithNewPager(page: Int) {
        _newPagerState?.animateScrollToPage(page)
    }
    
    // 获取当前页面的方法
    fun getCurrentPageFromNewPager(): Int? {
        return _newPagerState?.currentPage
    }

    init {
        /* 在VM初始化过程对上一周、这一周、下一周的数据内容，依据今天的日期做初始化 */
        this.calibrateWeeksData(indicatorCalendar = this.todayCalendar.value)
    }

    override fun changeCurrentImprovementPageTabState(newValue: ImprovementPageTabType) {
        this.currentImprovementTabPageState.update { newValue }
    }

    override fun changeChooseMonthVisibleState(newValue: Boolean) {
        this.monthChooseDialogVisibleState.update { newValue }
    }

    override fun loadImprovementProgramsData(
        loadType: ImprovementCardProgramsType
    ) {
        /* 若非初始化记载，对其状态首先进行 非初始化加载 状态的赋值 */
        this.improvementDataState.let { state ->
            if (state.value != ImprovementProgramsLoadState.InitLoading) {
                state.update { ImprovementProgramsLoadState.Loading }
            }
        }
        LogUtil.i("loadImprovementProgramsData origin")
        apiService.getImprovementProgramData().enqueue(
            onResponse = { _, response ->
                try {
                    if (response!!.code == 1) {
                        /* 若成功请求到数据 */
                        this.improvementDataState.update {
                            ImprovementProgramsLoadState.Success(improvementProgramsData = response.data)
                        }
                        /* 更新缓存列表数据 */
                        this.improvementDataCacheState.value = response.data
                    } else {
                        /* 响应不正常的情况 */
                        this.improvementDataState.update {
                            ImprovementProgramsLoadState.Failure(
                                error = Exception(),
                                message = "data of response is null"
                            )
                        }
                    }
                } catch (e: Exception) {
                    /* 出现了response出现了空指针的情况 */
                    this.improvementDataState.update {
                        ImprovementProgramsLoadState.Failure(error = e, message = "request error")
                    }
                }
            },
            onFailure = { _, throwable ->
                this.improvementDataState.update {
                    ImprovementProgramsLoadState.Failure(
                        error = throwable.message?.let { Exception(it) } ?: Exception(),
                        message = "request error"
                    )
                }
            }
        )

    }

    override fun loadImproveProgramUserExerciseData() {
        val startDate = this.targetCalendar.value.copy()
        val end = startDate.clone() as Calendar
        end.add(Calendar.DATE, 1)
        LogUtil.i("startDate: $startDate")
        val startTime =
            "${startDate.year}-${startDate.month + 1}-${startDate.date}T00:00:00.000000000-00:00"
        val endTime =
            "${end.year}-${end.month + 1}-${end.date}T00:00:00.000000000-00:00"

        this.improveProgramUserExerciseDataState.let {
            if (it.value != ImproveProgramUserExerciseLoadState.InitLoading) {
                it.update { ImproveProgramUserExerciseLoadState.Loading }
            }
        }

        apiService.getImprovementProgramUserExerciseData(
            fromDate = startTime,
            toDate = endTime
        ).enqueue(
            onResponse = { _, response ->
                try {
                    val isDataEffect = response!!.data.isJsonNull
                    when (response.code) {
                        1 -> {

                            val practiceDataList: List<ImproveProgramUserExercise> =
                                Gson().fromJson(
                                    response.data,
                                    object : TypeToken<List<ImproveProgramUserExercise>>() {}.type
                                )
                            //                    LogUtil.i("我的练习dataList：${practiceDataList}")
                            /* 更新我的已练习改善项目方案的相关状态 */
                            this.improveProgramUserExerciseDataState.update {
                                ImproveProgramUserExerciseLoadState.Success(dataList = practiceDataList)
                            }
                            /* 将数据放入我的练习数据缓存列表 */
                            this.improveProgramUserExerciseDataCacheState.update { practiceDataList }
                        }

                        -1 -> {
                            this.improveProgramUserExerciseDataState.update {
                                ImproveProgramUserExerciseLoadState.EmptyData
                            }
                        }

                        else -> {
                            /* 响应不正常的情况 */
                            this.improveProgramUserExerciseDataState.update {
                                ImproveProgramUserExerciseLoadState.Failure(
                                    error = Exception(),
                                    message = "data of response is null"
                                )
                            }
                        }
                    }
                } catch (e: Exception) {
                    /* 出现了response出现了空指针的情况 */
                    this.improveProgramUserExerciseDataState.update {
                        ImproveProgramUserExerciseLoadState.Failure(
                            error = e,
                            message = "request error"
                        )
                    }
                }
            },
            onFailure = { _, throwable ->
                this.improveProgramUserExerciseDataState.update {
                    ImproveProgramUserExerciseLoadState.Failure(
                        error = throwable.message?.let { Exception(it) } ?: Exception(),
                        message = "request error"
                    )
                }
            }

        )
    }

    fun loadMyMeetingData() {
        this.userMeetingDataState.let {
            if (it.value != MeetingListLoadState.InitLoading) {
                it.update { MeetingListLoadState.Loading }
            }
        }
        apiService.getMeetingList().enqueue(
            onResponse = { _, response ->
                try {
                    when (response!!.code) {
                        1 -> {
                            this.userMeetingDataState.update {
                                MeetingListLoadState.Success(dataList = response.data)
                            }
                            this.userMeetingDataStateCacheState.update { response.data }
                        }

                        -1 -> {
                            this.userMeetingDataState.update {
                                MeetingListLoadState.EmptyData
                            }
                        }

                        else -> {
                            /* 响应不正常的情况 */
                            this.userMeetingDataState.update {
                                MeetingListLoadState.Failure(
                                    error = Exception(),
                                    message = "data of response is null"
                                )
                            }
                        }
                    }
                } catch (e: Exception) {
                    /* 出现了response出现了空指针的情况 */
                    this.userMeetingDataState.update {
                        MeetingListLoadState.Failure(
                            error = e,
                            message = "request error"
                        )
                    }
                }
            },
            onFailure = { _, throwable ->
                this.userMeetingDataState.update {
                    MeetingListLoadState.Failure(
                        error = throwable.message?.let { Exception(it) } ?: Exception(),
                        message = "request error"
                    )
                }
            }
        )
    }

    /** 改善页面内容刷新方法 */
    override suspend fun onRefresh() {
        loadImproveProgramUserExerciseData()
        loadImprovementProgramsData(loadType = this.currentImprovementCardRecommendType.value)
        loadMyMeetingData()
        this.refreshState.refreshing = false
    }

    override fun changeTargetCalendar(newTargetDay: Calendar) {
        this.targetCalendar.update { newTargetDay }
//        LogUtil.i("targetCalendar: ${this.targetCalendar.value}\n newTargetDay: $newTargetDay")
        loadImproveProgramUserExerciseData()
    }

    override fun changeCurrentMonthCalendar(newMonthOfCalendar: Calendar) {
        this.currentMonthOfViewWeekCalendar.update { newMonthOfCalendar.get(Calendar.MONTH) + 1 }
    }

    override fun calibrateWeeksData(indicatorCalendar: Calendar) {
        this.targetCalendar.update { indicatorCalendar }
        /* 首先修正当前星期数据 */
        val newCurrentWeekList = TimeUtil.getCalendarsInCurrentWeek(this.targetCalendar.value)
        this.currentWeek.apply {
            clear()
            newCurrentWeekList.forEach { calendar ->
                this.add(calendar)
            }
        }
        /* 依据当前星期的第一天日期， 修正上一周数据 */
        val newPreviousWeekList = TimeUtil.getCalendarsInCurrentWeek(
            (this.currentWeek.first().clone() as Calendar).apply { add(Calendar.DATE, -1) })
        this.previousWeek.apply {
            clear()
            newPreviousWeekList.forEach { calendar ->
                this.add(calendar)
            }
        }
        /* 依据当前星期的最后一天， 修正下一周的数据 */
        val newNextWeekList = TimeUtil.getCalendarsInCurrentWeek(
            (this.currentWeek.last().clone() as Calendar).apply { add(Calendar.DATE, 1) })
        this.nextWeek.apply {
            clear()
            newNextWeekList.forEach { calendar ->
                this.add(calendar)
            }
        }
    }

    override fun calibrateTargetWeeksData(
        indicatorCalendar: Calendar,
        weekType: WeekCalendarType
    ) {
        val newWeekList = TimeUtil.getCalendarsInCurrentWeek(indicatorCalendar)
        when (weekType) {
            WeekCalendarType.PreviousWeek -> {
                this.previousWeek.apply {
                    clear()
                    newWeekList.forEach { calendar ->
                        this.add(calendar)
                    }
                }
            }

            WeekCalendarType.CurrentWeek -> {
                this.currentWeek.apply {
                    clear()
                    newWeekList.forEach { calendar ->
                        this.add(calendar)
                    }
                }

//                LogUtil.d("_chenx", "修改: ${this.currentWeek[3].get(Calendar.YEAR)}-${this.currentWeek[3].get(Calendar.MONTH)}")
            }

            WeekCalendarType.NextWeek -> {
                this.nextWeek.apply {
                    clear()
                    newWeekList.forEach { calendar ->
                        this.add(calendar)
                    }
                }
            }
        }
    }

    override fun copyPreviousToCurrentWeek() {
        this.currentWeek.clear()
        this.previousWeek.forEach { calendar ->
            this.currentWeek.add(calendar)
        }
        this.currentMonthOfViewWeekCalendar.update { this.currentWeek[3].get(Calendar.MONTH) + 1 }
        this.currentYearOfViewWeekCalendar.update { this.currentWeek[3].get(Calendar.YEAR) }
    }

    override fun copyNextToCurrentWeek() {
        this.currentWeek.clear()
        this.nextWeek.forEach { calendar ->
            this.currentWeek.add(calendar)
        }
        this.currentMonthOfViewWeekCalendar.update { this.currentWeek[3].get(Calendar.MONTH) + 1 }
        this.currentYearOfViewWeekCalendar.update { this.currentWeek[3].get(Calendar.YEAR) }
    }

    override fun getUserExerciseCardImg(materialId: Int): Any {
        var pictureUrl = "https://www.aihnet.com/wp-content/uploads/2023/11/default-avatar.png"
        improvementDataCacheState.value.programs.forEach {
            if (it.materialId == materialId) {
                pictureUrl = it.cover
            }
        }
        return pictureUrl
    }

    override fun getUserExerciseCardTitle(materialId: Int): Any {
        var title = "Error"
        improvementDataCacheState.value.programs.forEach {
            if (it.materialId == materialId) {
                title = it.title
            }
        }
        return title
    }

    override fun jumpToExerciseDetailPage(materialId: Int) {
        LogUtil.i("materialId:$materialId")

        improvementDataCacheState.value.programs.forEach {
            if (it.materialId == materialId) {
                val model = it.toImprovementDetailModel()
                startScreen(
                    route = ImprovementDetailDirections.actionToImprovementDetailCompose(
                        model = model
                    ),
                    finish = false
                )
                return
            }
        }
    }

    override fun getExerciseItemData(materialId: Int): ImproveProgram {
        improvementDataCacheState.value.programs.forEach {
            if (it.materialId == materialId) {
                return it
            }
        }
        return ImproveProgram(
            materialId = 0,
            type = "Video",
            cover = "",
            duration = 0,
            isDevicesRequired = false,
            isMembershipRequired = false,
            title = "Error",
            subtitle = "",
            description = "",
            frequency = 0
        )
    }

    fun getExerciseItemDataTow(): ImprovementProgramsData {

        if (improvementDataCacheState.value.programs.size >= 2) {
            val improvementProgramsData =
                ImprovementProgramsData(
                    2,
                    improvementDataCacheState.value.programs.subList(0, 2)
                )
            return improvementProgramsData
        }
        val improvementProgramsData = ImprovementProgramsData(
            number = 2,
            programs = listOf(
                ImproveProgram(
                    1,
                    "Video",
                    "https://myaih.net/material/image/get/12",
                    32,
                    false,
                    false,
                    "颈椎改善练习1",
                    "颈椎改善练习1",
                    "颈椎改善练习1",
                    100
                ),
                ImproveProgram(
                    2,
                    "Image",
                    "https://myaih.net/material/image/get/3",
                    1800,
                    false,
                    false,
                    "颈椎改善练习2",
                    "颈椎改善练习2",
                    "颈椎改善练习2",
                    100
                )
            )
        )
        return improvementProgramsData
    }
}

/**
 *  改善页Tab表头类型
 */
enum class ImprovementPageTabType {
    ImprovementPlan, MyPractice
}

/**
 *  改善方案项目卡片
 */
enum class ImprovementCardProgramsType {
    RecommendFromExperts, TailoredForYou
}

/**
 * 创建新的 PagerState 的 Composable 函数
 * 用于替代 accompanist 的 PagerState
 */
@Composable
fun ImprovementEventImpl.rememberNewPagerState(): PagerState {
    val pagerState = rememberPagerState(
        initialPage = convertPageStateToNumberPagerState(ImprovementPageTabType.ImprovementPlan)
    ) { 
        ImprovementPageTabType.entries.size 
    }
    
    // 将创建的 PagerState 设置到 ViewModel 中
    setNewPagerState(pagerState)
    
    return pagerState
}

/**
 * 从页类型状态切换到页码状态的本地版本
 */
private fun convertPageStateToNumberPagerState(pageState: ImprovementPageTabType): Int {
    return when (pageState) {
        ImprovementPageTabType.ImprovementPlan -> 0
        ImprovementPageTabType.MyPractice -> 1
    }
}

/**
 * 从页码状态切换到页类型状态的本地版本
 */
fun convertNumberPagerStateToPageState(page: Int): ImprovementPageTabType {
    return when (page) {
        0 -> ImprovementPageTabType.ImprovementPlan
        1 -> ImprovementPageTabType.MyPractice
        else -> ImprovementPageTabType.ImprovementPlan
    }
}