package org.aihealth.ineck.viewmodel

import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import org.aihealth.ineck.util.LogUtil

class QuestionnaireViewModel() : ViewModel() {

    var questionnaireRecord = mutableListOf<Record>().apply {
        add(0, Record(2))
        add(1, Record(3))
        add(2, Record(2))
        add(3, Record(2))
        add(4, Record(2))
    }.toList()


    fun jumpToTargetScreen(currentPage: Int, currentInnerPage: Int): Int {
        LogUtil.i("jumpToTargetScreen currentPage:$currentPage currentInnerPage:$currentInnerPage")
        var str = ""
        questionnaireRecord.forEach {
            str += "{Record("
            it.data.value.forEach {
                str += it.toString() + ","
            }
            str += ")},"
        }
        LogUtil.i("list:${str}")
        questionnaireRecord.forEachIndexed { index, record ->
            if (index >= currentPage + 1) {
                if (record.data.value.contains(false)) {
                    LogUtil.i("jumpToTargetScreen ${index} ")
                    return index
                }
            }
        }
        LogUtil.i("jumpToTargetScreen ${currentPage + 1} ")
        return currentPage + 1
    }

    fun targetRecord(currentPage: Int, currentInnerPage: Int) {
        questionnaireRecord.forEachIndexed { index, record ->
            if (index == currentPage) {
                record.data.value.apply {
                    record.updateData(currentInnerPage, true)
                }
            }
        }
    }

}

class Record(size: Int) {
    var data = mutableStateOf(List(size) { false })

    // 如果需要单个元素的更新方法
    fun updateData(index: Int, value: Boolean) {
        val newData = data.value.toMutableList()
        newData[index] = value
        data.value = newData
    }
}