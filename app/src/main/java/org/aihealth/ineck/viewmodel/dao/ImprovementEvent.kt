package org.aihealth.ineck.viewmodel.dao

import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.flow.MutableStateFlow
import org.aihealth.ineck.model.improvement.ImproveProgramUserExercise
import org.aihealth.ineck.model.improvement.ImproveProgramUserExerciseLoadState
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.model.improvement.ImprovementProgramsLoadState
import org.aihealth.ineck.model.meet.MeetMsg
import org.aihealth.ineck.model.meet.MeetingListLoadState
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshState
import org.aihealth.ineck.viewmodel.impl.ImprovementCardProgramsType
import org.aihealth.ineck.viewmodel.impl.ImprovementPageTabType
import java.util.Calendar

/** 改善页 关键属性 */
abstract class ImprovementEvent {

    /* 刷新状态 */
    var refreshState = AIHRefreshState(false)

    /** 当前改善页加载项目方案内容 */
    val improvementDataState: MutableStateFlow<ImprovementProgramsLoadState> =
        MutableStateFlow(ImprovementProgramsLoadState.InitLoading)

    /** 当前改善页加载项目方案内容数据列表缓存 */
    val improvementDataCacheState = mutableStateOf(ImprovementProgramsData(0, emptyList()))

    /** 用户已练习项目方案数据内容 */
    val improveProgramUserExerciseDataState: MutableStateFlow<ImproveProgramUserExerciseLoadState> =
        MutableStateFlow(ImproveProgramUserExerciseLoadState.InitLoading)

    /** 用户已练习项目方案数据列表缓存 */
    val improveProgramUserExerciseDataCacheState: MutableStateFlow<List<ImproveProgramUserExercise>> = MutableStateFlow(emptyList())

    /** 用户会议数据内容 */
    val userMeetingDataState: MutableStateFlow<MeetingListLoadState> =
        MutableStateFlow(MeetingListLoadState.InitLoading)

    /** 用户会议数据内容列表缓存 */
    val userMeetingDataStateCacheState: MutableStateFlow<List<MeetMsg>> = MutableStateFlow(emptyList())


    /** 当前改善页显示标题内容 */
    var currentImprovementTabPageState = MutableStateFlow(ImprovementPageTabType.ImprovementPlan)

//    var pagerState = PagerState(convertPageStateToNumberPagerState(currentImprovementTabPageState.value))
    /** 推荐项目方案的栏目类型
     *  ”专家推荐“ 或是 ”为你打造”
     */
    var currentImprovementCardRecommendType: MutableStateFlow<ImprovementCardProgramsType> =
        MutableStateFlow(ImprovementCardProgramsType.RecommendFromExperts)

    /** 当前日期 */
    val todayCalendar: MutableStateFlow<Calendar> = MutableStateFlow(getDefaultDate())
    /** 指定日期 */
    var targetCalendar: MutableStateFlow<Calendar> = MutableStateFlow(todayCalendar.value)
    /** 当前浏览月份 */
    var currentMonthOfViewWeekCalendar: MutableStateFlow<Int> = MutableStateFlow(todayCalendar.value.get(Calendar.MONTH) + 1)
    /** 当前浏览年份 */
    var currentYearOfViewWeekCalendar: MutableStateFlow<Int> = MutableStateFlow(todayCalendar.value.get(Calendar.YEAR))

    /** 上星期 */
    var previousWeek = mutableListOf<Calendar>()
    /** 这星期 */
    var currentWeek = mutableListOf<Calendar>()
    /** 下星期 */
    var nextWeek = mutableListOf<Calendar>()

    /** 切换月份对话框显示状态 */
    var monthChooseDialogVisibleState: MutableStateFlow<Boolean> = MutableStateFlow(false)

    abstract suspend fun onRefresh()

    /**
     *  请求改善页内容
     *  @param  loadType    改善方案卡片请求状态
     */
    abstract fun loadImprovementProgramsData(loadType: ImprovementCardProgramsType)

    /**
     *  请求用户练习数据
     */
    abstract fun loadImproveProgramUserExerciseData()

    /**
     *  变更当前改善页显示内容
     *  @param  newValue    新的Tab页雷类型
     */
    abstract fun changeCurrentImprovementPageTabState(newValue: ImprovementPageTabType)

    /**
     *  变更指定日期
     *  @param  newTargetDay    选择新的指定日期
     */
    abstract fun changeTargetCalendar(newTargetDay: Calendar)

    /**
     *  变更当前显示月份
     *  @param  newMonthOfCalendar  新的所属月份的第一天Calendar对象
     */
    abstract fun changeCurrentMonthCalendar(newMonthOfCalendar: Calendar)

    /**
     *  变更月份选择底部对话框的显示状态
     *  @param  newValue    新的状态值
     */
    abstract fun changeChooseMonthVisibleState(newValue: Boolean)

    /**
     *  校准星期数据，并依据游标日期，修正星期列表数据
     *  @param  indicatorCalendar   游标日期，用于索引并初始化星期列表
     */
    abstract fun calibrateWeeksData(indicatorCalendar: Calendar)

    /**
     *  校准指定周数据
     *  @param  indicatorCalendar   游标日期，用于索引并初始化星期列表
     *  @param  weekType    星期类型，内容范围是上一周、这一周、下一周
     */
    abstract fun calibrateTargetWeeksData(
        indicatorCalendar: Calendar,
        weekType: WeekCalendarType
    )

    /**
     *  将上一周的星期内容复制到当前周
     */
    abstract fun copyPreviousToCurrentWeek()

    /**
     *  将下一周的星期内容赋值到当前周
     */
    abstract fun copyNextToCurrentWeek()

    /**
     *  获取用户练习卡片图片
     *  @param  materialId  素材ID
     */
    abstract fun getUserExerciseCardImg(materialId: Int): Any?

    abstract fun getUserExerciseCardTitle(materialId: Int):Any?

    /**
     *  跳转到练习详情页
     *  @param  it  练习卡片数据
     */
    abstract fun jumpToExerciseDetailPage(it: Int)

    abstract fun getExerciseItemData(materialId: Int):Any?

    /**
     *  上一周、这一周、下一周
     */
    enum class WeekCalendarType {
        PreviousWeek, CurrentWeek, NextWeek
    }
}

/** 从页类型状态切换到页码状态 */
fun convertPageStateToNumberPagerState(pageState: ImprovementPageTabType): Int {
    return when (pageState) {
        ImprovementPageTabType.ImprovementPlan -> {
            0
        }

        ImprovementPageTabType.MyPractice -> {
            1
        }
    }
}