package org.aihealth.ineck.push;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.widget.TextView;

import com.umeng.message.UmengNotifyClick;
import com.umeng.message.entity.UMessage;

import org.aihealth.ineck.R;
import org.aihealth.ineck.util.LogUtil;

public class MfrMessageActivity extends Activity {
    private static final String TAG = "MfrMessageActivity";

    private final UmengNotifyClick mNotificationClick = new UmengNotifyClick() {
        @Override
        public void onMessage(UMessage msg) {
            final String body = msg.getRaw().toString();
            LogUtil.INSTANCE.d(TAG, "msg: " + body);
            if (!TextUtils.isEmpty(body)) {
                runOnUiThread(new Runnable() {
                    @Override
                    public void run() {
                        TextView textView = findViewById(R.id.tv);
                        if (textView != null) {
                            textView.setText(body);
                        }
                    }
                });
            }
        }
    };

    @Override
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.mfr_message_layout);
        mNotificationClick.onCreate(this, getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        mNotificationClick.onNewIntent(intent);
    }
}
