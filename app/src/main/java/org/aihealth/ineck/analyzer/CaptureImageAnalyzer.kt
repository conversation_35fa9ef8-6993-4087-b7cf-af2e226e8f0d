package org.aihealth.ineck.analyzer

import androidx.annotation.OptIn
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions

class CaptureImageAnalyzer(
    private val onImageCaptured: (Float, Float, Float) -> Unit
) : ImageAnalysis.Analyzer {
    @Volatile
    var captureNextImage = false

    @Volatile
    var closed = false

    @OptIn(ExperimentalGetImage::class)
    override fun analyze(imageProxy: ImageProxy) {
//        LogUtil.i("CaptureImageAnalyzer analyze... ")
        val mediaImage = imageProxy.image
        val detector = FaceDetection.getClient(
            FaceDetectorOptions.Builder()
                .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
                .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
                .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
                .build()
        )
        if (mediaImage != null) {
            val image =
                InputImage.fromMediaImage(
                    mediaImage,
                    imageProxy.imageInfo.rotationDegrees
                )
            detector.process(image)
                .addOnSuccessListener { faces ->
                    if (faces.isEmpty()) {
                        if (captureNextImage) {
//                        LogUtil.i("analyze: no face found")
                            onImageCaptured(
                                0f, 0f, 0f
                            )
                            captureNextImage = false
                        }
                    } else {
                        for (face in faces) {
                            if (captureNextImage) {
                                onImageCaptured(
                                    face.headEulerAngleX,
                                    face.headEulerAngleY,
                                    face.headEulerAngleZ,
                                )
                                captureNextImage = false
                            }

                        }
                    }
                }
                .addOnFailureListener {
                    if (captureNextImage) {
                        onImageCaptured(
                            0f, 0f, 0f
                        )
                        captureNextImage = false
                    }
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        }
    }

    fun triggerCapture() {
        captureNextImage = true
    }

    fun close() {
        closed = true
    }
}