package org.aihealth.ineck.analyzer

import androidx.annotation.OptIn
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.ImageProxy
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import org.aihealth.ineck.util.LogUtil

class FaceImageAnalyzer(
    private val opts: FaceDetectorOptions,
    private val onUpdateAngle: (Float, Float, Float, Boolean) -> Unit
) : ImageAnalysis.Analyzer {

    @OptIn(ExperimentalGetImage::class)
    override fun analyze(imageProxy: ImageProxy) {
        val mediaImage = imageProxy.image
        if (mediaImage != null) {
            val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
            val detector = FaceDetection.getClient(opts)
            detector.process(image)
                .addOnSuccessListener { faces ->
//                    LogUtil.d("faces"," "+faces.isEmpty())
                    if (faces.isEmpty()) {
                        onUpdateAngle(0f, 0f, 0f, false)
                    }
                    for (face in faces) {
                        /* 打印各轴欧拉角数据 */
                        LogUtil.d(
                            "Euler X = ${face.headEulerAngleX} | Euler Y = ${face.headEulerAngleY} | Euler Z = ${face.headEulerAngleZ}"
                        )
                        onUpdateAngle(
                            face.headEulerAngleX,
                            face.headEulerAngleY,
                            face.headEulerAngleZ,
                            true
                        )
                    }
                }
                .addOnFailureListener {
                    onUpdateAngle(0f, 0f, 0f, false)
                }
                .addOnCompleteListener {
                    imageProxy.close()
                }
        }
    }
}