package org.aihealth.ineck

import android.Manifest
import android.annotation.SuppressLint
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.ActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.OptIn
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Density
import androidx.lifecycle.ViewModelProvider
import org.aihealth.ineck.viewmodel.MainViewModel
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.offline.DownloadService
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.rememberNavController
import com.android.installreferrer.api.InstallReferrerClient
import com.android.installreferrer.api.InstallReferrerStateListener
import com.android.installreferrer.api.ReferrerDetails
import org.aihealth.ineck.model.ConstantData
import org.aihealth.ineck.model.SPConstant.DOCTOR_UUID
import org.aihealth.ineck.model.StepEntity
import org.aihealth.ineck.model.TimeStamp
import org.aihealth.ineck.service.CallService
import org.aihealth.ineck.service.VideoDownloadService
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.AuthingUtil
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.ErrorMsgUtil
import org.aihealth.ineck.util.HybridStepTracker
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.StepCountCheckUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.util.StepModuleManager
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.UnitUtil.calorieConvert
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.work.StepSyncWorker
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.NetworkType
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import java.util.concurrent.TimeUnit
import org.aihealth.ineck.viewmodel.ChatViewModel
import org.aihealth.ineck.viewmodel.user
import us.zoom.sdk.ZoomVideoSDK
import us.zoom.sdk.ZoomVideoSDKErrors
import us.zoom.sdk.ZoomVideoSDKInitParams
import us.zoom.sdk.ZoomVideoSDKRawDataMemoryMode
import java.util.Timer
import java.util.TimerTask


@SuppressLint("StaticFieldLeak")
lateinit var navController: NavHostController
var isLoadSuccess by mutableStateOf(false)
lateinit var density: Density

// 安全获取density的函数
fun getSafeDensity(): Density {
    return if (::density.isInitialized) {
        density
    } else {
        // 默认密度值，用于在density未初始化时使用
        Density(density = 2.75f, fontScale = 1f)
    }
}

val activity: MainActivity
    get() = MainActivity.getInstance()
var curStep by mutableIntStateOf(-1)
var curGoogleFitsStep by mutableIntStateOf(-1)
var curCaloriesBurnedRecord by mutableDoubleStateOf(-1.0)
var curGoogleCaloriesBurnedRecord by mutableDoubleStateOf(-1.0)
// 服务器步数数据
var serverStep by mutableIntStateOf(-1)
var serverCaloriesBurnedRecord by mutableDoubleStateOf(-1.0)

class MainActivity : ComponentActivity(), Handler.Callback{
    companion object {
        private lateinit var instance: MainActivity
        fun getInstance(): MainActivity {
            return instance
        }

        // 添加标志来跟踪是否已初始化ZoomVideoSDK
        private var isZoomSDKInitialized = false
    }

    /**
     *  Install API client
     */
    private lateinit var referrerClient: InstallReferrerClient

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        resultContent(it)
    }
    private var resultContent: (ActivityResult) -> Unit = {}

    private val stepEntityList: MutableList<StepEntity> = ArrayList()
    private var curSelDate: String = TimeUtil.getCurrentDate()


    var isBind = false
    private val mGetReplyMessenger = Messenger(Handler(Looper.getMainLooper(), this))
    private var messenger: Messenger? = null

    // 混合步数追踪器
    private lateinit var hybridStepTracker: HybridStepTracker

    /**
     * 定时任务
     */
    private var timerTask: TimerTask? = null

    private var timer: Timer? = null

    @OptIn(UnstableApi::class)
    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(R.style.Theme_AIH_User)
        super.onCreate(savedInstanceState)
        isLoadSuccess = false
        LogUtil.i("activity onCreate: ")
        instance = this
        enableEdgeToEdge()
        AuthingUtil.init()
        requestStepPermissionFirst()
        handleDeepLink(intent)
        handlePushNotification(intent)
        afterFirstInstallEvent()

        setContent {
            navController = rememberNavController()
            if (!::density.isInitialized) {
                density = Density(
                    density = LocalDensity.current.density,
                    fontScale = 1f
                )
            }

            // 初始化屏幕密度和字体缩放比例
            CompositionLocalProvider(LocalDensity provides density) {
                AIH_UserTheme {
//                    val context = LocalContext.current
//                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
//                    APPUtil.loadLocale(context)
//                }

                    // 组合函数 初始化导航
                    MainScreenPage(
                        startDestination = Screen.Splash.route,
                        requestCameraPermissionEvent = { requestCameraPermission() })
//                        // 启动app时的加载页，为了防止出现白屏，把主Activity的背景设置为启动页的背景
//                        if (!isLoadSuccess) {
//                            Box(modifier = Modifier.fillMaxSize()) {
//                                Image(
//                                    painter = painterResource(id = R.drawable.splash),
//                                    contentDescription = null,
//                                    modifier = Modifier.fillMaxSize(),
//                                    contentScale = ContentScale.FillBounds
//                                )
//                            }
//                        }
                    DialogUtil.DialogContent()

                }
            }
        }

        try {
            DownloadService.start(activity, VideoDownloadService::class.java)
        } catch (e: IllegalStateException) {
            DownloadService.startForeground(activity, VideoDownloadService::class.java)
        }

        TextToSpeech.ttsCreate(this)
    }

    /**
     * 延迟初始化ZoomVideoSDK
     * 只有在需要时才会初始化
     */
    fun initZoomVideoSDK() {
        if (!isZoomSDKInitialized) {
            val params = ZoomVideoSDKInitParams().apply {
                domain = "zoom.us"
                enableLog = true
                videoRawDataMemoryMode = ZoomVideoSDKRawDataMemoryMode.ZoomVideoSDKRawDataMemoryModeHeap
            }
            val ret = ZoomVideoSDK.getInstance().initialize(BaseApplication.instance, params)
            if (ret != ZoomVideoSDKErrors.Errors_Success) {
                DialogUtil.run { showToast(ErrorMsgUtil.getMsgByErrorCode(ret)) }
            } else {
                isZoomSDKInitialized = true
            }
        }
    }

    /**
     * 处理安装推荐，暂时不用
     */
    private fun afterFirstInstallEvent() {
        if (SPUtil.getBoolean("isAPIFirst", true)) {
            // 初始化 Install Referrer Client
            referrerClient = InstallReferrerClient.newBuilder(this).build()
            referrerClient.startConnection(object : InstallReferrerStateListener {
                override fun onInstallReferrerSetupFinished(responseCode: Int) {
                    when (responseCode) {
                        InstallReferrerClient.InstallReferrerResponse.OK -> {
                            // 成功连接到服务
                            try {
                                val response: ReferrerDetails = referrerClient.installReferrer
                                val referrerUrl: String = response.installReferrer
                                LogUtil.e("referrerUrl: $referrerUrl")
                                SPUtil.putBoolean("isAPIFirst", false)
                                val uuid = parseUuidFromReferrer(referrerUrl)
                                // 使用获取的 uuid
                                handleUuid(uuid)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            } finally {
                                referrerClient.endConnection()
                            }
                        }

                        InstallReferrerClient.InstallReferrerResponse.FEATURE_NOT_SUPPORTED -> {
                            // 当前 Play 商店版本不支持该功能
                        }

                        InstallReferrerClient.InstallReferrerResponse.SERVICE_UNAVAILABLE -> {
                            // 无法连接到服务
                        }
                    }
                }

                override fun onInstallReferrerServiceDisconnected() {
                    // 尝试重新连接或处理断开连接的情况
                }
            })
        }
    }

    /**
     * @param referrerUrl 从 Install Referrer API 获取的 referrer URL
     * 解析 referrerUrl 以获取 uuid
     *
     */
    private fun parseUuidFromReferrer(referrerUrl: String): String? {
        // 假设 referrerUrl 类似于 "utm_source=google&utm_medium=cpc&uuid=google-oauth2|112205628288018731193"
        val decodedReferrer = Uri.decode(referrerUrl)
        val uri = Uri.parse("http://dummy.url/?$decodedReferrer")
        return uri.getQueryParameter("uuid")
    }

    /**
     * @param uuid
     * 使用获取的 uuid
     *
     */
    private fun handleUuid(uuid: String?) {
        uuid?.let {
            // 在这里处理获取到的 uuid，例如保存到 SharedPreferences 或发送到服务器
            // 示例：保存到 SharedPreferences
            SPUtil.putString(DOCTOR_UUID, uuid)
        }
    }

    /**
     * @param intent
     * 处理深度链接
     */
    private fun handleDeepLink(intent: Intent?) {
        intent?.data?.let { uri: Uri ->
            // 获取完整的 URI 数据，例如：myapp://page/follow/Uuid=apple|000169...
            // 从 URI 中解析 `uuid` 查询参数
            val uuid = uri.getQueryParameter("uuid")
            LogUtil.i("uuid: $uuid")
            handleUuid(uuid)
        }
    }

    /**
     * @param intent
     * 处理推送通知跳转
     */
    private fun handlePushNotification(intent: Intent?) {
        intent?.let {
            val action = it.getStringExtra("action")
            if (action == "open_chat") {
                val fromUserId = it.getStringExtra("from_user_id")
                val chatMessage = it.getStringExtra("chat_message")
                
                LogUtil.i("收到聊天推送通知: fromUserId=$fromUserId, message=$chatMessage")
                
                if (!fromUserId.isNullOrEmpty()) {
                    // 延迟一下确保应用已完全启动，然后导航到聊天界面
                    Handler(Looper.getMainLooper()).postDelayed({
                        navigateToChat(fromUserId, chatMessage ?: "")
                    }, 500)
                }
            }
        }
    }

    /**
     * 导航到聊天界面
     */
    private fun navigateToChat(fromUserId: String, message: String) {
        try {
            // 确保用户已登录
            if (user.uuid.isEmpty()) {
                LogUtil.w("用户未登录，无法打开聊天界面")
                return
            }

            // 获取ChatViewModel并设置聊天用户
            val chatViewModel = ViewModelProvider(this)[ChatViewModel::class.java]
            
            // 这里需要根据fromUserId获取用户信息（姓名、头像等）
            // 由于我们只有UUID，可以使用一个默认的显示方式，实际的用户信息会通过API获取
            chatViewModel.setChatUser(fromUserId, "医生", "")
            
            // 检查导航控制器是否已初始化
            if (::navController.isInitialized) {
                // 导航到聊天界面
                navController.navigate(Screen.Chat.route) {
                    // 可选：清除导航栈，确保用户按返回键时不会回到之前的界面
                    popUpTo(Screen.Main.route) {
                        inclusive = false
                    }
                }
                LogUtil.i("成功导航到聊天界面: $fromUserId")
            } else {
                LogUtil.w("导航控制器未初始化，延迟重试")
                // 如果导航控制器还未初始化，延迟重试
                Handler(Looper.getMainLooper()).postDelayed({
                    navigateToChat(fromUserId, message)
                }, 1000)
            }
        } catch (e: Exception) {
            LogUtil.e("导航到聊天界面失败: ${e.message}")
        }
    }

    fun startLauncher(intent: Intent, content: (ActivityResult) -> Unit) {
        resultContent = content
        launcher.launch(intent)
    }

    override fun onResume() {
        super.onResume()
        LogUtil.i("activity onResume: ")
        val startTime = System.currentTimeMillis() / 1000
        addTimeStamp(startTime)
        
        // 检查系统语言是否变化，如果变化了就更新currentLocale
        val systemLanguage = resources.configuration.locales.get(0).language
        val currentLanguage = if (currentLocale == java.util.Locale.CHINESE) "zh" else "en"
        if (systemLanguage != currentLanguage) {
            currentLocale = if (systemLanguage == "zh") java.util.Locale.CHINESE else java.util.Locale.ENGLISH
            LogUtil.i("System language changed, updated currentLocale to: $currentLocale")
        }
        
        // 总是检查是否有通话进行中，不只依赖resume原因判断
        checkForCallCompletionAndStopTracking()
        
        // 启动混合步数追踪器
        if (::hybridStepTracker.isInitialized) {
            hybridStepTracker.startForegroundTracking()
            // 加载最新数据并更新UI
            updateStepUI()
        } else {
            // 如果追踪器未初始化，尝试从数据库更新UI
            updateStepUI()
        }
    }

    /**
     * 判断是否从电话应用返回
     * 通过检测当前resumeReason判断是否从通话返回
     */
    private var lastPauseTime: Long = 0

    /**
     * Check if we're returning from a phone call and stop tracking if needed
     */
    private fun checkForCallCompletionAndStopTracking() {
        try {
            val viewModel = ViewModelProvider(this)[ChatViewModel::class.java]
            
            // 首先检查是否有Service在运行但没有对应的通话状态
            if (CallService.isTracking() && !viewModel.isCallInProgress()) {
                LogUtil.i("Service is tracking but no call state found, forcing stop")
                viewModel.forceEndCallTracking()
                return
            }
            
            // 检查是否有通话追踪服务运行超过设定时间
            checkForStuckCallService(viewModel)
            
            // 如果有通话在进行中，需要智能判断是否真的结束了
            if (viewModel.isCallInProgress()) {
                val serviceRunTime = viewModel.getServiceRunningTime()
                
                // 如果Service运行时间很短（小于3秒），可能只是电话应用启动瞬间
                // 不要立即停止，给通话一些时间进行
                if (serviceRunTime < 3) {
                    LogUtil.d("Call just started ($serviceRunTime seconds), not stopping tracking yet")
                    
                    // 设置一个延迟检查，在5秒后再次检查通话状态
                    Handler(Looper.getMainLooper()).postDelayed({
                        try {
                            // 检查用户是否还在app中，如果是则可能通话真的结束了
                            if (!isAppInBackground()) {
                                LogUtil.d("User still in app after delay, checking call completion again")
                                val handled = viewModel.handlePossibleCallCompletion()
                                if (!handled && CallService.isTracking()) {
                                    LogUtil.i("Delayed check: call not handled but service still running, forcing stop")
                                    viewModel.forceEndCallTracking()
                                }
                            } else {
                                LogUtil.d("App is in background, call likely still in progress")
                            }
                        } catch (e: Exception) {
                            LogUtil.e("Error in delayed call check: ${e.message}")
                        }
                    }, 5000) // 5秒延迟
                    
                    return
                }
                
                // 如果Service已经运行了一段时间，正常处理通话完成
                val handled = viewModel.handlePossibleCallCompletion()
                LogUtil.d("Call completion check result: $handled")
                
                // 如果handlePossibleCallCompletion没有处理，但用户已经回到app较长时间
                if (!handled && CallService.isTracking() && serviceRunTime > 10) {
                    LogUtil.i("Long-running call not handled, service running for $serviceRunTime seconds, forcing stop")
                    viewModel.forceEndCallTracking()
                }
            } else if (CallService.isTracking()) {
                // 如果没有通话状态但Service还在运行，检查运行时间
                val serviceRunTime = viewModel.getServiceRunningTime()
                if (serviceRunTime > 5) { // 只有运行超过5秒才强制停止
                    LogUtil.i("No call in progress but service is tracking for $serviceRunTime seconds, forcing stop")
                    viewModel.forceEndCallTracking()
                } else {
                    LogUtil.d("Service tracking but call state cleared, giving it more time")
                }
            } else {
                LogUtil.d("No call in progress and no service tracking")
            }
        } catch (e: Exception) {
            LogUtil.e("Error checking call state: ${e.message}")
        }
    }
    
    /**
     * 检查应用是否在后台
     */
    private fun isAppInBackground(): Boolean {
        return isFinishing || isDestroyed
    }

    /**
     * 检查是否有通话追踪服务运行太久，可能是用户直接挂断电话导致计时器没有正确结束
     */
    private fun checkForStuckCallService(viewModel: ChatViewModel) {
        if (CallService.isTracking()) {
            // 检查服务运行时间
            val callStartTimestamp = viewModel.getCallStartTimestamp()
            if (callStartTimestamp > 0) {
                val currentTime = System.currentTimeMillis()
                val callDuration = (currentTime - callStartTimestamp) / 1000
                
                // 如果通话时长超过60秒，可能是计时器卡住了
                // 增加阈值，给真实的长通话更多时间
                if (callDuration > 60) {
                    LogUtil.i("Call service has been running for $callDuration seconds, forcing stop")
                    viewModel.forceEndCallTracking()
                    return
                }
            }
            
            // 如果服务运行但没有记录开始时间，这是一个异常情况
            if (callStartTimestamp <= 0) {
                LogUtil.i("Call service is running but no start timestamp, forcing stop")
                viewModel.forceEndCallTracking()
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleDeepLink(intent)
        handlePushNotification(intent)
        LogUtil.i("activity onNewIntent: handleDeepLink and push notification")
    }

    override fun onPause() {
        super.onPause()
        LogUtil.i("activity onPause: ")
        val endTime = System.currentTimeMillis() / 1000
        updateEndTimeStamp(endTime)
        
        // 检查是否有通话进行中，如果有则记录当前时间戳
        // 这将用于在应用恢复时检测可能的通话结束
        try {
            val viewModel = ViewModelProvider(this)[ChatViewModel::class.java]
            if (viewModel.isCallInProgress()) {
                LogUtil.d("Call in progress when app paused, will check on resume")
                
                // 设置一个较短的延迟，在5秒后检查如果应用没有恢复
                // 可能是用户挂断电话后没有回到应用，此时需要强制结束通话追踪
                Handler(Looper.getMainLooper()).postDelayed({
                    try {
                        // 检查是否应用仍在后台且通话服务仍在运行
                        if (!isAppInForeground() && CallService.isTracking()) {
                            LogUtil.i("App still in background and call service running, forcing stop")
                            viewModel.forceEndCallTracking()
                        }
                    } catch (e: Exception) {
                        LogUtil.e("Error in delayed call check: ${e.message}")
                    }
                }, 5000) // 5秒后检查
            }
        } catch (e: Exception) {
            LogUtil.e("Error checking call state on pause: ${e.message}")
        }
        
        // 停止混合步数追踪器
        if (::hybridStepTracker.isInitialized) {
            hybridStepTracker.stopForegroundTracking()
        }
    }

    /**
     * 检查应用是否在前台
     */
    private fun isAppInForeground(): Boolean {
        // 简单的判断方法，更复杂的实现可能需要使用ActivityLifecycleCallbacks
        return !isFinishing && !isDestroyed
    }

    /**
     * 添加时间片段
     */
    fun addTimeStamp(startTime: Long) {
        //没有登录
        if (user.uuid.isEmpty()) {
            return
        }

        val timeStamp = TimeStamp(
            uid = user.uuid,
            StartTime = startTime,
            EndTime = 0L,
            TimeSpent = 0L,
        )
        val timeStampList = timeStampDao.getTimeStampByUid(user.uuid)
        // 如果列表为空，直接添加
        if (timeStampList.isEmpty()) {
            timeStampDao.addTimeStamp(timeStamp)
            return
        }
//        LogUtil.i("mytag", "the start time ${startTime}")

        // 获取最近的时间片段，如果最近的时间片段的结束时间不为0，说明已经结束，直接添加新的时间片段
        val lastTimeStamp = timeStampList.last()
        if (lastTimeStamp.EndTime != 0L) {
            timeStampDao.addTimeStamp(timeStamp)
            return
        }
        // 如果最近的时间片段的结束时间为0，说明正在进行中，更新最近的时间片段的结束时间和持续时间
        if (startTime - lastTimeStamp.StartTime > 0L) {
            timeStampDao.updateTimeStamp(
                lastTimeStamp.copy(
                    EndTime = startTime,
                    TimeSpent = (startTime - lastTimeStamp.StartTime),
                )
            )
        }
    }

    /**
     * 更新时间片段
     */
    fun updateEndTimeStamp(endTime: Long) {
        //没有登录
        if (user.uuid.isEmpty()) {
            return
        }

        val timeStampList = timeStampDao.getTimeStampByUid(user.uuid)
        // 如果列表为空，放弃本次操作
        if (timeStampList.isEmpty()) {
            return
        }
        val lastTimeStamp = timeStampList.last()
        if (lastTimeStamp.EndTime == 0L) {
//            LogUtil.i("mytag", "the start time ${lastTimeStamp.StartTime},the useful update Time ${endTime}")
            timeStampDao.updateTimeStamp(
                lastTimeStamp.copy(
                    EndTime = endTime,
                    TimeSpent = (endTime - lastTimeStamp.StartTime),
                )
            )
            return
        }
    }

    @SuppressLint("UnsafeOptInUsageError")
    override fun onDestroy() {
        LogUtil.i("activity onDestroy: ")
        super.onDestroy()
        TextToSpeech.ttsDestroy()
        //记得解绑Service，不然多次绑定Service会异常
        if (isBind) this.unbindService(conn)
    }


    override fun onStop() {
        super.onStop()
        LogUtil.i("activity onStop: ")
    }

    /**
     *  检查相机权限
     */
    fun requestCameraPermission() {
        ActivityResultUtils.requestPermissions(
            permissions = arrayOf(Manifest.permission.CAMERA),
            onAllGranted = {},
            onProhibited = {
                DialogUtil.showToast(text = localeResources.getString(R.string.permission_denied))
            }
        )
    }

    fun requestStepPermissionFirst() {
        LogUtil.i("📱 MainActivity: ========== 应用启动权限检查 ==========")
        
        // 使用StepModuleManager统一管理步数模块状态
        val shouldStart = StepModuleManager.checkOnAppStart()
        
        LogUtil.i("📱 MainActivity: 权限检查结果 - 应该启动: $shouldStart")
        
        if (shouldStart) {
            val hasPermissions = ActivityResultUtils.checkPermissions(
                arrayOf(
                    Manifest.permission.ACTIVITY_RECOGNITION,
                    Manifest.permission.BODY_SENSORS
                )
            )
            
            LogUtil.i("📱 MainActivity: 权限状态检查 - 有权限: $hasPermissions")
            
            if (hasPermissions) {
                LogUtil.i("✅ MainActivity: 应该启动且有权限 - 开启步数服务")
                startStepService()
            } else {
                LogUtil.i("⚠️ MainActivity: 应该启动但权限不足 - 等待用户手动授权")
            }
        } else {
            LogUtil.i("❌ MainActivity: 不满足自动启动条件 - 不开启步数服务")
        }
        
        LogUtil.i("📱 MainActivity: ========== 启动权限检查完成 ==========")
    }

    /**
     * 请求步数权限并处理UI状态回调
     */
    fun requestStepPermission(
        onPermissionGranted: (() -> Unit)? = null,
        onPermissionDenied: (() -> Unit)? = null
    ) {
        val permissions = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.Q) {
            arrayOf(Manifest.permission.BODY_SENSORS)
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && Build.VERSION.SDK_INT < Build.VERSION_CODES.TIRAMISU) {
            arrayOf(
                Manifest.permission.ACTIVITY_RECOGNITION,
                Manifest.permission.BODY_SENSORS
            )
        } else {
            arrayOf(
                Manifest.permission.ACTIVITY_RECOGNITION,
                Manifest.permission.BODY_SENSORS,
                Manifest.permission.POST_NOTIFICATIONS
            )
        }

        if (ActivityResultUtils.checkPermissions(permissions)) {
            // 权限已授予
            startStepService()
            onPermissionGranted?.invoke()
            LogUtil.i("步数权限已授予")
        } else {
            //没有权限，申请权限
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                ActivityResultUtils.requestPermissions(
                    permissions = permissions,
                    onAllGranted = {
                        startStepService()
                        onPermissionGranted?.invoke()
                        LogUtil.i("步数权限申请成功")
                    },
                    onProhibited = {
                        DialogUtil.showToast(
                            activity.getString(R.string.aifits_function_error)
                        )
                        onPermissionDenied?.invoke()
                        LogUtil.i("步数权限申请被拒绝")
                    }
                )
            } else {
                // Android 9及以下版本，直接启动服务
                startStepService()
                onPermissionGranted?.invoke()
            }
        }
    }

    private fun startStepService() {
        LogUtil.i("🔧 MainActivity: ========== 开始启动步数服务 ==========")
        
        /**
         * 这里判断当前设备是否支持计步
         */
        if (StepCountCheckUtil.isSupportStepCountSensor(this)) {
            LogUtil.i("✅ MainActivity: 设备支持计步功能")
            
            LogUtil.i("📊 MainActivity: 开始获取历史记录")
            getRecordList()
            
            LogUtil.i("💾 MainActivity: 开始设置数据")
            setData()
            
            LogUtil.i("🚀 MainActivity: 开始设置混合步数追踪器")
            setupHybridStepTracking()
            
            LogUtil.i("✅ MainActivity: 步数服务启动完成")
        } else {
            LogUtil.e("❌ MainActivity: 设备不支持计步功能")
            DialogUtil.showToast(
                getString(R.string.aifits_function_error)
            )
        }
        
        LogUtil.i("🔧 MainActivity: ========== 步数服务启动结束 ==========")
    }

    /**
     * 获取全部运动历史纪录
     */
    private fun getRecordList() {
        //获取数据库
        stepEntityList.clear()
        stepEntityList.addAll(stepDataDao.getAllDatas())
        LogUtil.i("step: ${stepEntityList.toJson()}")
        if (stepEntityList.size > 7) {
            //在这里获取历史记录条数，当条数达到7条以上时，就开始删除第七天之前的数据
            for (entity in stepEntityList) {
                if (TimeUtil.isDateOutDate(entity.curDate!!)) {
                    stepDataDao?.deleteCurData(entity)
                }
            }
        }
    }

    /**
     * 设置记录数据 - 改为仅用于数据库初始化，不设置UI显示值
     */
    private fun setData() {
        val stepEntity = stepDataDao.getCurDataByDate(curSelDate)
        val dbStep = stepEntity?.steps ?: 0
        val dbCalories = stepEntity?.calories ?: 0.0
        
        // 与Google Fit数据比较，仅用于日志记录
        if (dbStep < curGoogleFitsStep) {
            LogUtil.i("Google Fit数据更大: DB=$dbStep, GoogleFit=$curGoogleFitsStep")
        }
        
        LogUtil.i("step :setData 数据库数据: $stepEntity, 不直接设置curStep，等待HybridStepTracker初始化")
    }

    /**
     * 设置混合步数追踪器
     */
    private fun setupHybridStepTracking() {
        try {
            LogUtil.i("MainActivity: setup HybridStepTracker")
            
            // 初始化混合追踪器
            hybridStepTracker = HybridStepTracker(this) { steps, calories ->
                // 实时更新UI
                curStep = steps
                curCaloriesBurnedRecord = calories
                LogUtil.d("HybridStep: 更新UI - 步数:$steps, 卡路里:$calories")
            }
            
            // 检查传感器可用性
            if (!hybridStepTracker.isSensorAvailable()) {
                LogUtil.w("MainActivity: 步数传感器不可用")
                DialogUtil.showToast(getString(R.string.aifits_function_error))
                return
            }
            
            // 启动后台同步WorkManager
            setupBackgroundSync()
            
            // 加载最新数据
            hybridStepTracker.loadLatestData()
            
            LogUtil.i("MainActivity: HybridStepTracker 设置完成")
        } catch (e: Exception) {
            LogUtil.e("MainActivity: setup HybridStepTracker error: ${e.message}")
        }
    }
    
    /**
     * 设置后台同步
     */
    private fun setupBackgroundSync() {
        val syncWorkRequest = PeriodicWorkRequestBuilder<StepSyncWorker>(
            15, TimeUnit.MINUTES // 每15分钟同步一次（最短间隔）
        ).setConstraints(
            Constraints.Builder()
                .setRequiredNetworkType(NetworkType.NOT_REQUIRED) // 不需要网络
                .setRequiresBatteryNotLow(false) // 不需要电量充足
                .setRequiresCharging(false) // 不需要充电状态
                .setRequiresDeviceIdle(false) // 不需要设备空闲
                .build()
        ).build()
        
        WorkManager.getInstance(this).enqueueUniquePeriodicWork(
            "step_sync_work",
            ExistingPeriodicWorkPolicy.REPLACE, // 替换现有任务以确保使用新配置
            syncWorkRequest
        )
        
        LogUtil.i("MainActivity: 后台同步任务已启动（每15分钟执行一次）")
    }
    
    /**
     * 更新步数UI - 统一数据源，只从HybridStepTracker读取
     */
    private fun updateStepUI() {
        if (::hybridStepTracker.isInitialized) {
            val hybridSteps = hybridStepTracker.getCurrentSteps()
            val hybridCalories = hybridStepTracker.getCurrentCalories()
            
            // 统一数据源：只使用HybridStepTracker的数据
            curStep = hybridSteps
            curCaloriesBurnedRecord = hybridCalories
            
            LogUtil.d("MainActivity: 更新UI(统一数据源) - 步数:$curStep, 卡路里:$curCaloriesBurnedRecord")
        } else {
            // 如果HybridStepTracker未初始化，从数据库读取作为备用
            val stepEntity = stepDataDao.getCurDataByDate(curSelDate)
            curStep = stepEntity?.steps ?: 0
            curCaloriesBurnedRecord = stepEntity?.calories ?: 0.0
            LogUtil.d("MainActivity: 更新UI(备用数据源) - 步数:$curStep, 卡路里:$curCaloriesBurnedRecord")
        }
    }

    override fun handleMessage(msg: Message): Boolean {
        // 已由HybridStepTracker处理步数数据，保留接口以便兼容
        LogUtil.d("MainActivity: handleMessage - 使用HybridStepTracker替代Service")
        return false
    }


    /**
     * 用于查询应用服务（application Service）的状态的一种interface，
     * 更详细的信息可以参考Service 和 context.bindService()中的描述，
     * 和许多来自系统的回调方式一样，ServiceConnection的方法都是进程的主线程中调用的。
     */
    val conn = object : ServiceConnection {
        /**
         * 在建立起于Service的连接时会调用该方法，目前Android是通过IBind机制实现与服务的连接。
         * @param name 实际所连接到的Service组件名称
         * @param service 服务的通信信道的IBind，可以通过Service访问对应服务
         */
        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            timerTask = object : TimerTask() {
                override fun run() {
                    try {
                        messenger = Messenger(service)
                        val msg = Message.obtain(null, ConstantData.MSG_FROM_CLIENT)
                        msg.replyTo = mGetReplyMessenger
                        messenger!!.send(msg)
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }
            }
            timer = Timer()
            timer!!.schedule(timerTask, 0, 500)
        }

        /**
         * 当与Service之间的连接丢失的时候会调用该方法，
         * 这种情况经常发生在Service所在的进程崩溃或者被Kill的时候调用，
         * 此方法不会移除与Service的连接，当服务重新启动的时候仍然会调用 onServiceConnected()。
         * @param name 丢失连接的组件名称
         */
        override fun onServiceDisconnected(name: ComponentName) {

        }
    }

}

@Composable
private fun MainScreenPage(
    startDestination: String,
    requestCameraPermissionEvent: () -> Unit
) {
    NavHost(
        navController = navController,
        startDestination = startDestination,

        // 进入过渡动画，在目的地出现时播放。
        // 根据初始状态和当前目的地的路径来决定动画效果。
        // 如果当前目的地是Splash页面，则使用fadeIn将其淡入,一个持续时间为0毫秒的动画，即立即将目的地淡入。
        // 进入时立即显示Splash页面。
        // 否则，使用slideIntoContainer将其滑入容器。使用了tween函数来定义一个持续时间为300毫秒的动画。
        // 它会将目的地从左边滑入容器的当前位置。
        enterTransition = {
            if (initialState.destination.route == Screen.Splash.route) {
                fadeIn(tween(0))
            } else {
                slideIntoContainer(
                    AnimatedContentTransitionScope.SlideDirection.Companion.Left,
                    animationSpec = tween(300)
                )
            }
        },
        // 退出过渡动画，在目的地消失时播放。
        // 同样根据初始状态和当前目的地的路径来决定动画效果。
        // 如果当前目的地是Splash页面，则使用fadeOut将其淡出；
        // 否则，使用slideOutOfContainer将其滑出容器。
        exitTransition = {
            if (initialState.destination.route == Screen.Splash.route) {
                fadeOut(tween(0))
            } else {
                slideOutOfContainer(
                    AnimatedContentTransitionScope.SlideDirection.Left,
                    animationSpec = tween(300)
                )
            }
        },
        // 返回过渡动画，在返回到上一个目的地时播放。
        // 根据初始状态和当前目的地的路径来决定动画效果。
        // 如果当前目的地是Splash页面，则使用fadeIn将其淡入；
        // 否则，使用slideIntoContainer将其滑入容器的右边。
        popEnterTransition = {
            if (initialState.destination.route == Screen.Splash.route) {
                fadeIn(tween(0))
            } else {
                slideIntoContainer(
                    AnimatedContentTransitionScope.SlideDirection.Right,
                    animationSpec = tween(300)
                )
            }
//            fadeIn(tween(0))
        },
        // 后退时过渡动画。
        // 根据初始状态和当前目的地的路径来决定动画效果。
        // 如果当前目的地是Splash页面，则使用fadeOut将其淡出；使用了tween函数来定义一个持续时间为0毫秒的动画，
        // 即立即将目的地淡出。在后退时立即隐藏Splash页面。
        // 否则，使用slideIntoContainer将其滑入容器的右边。 使用了tween函数来定义一个持续时间为300毫秒的动画。
        // 它会将目的地从容器的当前位置向右滑出。
        popExitTransition = {
            if (initialState.destination.route == Screen.Splash.route) {
                fadeOut(tween(0))
            } else {
                slideOutOfContainer(
                    AnimatedContentTransitionScope.SlideDirection.Right,
                    animationSpec = tween(300)
                )
            }
        }
    ) {
        Route(navController, requestCameraPermissionEvent)
    }
}