package org.aihealth.ineck.model

object SPConstant {
    /**
     * 应用是否第一次启动
     */
    const val IS_FIRST_START = "isfirststart"

    const val IS_FIRST_READ_AGREEMENT = "isfirstreadagreement"


    /**
     * 是否是国内服务器
     */
    const val IS_IN_CHINA = "isinchina"

    /**
     * 应用语言
     */
    const val LAUGUAGE = "lauguage"


    /**
     * 用户token
     */
    const val TOKEN = "token"

    /**
     * 第一次请求蓝牙权限
     */
    const val IS_FIRST_REQUEST_BLUETOOTH_PERMISSION = "isfirstrequestbluetoothpermission"

    /**
     * 第一次请求摄像机
     */
    const val IS_FIRST_REQUEST_CAMERA_PERMISSION = "isfirstrequestcamerapermission"

    /**
     * 是否连接过设备
     */
    const val IS_HAS_CONNECTED = "ishasconnected"

    const val DEVICE_STEP = "devicestep"

    const val DEVICE_NAME = "devicename"

    const val DOCTOR_UUID = "doctorUuid"

    /**
     * FCM token
     */
    const val FCM_TOKEN = "fcm_token"
    
    /**
     * 步数模块相关常量
     */
    const val USE_LOCAL_STEP_DATA = "use_local_step_data"
    const val STEP_MANUALLY_DISABLED = "step_manually_disabled"
    const val STEP_AUTO_START_ENABLED = "step_auto_start_enabled"
}