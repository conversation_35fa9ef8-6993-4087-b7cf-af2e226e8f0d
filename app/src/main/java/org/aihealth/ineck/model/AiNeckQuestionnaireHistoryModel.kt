package org.aihealth.ineck.model

import com.google.gson.annotations.SerializedName


data class AiNeckQuestionnaireHistoryModel(

    @SerializedName("datetime") val dateTime: String,
    @SerializedName("duration") val duration: String,
    @SerializedName("health_status") val healthStatus: String,
    @SerializedName("info") val info: String,
)

data class AiNeckQuestionnaireHistoryDetailModel(
    @SerializedName("duration") val duration: Int,
    @SerializedName("questionnaire_id") val id: String,
    @SerializedName("questions") val questions: List<AiNeckQuestionnaireHistoryDetailItem>,
)

data class AiNeckQuestionnaireHistoryDetailItem(
    @SerializedName("question_id") val questionId: String,
    @SerializedName("answer") val answer: List<String>,
)