package org.aihealth.ineck.model.angles

import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.viewmodel.user

@Parcelize
data class Promis(
    val uuid: String = user.uuid,
    var average_pain: Int = 1,
    var health: Int = 1,
    var life: Int = 1,
    var physical: Int = 1,
    var mental: Int = 1,
    var social: Int = 1,
    var behave: Int = 1,
    var sport: Int = 1,
    var sentiment: Int = 1,
    var fatigue: Int = 1
): BaseTime()
