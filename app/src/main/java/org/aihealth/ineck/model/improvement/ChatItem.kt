package org.aihealth.ineck.model.improvement

import com.google.gson.annotations.SerializedName

data class ChatItem(
    @SerializedName("uuid") val uuid: String = "",
    @SerializedName("photo") val avatar: String? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("msg") val lastMessage: String? = null,
    @SerializedName("datetime") val time: String = "",
    @SerializedName("status") val unread: Int = 0
) {
    val displayName: String
        get() = name ?: "Unknown User"
    
    val displayMessage: String
        get() = lastMessage ?: ""
    
    val displayAvatar: String
        get() = avatar ?: ""
}