package org.aihealth.ineck.model

import com.google.gson.annotations.SerializedName

/**
 * 吊起微信支付所需参数
 */
data class WeChatResult(
    @SerializedName("prepay_id")
    val preId:String,
    @SerializedName("noncestr")
    val nonceStr:String,
    @SerializedName("timestamp")
    val timeStamp:String,
    @SerializedName("sign")
    val sign :String,
    @SerializedName("out_trade_no")
    val outTradeNo:String,

)
data class PayResult(
    @SerializedName("payment_status")
    val paymentStatus:Boolean
)