package org.aihealth.ineck.model.improvement

import com.google.gson.annotations.SerializedName

/**
 *  用户已练习改善方案数据单元
 *  @property   actualDurationOfIndividual  用户实际持续练习时长
 *  @property   datetime    上一次练习事件
 *  @property   duration    该改善项目单次全程持续时长
 *  @property   durationOfIndividual    用户非实际总体练习时长
 *  @property   materialId  素材ID
 *  @property   numberOfIndividual  个人练习次数
 *  @property   numberOfPeople      已练习人数
 *  @property   score   分数
 *  @property   title   改善项目标题
 *
 */
data class ImproveProgramUserExercise(
    @SerializedName("actual_duration_of_individual")
    val actualDurationOfIndividual: Int,
    @SerializedName("datetime")
    val datetime: Long,
    @SerializedName("duration")
    val duration: Int,
    @SerializedName("duration_of_individual")
    val durationOfIndividual: Int,
    @SerializedName("material_id")
    val materialId: Int,
    @SerializedName("number_of_individual")
    val numberOfIndividual: Int,
    @SerializedName("number_of_people")
    val numberOfPeople: Int,
    @SerializedName("score")
    val score: Int,
    @SerializedName("title")
    val title: String
)

/**
 *  改善页用户练习数据请求加载状态
 */
sealed class ImproveProgramUserExerciseLoadState() {
    object  InitLoading: ImproveProgramUserExerciseLoadState()                                   /* 初始化加载中 */
    object  Loading: ImproveProgramUserExerciseLoadState()                                     /* 非初始化加载 */
    class   Failure(val error: Exception, val message: String = ""): ImproveProgramUserExerciseLoadState()                 /* 加载错误 */
    class   Success(val dataList: List<ImproveProgramUserExercise>): ImproveProgramUserExerciseLoadState()       /* 加载成功 */
    object  EmptyData: ImproveProgramUserExerciseLoadState()        /* 空数据， 用户无任何练习数据 */
}
