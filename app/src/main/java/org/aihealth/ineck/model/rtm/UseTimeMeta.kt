package org.aihealth.ineck.model.rtm

data class UseTimeMeta(
    val fromUser:String,
    val toUser: String,
    val startTime: String,
    val endTime: String,
    val timeSpent: Int,
    val type: Int =UseTimeType.USER_CALl_UNCONNECT.value
)

enum class UseTimeType(val value: Int) {
    REPORT(1),
    CHAT(2),
    MEETING(3),
    PROVIDER_CALl_CONNECT(4),
    PROVIDER_CALl_UNCONNECT(5),
    USER_CALl_CONNECT(6),
    USER_CALl_UNCONNECT(7),

}