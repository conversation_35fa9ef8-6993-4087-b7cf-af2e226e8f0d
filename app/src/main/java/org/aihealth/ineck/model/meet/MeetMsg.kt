package org.aihealth.ineck.model.meet

import com.google.gson.annotations.SerializedName

data class MeetMsg(
    @SerializedName("creator") val creatorUuid: String,
    @SerializedName("invitee") val inviteeUuid: String,
    @SerializedName("time") val time: String,
    @SerializedName("meeting_name") val meetingName: String,
    @SerializedName("tpc") val tpc: String,
    @SerializedName("creator_name") val creatorName: String,
    @SerializedName("invitee_name") val inviteeName: String
)

/**
 *  用户会议列表请求加载状态
 */
sealed class MeetingListLoadState() {
    object  InitLoading: MeetingListLoadState()                                   /* 初始化加载中 */
    object  Loading: MeetingListLoadState()                                     /* 非初始化加载 */
    class   Failure(val error: Exception, val message: String = ""): MeetingListLoadState()                 /* 加载错误 */
    class   Success(val dataList: List<MeetMsg>): MeetingListLoadState()       /* 加载成功 */
    object  EmptyData: MeetingListLoadState()        /* 空数据， 用户无任何练习数据 */
}
