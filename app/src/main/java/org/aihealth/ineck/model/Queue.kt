package org.aihealth.ineck.model

/**
 *  队列
 */
class Queue<T>() {
    /**
     *  @param  ratedCapacity   额定容量
     */
    constructor(ratedCapacity: Int) : this() {
        this.ratedCapacity = ratedCapacity
        this.isRated = true
    }
    /** 当前队列是否拥有额定容量 */
    var isRated: Boolean = false
    /** 额定容量的值 */
    var ratedCapacity: Int = 0
    /** 元素内容列表 */
    val elements = mutableListOf<T>()

    /** 入队列 */
    fun enqueue(element: T) {
        if (isRated) {
            while (this.size() >= ratedCapacity) {
                this.elements.removeAt(0)
            }
        }
        elements.add(element)
    }

    /** 出队列 */
    fun dequeue(): T? {
        if (isEmpty()) return null

        return elements.removeAt(0)
    }

    fun peek(): T? {
        if (isEmpty()) return null

        return elements[0]
    }

    fun size(): Int {
        return elements.size
    }

    fun isEmpty(): Boolean {
        return elements.isEmpty()
    }

    fun last(): T {
        return elements.last()
    }
}

fun Queue<AccelerometerParam>.toList(): List<AccelerometerParam> {
    return this.elements.toList()
}