package org.aihealth.ineck.model.splash

import android.annotation.SuppressLint
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import org.aihealth.ineck.R


data class Page(@DrawableRes val image: Int, @StringRes val text: Int)

@SuppressLint("ResourceType")
val pages = listOf(
    Page(R.drawable.splash1, R.string.monitoring_cervical_vertebra),
    Page(R.drawable.splash2, R.string.monitoring_cervical_vertebra),
    Page(R.drawable.splash3, R.string.monitoring_lumbar),
    Page(R.drawable.splash4, R.string.monitoring_lumbar),
)