package org.aihealth.ineck.model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import org.aihealth.ineck.util.DeviceConfigManager
import org.aihealth.ineck.util.LogUtil

/**
 *  设备配置信息
 */
class DeviceConfig {
    // 设备名称
    var name: String = ""

    // 设备mac地址
    var mac: String = ""
    // 设备版本号
    var version = ""
    // 设备序列号
    var sn = ""
    // 设备类型
    var deviceType by mutableStateOf(DeviceType.None)
    // 是否开启振动
    var isVibration by mutableStateOf(true)
    // 振动角度
    var vibrationAngle by mutableIntStateOf(15)
    // 震动强度 0-14
    var vibrationIntensity by mutableIntStateOf(12)
    // 提醒间隔(单位: 秒)
    var vibrationFrequency by mutableIntStateOf(10)
    // 寻找设备（无限振动）
    var findDevice by mutableStateOf(false)
    // 最大角度（校准后的角度）
    var maxAngle by mutableIntStateOf(0)
    // 是否已经校准
    var isCalibrated by mutableStateOf(false)


    /**
     * 保存设备配置到本地
     */
    fun saveToLocal() {
        LogUtil.i("DeviceConfig saveToLocal: $this")
        
        // 使用DeviceConfigManager保存数据
        DeviceConfigManager.saveDeviceConfig(this)
    }
    
    /**
     * 保存校准角度（仅在校准完成后调用）
     */
    fun saveCalibrationAngle(angle: Int) {
        this.maxAngle = angle
        this.isCalibrated = true
        
        // 使用DeviceConfigManager保存校准角度
        DeviceConfigManager.saveCalibrationAngle(deviceType, angle)
    }

    companion object {
        /**
         * 从DeviceConfigManager获取设备配置流
         */
        fun getDeviceConfigFlow(type: DeviceType) = DeviceConfigManager.getDeviceConfigFlow(type)
        
        /**
         * 同步获取设备配置（从缓存）
         */
        fun getDeviceConfig(type: DeviceType): DeviceConfig {
            return DeviceConfigManager.getDeviceConfig(type)
        }
    }

    override fun toString(): String {
        return "DeviceConfig: name=$name, mac=$mac, version=$version, sn=$sn, deviceType=$deviceType, isVibration=$isVibration, vibrationAngle=$vibrationAngle, vibrationIntensity=$vibrationIntensity, vibrationFrequency=$vibrationFrequency, findDevice=$findDevice, maxAngle=$maxAngle, isCalibrated=$isCalibrated"
    }
}

/**
 * 创建DeviceConfig副本
 */
fun DeviceConfig.copy(): DeviceConfig {
    return DeviceConfig().apply {
        name = <EMAIL>
        mac = <EMAIL>
        version = <EMAIL>
        sn = <EMAIL>
        deviceType = <EMAIL>
        isVibration = <EMAIL>
        vibrationAngle = <EMAIL>
        vibrationIntensity = <EMAIL>
        vibrationFrequency = <EMAIL>
        findDevice = <EMAIL>
        maxAngle = <EMAIL>
        isCalibrated = <EMAIL>
    }
}
