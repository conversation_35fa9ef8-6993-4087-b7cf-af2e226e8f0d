package org.aihealth.ineck.model.vitalsigns

import com.google.gson.annotations.SerializedName

/**
 *  血氧水平
 */
data class BloodOxygen(
    /* 饱和度 */
    @SerializedName("saturation")
    val saturation: Float
)

/**
 *  血氧水平 - 历史数据单元
 */
data class BloodOxygenHistoryUnit(
    @SerializedName("saturation")
    val saturation: Float,
    @SerializedName("uuid")
    val uuid: String,
    @SerializedName("date_time")
    override val dateTime: String
): VitalSigns