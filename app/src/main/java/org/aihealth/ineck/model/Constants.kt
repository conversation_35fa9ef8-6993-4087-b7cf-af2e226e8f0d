package org.aihealth.ineck.model

object Constants {
    const val LAST_FINISHED_NECK_QUESTIONNAIRE = "last_finished_neck_questionnaire"

    const val LAST_FINISHED_BACK_QUESTIONNAIRE = "last_finished_back_questionnaire"

    const val DAY_7 = 7 * 24 * 60 * 60 * 1000L

    const val HEIGHT_CM_MIN_LIMIT = 0.0
    const val HEIGHT_CM_MAX_LIMIT = 300.00
    const val WEIGHT_KG_MIN_LIMIT = 0.00
    const val WEIGHT_KG_MAX_LIMIT = 500.00

    const val WEIGHT_LB_MIN_LIMIT = 0.0
    const val WEIGHT_LB_MAX_LIMIT = 1100.00
}