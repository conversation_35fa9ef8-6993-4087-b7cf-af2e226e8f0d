package org.aihealth.ineck.model.vitalsigns

import com.google.gson.annotations.SerializedName

/**
 *  血压水平
 */
data class BloodPressure(
    /* 舒张压 */
    @SerializedName("diastolic")
    val diastolic: Int,
    /* 收缩压 */
    @SerializedName("systolic")
    val systolic: Int,
)

/**
 *  血压水平
 */
data class BloodPressureHistoryUnit(
    /* 舒张压 */
    @SerializedName("diastolic")
    val diastolic: Int,
    /* 收缩压 */
    @SerializedName("systolic")
    val systolic: Int,
    @SerializedName("uuid")
    val uuid: String,
    @SerializedName("date_time")
    override val dateTime: String
): VitalSigns