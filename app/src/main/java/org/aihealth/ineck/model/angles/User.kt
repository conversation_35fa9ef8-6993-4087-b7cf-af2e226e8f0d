package org.aihealth.ineck.model.angles

import com.google.gson.annotations.SerializedName
import java.io.Serializable


data class User(
    // 非字符串字段保持原样
    var height: Double = 0.0,
    var weight: Double = 0.0, // in metric

    @SerializedName("membership_id")
    var membershipId: Int = -1, //会员ID-membership_id: -1（普通），0（试用），1（会员），2（银卡），3（金卡）；
    @SerializedName("vip_start_date")
    var vipStartDate: Long = 0L,//续费时间："vip_start_date": **********；
    @SerializedName("vip_end_date")
    var vipEndDate: Long = 0L, //到期时间："vip_end_date": **********；
    @SerializedName("vip_active_time")
    var vipActiveTime: Long = 0L, //剩余时间："vip_active_time": 604800,
    @SerializedName("vip_status")
    var vipStatus: Boolean = false, //会员状态："vip_status": "yes", "no"
    @SerializedName("member_trial")
    var memberTrial: Boolean = false, // 能不能试用
    @SerializedName("followers_number")
    var followersNumber: Int = 0,
    @SerializedName("following_number")
    var followingNumber: Int = 0,
    @SerializedName("article_views")
    var articleViews: Int = 0,
    @SerializedName("certification_status")
    var certificationStatus: Boolean = false,
    @SerializedName("device_status")
    var deviceStatus: Boolean = false,
    @SerializedName("disease_type")
    var diseaseType: List<String> = listOf(),
    var isPersonalizeRecommendation: Boolean = true,
    var tags: List<String> = listOf(),
    var preferences: Preferences = Preferences(),
    @SerializedName("back_questionnaire")
    var backQuestionnaire: Int = 0, // 0: 没填写问卷, 1: 填写过问卷
    @SerializedName("neck_questionnaire")
    var neckQuestionnaire: Int = 0, // 0: 没填写问卷, 1: 填写过问卷
    ) : Serializable {
    
    var age: Int = 0
    
    // 私有字段用于 JSON 反序列化，允许为 null
    @SerializedName("uuid")
    private var _uuid: String? = null
    
    @SerializedName("email")
    private var _email: String? = null
    
    @SerializedName("photo")
    private var _photo: String? = null
    
    @SerializedName("name")
    private var _name: String? = null
    
    @SerializedName("gender")
    private var _gender: String? = null
    
    @SerializedName("phone")
    private var _phone: String? = null
    
    @SerializedName("membership_name")
    private var _membershipName: String? = null
    
    @SerializedName("birthday")
    private var _birthday: String? = null
    
    @SerializedName("hospital")
    private var _hospital: String? = null
    
    @SerializedName("hospital_level")
    private var _hospitalLevel: String? = null
    
    @SerializedName("doctor_title")
    private var _doctorTitle: String? = null
    
    @SerializedName("location")
    private var _location: String? = null
    
    @SerializedName("device_token")
    private var _deviceToken: String? = null
    
    @SerializedName("device_platform")
    private var _devicePlatform: String? = null
    
    @SerializedName("birthdate")
    private var _birthdate: String? = null
    
    @SerializedName("user_id")
    private var _userId: String? = null
    
    @SerializedName("address")
    private var _address: String? = null
    
    @SerializedName("ssn")
    private var _ssn: String? = null
    
    @SerializedName("medical_history")
    private var _medicalHistory: String? = null
    
    // 公共属性，永远不会返回 null
    var uuid: String
        get() = _uuid ?: ""
        set(value) { _uuid = value.takeIf { it.isNotEmpty() } }
    
    var email: String                  //email
        get() = _email ?: ""
        set(value) { _email = value.takeIf { it.isNotEmpty() } }
    
    var photo: String
        get() = _photo ?: ""
        set(value) { _photo = value.takeIf { it.isNotEmpty() } }
    
    var name: String                   //name
        get() = _name ?: ""
        set(value) { _name = value.takeIf { it.isNotEmpty() } }
    
    var gender: String                // Male, Female, Other
        get() = _gender ?: "U"
        set(value) { _gender = value.takeIf { it.isNotEmpty() } }
    
    var phone: String
        get() = _phone ?: ""
        set(value) { _phone = value.takeIf { it.isNotEmpty() } }
    
    var membershipName: String //会员标识符-membership_name:"free", "probation", "vip", "silver", "Gold"
        get() = _membershipName ?: ""
        set(value) { _membershipName = value.takeIf { it.isNotEmpty() } }
    
    var birthday: String
        get() = _birthday ?: ""
        set(value) { _birthday = value.takeIf { it.isNotEmpty() } }
    
    var hospital: String
        get() = _hospital ?: ""
        set(value) { _hospital = value.takeIf { it.isNotEmpty() } }
    
    var hospitalLevel: String
        get() = _hospitalLevel ?: ""
        set(value) { _hospitalLevel = value.takeIf { it.isNotEmpty() } }
    
    var doctorTitle: String
        get() = _doctorTitle ?: ""
        set(value) { _doctorTitle = value.takeIf { it.isNotEmpty() } }
    
    var location: String
        get() = _location ?: ""
        set(value) { _location = value.takeIf { it.isNotEmpty() } }
    
    var deviceToken: String
        get() = _deviceToken ?: ""
        set(value) { _deviceToken = value.takeIf { it.isNotEmpty() } }
    
    var devicePlatform: String
        get() = _devicePlatform ?: ""
        set(value) { _devicePlatform = value.takeIf { it.isNotEmpty() } }
    
    var birthdate: String
        get() = _birthdate ?: ""
        set(value) { _birthdate = value.takeIf { it.isNotEmpty() } }
    
    var userId: String
        get() = _userId ?: ""
        set(value) { _userId = value.takeIf { it.isNotEmpty() } }
    
    var address: String
        get() = _address ?: ""
        set(value) { _address = value.takeIf { it.isNotEmpty() } }
    
    var ssn: String
        get() = _ssn ?: ""
        set(value) { _ssn = value.takeIf { it.isNotEmpty() } }
    
    var medicalHistory: String
        get() = _medicalHistory ?: ""
        set(value) { _medicalHistory = value.takeIf { it.isNotEmpty() } }
    fun copy(): User {
        return User(
            height = this.height,
            weight = this.weight,
            membershipId = this.membershipId,
            vipStartDate = this.vipStartDate,
            vipEndDate = this.vipEndDate,
            vipActiveTime = this.vipActiveTime,
            vipStatus = this.vipStatus,
            memberTrial = this.memberTrial,
            followersNumber = this.followersNumber,
            followingNumber = this.followingNumber,
            articleViews = this.articleViews,
            certificationStatus = this.certificationStatus,
            deviceStatus = this.deviceStatus,
            diseaseType = this.diseaseType,
            isPersonalizeRecommendation = this.isPersonalizeRecommendation,
            tags = this.tags,
            preferences = this.preferences,
            backQuestionnaire = this.backQuestionnaire,
            neckQuestionnaire = this.neckQuestionnaire
        ).apply {
            // 复制所有字符串字段
            this.age = <EMAIL>
            this.uuid = <EMAIL>
            this.email = <EMAIL>
            this.photo = <EMAIL>
            this.name = <EMAIL>
            this.gender = <EMAIL>
            this.phone = <EMAIL>
            this.membershipName = <EMAIL>
            this.birthday = <EMAIL>
            this.hospital = <EMAIL>
            this.hospitalLevel = <EMAIL>
            this.doctorTitle = <EMAIL>
            this.location = <EMAIL>
            this.deviceToken = <EMAIL>
            this.devicePlatform = <EMAIL>
            this.birthdate = <EMAIL>
            this.userId = <EMAIL>
            this.address = <EMAIL>
            this.ssn = <EMAIL>
            this.medicalHistory = <EMAIL>
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as User

        // 比较构造函数参数
        if (height != other.height) return false
        if (weight != other.weight) return false
        if (membershipId != other.membershipId) return false
        if (vipStartDate != other.vipStartDate) return false
        if (vipEndDate != other.vipEndDate) return false
        if (vipActiveTime != other.vipActiveTime) return false
        if (vipStatus != other.vipStatus) return false
        if (memberTrial != other.memberTrial) return false
        if (followersNumber != other.followersNumber) return false
        if (followingNumber != other.followingNumber) return false
        if (articleViews != other.articleViews) return false
        if (certificationStatus != other.certificationStatus) return false
        if (deviceStatus != other.deviceStatus) return false
        if (diseaseType != other.diseaseType) return false
        if (isPersonalizeRecommendation != other.isPersonalizeRecommendation) return false
        if (tags != other.tags) return false
        if (preferences != other.preferences) return false
        if (backQuestionnaire != other.backQuestionnaire) return false
        if (neckQuestionnaire != other.neckQuestionnaire) return false
        
        // 比较类体内的属性
        if (age != other.age) return false
        if (uuid != other.uuid) return false
        if (email != other.email) return false
        if (photo != other.photo) return false
        if (name != other.name) return false
        if (gender != other.gender) return false
        if (phone != other.phone) return false
        if (membershipName != other.membershipName) return false
        if (birthday != other.birthday) return false
        if (hospital != other.hospital) return false
        if (hospitalLevel != other.hospitalLevel) return false
        if (doctorTitle != other.doctorTitle) return false
        if (location != other.location) return false
        if (deviceToken != other.deviceToken) return false
        if (devicePlatform != other.devicePlatform) return false
        if (birthdate != other.birthdate) return false
        if (userId != other.userId) return false
        if (address != other.address) return false
        if (ssn != other.ssn) return false
        if (medicalHistory != other.medicalHistory) return false
        
        return true
    }

    override fun hashCode(): Int {
        var result = height.hashCode()
        result = 31 * result + weight.hashCode()
        result = 31 * result + membershipId
        result = 31 * result + vipStartDate.hashCode()
        result = 31 * result + vipEndDate.hashCode()
        result = 31 * result + vipActiveTime.hashCode()
        result = 31 * result + vipStatus.hashCode()
        result = 31 * result + memberTrial.hashCode()
        result = 31 * result + followersNumber
        result = 31 * result + followingNumber
        result = 31 * result + articleViews
        result = 31 * result + certificationStatus.hashCode()
        result = 31 * result + deviceStatus.hashCode()
        result = 31 * result + diseaseType.hashCode()
        result = 31 * result + isPersonalizeRecommendation.hashCode()
        result = 31 * result + tags.hashCode()
        result = 31 * result + preferences.hashCode()
        result = 31 * result + backQuestionnaire
        result = 31 * result + neckQuestionnaire
        result = 31 * result + age
        result = 31 * result + uuid.hashCode()
        result = 31 * result + email.hashCode()
        result = 31 * result + photo.hashCode()
        result = 31 * result + name.hashCode()
        result = 31 * result + gender.hashCode()
        result = 31 * result + phone.hashCode()
        result = 31 * result + membershipName.hashCode()
        result = 31 * result + birthday.hashCode()
        result = 31 * result + hospital.hashCode()
        result = 31 * result + hospitalLevel.hashCode()
        result = 31 * result + doctorTitle.hashCode()
        result = 31 * result + location.hashCode()
        result = 31 * result + deviceToken.hashCode()
        result = 31 * result + devicePlatform.hashCode()
        result = 31 * result + birthdate.hashCode()
        result = 31 * result + userId.hashCode()
        result = 31 * result + address.hashCode()
        result = 31 * result + ssn.hashCode()
        result = 31 * result + medicalHistory.hashCode()
        return result
    }
}

data class Preferences(
    @SerializedName("blood_sugar_unit")
    var bloodSugarUnit: String = "I",
    @SerializedName("height_unit")
    var heightUnit: String = "I",
    @SerializedName("temperature_unit")
    var temperatureUnit: String = "I",
    @SerializedName("weight_unit")
    var weightUnit: String = "I",
)