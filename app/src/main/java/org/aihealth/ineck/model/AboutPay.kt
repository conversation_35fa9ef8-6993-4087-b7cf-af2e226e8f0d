package org.aihealth.ineck.model

import androidx.annotation.DrawableRes
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
data class GoodItem(

    val goodIcon:Int,
    val goodName:String,
    val goodSalePrice:String,
    val goodPrice:String,
)

data class PayWayItem(
    @DrawableRes val payIcon:Int,
    var payName:String,
){
    var isCheck by mutableStateOf(false)
}