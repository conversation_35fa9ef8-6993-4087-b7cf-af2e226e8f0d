package org.aihealth.ineck.model.angles

import org.aihealth.ineck.viewmodel.user

data class BackPain(
    var uuid: String = user.uuid,
    // 背部疼痛平均值
    var back: Int = 0,
    // 1 正常，0 失衡
    var balance: Int = 1,
    // 腿部疼痛平均值
    var leg: Int = 0,
    // 肌肉疼痛平均值
    var muscle: Int = 0,
    // 1 没有麻木，0 是麻木
    var numb: Int = 1,
)

data class BackPainRecord(
// 背部疼痛平均值
    var back: Int = 0,
// 腿部疼痛平均值
    var leg: Int = 0
)

data class NeuralRecord(
    // 肌肉疼痛平均值
    var muscle: Int = 0,
    // 1 没有麻木，0 是麻木
    var numb: Int = 1,
    // 1 正常，0 失衡
    var balance: Int = 1
)