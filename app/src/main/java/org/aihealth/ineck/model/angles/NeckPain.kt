package org.aihealth.ineck.model.angles

import com.google.gson.annotations.SerializedName
import org.aihealth.ineck.viewmodel.user

data class NeckPain(
    @SerializedName("uuid")
    var uuid: String = user.uuid,
    // 1 正常，0 失衡
    @SerializedName("balance")
    var balance: Int = 1,
    // 手疼痛平均值
    @SerializedName("hand")
    var hand: Int = 0,
    // 肌肉疼痛平均值
    @SerializedName("muscle")
    var muscle: Int = 0,
    // 颈部疼痛平均值
    @SerializedName("neck")
    var neck: Int = 0,
    // 1 没有麻木，0 是麻木
    @SerializedName("numb")
    var numb: Int = 1
)
data class NeckPainRecord(
    // 手疼痛平均值
    var hand: Int = 0,
    // 颈部疼痛平均值
    var neck: Int = 0
)
