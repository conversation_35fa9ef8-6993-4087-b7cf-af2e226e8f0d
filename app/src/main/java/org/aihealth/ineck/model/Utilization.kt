package org.aihealth.ineck.model

import com.google.gson.annotations.SerializedName

/**
 * 上传用户app使用情况数据返回结果
 */
data class PostUtilizationResult(
    @SerializedName("result")
    val result: UtilizationResult
)

data class UtilizationResult(
    @SerializedName("\$oid")
    val oid: String
)

data class Utilization(
    @SerializedName("sessionId")
    val sessionId: String = "0001",
    @SerializedName("activityName")
    val activityName: String = "global",
    @SerializedName("startTime")
    val startTime: String,
    @SerializedName("endTime")
    val endTime: String,
    @SerializedName("timeSpent")
    val timeSpent: Long
)

data class TimeCost(
    @SerializedName("exercise_duration")
    val exerciseDuration: Long,
    @SerializedName("usage_duration")
    val usageDuration: Long
)