package org.aihealth.ineck.model.vitalsigns

import com.google.gson.annotations.SerializedName

/**
 *  血糖水平
 */
data class BloodGlucose(
    @SerializedName("level")
    val level: Float
)

/**
 *  血糖水平 - 历史数据单元
 */
data class BloodGlucoseHistoryUnit(
    @SerializedName("level")
    val level: Float,
    @SerializedName("uuid")
    val uuid: String? = null,
    @SerializedName("date_time")
    override val dateTime: String,
    @SerializedName("datetime")
    val timestamp: Long? = null,
    @SerializedName("data_sources")
    val dataSources: Int? = null,
    @SerializedName("time_type")
    val timeType: Int? = null,
    @SerializedName("type")
    val type: Int? = null
) : VitalSigns