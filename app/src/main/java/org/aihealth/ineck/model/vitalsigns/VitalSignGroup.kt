package org.aihealth.ineck.model.vitalsigns

import com.google.gson.annotations.SerializedName

/**
 *  生命体征数据组
 */
data class VitalSignGroup(
    /* 血糖 */
    @SerializedName("blood_glucose")
    val bloodGlucose: BloodGlucose,
    /* 血压 */
    @SerializedName("blood_pressure")
    val bloodPressure: BloodPressure,
    /* 血氧 */
    @SerializedName("blood_oxygen")
    val bloodOxygen: BloodOxygen,
    /* 体温 */
    @SerializedName("body_temperature")
    val bodyTemperature: BodyTemperature,
    /* 心率 */
    @SerializedName("heart_rate")
    val heartRate: HeartRate
)

/**
 *  生命体征数据加载状态
 */
sealed class VitalSignGroupLoadingState() {
    object  Loading: VitalSignGroupLoadingState()                                   /* 加载中 */
    class   Failure(val error: Throwable,val msg:String): VitalSignGroupLoadingState()                 /* 加载错误 */
    class   Success(val vitalSigns: VitalSignGroup): VitalSignGroupLoadingState()       /* 加载成功 */
}