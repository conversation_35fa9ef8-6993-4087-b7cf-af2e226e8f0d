package org.aihealth.ineck.model.chat

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.room.ColumnInfo
import com.google.gson.Gson
import com.google.gson.annotations.SerializedName
import org.aihealth.ineck.util.LogUtil
import org.json.JSONObject

data class ChatSmsEntity(
    /**
     * 用户id
     */
    var fromUserUid: String = "",
    /**
     * 消息内容
     * 对于普通文本：存储文本内容
     * 对于MeetContent/FollowContent：存储原始JSON字符串
     */
    var message: String = "",
    /**
     * 消息内容类型，与枚举类MediaType对应
     * MediaType.TEXT(1) - 普通文本
     * MediaType.MEETING(5) - 会议内容
     * MediaType.FOLLOW(6) - 关注内容
     */
    var mediaType: Int = 1,
    /**
     * 消息类型，与枚举类MessageType对应
     */
    var messageType: Int = 0,
    /**
     * 创建时间
     */
    var createBy: Long = 0
)


data class ChatSession(
    val providerUUID: String = "",
    val avatar: String = "",
    val name: String = "",
    val message: String = "",
    val messageType: MessageType = MessageType.RECEIVE,
    val createBy: Long = 0L,
    val phone: String = "",
)

enum class MediaType(var value: Int) {
    TEXT(1),
    VIDEO(2),
    IMAGE(3),
    AUDIO(4),
    MEETING(5),
    FOLLOW(6),
    REPORT(7),
    PHONE_REQUEST(8),
    CALL_DURATION(9);
}

enum class MessageType(val value: Int) {
    /**
     * 接收的信息
     */
    RECEIVE(-1),

    None(0),
    /**
     * 发送的信息
     */
    SEND(1),
}

data class Message(
    @SerializedName("datetime") val datetime: Long = 0L,
    @SerializedName("from_user") val fromUser: String = "",
    @SerializedName("msg") val msg: String = ""
)

data class MeetContent(
    @SerializedName("type") val type: String = "meeting",
    @SerializedName("title") val title: String = "",
    @SerializedName("time") val time: String = "",
    @SerializedName("creator_id") val creatorId: String = "",
    @SerializedName("from_id") val fromId: String = "",
    @SerializedName("isAccepted") val isAccepted: Int = 0
)

data class MeetContentOld(
    @SerializedName("type") val type: String = "meeting",
    @SerializedName("title") val title: String = "",
    @SerializedName("time") val time: String = "",
    @SerializedName("creator_id") val creatorId: String = "",
    @SerializedName("from_id") val fromId: String = "",
    @SerializedName("isAccepted") val isAccepted: Boolean = false
)
data class PhoneRequestContent(
    @SerializedName("type") val type: String = "phone_request",
    @SerializedName("user_id") val userId: String = "",
    @SerializedName("requester_id") val requesterId: String = "",
    @SerializedName("isAccepted") val isAccepted: Int = 0,
    @SerializedName("avatar") val avatar: String = "",
    @SerializedName("phone") val phone: String = ""
)
/**
 * 通话时长记录卡片内容
 */
data class CallDurationContent(
    @SerializedName("type") val type: String = "call_duration",
    @SerializedName("duration") val duration: Long = 0, // 通话时长（秒）
    @SerializedName("time") val time: String = "", // 通话时间，格式为HH:mm:ss
    @SerializedName("user_id") val userId: String = "",
    @SerializedName("requester_id") val requesterId: String = ""
)

data class FollowContent(
    @SerializedName("type") val type: String = "follow",
    @SerializedName("user_id") val userId: String = "",
    @SerializedName("following_id") val followingId: String = "",
    @SerializedName("avatar") val avatar: String = "",
    @SerializedName("name") val name: String = "",
    @SerializedName("isAccepted") val isAccepted: Int = 0
)

data class ReportContent(
    @SerializedName("type") val type: String = "report",
    @SerializedName("user_id") val userId: String = "",
    @SerializedName("avatar") val avatar: String = "",
    @SerializedName("name") val name: String = "",
)

/**
 * 根据消息内容类型解析并返回对应的ChatSmsEntity对象
 * @param gson Gson解析器
 * @param message 消息对象
 * @param currentUserUuid 当前医生UUID，用于判断消息方向
 * @return 解析后的ChatSmsEntity对象
 */
fun parseMessageContent(
    gson: Gson,
    message: Message,
    currentUserUuid: String
): ChatSmsEntity {
    // 尝试解析为MeetContent
    tryParseAsMeetContent(gson, message.msg)?.let { meetContent ->
        // 将原始JSON字符串存入message字段
        return ChatSmsEntity(
            fromUserUid = message.fromUser,
            message = message.msg, // 存储原始JSON字符串
            mediaType = MediaType.MEETING.value,
            messageType = if (currentUserUuid == meetContent.creatorId) MessageType.RECEIVE.value else MessageType.SEND.value,
            createBy = message.datetime
        )
    }

    // 尝试解析为MeetContent
    tryParseAsMeetContentOld(gson, message.msg)?.let { meetContent ->
        // 将原始JSON字符串存入message字段
        return ChatSmsEntity(
            fromUserUid = message.fromUser,
            message = message.msg, // 存储原始JSON字符串
            mediaType = MediaType.MEETING.value,
            messageType = if (currentUserUuid == meetContent.creatorId) MessageType.RECEIVE.value else MessageType.SEND.value,
            createBy = message.datetime
        )
    }

    // 尝试解析为FollowContent
    tryParseAsFollowContent(gson, message.msg)?.let { followContent ->
        // 将原始JSON字符串存入message字段
        return ChatSmsEntity(
            fromUserUid = message.fromUser,
            message = message.msg, // 存储原始JSON字符串
            mediaType = MediaType.FOLLOW.value,
            messageType = if (currentUserUuid == followContent.userId) MessageType.SEND.value else MessageType.RECEIVE.value,
            createBy = message.datetime
        )
    }
    // 尝试解析为phoneRequestContent
    tryParsePhoneRequestContent(gson, message.msg)?.let { phoneRequestContent ->
        return ChatSmsEntity(
            fromUserUid = message.fromUser,
            message = message.msg, // 存储原始JSON字符串
            mediaType = MediaType.PHONE_REQUEST.value,
            messageType = MessageType.None.value,
            createBy = message.datetime
        )
    }

    // 尝试解析为FollowContent
    tryParseAsReportContent(gson, message.msg)?.let { reportContent ->
        // 将原始JSON字符串存入message字段
        return ChatSmsEntity(
            fromUserUid = message.fromUser,
            message = message.msg, // 存储原始JSON字符串
            mediaType = MediaType.REPORT.value,
            messageType = MessageType.SEND.value,
            createBy = message.datetime
        )
    }

    // 当作普通字符串处理
    return ChatSmsEntity(
        fromUserUid = message.fromUser,
        message = message.msg,
        mediaType = MediaType.TEXT.value,
        messageType = if (currentUserUuid == message.fromUser) MessageType.RECEIVE.value else MessageType.SEND.value,
        createBy = message.datetime
    )
}

/**
 * 尝试将字符串解析为MeetContent对象
 * @param gson Gson解析器
 * @param json 要解析的JSON字符串
 * @return 解析成功返回MeetContent对象，失败返回null
 */
private fun tryParseAsMeetContent(gson: Gson, json: String): MeetContent? {
    return try {
        val meetContent = gson.fromJson(json, MeetContent::class.java)
        // 判断type字段确定是否为MeetContent类型
        if (meetContent.type == "meeting") {
            meetContent
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
}

/**
 * 尝试将字符串解析为MeetContent对象
 * @param gson Gson解析器
 * @param json 要解析的JSON字符串
 * @return 解析成功返回MeetContent对象，失败返回null
 */
private fun tryParseAsMeetContentOld(gson: Gson, json: String): MeetContent? {
    return try {
        val meetContentOld = gson.fromJson(json, MeetContentOld::class.java)
        // 判断type字段确定是否为MeetContent类型
        if (meetContentOld.type == "meeting") {
            return MeetContent(
                type = meetContentOld.type,
                title = meetContentOld.title,
                time = meetContentOld.time,
                creatorId = meetContentOld.creatorId,
                fromId = meetContentOld.fromId,
                isAccepted = if (meetContentOld.isAccepted) 1 else 0
            )
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
}
/**
 * 尝试将字符串解析为FollowContent对象
 * @param gson Gson解析器
 * @param json 要解析的JSON字符串
 * @return 解析成功返回FollowContent对象，失败返回null
 */
private fun tryParsePhoneRequestContent(gson: Gson, json: String): PhoneRequestContent? {
    return try {
        val phoneRequestContent = gson.fromJson(json, PhoneRequestContent::class.java)
        // 判断type字段确定是否为FollowContent类型
        if (phoneRequestContent.type == "phone_request") {
            phoneRequestContent
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
}

/**
 * 尝试将字符串解析为FollowContent对象
 * @param gson Gson解析器
 * @param json 要解析的JSON字符串
 * @return 解析成功返回FollowContent对象，失败返回null
 */
private fun tryParseAsFollowContent(gson: Gson, json: String): FollowContent? {
    return try {
        val followContent = gson.fromJson(json, FollowContent::class.java)
        // 判断type字段确定是否为FollowContent类型
        if (followContent.type == "follow") {
            followContent
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
}

/**
 * 尝试将字符串解析为ReportContent对象
 * @param gson Gson解析器
 * @param json 要解析的JSON字符串
 * @return 解析成功返回ReportContent对象，失败返回null
 */
private fun tryParseAsReportContent(gson: Gson, json: String): ReportContent? {
    return try {
        val reportContent = gson.fromJson(json, ReportContent::class.java)
        // 判断type字段确定是否为FollowContent类型
        if (reportContent.type == "report") {
            reportContent
        } else {
            null
        }
    } catch (e: Exception) {
        null
    }
}

/**
 * 从ChatSmsEntity获取原始的MeetContent对象
 * @return 如果mediaType为MediaType.MEETING则返回解析后的对象，否则返回null
 */
fun ChatSmsEntity.getMeetContent(gson: Gson = Gson()): MeetContent? {
    var meet: MeetContent? = null
    LogUtil.d("the meetingdata ge Meet message: ${this}")
    if (mediaType == MediaType.MEETING.value && !message.isEmpty()) {
        LogUtil.d("the meetingdata is meeting")
        meet = try {
            gson.fromJson(message, MeetContent::class.java)
        } catch (e: Exception) {
            try {
                val meetContentOld = gson.fromJson(message, MeetContentOld::class.java)
                MeetContent(
                    type = meetContentOld.type,
                    title = meetContentOld.title,
                    time = meetContentOld.time,
                    creatorId = meetContentOld.creatorId,
                    fromId = meetContentOld.fromId,
                    isAccepted = if (meetContentOld.isAccepted) 1 else 0
                )
            } catch (e: Exception) {
                null
            }
        }
        return meet
    }
    return null
}

/**
 * 从ChatSmsEntity获取原始的FollowContent对象
 * @return 如果mediaType为MediaType.FOLLOW则返回解析后的对象，否则返回null
 */
fun ChatSmsEntity.getFollowContent(gson: Gson = Gson()): FollowContent? {
    if (mediaType == MediaType.FOLLOW.value && !message.isEmpty()) {
        return try {
            gson.fromJson(message, FollowContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    return null
}

/**
 * 从ChatSmsEntity获取原始的FollowContent对象
 * @return 如果mediaType为MediaType.FOLLOW则返回解析后的对象，否则返回null
 */
fun ChatSmsEntity.getReportContent(gson: Gson = Gson()): ReportContent? {
    if (mediaType == MediaType.REPORT.value && !message.isEmpty()) {
        return try {
            gson.fromJson(message, ReportContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    return null
}

/**
 * 获取消息的原始内容，无论是什么类型
 */
fun ChatSmsEntity.getRawContent(): Any? {
    return when (mediaType) {
        MediaType.MEETING.value -> getMeetContent()
        MediaType.FOLLOW.value -> getFollowContent()
        else -> message
    }
}


/**
 * 功能按钮数据类
 */
data class FunctionButton(
    @StringRes val name: Int,
    @DrawableRes val icon: Int
)



data class ChatMessageEntity(
    @SerializedName("from_user") val fromUserUid: String,
    @SerializedName("to_user") val toUserUid: String,
    @SerializedName("msg_id") val messageId: Long,
    @SerializedName("msg") val message: String,
    @SerializedName("date_time") val dateTime: Long,
    @SerializedName("status") val status: Int,
    @SerializedName("messageType") val messageType:MessageType
){
    val mediaType: MediaType
        get() = determineMediaType(message)

    private fun determineMediaType(message: String): MediaType {
        return try {
            // 检查消息是否为JSON格式
            if (message.trim().startsWith("{") && message.trim().endsWith("}")) {
                // 尝试解析JSON
                val jsonObject = JSONObject(message)

                // 检查是否包含特定字段来确定消息类型
                when {
                    jsonObject.has("type") && jsonObject.getString("type") == "follow" ->
                        MediaType.FOLLOW

                    jsonObject.has("type") && jsonObject.getString("type") == "meeting" ->
                        MediaType.MEETING

                    jsonObject.has("type") && jsonObject.getString("type") == "report" ->
                        MediaType.REPORT
                    jsonObject.has("type") && jsonObject.getString("type") == "phone_request" ->
                        MediaType.PHONE_REQUEST

                    jsonObject.has("type") && jsonObject.getString("type") == "call_duration" ->
                        MediaType.CALL_DURATION

                    else -> MediaType.TEXT
                }
            }else {
                // 不是JSON格式，认为是普通文本
                MediaType.TEXT
            }
        }
        catch (e: Exception){
            return MediaType.TEXT
        }
    }
}

/**
 * 从ChatMessageEntity获取原始的MeetContent对象
 * @return 如果mediaType为MediaType.MEETING则返回解析后的对象，否则返回null
 */
fun ChatMessageEntity.getMeetContent(gson: Gson = Gson()): MeetContent? {
    var meet: MeetContent? = null
    LogUtil.d("the meetingdata ge Meet message: ${this}")
    if (mediaType == MediaType.MEETING && !message.isEmpty()) {
        LogUtil.d("the meetingdata is meeting")
        meet = try {
            gson.fromJson(message, MeetContent::class.java)
        } catch (e: Exception) {
            try {
                val meetContentOld = gson.fromJson(message, MeetContentOld::class.java)
                MeetContent(
                    type = meetContentOld.type,
                    title = meetContentOld.title,
                    time = meetContentOld.time,
                    creatorId = meetContentOld.creatorId,
                    fromId = meetContentOld.fromId,
                    isAccepted = if (meetContentOld.isAccepted) 1 else 0
                )
            } catch (e: Exception) {
                null
            }
        }
        return meet
    }
    return null
}

/**
 * 从 ChatMessageEntity 获取原始的FollowContent对象
 * @return 如果mediaType为MediaType.FOLLOW则返回解析后的对象，否则返回null
 */
fun ChatMessageEntity.getFollowContent(gson: Gson = Gson()): FollowContent? {
    if (mediaType == MediaType.FOLLOW && !message.isEmpty()) {
        return try {
            gson.fromJson(message, FollowContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    return null
}

/**
 * 从 ChatMessageEntity 获取原始的FollowContent对象
 * @return 如果mediaType为MediaType.FOLLOW则返回解析后的对象，否则返回null
 */
fun ChatMessageEntity.getReportContent(gson: Gson = Gson()): ReportContent? {
    if (mediaType == MediaType.REPORT && !message.isEmpty()) {
        return try {
            gson.fromJson(message, ReportContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    return null
}
/**
 * 从 ChatMessageEntity 获取原始的FollowContent对象
 * @return 如果mediaType为MediaType.FOLLOW则返回解析后的对象，否则返回null
 */
fun ChatMessageEntity.getPhoneRequestContent(gson: Gson = Gson()): PhoneRequestContent? {
    if (mediaType == MediaType.PHONE_REQUEST && !message.isEmpty()) {
        return try {
            gson.fromJson(message, PhoneRequestContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    return null
}

/**
 * 从 ChatMessageEntity 获取原始的CallDurationContent对象
 */
fun ChatMessageEntity.getCallDurationContent(gson: Gson = Gson()): CallDurationContent? {
    if (mediaType == MediaType.CALL_DURATION && !message.isEmpty()) {
        return try {
            gson.fromJson(message, CallDurationContent::class.java)
        } catch (e: Exception) {
            null
        }
    }
    return null
}