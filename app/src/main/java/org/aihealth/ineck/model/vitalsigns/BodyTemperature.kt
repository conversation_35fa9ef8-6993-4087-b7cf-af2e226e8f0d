package org.aihealth.ineck.model.vitalsigns

import com.google.gson.annotations.SerializedName

/**
 *  体温值
 */
data class BodyTemperature(
    @SerializedName("temperature")
    val temperature: Float
)

/**
 *  体温值 - 历史数据单元
 */
data class BodyTemperatureHistoryUnit(
    @SerializedName("temperature")
    val temperature: Float,
    @SerializedName("uuid")
    val uuid: String,
    @SerializedName("date_time")
    override val dateTime: String
): VitalSigns
