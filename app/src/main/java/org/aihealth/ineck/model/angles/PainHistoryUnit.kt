package org.aihealth.ineck.model.angles

import com.google.gson.annotations.SerializedName
import org.aihealth.ineck.viewmodel.user

data class NeckPainHistoryItem(
    @SerializedName("uuid")
    var uuid: String,
    // 手疼痛平均值
    @SerializedName("hand")
    var hand: Int = 0,
    // 颈部疼痛平均值
    @SerializedName("neck")
    var neck: Int = 0,
    var datetime: Long = 0L
)

data class BackPainHistoryItem(
    @SerializedName("uuid")
    var uuid: String = user.uuid,
    // 手疼痛平均值
    @SerializedName("back")
    var back: Int = 0,
    // 颈部疼痛平均值
    @SerializedName("leg")
    var leg: Int = 0,
    @SerializedName("datetime")
    var datetime: Long = 0L
)