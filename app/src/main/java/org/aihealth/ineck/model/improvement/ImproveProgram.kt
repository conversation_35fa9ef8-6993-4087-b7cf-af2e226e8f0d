package org.aihealth.ineck.model.improvement

import com.google.gson.annotations.SerializedName
import org.aihealth.ineck.view.directions.ImprovementDetailDirections

/**
 *  改善页请求方案数据
 *  @property   number  项目方案数量
 *  @property   programs    项目数据列表
 */
class ImprovementProgramsData(
    @SerializedName("number")
    val number: Int,
    @SerializedName("programs")
    val programs: List<ImproveProgram>
)

/**
 *  改善页请求方案数据单元
 *  @property   materialId  材料资源ID
 *  @property   type        类型 Video/Photo
 *  @property   cover       封面
 *  @property   duration    时长
 *  @property   isDevicesRequired   是否需要设备
 *  @property   isMembershipRequired 是否需要会员
 *  @property   title       标题
 *  @property   subtitle    副标题
 *  @property   description 描述
 *  @property   frequency   频率
 *
 */
data class ImproveProgram(
    @SerializedName("material_id")
    val materialId: Int,
    @SerializedName("type")
    val type: String,
    @SerializedName("cover")
    val cover: String,
    @SerializedName("duration")
    val duration: Long,
    @SerializedName("isDevicesRequired")
    val isDevicesRequired: Boolean,
    @SerializedName("isMembershipRequired")
    val isMembershipRequired: Boolean,
    @SerializedName("title")
    val title: String,
    @SerializedName("subtitle")
    val subtitle: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("frequency")
    val frequency: Int
)

fun ImproveProgram.toImprovementDetailModel(): ImprovementDetailDirections.ImprovementDetailModel {
    return ImprovementDetailDirections.ImprovementDetailModel(
        materialId = materialId,
        type = type,
        needVip = isMembershipRequired
    )
}

/**
 * 对于视频来说，Section表示分集          对于图文来说，Section表示步骤
 * @property    duration    时长       时长
 * @property    resource    video链接   图片链接
 * @property    cover       封面       封面
 * @property    title       标题       标题
 * @property    subtitle    副标题      副标题
 * @property    description 描述       描述
 * @property    tags        标签       标签
 *
 */
data class Section(
    @SerializedName("duration")
    val duration: Long,
    @SerializedName("resource")
    val resource: String,
    @SerializedName("cover")
    val cover: String,
    @SerializedName("title")
    val title: String,
    @SerializedName("subtitle")
    val subtitle: String,
    @SerializedName("description")
    val description: String,
    @SerializedName("tags")
    val tags: List<String>
)

/**
 * 对于视频来说
 * @property    number      分集数量
 * @property    materialId  材料资源ID
 * @property    video       视频链接
 * @property    sections    分集列表
 */

data class ProgramModel(
    @SerializedName("number")
    val number: Int,
    @SerializedName("material_id")
    val materialId: Int,
    @SerializedName("video")
    val video: String,
    @SerializedName("sections")
    val sections: List<Section>
)

/**
 *  改善页内容请求加载状态
 */
sealed class ImprovementProgramsLoadState() {
    object InitLoading :
        ImprovementProgramsLoadState()                                   /* 初始化加载中 */

    object Loading : ImprovementProgramsLoadState()                                     /* 非初始化加载 */
    class Failure(val error: Exception, val message: String = "") :
        ImprovementProgramsLoadState()                 /* 加载错误 */

    class Success(val improvementProgramsData: ImprovementProgramsData) :
        ImprovementProgramsLoadState()       /* 加载成功 */
}

enum class ImprovementDetailType(val type: String) {
    VIDEO("Video"),
    IMAGE("Image"),
}