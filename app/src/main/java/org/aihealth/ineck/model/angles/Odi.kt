package org.aihealth.ineck.model.angles


import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.viewmodel.user
@Parcelize
data class Odi (
    val uuid: String = user.uuid,
    var pain: Int = 0,
    var care: Int = 0,
    var lifting: Int = 0,
    var walking: Int = 0,
    var sitting: Int = 0,
    var standing: Int = 0,
    var sleeping: Int = 0,
    var sex: Int = -1,
    var social: Int = 0,
    var travelling: Int = 0
): BaseTime()