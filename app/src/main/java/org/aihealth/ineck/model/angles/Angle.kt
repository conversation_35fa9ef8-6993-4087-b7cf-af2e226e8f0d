package org.aihealth.ineck.model.angles

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey

/**
 *  储存设备反馈角度数据的 角度类型
 */
@Entity(indices = [Index(value = arrayOf("timestamp"), unique = true)])
data class Angle(

    //ID
    @PrimaryKey(autoGenerate = true)
    var id:Int=0,
    // 用户UUID
    var uuid: String,
    // 角度数据
    var angle: Int,
    // 设备类型(aiNeck、aiBack)
    var type: String,
    // 10位时间戳
    @ColumnInfo(name = "timestamp")
    var timestamp: Long
)
