package org.aihealth.ineck.model

import com.google.gson.annotations.SerializedName

data class ExerciseResult(
    @SerializedName("material_id")
    var materialId: Int,
    @SerializedName("score")
    var score: Int = 100,
    @SerializedName("actual_duration")
    var actualDuration: Long,
)

data class FeelingResult(
    @SerializedName("material_id")
    var materialId: Int,
    @SerializedName("feelings")
    var feelings: Int,
    @SerializedName("evaluation")
    var evaluation: String,
)