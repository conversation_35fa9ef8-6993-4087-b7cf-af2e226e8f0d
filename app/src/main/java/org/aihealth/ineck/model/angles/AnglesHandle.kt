package org.aihealth.ineck.model.angles

import com.google.gson.annotations.SerializedName

/**
 * 日报告页面数据实体类
 */
data class AnglesHandle(
    // 平均角度
    @SerializedName("average_angle")
    val average_angle: Int = 0,
    // 体姿状况（1: "良好", 2": 一般" ,3: "严重" ）
    @SerializedName("body_posture_state")
    val body_posture_state: Int = 1,
    // 角度比例
    @SerializedName("degree_list")
    val degree_list: SeverityProportion = SeverityProportion(),
    // 时间范围内振动次数
    @SerializedName("number_of_vibrations")
    val number_of_vibrations: Int = 0,
    // 佩戴时间 - 使用可空类型以安全处理null值
    @SerializedName("wearing_time")
    private val _wearing_time: String? = null
) {
    // 提供一个安全的访问器，确保永远不返回null
    val wearing_time: String get() = _wearing_time ?: "00:00:00"
}
