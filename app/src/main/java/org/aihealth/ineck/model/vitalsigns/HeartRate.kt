package org.aihealth.ineck.model.vitalsigns

import com.google.gson.annotations.SerializedName

/**
 *  心率值
 */
data class HeartRate(
    @SerializedName("value")
    val value: Int
)

/**
 *  心率值 - 历史数据单元
 */
data class HeartRateHistoryUnit(
    @SerializedName("value")
    val value: Int,
    @SerializedName("uuid")
    val uuid: String,
    @SerializedName("date_time")
    override val dateTime: String
) : VitalSigns