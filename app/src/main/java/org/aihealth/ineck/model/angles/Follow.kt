package org.aihealth.ineck.model.angles

import com.google.gson.annotations.SerializedName

data class Follow(
    val uuid: String = "",
    @SerializedName("user_id")
    val userId:String = "",
    val name: String = "",
    val gender: String = "",
    val photo: String = "",
    val certification_status: Boolean = false,
    val email: String = "",
    val doctor_title: String = "",
    val hospital_level: String = "",
    val hospital: String = "",
    var followed: Boolean = false
) {
}

data class ProviderSearchResult(
    @SerializedName("data")
    val providers: List<Follow> = emptyList(),
    val total: Int = 0,
    @SerializedName("page_size") val pageSize: Int = 0,
    @SerializedName("page") val currentPage: Int = 0
)