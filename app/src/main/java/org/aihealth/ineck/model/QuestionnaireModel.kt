package org.aihealth.ineck.model

import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import com.google.gson.annotations.SerializedName


/**
 * 颈椎健康测评的答案
 * @param  questions_id 问卷ID
 * @param  questions 问卷答案列表
 */
data class AnswerModel(
    @SerializedName("questionnaire_id")
    val questionId:String,
    @SerializedName("duration")
    var duration: Long,
    @SerializedName("questions")
    val questions:List<Question>
)

/**
 * 颈椎健康测评具体问题的答案
 * @param  id 问题ID
 * @param  answer 问题答案
 */
data class Question(
    @SerializedName("question_id")
    val id: String,
){
    @SerializedName("answer")
    var answer: List<String> = mutableListOf()
}

/**
 * 颈椎健康测评的问题
 * @param  id 问题ID
 * @param  type 问题类型
 * @param  text 问题内容
 * @param  answer 问题答案列表
 */
data class QuestionModel(
    val id:String,
    val type: String,
    val text: String,
    val answer:List<Answer>,
)

/**
 * 颈椎健康测评的答案
 * @param  select 选项ID
 * @param  text 选项内容
 * @property  check 是否选中
 */
data class Answer(
    val select:String,
    val text:String,
){
    var check by mutableStateOf(false)
}