package org.aihealth.ineck.model

import androidx.annotation.DrawableRes
import org.aihealth.ineck.R

enum class DeviceType(
    @DrawableRes val unConnecteDrawable: Int = 0,
    @DrawableRes val connectedDrawable_negative_15: Int = 0,
    @DrawableRes val connectedDrawable_0: Int = 0,
    @DrawableRes val connectedDrawable_15: Int = 0,
    @DrawableRes val connectedDrawable_30: Int = 0,
    @DrawableRes val connectedDrawable_45: Int = 0,
    val netWorkName: String = "",
    val apiName: String = "",
) {
    /** 无连接状态  */
    None,

    /** aiNeck连接状态 */
    aiNeck(
        R.drawable.img_no_device_aineck,
        R.drawable.img_no_device_aineck_negative_15,
        R.drawable.img_aineck_0,
        R.drawable.img_aineck_15,
        R.drawable.img_aineck_30,
        R.drawable.img_aineck_45,
        "neck",
        "neck"
    ),

    /** aiNeck视频监测状态 */
    aiNeckCV(
        R.drawable.img_no_device_aineck,
        R.drawable.img_no_device_aineck_negative_15,
        R.drawable.img_aineck_0,
        R.drawable.img_aineck_15,
        R.drawable.img_aineck_30,
        R.drawable.img_aineck_45,
        "neckCV",
        "neck_cv"
    ),
    /** aiNeck设备连接 并 同时开启视频监测状态 */
//    aiNeckwithCV(R.drawable.img_no_device_aineck,R.drawable.img_aineck_0,R.drawable.img_aineck_15,R.drawable.img_aineck_30,R.drawable.img_aineck_45,"neck"),
    /** aiBack连接状态 */
    aiBack(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "back",
        "back"
    ),

    /** aiBack视频监测状态 */
    aiBackCV(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "backCV",
        "back_cv"
    ),
    /** aiBack设备连接 并 同时开启视频监测状态 */
//    aiBackwithCV(R.drawable.img_no_device_aiback,R.drawable.img_aiback_0,R.drawable.img_aiback_15,R.drawable.img_aiback_30,R.drawable.img_aiback_45,"back");

    aiJointCV(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "aiJointCV",
        "joint_cv"
    ),
    aiJoint(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "aiJoint",
        "joint"
    ),
    KneeJoint(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "Knee joint",
        "knee"
    ),
    ShoulderJoint(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "Shoulder joint",
        "shoulder"
    ),
    ElbowJoint(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "Elbow joint",
        "elbow"
    ),
    HipJoint(
        R.drawable.img_no_device_aiback,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_0,
        R.drawable.img_aiback_15,
        R.drawable.img_aiback_30,
        R.drawable.img_aiback_45,
        "Hip joint",
        "hip"
    ),
}
