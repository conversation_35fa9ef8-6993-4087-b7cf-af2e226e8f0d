package org.aihealth.ineck.model

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "TimeStamp")
data class TimeStamp(
    @PrimaryKey(autoGenerate = true) val id: Int = 0,
    @ColumnInfo(name = "uid") val uid: String,
    @ColumnInfo(name = "start_time") val StartTime: Long,
    @ColumnInfo(name = "end_time") val EndTime: Long,
    @ColumnInfo(name = "time_spent") val TimeSpent: Long
)

