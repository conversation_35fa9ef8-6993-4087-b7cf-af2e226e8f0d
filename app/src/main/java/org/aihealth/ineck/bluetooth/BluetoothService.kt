package org.aihealth.ineck.bluetooth

import android.annotation.SuppressLint
import android.app.PendingIntent
import android.app.Service
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothProfile
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Binder
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.os.Message
import android.os.Messenger
import android.os.RemoteException
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.NotificationConstants
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.NotificationUtils
import org.aihealth.ineck.util.localeResources
import java.lang.ref.WeakReference
import java.util.UUID
import java.util.concurrent.LinkedBlockingDeque

@OptIn(ExperimentalCoroutinesApi::class)
@SuppressLint("MissingPermission")
class BluetoothService(
) : Service() {
    companion object {
        const val MSG_DEVICE = 1
        const val KEY_DEVICE = "key_device"
        const val MSG_REGISTER_CLIENT = 2
        const val MSG_UNREGISTER_CLIENT = 3
        const val MSG_SEND_DATA = 4
        const val MSG_CODE = 5
        const val KEY_CODE = "key_code"
    }

    // Define a CoroutineExceptionHandler
    private val exceptionHandler = CoroutineExceptionHandler { _, exception ->
        LogUtil.e("CoroutineException Caught $exception")
    }
    private val scope = CoroutineScope(Dispatchers.IO + exceptionHandler)

    /**
     * 设备连接对象
     */
    // aiNeck
    var singleConnection = MutableStateFlow<BLEDeviceConnection?>(null)
    private val _singleDeviceState = MutableStateFlow(BLEClientState())

    // aiBack
    var backConnection = MutableStateFlow<BLEDeviceConnection?>(null)
    private val _backDeviceState = MutableStateFlow(BLEClientState())

    // ElbowJoint
    private var elbowConnection = MutableStateFlow<BLEDeviceConnection?>(null)
    private val _elbowDeviceState = MutableStateFlow(BLEClientState())

    // KneeJoint
    private var kneeConnection = MutableStateFlow<BLEDeviceConnection?>(null)
    private val _kneeDeviceState = MutableStateFlow(BLEClientState())

    // ShoulderJoint
    private var shoulderConnection = MutableStateFlow<BLEDeviceConnection?>(null)
    private val _shoulderDeviceState = MutableStateFlow(BLEClientState())

    // HipJoint
    private var hipConnection = MutableStateFlow<BLEDeviceConnection?>(null)
    private val _hipDeviceState = MutableStateFlow(BLEClientState())

    // aiNeck
    private val isSingleDeviceConnected =
        singleConnection.flatMapLatest { it?.isConnected ?: flowOf(null) }
    private val singleAngle = singleConnection.flatMapLatest { it?.angle ?: flowOf(null) }
    private val singleHistoryAngle =
        singleConnection.flatMapLatest { it?.historyAngle ?: flowOf(null) }
    private val singleMaxAngle = singleConnection.flatMapLatest { it?.maxAngle ?: flowOf(null) }
    private val singlePower = singleConnection.flatMapLatest { it?.power ?: flowOf(null) }
    private val singleSerialNumber = singleConnection.flatMapLatest { it?.sn ?: flowOf(null) }
    private val singleVersion = singleConnection.flatMapLatest { it?.version ?: flowOf(null) }

    // aiBack
    private val isBackDeviceConnected =
        backConnection.flatMapLatest { it?.isConnected ?: flowOf(null) }
    private val backHistoryAngle =
        backConnection.flatMapLatest { it?.historyAngle ?: flowOf(null) }
    private val backAngle = backConnection.flatMapLatest { it?.angle ?: flowOf(null) }
    private val backMaxAngle = backConnection.flatMapLatest { it?.maxAngle ?: flowOf(null) }
    private val backPower = backConnection.flatMapLatest { it?.power ?: flowOf(null) }
    private val backSerialNumber = backConnection.flatMapLatest { it?.sn ?: flowOf(null) }
    private val backVersion = backConnection.flatMapLatest { it?.version ?: flowOf(null) }

    // ElbowJoint
    private val isElbowDeviceConnected =
        elbowConnection.flatMapLatest { it?.isConnected ?: flowOf(null) }
    private val elbowHistoryAngle =
        elbowConnection.flatMapLatest { it?.historyAngle ?: flowOf(null) }
    private val elbowAngle = elbowConnection.flatMapLatest { it?.angle ?: flowOf(null) }
    private val elbowMaxAngle = elbowConnection.flatMapLatest { it?.maxAngle ?: flowOf(null) }
    private val elbowPower = elbowConnection.flatMapLatest { it?.power ?: flowOf(null) }
    private val elbowSerialNumber = elbowConnection.flatMapLatest { it?.sn ?: flowOf(null) }
    private val elbowVersion = elbowConnection.flatMapLatest { it?.version ?: flowOf(null) }

    // KneeJoint
    private val isKneeDeviceConnected =
        kneeConnection.flatMapLatest { it?.isConnected ?: flowOf(null) }
    private val kneeHistoryAngle =
        kneeConnection.flatMapLatest { it?.historyAngle ?: flowOf(null) }
    private val kneeAngle = kneeConnection.flatMapLatest { it?.angle ?: flowOf(null) }
    private val kneeMaxAngle = kneeConnection.flatMapLatest { it?.maxAngle ?: flowOf(null) }
    private val kneePower = kneeConnection.flatMapLatest { it?.power ?: flowOf(null) }
    private val kneeSerialNumber = kneeConnection.flatMapLatest { it?.sn ?: flowOf(null) }
    private val kneeVersion = kneeConnection.flatMapLatest { it?.version ?: flowOf(null) }

    // ShoulderJoint
    private val isShoulderDeviceConnected =
        shoulderConnection.flatMapLatest { it?.isConnected ?: flowOf(null) }
    private val shoulderHistoryAngle =
        shoulderConnection.flatMapLatest { it?.historyAngle ?: flowOf(null) }
    private val shoulderAngle = shoulderConnection.flatMapLatest { it?.angle ?: flowOf(null) }
    private val shoulderMaxAngle = shoulderConnection.flatMapLatest { it?.maxAngle ?: flowOf(null) }
    private val shoulderPower = shoulderConnection.flatMapLatest { it?.power ?: flowOf(null) }
    private val shoulderSerialNumber = shoulderConnection.flatMapLatest { it?.sn ?: flowOf(null) }
    private val shoulderVersion = shoulderConnection.flatMapLatest { it?.version ?: flowOf(null) }

    // HipJoint
    private val isHipDeviceConnected =
        hipConnection.flatMapLatest { it?.isConnected ?: flowOf(null) }
    private val hipHistoryAngle = hipConnection.flatMapLatest { it?.historyAngle ?: flowOf(null) }
    private val hipAngle = hipConnection.flatMapLatest { it?.angle ?: flowOf(null) }
    private val hipMaxAngle = hipConnection.flatMapLatest { it?.maxAngle ?: flowOf(null) }
    private val hipPower = hipConnection.flatMapLatest { it?.power ?: flowOf(null) }
    private val hipSerialNumber = hipConnection.flatMapLatest { it?.sn ?: flowOf(null) }
    private val hipVersion = hipConnection.flatMapLatest { it?.version ?: flowOf(null) }

    // aiNeck
    val singleDeviceState = combine(
        _singleDeviceState,
        isSingleDeviceConnected,
        singleSerialNumber,
        singleVersion,
        singleAngle,
    ) { state, isDeviceConnected, sn, version, angle ->
        state.copy(
            isDeviceConnected = isDeviceConnected,
            serialNumber = sn,
            version = version,
            angle = angle?.coerceIn(-15, 90) ?: 0,
        )
    }.combine(singleHistoryAngle) { state, historyAngle ->
        state.copy(
            historyAngle = historyAngle
        )
    }.combine(singleMaxAngle) { state, maxAngle ->
        state.copy(
            maxAngle = maxAngle,
        )
    }.combine(singlePower) { stater, power ->
        stater.copy(
            power = power
        )
    }.stateIn(scope, SharingStarted.WhileSubscribed(1000), BLEClientState())

    // aiBack
    val backDeviceState = combine(
        _backDeviceState,
        isBackDeviceConnected,
        backSerialNumber,
        backVersion,
        backAngle,
    ) { state, isDeviceConnected, sn, version, angle ->
        state.copy(
            isDeviceConnected = isDeviceConnected,
            serialNumber = sn,
            version = version,
            angle = angle?.coerceIn(-15, 90) ?: 0,
        )
    }.combine(backHistoryAngle) { state, historyAngle ->
        state.copy(
            historyAngle = historyAngle
        )
    }.combine(backMaxAngle) { state, maxAngle ->
        state.copy(
            maxAngle = maxAngle,
        )
    }.combine(backPower) { stater, power ->
        stater.copy(
            power = power
        )
    }.stateIn(scope, SharingStarted.WhileSubscribed(1000), BLEClientState())

    //  ElbowJoint
    private val elbowDeviceState = combine(
        _elbowDeviceState,
        isElbowDeviceConnected,
        elbowSerialNumber,
        elbowVersion,
        elbowAngle,
    ) { state, isDeviceConnected, sn, version, angle ->
        state.copy(
            isDeviceConnected = isDeviceConnected,
            serialNumber = sn,
            version = version,
            angle = angle,
        )
    }.combine(elbowHistoryAngle) { state, historyAngle ->
        state.copy(
            historyAngle = historyAngle
        )
    }.combine(elbowMaxAngle) { state, maxAngle ->
        state.copy(
            maxAngle = maxAngle,
        )
    }.combine(elbowPower) { stater, power ->
        stater.copy(
            power = power
        )
    }.stateIn(scope, SharingStarted.WhileSubscribed(1000), BLEClientState())

    //  KneeJoint
    private val kneeDeviceState = combine(
        _kneeDeviceState,
        isKneeDeviceConnected,
        kneeSerialNumber,
        kneeVersion,
        kneeAngle,
    ) { state, isDeviceConnected, sn, version, angle ->
        state.copy(
            isDeviceConnected = isDeviceConnected,
            serialNumber = sn,
            version = version,
            angle = angle,
        )
    }.combine(kneeHistoryAngle) { state, historyAngle ->
        state.copy(
            historyAngle = historyAngle
        )
    }.combine(kneeMaxAngle) { state, maxAngle ->
        state.copy(
            maxAngle = maxAngle,
        )
    }.combine(kneePower) { stater, power ->
        stater.copy(
            power = power
        )
    }.stateIn(scope, SharingStarted.WhileSubscribed(1000), BLEClientState())

    // ShoulderJoint
    private val shoulderDeviceState = combine(
        _shoulderDeviceState,
        isShoulderDeviceConnected,
        shoulderSerialNumber,
        shoulderVersion,
        shoulderAngle,
    ) { state, isDeviceConnected, sn, version, angle ->
        state.copy(
            isDeviceConnected = isDeviceConnected,
            serialNumber = sn,
            version = version,
            angle = angle,
        )
    }.combine(shoulderHistoryAngle) { state, historyAngle ->
        state.copy(
            historyAngle = historyAngle
        )
    }.combine(shoulderMaxAngle) { state, maxAngle ->
        state.copy(
            maxAngle = maxAngle,
        )
    }.combine(shoulderPower) { stater, power ->
        stater.copy(
            power = power
        )
    }.stateIn(scope, SharingStarted.WhileSubscribed(1000), BLEClientState())

    // HipJoint
    private val hipDeviceState = combine(
        _hipDeviceState,
        isHipDeviceConnected,
        hipSerialNumber,
        hipVersion,
        hipAngle,
    ) { state, isDeviceConnected, sn, version, angle ->
        state.copy(
            isDeviceConnected = isDeviceConnected,
            serialNumber = sn,
            version = version,
            angle = angle,
        )
    }.combine(hipHistoryAngle) { state, historyAngle ->
        state.copy(
            historyAngle = historyAngle
        )
    }.combine(hipMaxAngle) { state, maxAngle ->
        state.copy(
            maxAngle = maxAngle,
        )
    }.combine(hipPower) { stater, power ->
        stater.copy(
            power = power
        )
    }.stateIn(scope, SharingStarted.WhileSubscribed(1000), BLEClientState())

    private val serviceQueue = ServiceQueue()
    lateinit var notificationBuild: NotificationCompat.Builder

    var deviceRepository = DeviceRepository(this)
    private val callbackMap = HashMap<UUID, (ByteArray) -> Unit>()

    var bluetoothGatt: BluetoothGatt? = null
    var gattService: BluetoothGattService? = null
    var batteryGattService: BluetoothGattService? = null

    var lastUpdateTime = 0L
    var currentTime = 0L

    private val binder = LocalBinder()
    private lateinit var messenger: Messenger
    private val clients = mutableListOf<Messenger>()

    private class IncomingHandler(service: BluetoothService) : Handler(Looper.getMainLooper()) {
        private val serviceReference = WeakReference(service)

        override fun handleMessage(msg: Message) {
            val service = serviceReference.get()
            when (msg.what) {
                MSG_DEVICE -> {
                    /*
                    val device: BLEClientState? = msg.data.getParcelable(KEY_DEVICE)
                    device?.let {
                        // aiNeck
                        if (device.deviceType == DeviceType.aiNeck) {
                            service?.setSingleActiveDevice(device)
                            service?.connectSingleActiveDevice()
                        }
                        // aiBack
                        if (device.deviceType == DeviceType.aiBack) {
                            service?.setBackActiveDevice(device)
                            service?.connectBackActiveDevice()
                        }
                        // ElbowJoint
                        if (device.deviceType == DeviceType.ElbowJoint) {
                            service?.setElbowActiveDevice(device)
                            service?.connectElbowActiveDevice()
                        }
                        // KneeJoint
                        if (device.deviceType == DeviceType.KneeJoint) {
                            service?.setKneeActiveDevice(device)
                            service?.connectKneeActiveDevice()
                        }
                        // ShoulderJoint
                        if (device.deviceType == DeviceType.ShoulderJoint) {
                            service?.setShoulderActiveDevice(device)
                            service?.connectShoulderActiveDevice()
                        }
                        // HipJoint
                        if (device.deviceType == DeviceType.HipJoint) {
                            service?.setHipActiveDevice(device)
                            service?.connectHipActiveDevice()
                        }
                    }
                    */
                }

                MSG_REGISTER_CLIENT -> {
                    service?.clients?.add(msg.replyTo)

                }

                MSG_UNREGISTER_CLIENT -> {
                    service?.clients?.remove(msg.replyTo)
                }

                MSG_CODE -> {
                    val codes: String? = msg.data.getString(KEY_CODE)
                    val exceptionHandler = CoroutineExceptionHandler { _, exception ->
                        LogUtil.e("CoroutineException Caught $exception")
                    }
                    val innerScope = CoroutineScope(Dispatchers.IO + exceptionHandler)
                    codes?.let {
                        val code = it.split(".")
                        val deviceType = code[0]
                        service?.let {
                            when (deviceType) {
                                // aiNeck
                                DeviceType.aiNeck.name -> {
                                    val f = code[1]
                                    when (f) {
                                        "setDeviceType" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.setDeviceType(code[2].toString())
                                            }
                                        }

                                        "setVibration" -> {
                                            val value: Boolean = code[2].toBoolean()
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.setVibration(value)
                                            }
                                        }

                                        "onHeadUpCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.onHeadUpCalibrating()
                                            }
                                        }

                                        "onBendNeckCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.onBendNeckCalibrating()
                                            }
                                        }

                                        "onDisconnectDeviceClick" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.disconnect()
                                                service.singleConnection.value = null
                                            }
                                        }

                                        "power" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.getPower()
                                            }
                                        }

                                        "vibrate" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.setVibrate()
                                            }
                                        }

                                        "setVibrationFrequency" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.setVibrationFrequency(
                                                    value
                                                )
                                            }
                                        }

                                        "setMaxAngle" -> {
                                            val value: String = code[2]
                                            LogUtil.i("setMaxAngle $value")
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.setMaxAngle(value)
                                            }
                                        }

                                        "setVibrationAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.singleConnection.value!!.setVibrationAngle(
                                                    value
                                                )
                                            }
                                        }

                                        else -> ""
                                    }
                                }
                                // aiBack
                                DeviceType.aiBack.name -> {
                                    val f = code[1]
                                    when (f) {
                                        "setDeviceType" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.setDeviceType(code[2].toString())
                                            }
                                        }

                                        "setVibration" -> {
                                            val value: Boolean = code[2].toBoolean()
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.setVibration(value)
                                            }
                                        }

                                        "onHeadUpCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.onHeadUpCalibrating()
                                            }
                                        }

                                        "onBendNeckCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.onBendNeckCalibrating()
                                            }
                                        }

                                        "onDisconnectDeviceClick" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.disconnect()
                                                service.backConnection.value = null
                                            }
                                        }

                                        "power" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.getPower()
                                            }
                                        }

                                        "vibrate" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.setVibrate()
                                            }
                                        }

                                        "setVibrationFrequency" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.setVibrationFrequency(
                                                    value
                                                )
                                            }
                                        }

                                        "setMaxAngle" -> {
                                            val value: String = code[2]
                                            LogUtil.i("setMaxAngle $value")
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.setMaxAngle(value)
                                            }
                                        }

                                        "setVibrationAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.backConnection.value!!.setVibrationAngle(
                                                    value
                                                )
                                            }
                                        }

                                        else -> ""
                                    }
                                }
                                // KneeJoint
                                DeviceType.KneeJoint.name -> {
                                    val f = code[1]
                                    when (f) {
                                        "setDeviceType" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.setDeviceType(code[2].toString())
                                            }
                                        }

                                        "setVibration" -> {
                                            val value: Boolean = code[2].toBoolean()
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.setVibration(value)
                                            }
                                        }

                                        "onHeadUpCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.onHeadUpCalibrating()
                                            }
                                        }

                                        "onBendNeckCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.onBendNeckCalibrating()
                                            }
                                        }

                                        "onDisconnectDeviceClick" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.disconnect()
                                                service.kneeConnection.value = null
                                            }
                                        }

                                        "power" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.getPower()
                                            }
                                        }

                                        "vibrate" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.setVibrate()
                                            }
                                        }

                                        "setVibrationFrequency" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.setVibrationFrequency(
                                                    value
                                                )
                                            }
                                        }

                                        "setMaxAngle" -> {
                                            val value: String = code[2]
                                            LogUtil.i("knee setMaxAngle $value")
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.setMaxAngle(value)
                                            }
                                        }

                                        "setVibrationAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.kneeConnection.value!!.setVibrationAngle(
                                                    value
                                                )
                                            }
                                        }

                                        else -> ""
                                    }

                                }
                                // ElbowJoint
                                DeviceType.ElbowJoint.name -> {
                                    val f = code[1]
                                    when (f) {
                                        "setDeviceType" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.setDeviceType(code[2].toString())
                                            }
                                        }

                                        "setVibration" -> {
                                            val value: Boolean = code[2].toBoolean()
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.setVibration(value)
                                            }
                                        }

                                        "onHeadUpCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.onHeadUpCalibrating()
                                            }
                                        }

                                        "onBendNeckCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.onBendNeckCalibrating()
                                            }
                                        }

                                        "onDisconnectDeviceClick" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.disconnect()
                                                service.elbowConnection.value = null
                                            }
                                        }

                                        "power" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.getPower()
                                            }
                                        }

                                        "vibrate" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.setVibrate()
                                            }
                                        }

                                        "setVibrationFrequency" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.setVibrationFrequency(
                                                    value
                                                )
                                            }
                                        }

                                        "setMaxAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.setMaxAngle(value)
                                            }
                                        }

                                        "setVibrationAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.elbowConnection.value!!.setVibrationAngle(
                                                    value
                                                )
                                            }
                                        }

                                        else -> ""
                                    }

                                }
                                // ShoulderJoint
                                DeviceType.ShoulderJoint.name -> {
                                    val f = code[1]
                                    when (f) {
                                        "setDeviceType" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.setDeviceType(
                                                    code[2].toString()
                                                )
                                            }
                                        }

                                        "setVibration" -> {
                                            val value: Boolean = code[2].toBoolean()
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.setVibration(
                                                    value
                                                )
                                            }
                                        }

                                        "onHeadUpCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.onHeadUpCalibrating()
                                            }
                                        }

                                        "onBendNeckCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.onBendNeckCalibrating()
                                            }
                                        }

                                        "onDisconnectDeviceClick" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.disconnect()
                                                service.shoulderConnection.value = null
                                            }
                                        }

                                        "power" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.getPower()
                                            }
                                        }

                                        "vibrate" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.setVibrate()
                                            }
                                        }

                                        "setVibrationFrequency" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.setVibrationFrequency(
                                                    value
                                                )
                                            }
                                        }

                                        "setMaxAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.setMaxAngle(value)
                                            }
                                        }

                                        "setVibrationAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.shoulderConnection.value!!.setVibrationAngle(
                                                    value
                                                )
                                            }
                                        }

                                        else -> ""
                                    }

                                }
                                // HipJoint
                                DeviceType.HipJoint.name -> {
                                    val f = code[1]
                                    when (f) {
                                        "setDeviceType" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.setDeviceType(code[2].toString())
                                            }
                                        }

                                        "setVibration" -> {
                                            val value: Boolean = code[2].toBoolean()
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.setVibration(value)
                                            }
                                        }

                                        "onHeadUpCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.onHeadUpCalibrating()
                                            }
                                        }

                                        "onBendNeckCalibrating" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.onBendNeckCalibrating()
                                            }
                                        }

                                        "onDisconnectDeviceClick" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.disconnect()
                                                service.hipConnection.value = null
                                            }
                                        }

                                        "power" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.getPower()
                                            }
                                        }

                                        "vibrate" -> {
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.setVibrate()
                                            }
                                        }

                                        "setVibrationFrequency" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.setVibrationFrequency(
                                                    value
                                                )
                                            }
                                        }

                                        "setMaxAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.setMaxAngle(value)
                                            }
                                        }

                                        "setVibrationAngle" -> {
                                            val value: String = code[2]
                                            innerScope.launch(exceptionHandler) {
                                                service.hipConnection.value!!.setVibrationAngle(
                                                    value
                                                )
                                            }
                                        }

                                        else -> ""
                                    }

                                }

                                else -> {}
                            }
                        }
                    }
                }

                else -> super.handleMessage(msg)
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder {
        return binder
//        return messenger.binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        LogUtil.d("mytag", "Service onUnbind: ")
//        close()
        return super.onUnbind(intent)
    }

    // 自定义 Binder，用于获取 Service 实例和 Messenger
    inner class LocalBinder : Binder() {
        fun getService(): BluetoothService = this@BluetoothService
        fun getMessenger(): Messenger = messenger
    }

    override fun onCreate() {
        super.onCreate()
        val handler = IncomingHandler(this)
        messenger = Messenger(handler)
        notificationBuild = createForegroundNotification().apply {
            setContentText(localeResources.getString(R.string.connecting_aih_bluetooth_device))
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                NotificationConstants.BLUETOOTH_NOTIFY_ID,
                notificationBuild.build(),
                ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE
            )
        } else {
            startForeground(
                NotificationConstants.BLUETOOTH_NOTIFY_ID,
                notificationBuild.build()
            )
        }

        /*
        scope.launch(exceptionHandler) {
            kneeDeviceState.collect() {
                LogUtil.i("send data to clients")
                sendDataToClients(it)
                if (it.isDeviceConnected != null && it.isDeviceConnected) {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.your_current_angle, "Knee", it.angle)
                    )
                } else {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.device_disconnect, "Knee")
                    )
                }
                NotificationUtils.sendNotification(
                    NotificationConstants.BLUETOOTH_NOTIFY_ID,
                    notificationBuild
                )
            }
        }
        scope.launch(exceptionHandler) {
            elbowDeviceState.collect() {
                LogUtil.i("send data to clients")
                sendDataToClients(it)
                if (it.isDeviceConnected != null && it.isDeviceConnected) {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.your_current_angle, "Elbow", it.angle)
                    )
                } else {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.device_disconnect, "Elbow")
                    )
                }
                NotificationUtils.sendNotification(
                    NotificationConstants.BLUETOOTH_NOTIFY_ID,
                    notificationBuild
                )
            }
        }
        scope.launch(exceptionHandler) {
            hipDeviceState.collect() {
                LogUtil.i("send data to clients")
                sendDataToClients(it)
                if (it.isDeviceConnected != null && it.isDeviceConnected) {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.your_current_angle, "Hip", it.angle)
                    )
                } else {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.device_disconnect, "Hip")
                    )
                }
                NotificationUtils.sendNotification(
                    NotificationConstants.BLUETOOTH_NOTIFY_ID,
                    notificationBuild
                )

            }
        }
        scope.launch(exceptionHandler) {
            shoulderDeviceState.collect() {
                LogUtil.i("send data to clients")
                sendDataToClients(it)
                if (it.isDeviceConnected != null && it.isDeviceConnected) {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.your_current_angle, "Shoulder", it.angle)
                    )
                } else {
                    notificationBuild.setContentText(
                        localeResources.getString(R.string.device_disconnect, "Shoulder")
                    )
                }
                NotificationUtils.sendNotification(
                    NotificationConstants.BLUETOOTH_NOTIFY_ID,
                    notificationBuild
                )
            }
        }

         */
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        return START_STICKY
    }

    private fun sendDataToClients(data: BLEClientState) {
        LogUtil.i("client size ${clients.size}")
        for (client in clients) {
            try {
                val msg = Message.obtain(null, MSG_SEND_DATA)
                val bundle = Bundle()
                bundle.putParcelable("data", data)
                msg.data = bundle
                client.send(msg)
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }

    /**
     * 初始化设备连接
     */
    fun setSingleActiveDevice(device: BLEClientState?) {
        device?.let {
            _singleDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setSingleActiveDevice ${_singleDeviceState.value.activeDevice?.address}")
        singleConnection.value = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
    }

    /**
     * 初始化 aiNeck 设备连接
     */
    fun setNeckActiveDevice(device: BLEClientState?) {
        device?.let {
            _singleDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setNeckActiveDevice ${_singleDeviceState.value.activeDevice?.address}")
        val newConnection = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
        singleConnection.update { newConnection }
    }

    /**
     * 初始化 aiBack 设备连接
     */
    fun setBackActiveDevice(device: BLEClientState?) {
        device?.let {
            _backDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setSingleActiveDevice ${_backDeviceState.value.activeDevice?.address}")
        val newConnect = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
        backConnection.update { newConnect }
    }

    /**
     * 初始化设备连接
     */
    fun setElbowActiveDevice(device: BLEClientState?) {
        device?.let {
            _elbowDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setElbowActiveDevice ${_elbowDeviceState.value.activeDevice?.address}")
        elbowConnection.value = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
    }

    /**
     * 初始化设备连接
     */
    fun setKneeActiveDevice(device: BLEClientState?) {
        device?.let {
            _kneeDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setKneeActiveDevice ${_kneeDeviceState.value.activeDevice?.address}")
        kneeConnection.value = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
    }

    /**
     * 初始化设备连接
     */
    fun setShoulderActiveDevice(device: BLEClientState?) {
        device?.let {
            _shoulderDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setShoulderActiveDevice ${_shoulderDeviceState.value.activeDevice?.address}")
        shoulderConnection.value = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
    }

    /**
     * 初始化设备连接
     */
    fun setHipActiveDevice(device: BLEClientState?) {
        device?.let {
            _hipDeviceState.update {
                it.copy(
                    activeDevice = device.activeDevice,
                    deviceType = device.deviceType,
                    isDeviceConnected = null
                )
            }
        }
        LogUtil.i("setHipActiveDevice ${_hipDeviceState.value.activeDevice?.address}")
        hipConnection.value = device?.run {
            device.activeDevice?.let {
                BLEDeviceConnection(
                    activity,
                    it
                )
            }
        }
    }

    /**
     * 连接设备
     */
    fun connectSingleActiveDevice() {
        singleConnection.value?.connect(DeviceType.aiNeck.name)
    }

    /**
     * 连接设备
     */
    fun connectNeckActiveDevice() {
        singleConnection.value?.connect(DeviceType.aiNeck.name)
    }

    /**
     * 连接设备
     */
    fun connectBackActiveDevice() {
        backConnection.value?.connect(DeviceType.aiBack.name)
    }

    /**
     * 连接设备
     */
    fun connectElbowActiveDevice() {
        elbowConnection.value?.connect(DeviceType.ElbowJoint.name)
    }

    /**
     * 连接设备
     */
    fun connectKneeActiveDevice() {
        kneeConnection.value?.connect(DeviceType.KneeJoint.name)
    }

    /**
     * 连接设备
     */
    fun connectShoulderActiveDevice() {
        shoulderConnection.value?.connect(DeviceType.ShoulderJoint.name)
    }

    /**
     * 连接设备
     */
    fun connectHipActiveDevice() {
        hipConnection.value?.connect(DeviceType.HipJoint.name)
    }

    /**
     * 断开连接
     */
    suspend fun disconnectSingleActiveDevice() {
        singleConnection.value?.disconnect()
    }

    /**
     * 断开连接
     */
    suspend fun disconnectBackActiveDevice() {
        backConnection.value?.disconnect()
    }

    /**
     * 断开连接
     */
    suspend fun disconnectKneeActiveDevice() {
        kneeConnection.value?.disconnect()
    }

    /**
     * 断开连接
     */
    suspend fun disconnectElbowActiveDevice() {
        elbowConnection.value?.disconnect()
    }

    /**
     * 断开连接
     */
    suspend fun disconnectShoulderActiveDevice() {
        shoulderConnection.value?.disconnect()
    }

    /**
     * 断开连接
     */
    suspend fun disconnectHipActiveDevice() {
        hipConnection.value?.disconnect()
    }


    /* 定义蓝牙通信Gatt回调对象 */
    val bluetoothGattCallback = object : BluetoothGattCallback() {
        // 当连接状态改变时会回调onConnectionStateChange，比如连接建立，连接断开
        override fun onConnectionStateChange(gatt: BluetoothGatt?, status: Int, newState: Int) {
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                LogUtil.d(
                    "mytag",
                    "onConnectionStateChange: successfully connected to the GATT Server"
                )
                deviceRepository = DeviceRepository(this@BluetoothService)
                bluetoothGatt = gatt

                gatt?.discoverServices()
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                LogUtil.d("mytag", "onConnectionStateChange: disconnected from the GATT Server")
                onConnectionStateChange(status, newState)
            }
        }


        /**
         *  当BluetoothGatt.discoverServices()方法发现设备时执行
         *  监听外设特征值改变：
         *  无论是对外设写入新值，还是读取外设特定Characteristic的值，其实都只是单方通信。如果需要双向通信，可以在BluetoothGattCallback#onServicesDiscovered中对某个特征值设置监听（前提是该Characteristic具有NOTIFY属性）
         */
        override fun onServicesDiscovered(gatt: BluetoothGatt?, status: Int) {
            super.onServicesDiscovered(gatt, status)
            gattService = gatt?.getService(DeviceUUID.serviceUUID)
            batteryGattService = gatt?.getService(DeviceUUID.batteryServiceUUID)
            LogUtil.d("mytag", "onServicesDiscovered: ")
            onConnectedSuccess()
        }

        /**
         *  监听外设特征值改变
         */
        @Deprecated("Deprecated in Java")
        override fun onCharacteristicChanged(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic
        ) {
            super.onCharacteristicChanged(gatt, characteristic)
            serviceScope {
                when (characteristic.uuid) {
                    DeviceUUID.getAngleUUID -> {
                        val angle = characteristic.value[7].toInt().coerceIn(-15, 90)
//                        characteristic.value.let { value ->
//                            val hour = value[4].toInt().let { if (it < 10) "0$it" else it.toString()}
//                            val minute = value[5].toInt().let { if (it < 10) "0$it" else it.toString()}
//                            val second = value[6].toInt().let { if (it < 10) "0$it" else it.toString()}
//                            Timber.i("${hour}:${minute}:${second},${angle}")
//                        }
                        currentTime = System.currentTimeMillis()
                        onAngleChanged(
                            characteristic.value[7].toInt(),
                            angle,
                            characteristic.value[8],
                            currentTime
                        )
                        if (currentTime - lastUpdateTime >= 1000) {
                            lastUpdateTime = currentTime
                            notificationBuild.setContentText(
                                localeResources.getString(R.string.your_current_angle, "", angle)
                            )
                            NotificationUtils.sendNotification(
                                NotificationConstants.BLUETOOTH_NOTIFY_ID,
                                notificationBuild
                            )
                        }
                    }

                    else -> {
                        callbackMap.get(characteristic.uuid)?.invoke(characteristic.value)
                    }
                }
            }
        }

        override fun onCharacteristicWrite(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic,
            status: Int
        ) {
            super.onCharacteristicWrite(gatt, characteristic, status)
            serviceQueue.release()
        }

        @Deprecated("Deprecated in Java")
        override fun onCharacteristicRead(
            gatt: BluetoothGatt?,
            characteristic: BluetoothGattCharacteristic,
            status: Int
        ) {
            super.onCharacteristicRead(gatt, characteristic, status)
            callbackMap.get(characteristic.uuid)?.invoke(characteristic.value)
            serviceQueue.release()
        }

        override fun onDescriptorWrite(
            gatt: BluetoothGatt?,
            descriptor: BluetoothGattDescriptor,
            status: Int
        ) {
            super.onDescriptorWrite(gatt, descriptor, status)
            serviceQueue.release()
        }
    }

    /**
     * 设备连接成功后执行
     */
    private fun onConnectedSuccess() {
        setNotification(gattService?.getCharacteristic(DeviceUUID.getVersionUUID))
        setNotification(gattService?.getCharacteristic(DeviceUUID.getLocalAngleUUID))
        setNotification(batteryGattService?.getCharacteristic(DeviceUUID.getPowerUUID))
        onConnectionStateChange(
            BluetoothProfile.STATE_DISCONNECTED,
            BluetoothProfile.STATE_CONNECTED
        )
        LogUtil.d("mytag", "onConnectedSuccess: ")
    }

    /**
     * 阻塞线程等待回调执行完毕
     */
    private fun BluetoothGattCharacteristic?.runWithBlock(
        content: (BluetoothGattCharacteristic) -> Unit
    ) {
        serviceQueue.addQueue {
            this?.let {
                content(it)
            }
        }
    }

    val coroutineScope = MainScope()

    /**
     * 回调耗时操作放到协程中进行
     */
    private fun serviceScope(
        content: () -> Unit
    ) = coroutineScope.launch(Dispatchers.IO) {
        content()
    }

    /**
     * 读数据
     */
    fun read(
        characteristic: BluetoothGattCharacteristic?,
        callback: (value: ByteArray) -> Unit = {}
    ) {
        characteristic.runWithBlock {
            callbackMap.put(it.uuid, callback)
            bluetoothGatt?.readCharacteristic(it)
        }
    }

    /**
     * 开启监听writeCharacteristic数据的改变
     * 不开启默认不会监听到数据的改变
     */
    fun setNotification(
        characteristic: BluetoothGattCharacteristic?
    ) {
        characteristic.runWithBlock {
            bluetoothGatt?.setCharacteristicNotification(it, true)
            it.getDescriptor(DeviceUUID.descriptorUUID)?.let {
                it.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                bluetoothGatt?.writeDescriptor(it)
            }
        }
    }

    /**
     * 写数据
     */
    fun write(
        characteristic: BluetoothGattCharacteristic?,
        value: String,
        callbackUUID: UUID,
        callback: ((value: ByteArray) -> Unit)? = null
    ) {
        characteristic.runWithBlock {
            LogUtil.d("mytag", "write: ")
            callback?.let { value ->
                bluetoothGatt?.setCharacteristicNotification(it, true)
                callbackMap.put(callbackUUID, value)
            }
            it.value = value.hexToByteArray()
            it.writeType = BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
            bluetoothGatt?.writeCharacteristic(it)
        }
    }

    // (Kotlin中函数变量的非空初始化的一种方式)
    private var onConnectionStateChange: (oldStatus: Int, newStatus: Int) -> Unit =
        { oldStatus: Int, newStatus: Int -> }

    private var onAngleChanged: (primaryAngle: Int, angle: Int, maxAngle: Byte, timestamp: Long) -> Unit =
        { primaryAngle, angle, maxAngle, timestamp -> }

    /**
     * 监听当前设备角度
     */
    fun setOnAngleChangedListener(
        onAngleChanged: (primaryAngle: Int, angle: Int, maxAngle: Byte, timestamp: Long) -> Unit
    ) {
        this.onAngleChanged = onAngleChanged
        setNotification(gattService?.getCharacteristic(DeviceUUID.getAngleUUID))
    }

    fun close() {
        coroutineScope.cancel()
        bluetoothGatt?.let { gatt ->
            gattService = null
            batteryGattService = null
            gatt.close()
            bluetoothGatt = null
        }
    }

    private fun String.hexToByteArray(): ByteArray {
        require(length % 2 == 0) { "Unexpected hex string: $this" }
        val result = ByteArray(length / 2)
        for (i in result.indices) {
            val d1 = decodeHexDigit(this[i * 2]) shl 4
            val d2 = decodeHexDigit(this[i * 2 + 1])
            result[i] = (d1 + d2).toByte()
        }
        return result
    }

    private fun decodeHexDigit(c: Char): Int {
        return when (c) {
            in '0'..'9' -> c - '0'
            in 'a'..'f' -> c - 'a' + 10
            in 'A'..'F' -> c - 'A' + 10
            else -> throw IllegalArgumentException("Unexpected hex digit: $c")
        }
    }

    override fun onDestroy() {
        LogUtil.d("mytag", "service onDestroy: ")
        stopForeground(true)
        super.onDestroy()
    }

    /**
     * 创建服务通知
     */
    private fun createForegroundNotification(): NotificationCompat.Builder {
        val builder: NotificationCompat.Builder =
            NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.CHANNEL_BLUETOOTH_ID
            )
        //通知小图标
        builder.setSmallIcon(R.mipmap.ic_launcher_round)
        //通知标题
        builder.setContentTitle(localeResources.getString(R.string.using_aih_bluetooth_device))
        //设置通知显示的时间
        builder.setWhen(System.currentTimeMillis())
        //设定启动的内容
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this,
            1,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )
        builder.setContentIntent(pendingIntent)
        builder.setVibrate(null)
        builder.setSound(null)
        builder.setLights(0, 0, 0)
        //设置通知优先级
        builder.priority = NotificationCompat.PRIORITY_DEFAULT
        //设置为进行中的通知
        builder.setOngoing(true)
        builder.setAutoCancel(false)
        //创建通知并返回
        return builder
    }

    private inner class ServiceQueue {
        private val lock = Object()
        private val lock2 = Object()
        private var queue = LinkedBlockingDeque<Runnable>()

        // 需要保证onCharacteristicChanged和onCharacteristicWrite都执行才notify
        init {
            Thread {
                synchronized(lock2) {
                    lock2.wait()
                }
                while (true) {
                    synchronized(lock2) {
                        synchronized(lock) {
                            queue.poll()?.let {
                                it.run()
                                lock.wait()
                            } ?: lock2.wait()
                        }
                    }
                }
            }.start()
        }

        fun addQueue(runnable: Runnable) = serviceScope {
            queue.add(runnable)
            synchronized(lock2) {
                lock2.notifyAll()
            }
        }

        fun release() {
            synchronized(lock) {
                lock.notifyAll()
            }
        }
    }
}