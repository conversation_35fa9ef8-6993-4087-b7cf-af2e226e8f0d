package org.aihealth.ineck.bluetooth

import android.bluetooth.BluetoothGatt

/**
 * 蓝牙相关常量
 */
object BluetoothConstants {
    // 蓝牙扫描
    const val SCAN_PERIOD = 10000L // 扫描持续时间（毫秒）
    const val SCAN_INTERVAL = 5000L // 扫描间隔时间（毫秒）

    // 蓝牙连接
    const val CONNECTION_TIMEOUT = 30000L // 连接超时时间（毫秒）
    const val MTU_SIZE = 512 // 请求的MTU大小

    // 操作超时
    const val OPERATION_TIMEOUT = 10000L // 普通操作超时时间
    const val FIRMWARE_CHUNK_TIMEOUT = 2000L // 固件分块传输超时

    // 通知
    const val NOTIFICATION_ID = 10001
    const val NOTIFICATION_CHANNEL_ID = "bluetooth_channel"
    const val NOTIFICATION_CHANNEL_NAME = "Bluetooth Service"

    // 错误码
    object ErrorCode {
        const val SUCCESS = 0
        const val ERROR_BLUETOOTH_DISABLED = 1001
        const val ERROR_DEVICE_NOT_FOUND = 1002
        const val ERROR_CONNECTION_FAILED = 1003
        const val ERROR_DISCONNECTED = 1004
        const val ERROR_SERVICE_NOT_FOUND = 1005
        const val ERROR_CHARACTERISTIC_NOT_FOUND = 1006
        const val ERROR_OPERATION_FAILED = 1007
        const val ERROR_FIRMWARE_INVALID = 2001
    }

    // GATT状态映射
    val GATT_STATUS = mapOf(
        BluetoothGatt.GATT_SUCCESS to "GATT_SUCCESS",
        BluetoothGatt.GATT_FAILURE to "GATT_FAILURE",
        BluetoothGatt.GATT_CONNECTION_CONGESTED to "GATT_CONNECTION_CONGESTED",
        BluetoothGatt.GATT_INSUFFICIENT_AUTHENTICATION to "GATT_INSUFFICIENT_AUTHENTICATION",
        BluetoothGatt.GATT_INSUFFICIENT_ENCRYPTION to "GATT_INSUFFICIENT_ENCRYPTION",
        BluetoothGatt.GATT_INVALID_ATTRIBUTE_LENGTH to "GATT_INVALID_ATTRIBUTE_LENGTH",
        BluetoothGatt.GATT_INVALID_OFFSET to "GATT_INVALID_OFFSET",
        BluetoothGatt.GATT_READ_NOT_PERMITTED to "GATT_READ_NOT_PERMITTED",
        BluetoothGatt.GATT_REQUEST_NOT_SUPPORTED to "GATT_REQUEST_NOT_SUPPORTED",
        BluetoothGatt.GATT_WRITE_NOT_PERMITTED to "GATT_WRITE_NOT_PERMITTED"
    )
}