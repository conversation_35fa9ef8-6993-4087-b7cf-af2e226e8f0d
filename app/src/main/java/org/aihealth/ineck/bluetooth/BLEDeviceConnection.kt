package org.aihealth.ineck.bluetooth

import android.Manifest
import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService
import android.content.Context
import androidx.annotation.RequiresPermission
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch
import org.aihealth.ineck.bluetooth.DeviceUUID.batteryServiceUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.descriptorUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.getAngleUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.getLocalAngleUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.getPowerUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.getVersionUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.serviceUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.setVibrationTimeUUID
import org.aihealth.ineck.bluetooth.DeviceUUID.settingsUUID
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.hour
import org.aihealth.ineck.util.minute
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.second
import org.aihealth.ineck.util.year
import java.nio.ByteBuffer
import java.util.Calendar
import java.util.GregorianCalendar
import java.util.UUID

@SuppressLint("MissingPermission")
class BLEDeviceConnection @RequiresPermission("PERMISSION_BLUETOOTH_CONNECT") constructor(
    private val context: Context,
    private val bluetoothDevice: BluetoothDevice
) {
    private var deviceType = "aiNeck"
    private val scope = CoroutineScope(Dispatchers.Default + Job())
    private val writeCharacteristicChannel = Channel<UUID>()
    private var gatt: BluetoothGatt? = null
    private var powerCheckJob: Job? = null
    val services = MutableStateFlow<List<BluetoothGattService>>(emptyList())
    val isConnected = MutableStateFlow<Boolean?>(null)
    val version = MutableStateFlow<String?>(null)
    val sn = MutableStateFlow<String?>(null)
    val power = MutableStateFlow<Int?>(null)
    val angle = MutableStateFlow<Int?>(null)
    val maxAngle = MutableStateFlow<Int?>(null)
    val historyAngle = MutableStateFlow<Pair<Int, Long>?>(null)
    private val callback = object : BluetoothGattCallback() {

        override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
            super.onConnectionStateChange(gatt, status, newState)
            val connected = newState == BluetoothGatt.STATE_CONNECTED
            LogUtil.i("onConnectionStateChange: connected: $connected")
            if (connected) {
                //read the list of services
                services.value = gatt.services
                isConnected.value = true
                scope.launch {
                    delay(1000)
                    gatt.discoverServices()
                }
                // Start periodic power check
                startPeriodicPowerCheck()
            } else {
                LogUtil.d("start connected again")
//                gatt.connect()
                isConnected.value = false
                // Stop periodic power check
                stopPeriodicPowerCheck()
            }
        }

        @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
        override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
            super.onServicesDiscovered(gatt, status)
            services.value = gatt.services
            scope.launch {
                setNotification(gatt, getVersionUUID)
                setBatteryNotification(gatt, getPowerUUID)
                setNotification(gatt, getLocalAngleUUID)
                setNotification(gatt, getAngleUUID)
                setCurrentTime()
                setDeviceType(deviceType)
                getVersion()
                getSN()
                getPower()
                getLocalAngle()
            }
//                setNotification(gatt, getVersionUUID)
//                setBatteryNotification(getPowerUUID)
////                setNotification(gatt, getStepUUID)
////                setNotification(gatt, getAngleUUID)
//                setCurrentTime()
//                setDeviceType("aiNeck")
//                getVersion()
//                getPower()
//                setVibration(true)
//                getSN()
////                delay(2000)
////                setNotification(gatt, getLocalAngleUUID)
////                delay(2000)
//
////                delay(2000)
////                delay(4000)
////                getVersion()
////                delay(4000)
////                setDeviceType()
////                delay(4000)
////                getAngle()
////                delay(10000)
////                getPower()
////                delay(3000)
////                getSN()
////                delay(3000)
////                setVibration(true)
//            }
        }

        @Deprecated("Deprecated in Java")
        override fun onCharacteristicRead(
            gatt: BluetoothGatt,
            characteristic: BluetoothGattCharacteristic,
            status: Int
        ) {
            super.onCharacteristicRead(gatt, characteristic, status)
            LogUtil.d("channel onCharacteristicRead send: ${characteristic.uuid}")
            scope.launch {
                writeCharacteristicChannel.send(characteristic.uuid)
            }
            if (characteristic.uuid == getPowerUUID) {
                power.value = characteristic.value[0].toInt()
                LogUtil.d("channel onCharacteristicRead get power : ${characteristic.uuid}")
            }
        }

        override fun onCharacteristicWrite(
            gatt: BluetoothGatt,
            characteristic: BluetoothGattCharacteristic,
            status: Int
        ) {
            super.onCharacteristicWrite(gatt, characteristic, status)
            LogUtil.d("channel onCharacteristicWrite send: ${characteristic.uuid}")
            scope.launch {
                writeCharacteristicChannel.send(characteristic.uuid)
            }
        }

        @Deprecated("Deprecated in Java")
        override fun onCharacteristicChanged(
            gatt: BluetoothGatt,
            characteristic: BluetoothGattCharacteristic
        ) {
            super.onCharacteristicChanged(gatt, characteristic)
            if (characteristic.uuid == getVersionUUID) {
                if (characteristic.value.size == 18) {
                    val innerVersion = getVersion(characteristic.value)
                    if (innerVersion.contains(".")) {
                        version.value = innerVersion
                        LogUtil.i("Blue", "onCharacteristicChanged: version: $innerVersion")
                    } else {
                        sn.value = characteristic.value.toHexString()
                        LogUtil.i("Blue", "onCharacteristicChanged: sn: ${sn.value}")
                    }
                } else {
                    if (characteristic.value.size == 6) {
                        version.value = transferData(characteristic.value)
                    } else {
                        sn.value = characteristic.value.toHexString()
                    }
                }

            }
            if (characteristic.uuid == getPowerUUID) {
                power.value = characteristic.value[0].toInt()
                LogUtil.d("get power : ${characteristic.uuid}")
            }
            if (characteristic.uuid == getLocalAngleUUID) {
                isConnected.value = true
                historyAngle.value = getLocalAngle(characteristic.value)
            }
            if (characteristic.uuid == getAngleUUID) {
                isConnected.value = true
//                val angle1 = characteristic.value[7].toInt().coerceIn(-15, 90)
                angle.value = characteristic.value[7].toInt()
                maxAngle.value = (characteristic.value[8].toInt())
                LogUtil.d(
                    "Blue", "get angle: "+"${characteristic.value[7].toInt()} maxAngle: ${characteristic.value[8].toInt()}"
                )
            }
//            LogUtil.d(
//                "Blue",
//                "onCharacteristicChanged: ${characteristic.uuid}, value: ${String(value)}"
//            )
        }

        override fun onServiceChanged(gatt: BluetoothGatt) {
            super.onServiceChanged(gatt)
            LogUtil.d("Blue", "onServiceChanged")
        }
    }

    /**
     * 设置设备振动模式（干预/监测）
     * 震动提醒功能，默认关闭
     */
    suspend fun setVibration(enabled: Boolean) {
        LogUtil.i("setVibration: $enabled")
        writeSetting(if (enabled) "07" else "06")
    }

    suspend fun setVibrate() {
        writeSetting("0F")
    }

    suspend fun getSN() {
        LogUtil.d("Blue", "getSN")
        writeSetting("0A")
    }

    suspend fun getPower() {
        val uuid = writeCharacteristicChannel.receive()
        LogUtil.d("channel getPower receivce: ${uuid}")
        val service = services.value.firstOrNull { it.uuid == batteryServiceUUID }
        ?.getCharacteristic(getPowerUUID)
        LogUtil.d("getPower service: ${service}")
        gatt?.readCharacteristic(service)
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun onHeadUpCalibrating() {
        clearCalibrationData()
        startFirstCalibration()
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun onBendNeckCalibrating() {
        startSecondCalibration()
    }

    /**
     * 清空校准数据
     */
    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun clearCalibrationData() {
        writeSetting("0C")
    }

    /**
     * 第一次校准
     * 设置校准基准角度
     */
    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun startFirstCalibration() {
        writeSetting("01")
    }

    /**
     * 第二次校准
     * 设定最大偏转角
     */
    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun startSecondCalibration() {
        writeSetting("02")
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun setNotification(gatt: BluetoothGatt, uuid: UUID) {
        gatt.getService(serviceUUID)?.let {
            val settingCharacteristic = it.getCharacteristic(uuid)
            gatt.setCharacteristicNotification(settingCharacteristic, true)
            val descriptor = settingCharacteristic.getDescriptor(descriptorUUID)
            descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
            gatt.writeDescriptor(descriptor)
            delay(500)

        }
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun setBatteryNotification(gatt: BluetoothGatt, uuid: UUID) {
        services.value.firstOrNull { it.uuid == batteryServiceUUID }?.let {
            it.getCharacteristic(uuid)?.let { batteryCharacteristic ->
                gatt.setCharacteristicNotification(batteryCharacteristic, true)
                val descriptor = batteryCharacteristic.getDescriptor(descriptorUUID)
                descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                gatt.writeDescriptor(descriptor)
                delay(500)
            }
        }
    }

    suspend fun setVibrationFrequency(frequency: String) {
        writeSetting(frequency, setVibrationTimeUUID)
    }

    suspend fun setMaxAngle(maxAngle: String) {
        writeSetting(maxAngle, DeviceUUID.setMaxAngleUUID)
    }

    suspend fun setVibrationAngle(angle: String) {
        setMaxAngle(angle)
    }
//    @RequiresPermission()
//    suspend fun fun setStepAndCaloriasNotification(uuid: UUID) {
//        val service = services.value.firstOrNull { it.uuid == serviceUUID }
//        service?.getCharacteristic(uuid)?.let { batteryCharacteristic ->
//            gatt?.setCharacteristicNotification(batteryCharacteristic, true)
//            val descriptor = batteryCharacteristic.getDescriptor(descriptorUUID)
//            descriptor.value = BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
//            gatt?.writeDescriptor(descriptor)
//            delay(500)
//        }
//    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun getVersion() {
        LogUtil.d("Blue", "getVersion")
        writeSetting("08")
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun writeSetting(code: String, uuid: UUID = settingsUUID) {
        LogUtil.d("channel writeSetting: revcie $code")
        val uuid1 = writeCharacteristicChannel.receive()
        LogUtil.d("channel uuid:${uuid1} writeSetting: write $code")
        val service = services.value.firstOrNull { it.uuid == serviceUUID }
        service?.getCharacteristic(uuid)?.let { settingCharacteristic ->
//            bluetoothGatt.value?.setCharacteristicNotification(getVersionCharacteristic, true)
            settingCharacteristic.value = code.hexToByteArray()
            settingCharacteristic.writeType = BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
            gatt?.writeCharacteristic(settingCharacteristic)
        }

    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun setDeviceType(deviceType: String) {
        LogUtil.d("Blue", "setDeviceType: $deviceType")
        val code = when (deviceType) {
            "aiNeck" -> "0001"
            "aiBack" -> "0002"
            "KneeJoint" -> "0002"
            else -> "0001"
        }
        writeSetting(code)
    }

    /**
     * 设置设备时间
     */
    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun setCurrentTime() {
        val calendar = Calendar.getInstance()
        val hexYear = (calendar.year / 100).toHexStr() + (calendar.year % 100).toHexStr()
        val hexMonth = (calendar.month + 1).toHexStr()
        val hexDate = calendar.date.toHexStr()
        val hexHour = calendar.hour.toHexStr()
        val hexMinute = calendar.minute.toHexStr()
        val hexSecond = calendar.second.toHexStr()
        LogUtil.d("channel setCurrentTime send: getVersionUUID")
        scope.launch {
            writeCharacteristicChannel.send(getVersionUUID)
        }
        writeSetting("$hexYear$hexMonth$hexDate$hexHour$hexMinute$hexSecond")
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun getLocalAngle() {
        LogUtil.d("Blue", "getAngle")
        writeSetting("04")
    }

    fun ByteArray.toHexString(): String {
        if (isEmpty()) {
            return ""
        }
        val sb = StringBuilder()
        for (byte in this) {
            sb.append(Integer.toHexString(byte.toInt() and 0XF))
        }
        return sb.toString()
    }

    fun getLocalAngle(value: ByteArray): Pair<Int, Long> {
        if (value.size < 9) {
            return Pair(0, System.currentTimeMillis())
        }
        val year = value[0].toInt() * 100 + value[1].toInt()
        val month = value[2].toInt()
        val day = value[3].toInt()
        val hour = value[4].toInt()
        val minute = value[5].toInt()
        val second = value[6].toInt()
        val angle = value[7].toInt()
//            val maxAngle = value[8].toInt()
        LogUtil.d(
            "mytag",
            "getLocalAngle: ${year}-${month}-${day} ${hour}:${minute}:${second} (angle: ${angle})"
        )
        GregorianCalendar(year, month - 1, day, hour, minute, second).timeInMillis.let {
            if (it > 0) {
                return Pair(angle, it)
            }
        }
        return Pair(0, System.currentTimeMillis())
    }

    private fun String.hexToByteArray(): ByteArray {
        require(length % 2 == 0) { "Unexpected hex string: $this" }
        val result = ByteArray(length / 2)
        for (i in result.indices) {
            val d1 = decodeHexDigit(this[i * 2]) shl 4
            val d2 = decodeHexDigit(this[i * 2 + 1])
            result[i] = (d1 + d2).toByte()
        }
        return result
    }

    private fun decodeHexDigit(c: Char): Int {
        return when (c) {
            in '0'..'9' -> c - '0'
            in 'a'..'f' -> c - 'a' + 10
            in 'A'..'F' -> c - 'A' + 10
            else -> throw IllegalArgumentException("Unexpected hex digit: $c")
        }
    }

    fun transferData(data: ByteArray): String {
        val byteBuffer = ByteBuffer.wrap(data)
        LogUtil.d("transfer:${byteBuffer.array().contentToString()}")
        val version = byteBuffer.array().toString(Charsets.US_ASCII)
        return version
    }

    fun getVersion(data: ByteArray): String {
        val byteBuffer = ByteBuffer.wrap(data)
        LogUtil.d("getVersion:${byteBuffer.array().contentToString()}")
        val deviceDFUVer = if (byteBuffer.getShort(0) < 0) {
            Short.MAX_VALUE - (Short.MIN_VALUE - byteBuffer.getShort(0)) + 1
        } else {
            byteBuffer.getShort(0).toInt()
        }
        val version =
            ((deviceDFUVer / 10).toString().replace("0", ".") + deviceDFUVer % 10).replace(
                "..",
                ".0"
            )
        return version
    }


    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    suspend fun disconnect() {
        stopPeriodicPowerCheck()
        gatt?.disconnect()
        delay(1000)
        gatt?.close()
    }

    @RequiresPermission(PERMISSION_BLUETOOTH_CONNECT)
    fun connect(deviceType: String) {
        this.deviceType = deviceType
        gatt = bluetoothDevice.connectGatt(context, false, callback)
    }

    @SuppressLint("MissingPermission")
    fun discoverServices() {
        gatt?.discoverServices()
    }


    /**
     * 将int转化为16进制格式
     * formatNum: 16进制位数，默认2位数，不足用0补齐
     */

    private fun Int.toHexStr(formatNum: Int = 2): String {
        return String.format("%0${formatNum}X", this)
    }

    private fun startPeriodicPowerCheck() {
        powerCheckJob?.cancel()
        powerCheckJob = scope.launch {
            while (isConnected.value == true) {
                try {
                    getPower()
                    LogUtil.d("Periodic power check executed")
                } catch (e: Exception) {
                    LogUtil.e("Failed to get power: ${e.message}")
                }
                delay(60000) // 1 minute delay
            }
        }
    }

    private fun stopPeriodicPowerCheck() {
        powerCheckJob?.cancel()
        powerCheckJob = null
    }
}