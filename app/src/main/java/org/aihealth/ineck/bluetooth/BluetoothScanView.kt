package org.aihealth.ineck.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import androidx.annotation.RequiresPermission
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.scrollable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog

const val PERMISSION_BLUETOOTH_SCAN = "android.permission.BLUETOOTH_SCAN"
const val PERMISSION_BLUETOOTH_CONNECT = "android.permission.BLUETOOTH_CONNECT"

@SuppressLint("MissingPermission")
@Composable
fun ScanDialog(
    modifier: Modifier = Modifier,
    visible: Boolean,
    isScanning: Boolean,
    activeDevice: BluetoothDevice? = null,
    foundDevices: List<BluetoothDevice>,
    startScanning: () -> Unit,
    stopScanning: () -> Unit,
    selectDevice: (BluetoothDevice) -> Unit,
    unselectDevice: () -> Unit,
    isDeviceConnected: Boolean,
    connect: () -> Unit,
    disconnect: () -> Unit,
    discoverServices: () -> Unit,
    close: () -> Unit

) {
    if (visible) {
        Dialog(onDismissRequest = {
            stopScanning()
        }) {
            Card {
                if (activeDevice == null) {
                    BluetoothScanView(
                        isScanning = isScanning,
                        foundDevices = foundDevices,
                        startScanning = startScanning,
                        stopScanning = stopScanning,
                        selectDevice = selectDevice
                    )
                } else {
                    DeviceScreen(
                        isDeviceConnected = isDeviceConnected,
                        disconnect = disconnect,
                        connect = connect,
                        discoverServices = discoverServices,
                        close = close
                    )
                }
            }
        }
    }
}

@RequiresPermission(allOf = [PERMISSION_BLUETOOTH_SCAN, PERMISSION_BLUETOOTH_CONNECT])
@Composable
fun BluetoothScanView(
    modifier: Modifier = Modifier,
    isScanning: Boolean,
    foundDevices: List<BluetoothDevice>,
    startScanning: () -> Unit,
    stopScanning: () -> Unit,
    selectDevice: (BluetoothDevice) -> Unit

) {
    Card {
        Column(
            Modifier.padding(horizontal = 10.dp)
        ) {
            if (isScanning) {
                Text("Scanning...")

                Button(onClick = stopScanning) {
                    Text("Stop Scanning")
                }
            } else {
                Button(onClick = startScanning) {
                    Text("Start Scanning")
                }
            }

            LazyColumn(
                verticalArrangement = Arrangement.spacedBy(10.dp)
            ) {
                items(foundDevices) { device ->
                    DeviceItem(
                        deviceName = device.name + " " + device.address,
                        selectDevice = { selectDevice(device) }
                    )
                }
            }
        }
    }

}

@Composable
fun DeviceItem(deviceName: String?, selectDevice: () -> Unit) {
    Card(
        modifier = Modifier
            .fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant,
        )
    ) {
        Column(modifier = Modifier.padding(10.dp)) {
            Text(
                text = deviceName ?: "[Unnamed]",
                textAlign = TextAlign.Center,
            )
            Button(onClick = selectDevice) {
                Text("Connect")
            }
        }
    }
}


@Composable
fun DeviceScreen(
    isDeviceConnected: Boolean,
    connect: () -> Unit,
    disconnect: () -> Unit,
    discoverServices: () -> Unit,
    close: () -> Unit
) {
    Column(
        Modifier.scrollable(rememberScrollState(), Orientation.Vertical)
    ) {
        Button(onClick = connect) {
            Text("1. Connect")
        }
        Text("Device connected: $isDeviceConnected")
        Button(onClick = discoverServices, enabled = isDeviceConnected) {
            Text("2. Discover Services")
        }
        OutlinedButton(modifier = Modifier.padding(top = 40.dp), onClick = disconnect) {
            Text("Disconnect")
        }
        OutlinedButton(modifier = Modifier.padding(top = 40.dp), onClick = close) {
            Text("close")
        }
    }
}
