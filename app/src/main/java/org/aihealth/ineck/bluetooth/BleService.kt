package org.aihealth.ineck.bluetooth

import android.app.PendingIntent
import android.app.Service
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGattCharacteristic
import android.content.Intent
import android.content.pm.ServiceInfo
import android.os.Binder
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.NotificationConstants
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.localeResources
import java.util.UUID

class BleService : Service() {
    companion object {
        private const val TAG = "BluetoothService"

        // 用于启动前台服务的Intent Action
        const val ACTION_START_FOREGROUND_SERVICE = "com.example.myapplication.bluetooth.START_FOREGROUND"
        const val ACTION_STOP_FOREGROUND_SERVICE = "com.example.myapplication.bluetooth.STOP_FOREGROUND"

        // 传递参数的Key
        const val EXTRA_DEVICE_ADDRESS = "device_address"
    }

    val neckConnection = MutableStateFlow<BluetoothConnection?>(null)
    val backConnection = MutableStateFlow<BluetoothConnection?>(null)

    // 自定义 Binder，用于获取 Service 实例和 Messenger
    inner class LocalBinder : Binder() {
        fun getService(): BleService = this@BleService
    }
    private val binder = LocalBinder()
    lateinit var notificationBuild: NotificationCompat.Builder


    override fun onBind(intent: Intent): IBinder {
        return binder
    }

    override fun onUnbind(intent: Intent?): Boolean {
        return super.onUnbind(intent)
    }

    override fun onCreate() {
        super.onCreate()
        LogUtil.i("BleService onCreate")
        notificationBuild = createForegroundNotification().apply {
            setContentText(localeResources.getString(R.string.connecting_aih_bluetooth_device))
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            startForeground(
                NotificationConstants.BLUETOOTH_NOTIFY_ID,
                notificationBuild.build(),
                ServiceInfo.FOREGROUND_SERVICE_TYPE_CONNECTED_DEVICE
            )
        } else {
            startForeground(
                NotificationConstants.BLUETOOTH_NOTIFY_ID,
                notificationBuild.build()
            )
        }
    }


    /**
     * 创建服务通知
     */
    private fun createForegroundNotification(): NotificationCompat.Builder {
        val builder: NotificationCompat.Builder =
            NotificationCompat.Builder(
                applicationContext,
                NotificationConstants.CHANNEL_BLUETOOTH_ID
            )
        //通知小图标
        builder.setSmallIcon(R.mipmap.ic_launcher_round)
        //通知标题
        builder.setContentTitle(localeResources.getString(R.string.using_aih_bluetooth_device))
        //设置通知显示的时间
        builder.setWhen(System.currentTimeMillis())
        //设定启动的内容
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_NEW_TASK
        val pendingIntent: PendingIntent = PendingIntent.getActivity(
            this,
            1,
            intent,
            PendingIntent.FLAG_IMMUTABLE
        )
        builder.setContentIntent(pendingIntent)
        builder.setVibrate(null)
        builder.setSound(null)
        builder.setLights(0, 0, 0)
        //设置通知优先级
        builder.priority = NotificationCompat.PRIORITY_DEFAULT
        //设置为进行中的通知
        builder.setOngoing(true)
        builder.setAutoCancel(false)
        //创建通知并返回
        return builder
    }

    /**
     * 通过地址获取连接
     * @param address 设备地址
     * @return 连接对象，如果不存在则返回null
     */
    fun getConnection(address: String,type: DeviceType): BluetoothConnection? {
        return when (type) {
            DeviceType.aiNeck -> {
                neckConnection.value
            }
            DeviceType.aiBack -> {
                backConnection.value
            }
            else -> {
                null
            }
        }
    }
    fun getConnectionByDeviceType(deviceType: String): BluetoothConnection? {
        return when (deviceType) {
            DeviceType.aiNeck.name -> {
                neckConnection.value
            }
            DeviceType.aiBack.name -> {
                backConnection.value
            }

            else -> {
                null
            }
        }
    }

    /**
     * 创建新连接
     * @param device 蓝牙设备
     * @param autoConnect 是否自动重连
     * @param deviceType 设备类型
     * @return 连接对象
     */
    fun createConnection(
        device: BluetoothDevice,
        autoConnect: Boolean = false,
        deviceType: DeviceType = DeviceType.None
    ): BluetoothConnection? {
        val address = device.address
        LogUtil.d(TAG, "createConnection: $address, $autoConnect, $deviceType")
        when(deviceType){
            DeviceType.aiNeck -> {
                val connect = BluetoothConnection(baseApplication,device, autoConnect, deviceType)
                neckConnection.value = connect
                return connect
            }
            DeviceType.aiBack -> {
                val connect = BluetoothConnection(baseApplication,device, autoConnect, deviceType)
                backConnection.value = connect
                return connect
            }
            else ->{
                return BluetoothConnection(baseApplication,device, autoConnect, deviceType)
            }

        }
    }
    /**
     * 设置特征通知
     */
    suspend fun setCharacteristicNotification(
        connection: BluetoothConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID,
        enable: Boolean
    ): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                connection.setCharacteristicNotification(serviceUuid, characteristicUuid, enable)
            } catch (e: Exception) {
                LogUtil.e(TAG, "设置特征通知失败", e)
                false
            }
        }
    }
    /**
     * 写入特征值
     */
    suspend fun writeCharacteristic(
        connection: BluetoothConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID,
        value: ByteArray,
        writeType: Int = BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
    ): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                connection.writeCharacteristic(serviceUuid, characteristicUuid, value, writeType)
            } catch (e: Exception) {
                LogUtil.e(TAG, "写入特征值失败", e)
                false
            }
        }
    }

    /**
     * 读取特征值
     */
    suspend fun readCharacteristic(
        connection: BluetoothConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): ByteArray? {
        return withContext(Dispatchers.IO) {
            try {
                connection.readCharacteristic(serviceUuid, characteristicUuid)
            } catch (e: Exception) {
                LogUtil.e(TAG, "读取特征值失败", e)
                null
            }
        }
    }

    /**
     * 观察特征值变化
     */
    fun observeCharacteristic(
        connection: BluetoothConnection,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Flow<ByteArray?> {
        return flow {
            try {
                connection.observeCharacteristic(serviceUuid, characteristicUuid)
                    .collect { value ->
                        emit(value)
                    }
            } catch (e: Exception) {
                LogUtil.e(TAG, "观察特征值失败", e)
            }
        }
    }

    /**
     * 更新通知显示设备的运行模式和角度
     * @param deviceType 设备类型名称
     * @param title 通知标题，通常包含设备名称和运行模式
     * @param content 通知内容，通常包含角度信息
     */
    fun updateNotification(deviceType: String, title: String, content: String) {
        try {
            LogUtil.d(TAG, "更新通知: deviceType=$deviceType, title=$title, content=$content")
            
            // 更新通知内容
            notificationBuild.apply {
                setContentTitle(title)
                setContentText(content)
            }
            
            // 发送更新后的通知
            val notificationManager = baseApplication.getSystemService(NOTIFICATION_SERVICE) as android.app.NotificationManager
            notificationManager.notify(NotificationConstants.BLUETOOTH_NOTIFY_ID, notificationBuild.build())
        } catch (e: Exception) {
            LogUtil.e(TAG, "更新通知失败", e)
        }
    }

}