package org.aihealth.ineck.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattService
import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil
import java.util.UUID
import java.util.concurrent.atomic.AtomicBoolean

/**
 * 蓝牙连接类
 * 管理单个设备的连接和通信
 */
class BluetoothConnection(
    private val context: Context,
    private val device: BluetoothDevice,
    private var autoConnect: Boolean = false,
    private var deviceType: DeviceType = DeviceType.None
) {
    companion object {
        private const val TAG = "BluetoothConnection"
        private const val MAX_RECONNECT_ATTEMPTS = 3
        private const val RECONNECT_DELAY_MS = 2000L
    }

    // 协程作用域
    private val connectionScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // GATT回调
    private val gattCallback = GattCallback(connectionScope, device.address)

    // 当前状态
    private val _connectionState = MutableStateFlow<ConnectionState?>(null)
    private val _isConnected = MutableStateFlow(false)
    val connectionState: StateFlow<ConnectionState?> = _connectionState
    val isConnected: StateFlow<Boolean> = _isConnected

    // 重连相关
    private var reconnectJob: Job? = null
    private var reconnectAttempts = 0
    private var isReconnecting = false

    // 操作队列，防止并发操作GATT
    private val operationInProgress = AtomicBoolean(false)

    // MTU大小
    private val _mtu = MutableStateFlow(23) // 默认MTU大小
    val mtu: StateFlow<Int> = _mtu

    // 设备类型状态
    private val _deviceType = MutableStateFlow(deviceType)
    val deviceTypeFlow: StateFlow<DeviceType> = _deviceType

    // 初始化
    init {
        observeConnectionState()
    }

    /**
     * 观察连接状态
     */
    private fun observeConnectionState() {
        LogUtil.d(TAG, "Starting to observe connection state for device: ${device.address}")

        connectionScope.launch {
            try {
                LogUtil.d(TAG, "Collection of gattCallback.connectionState started")
                gattCallback.connectionState.collect { state ->
                    LogUtil.d(
                        TAG,
                        "Received connection state from flow: $state for device ${device.address}"
                    )
                    _connectionState.value = state

                    when (state) {
                        is ConnectionState.Connected -> {
                            LogUtil.d(TAG, "Updating isConnected to true for device: ${device.address}")
                            _isConnected.value = true
                            // 重置重连计数
                            reconnectAttempts = 0
                            isReconnecting = false
                            // 取消重连任务
                            reconnectJob?.cancel()
                            reconnectJob = null
                        }

                        is ConnectionState.ServicesDiscovered -> {
                            LogUtil.d(
                                TAG,
                                "Services discovered for device: ${device.address}, updating isConnected to true"
                            )
                            _isConnected.value = true
                            // 重置重连计数
                            reconnectAttempts = 0
                            isReconnecting = false
                            // 取消重连任务
                            reconnectJob?.cancel()
                            reconnectJob = null
                            // 尝试请求更大的MTU
//                            requestMtu(BluetoothConstants.MTU_SIZE)
                        }

                        is ConnectionState.Disconnected -> {
                            LogUtil.d(
                                TAG,
                                "Device disconnected: ${device.address}, updating isConnected to false"
                            )
                            _isConnected.value = false

                            // 如果是自动重连模式，尝试重新连接
                            if (autoConnect && !isReconnecting) {
                                handleDisconnection()
                            }
                        }

                        is ConnectionState.Failed -> {
                            LogUtil.d(
                                TAG,
                                "Connection failed for device: ${device.address}, updating isConnected to false"
                            )
                            _isConnected.value = false

                            // 如果是自动重连模式，尝试重新连接
                            if (autoConnect && !isReconnecting) {
                                handleDisconnection()
                            }
                        }

                        else -> {
                            // 不改变连接状态
                            LogUtil.d(
                                TAG,
                                "Other connection state received: $state for device ${device.address}"
                            )
                        }
                    }
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "Error observing connection state", e)
            }
        }

        // 监听MTU变化
        connectionScope.launch {
            try {
                LogUtil.d(TAG, "Collection of gattCallback.mtuChanged started")
                gattCallback.mtuChanged.collect { newMtu ->
                    LogUtil.d(TAG, "MTU changed to $newMtu for device ${device.address}")
                    _mtu.value = newMtu
                }
            } catch (e: Exception) {
                LogUtil.e(TAG, "Error collecting MTU changes", e)
            }
        }
    }

    /**
     * 处理断开连接
     */
    private fun handleDisconnection() {
        // 如果已经在重连，不要重复尝试
        if (isReconnecting) return

        // 如果重连次数超过最大尝试次数，停止重连
        if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
            LogUtil.d(TAG, "Max reconnect attempts reached for device: ${device.address}")
            return
        }

        isReconnecting = true
        reconnectAttempts++

        LogUtil.d(
            TAG,
            "Attempting to reconnect to device: ${device.address}, attempt: $reconnectAttempts"
        )

        // 延迟一段时间后尝试重连
        reconnectJob = connectionScope.launch {
            delay(RECONNECT_DELAY_MS)
            try {
                connect()
            } catch (e: Exception) {
                LogUtil.e(TAG, "Reconnection attempt failed", e)
                isReconnecting = false
            }
        }
    }

    /**
     * 连接设备
     * @return 连接结果
     */
    @SuppressLint("MissingPermission")
    suspend fun connect(): Boolean {
        if (_isConnected.value) {
            LogUtil.d(TAG, "Already connected to ${device.address}")
            return true
        }

        return try {
            LogUtil.d(TAG, "Connecting to ${device.address}")

            // 设置连接中状态
            _connectionState.value = ConnectionState.Connecting(device.address)

            // 连接到GATT服务器
            withContext(Dispatchers.IO) {
                device.connectGatt(context, autoConnect, gattCallback, BluetoothDevice.TRANSPORT_LE)
            }
            // 等待连接成功并发现服务
            withTimeout(BluetoothConstants.CONNECTION_TIMEOUT) {
                gattCallback.connectionState
                    .filter { it is ConnectionState.ServicesDiscovered || it is ConnectionState.Failed }
                    .first()
            }

            _isConnected.value
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error connecting to device", e)
            _connectionState.value = ConnectionState.Failed(
                device.address,
                BluetoothConstants.ErrorCode.ERROR_CONNECTION_FAILED,
                e.message ?: "Connection timeout"
            )
            false
        }
    }

    /**
     * 断开连接
     * @param disableAutoReconnect 是否禁用自动重连（默认为true）
     */
    @SuppressLint("MissingPermission")
    fun disconnect(disableAutoReconnect: Boolean = true) {
        LogUtil.d(TAG, "Disconnecting from ${device.address}")
        try {
            if (disableAutoReconnect) {
                // 临时保存当前状态
                val originalAutoConnect = autoConnect
                
                // 禁用自动重连
                autoConnect = false
                
                _connectionState.value = ConnectionState.Disconnecting(device.address)
                gattCallback.gatt?.disconnect()
                
                // 延迟恢复原始autoConnect值
                connectionScope.launch {
                    delay(1000)
                    autoConnect = originalAutoConnect
                }
            } else {
                _connectionState.value = ConnectionState.Disconnecting(device.address)
                gattCallback.gatt?.disconnect()
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error disconnecting", e)
        }
    }

    fun setAutoReconnect(enable: Boolean){
        autoConnect = enable
    }
    /**
     * 请求更大的MTU
     * @param mtu 请求的MTU大小
     */
    @SuppressLint("MissingPermission")
    fun requestMtu(mtu: Int) {
        val gatt = gattCallback.gatt ?: return
        try {
            gatt.requestMtu(mtu)
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error requesting MTU", e)
        }
    }

    /**
     * 读取特征值
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征UUID
     * @return 特征值读取结果
     */
    @SuppressLint("MissingPermission")
    suspend fun readCharacteristic(
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): ByteArray? {
        val gatt = gattCallback.gatt ?: return null

        val characteristic = BluetoothUtils.findCharacteristic(
            gatt, serviceUuid, characteristicUuid
        ) ?: return null

        return try {
            // 等待操作队列
            waitForOperationSlot()

            // 读取特征值
            BluetoothUtils.performCharacteristicOperation(
                BluetoothUtils.CharacteristicOperation(
                    type = BluetoothUtils.CharacteristicOperationType.READ,
                    characteristic = characteristic,
                    gatt = gatt
                )
            )

            // 等待特征值读取完成
            withTimeout(BluetoothConstants.OPERATION_TIMEOUT) {
                gattCallback.characteristicRead
                    .filter { it.characteristicUuid == characteristicUuid && it.serviceUuid == serviceUuid }
                    .first()
                    .value
            }
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error reading characteristic", e)
            null
        } finally {
            operationInProgress.set(false)
        }
    }

    /**
     * 写入特征值
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征UUID
     * @param data 要写入的数据
     * @param writeType 写入类型
     * @return 写入是否成功
     */
    @SuppressLint("MissingPermission")
    suspend fun writeCharacteristic(
        serviceUuid: UUID,
        characteristicUuid: UUID,
        data: ByteArray,
        writeType: Int = BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
    ): Boolean {
        val gatt = gattCallback.gatt ?: return false

        val characteristic = BluetoothUtils.findCharacteristic(
            gatt, serviceUuid, characteristicUuid
        ) ?: return false

        return try {
            // 等待操作队列
            waitForOperationSlot()
            LogUtil.e(TAG,"${data},writeCharacteristic wait is finished")
            // 写入特征值
            BluetoothUtils.performCharacteristicOperation(
                BluetoothUtils.CharacteristicOperation(
                    type = BluetoothUtils.CharacteristicOperationType.WRITE,
                    characteristic = characteristic,
                    gatt = gatt,
                    data = data,
                    writeType = writeType
                )
            )

            // 等待特征值写入完成
            val result = withTimeout(BluetoothConstants.OPERATION_TIMEOUT) {
                gattCallback.characteristicWrite
                    .filter { it.characteristicUuid == characteristicUuid && it.serviceUuid == serviceUuid }
                    .first()
            }

            result.status == BluetoothGatt.GATT_SUCCESS
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error writing characteristic:${characteristic.uuid},data${data}", e)
            false
        } finally {
            operationInProgress.set(false)
        }
    }

    /**
     * 写入特征值
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征UUID
     * @param data 要写入的数据
     * @param writeType 写入类型
     * @return 写入是否成功
     */
    @SuppressLint("MissingPermission")
    suspend fun writeCharacteristic(
        serviceUuid: UUID,
        characteristicUuid: UUID,
        callBackCharacteristicUuid: UUID,
        data: ByteArray,
        writeType: Int = BluetoothGattCharacteristic.WRITE_TYPE_DEFAULT
    ): Boolean {
        val gatt = gattCallback.gatt ?: return false

        val characteristic = BluetoothUtils.findCharacteristic(
            gatt, serviceUuid, characteristicUuid
        ) ?: return false

        return try {
            // 等待操作队列
            waitForOperationSlot()
            LogUtil.e(TAG,"writeCharacteristic wait is finished")
            // 写入特征值
            BluetoothUtils.performCharacteristicOperation(
                BluetoothUtils.CharacteristicOperation(
                    type = BluetoothUtils.CharacteristicOperationType.WRITE,
                    characteristic = characteristic,
                    gatt = gatt,
                    data = data,
                    writeType = writeType
                )
            )

            // 等待特征值写入完成
            val result = withTimeout(BluetoothConstants.OPERATION_TIMEOUT) {
                gattCallback.characteristicWrite
                    .filter { it.characteristicUuid == callBackCharacteristicUuid && it.serviceUuid == serviceUuid }
                    .first()
            }

            result.status == BluetoothGatt.GATT_SUCCESS
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error writing characteristic:${characteristic.uuid},data${data}", e)
            false
        } finally {
            operationInProgress.set(false)
        }
    }

    /**
     * 启用特征值通知
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征UUID
     * @param enable 是否启用
     * @return 操作是否成功
     */
    @SuppressLint("MissingPermission")
    suspend fun setCharacteristicNotification(
        serviceUuid: UUID,
        characteristicUuid: UUID,
        enable: Boolean
    ): Boolean {
        val gatt = gattCallback.gatt ?: return false

        val characteristic = BluetoothUtils.findCharacteristic(
            gatt, serviceUuid, characteristicUuid
        ) ?: return false

        return try {
            // 等待操作队列
            waitForOperationSlot()

            // 设置通知
            BluetoothUtils.performCharacteristicOperation(
                BluetoothUtils.CharacteristicOperation(
                    type = BluetoothUtils.CharacteristicOperationType.ENABLE_NOTIFICATION,
                    characteristic = characteristic,
                    gatt = gatt,
                    enabled = enable
                )
            )

            true
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error setting notification", e)
            false
        } finally {
            operationInProgress.set(false)
        }
    }

    /**
     * 获取特征值通知Flow
     * @param serviceUuid 服务UUID
     * @param characteristicUuid 特征UUID
     * @return 特征值变化通知Flow
     */
    fun observeCharacteristic(
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): Flow<ByteArray?> {
        return gattCallback.characteristicChanged
            .filter { it.serviceUuid == serviceUuid && it.characteristicUuid == characteristicUuid }
            .map { it.value }
    }

    /**
     * 等待操作队列
     */
    private suspend fun waitForOperationSlot() {
        var acquired = false
        while (!acquired) {
            acquired = operationInProgress.compareAndSet(false, true)
            if (!acquired) {
                kotlinx.coroutines.delay(10)
            }
        }
    }

    /**
     * 关闭连接并释放资源
     */
    fun close() {
        LogUtil.d(TAG, "Closing connection to ${device.address}")
        try {
            disconnect()
            gattCallback.close()
            connectionScope.cancel()
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error closing connection", e)
        }
    }

    /**
     * 获取设备服务列表
     */
    @SuppressLint("MissingPermission")
    fun getServices(): List<BluetoothGattService> {
        return gattCallback.gatt?.services ?: emptyList()
    }

    /**
     * 设置设备类型
     */
    fun setDeviceType(type: DeviceType) {
        this.deviceType = type
        _deviceType.value = type
    }

    /**
     * 获取设备类型
     */
    fun getDeviceType(): DeviceType {
        return deviceType
    }

    /**
     * 获取设备地址
     */
    fun getDeviceAddress(): String {
        return device.address
    }

    /**
     * 设置设备进入 DFU 模式
     */
    fun setDfuMode(enable: Boolean) {
        gattCallback.setDFU(enable)
    }


}