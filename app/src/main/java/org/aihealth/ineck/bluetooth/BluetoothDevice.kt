package org.aihealth.ineck.bluetooth

import android.bluetooth.BluetoothDevice
import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.model.DeviceType

@Parcelize
data class BLEClientState(
    val deviceType: DeviceType = DeviceType.None,
    val activeDevice: BluetoothDevice? = null,
    val isDeviceConnected: Boolean? = null,
    val serialNumber: String? = null,
    val version: String? = null,
    val power: Int? = null,
    val angle: Int? = null,
    val historyAngle: Pair<Int, Long>? = null,// <Angle,Time>
    val maxAngle: Int? = null,
) : Parcelable {
    override fun toString(): String {
        return "BLEClientState(deviceType=${deviceType.name}, activeDevice=${activeDevice?.address}, isDeviceConnected=$isDeviceConnected, serialNumber=$serialNumber, version=$version, power=$power, angle=${angle},maxAngle=${maxAngle}, historyAngle=(time:${historyAngle?.second},angle:${historyAngle?.first})"
    }
}