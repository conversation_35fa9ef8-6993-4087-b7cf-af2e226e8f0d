package org.aihealth.ineck.bluetooth

import android.annotation.SuppressLint
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCallback
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothGattService
import android.bluetooth.BluetoothProfile
import androidx.compose.runtime.mutableStateOf
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.launch
import org.aihealth.ineck.util.LogUtil
import java.util.UUID


/**
 * GATT回调实现类，处理蓝牙设备连接和数据通信
 */
@SuppressLint("MissingPermission")
class GattCallback(
    private val coroutineScope: CoroutineScope,
    private val deviceAddress: String
) : BluetoothGattCallback() {

    companion object {
        private const val TAG = "GattCallback"
    }

    // 内部事件流
    private val _connectionState =
        MutableSharedFlow<ConnectionState>(replay = 3, extraBufferCapacity = 5)
    private val _characteristicRead = MutableSharedFlow<CharacteristicEvent>()
    private val _characteristicWrite = MutableSharedFlow<CharacteristicEvent>()
    private val _characteristicChanged = MutableSharedFlow<CharacteristicEvent>()
    private val _mtuChanged = MutableSharedFlow<Int>()
    private var _isInDFU = mutableStateOf(false)

    // 公开的事件流
    val connectionState: SharedFlow<ConnectionState> = _connectionState
    val characteristicRead: SharedFlow<CharacteristicEvent> = _characteristicRead
    val characteristicWrite: SharedFlow<CharacteristicEvent> = _characteristicWrite
    val characteristicChanged: SharedFlow<CharacteristicEvent> = _characteristicChanged
    val mtuChanged: SharedFlow<Int> = _mtuChanged

    // GATT实例
    var gatt: BluetoothGatt? = null

    // 连接状态变化回调
    override fun onConnectionStateChange(gatt: BluetoothGatt, status: Int, newState: Int) {
        BluetoothUtils.logGattStatus("onConnectionStateChange", status)
        this.gatt = gatt
        if (!_isInDFU.value) {
            when (newState) {
                BluetoothProfile.STATE_CONNECTED -> {
                    if (status == BluetoothGatt.GATT_SUCCESS) {
                        LogUtil.d(TAG, "Connected to device: ${gatt.device.address}")
                        // 连接成功后发送状态
                        coroutineScope.launch {
                            LogUtil.d(TAG, "Emitting Connected state to flow")
                            _connectionState.emit(
                                ConnectionState.Connected(
                                    gatt.device.address,
                                    status
                                )
                            )
                            LogUtil.d(TAG, "Connected state emitted successfully")
                        }
                        // 然后开始服务发现
                        gatt.discoverServices()
                    } else {
                        LogUtil.e(TAG, "Error connecting to device: $status")
                        coroutineScope.launch {
                            _connectionState.emit(
                                ConnectionState.Failed(
                                    gatt.device.address,
                                    status,
                                    "Error connecting"
                                )
                            )
                        }
                    }
                }

                BluetoothProfile.STATE_DISCONNECTED -> {
                    LogUtil.d(TAG, "Disconnected from device: ${gatt.device.address}")
                    coroutineScope.launch {
                        _connectionState.emit(
                            ConnectionState.Disconnected(gatt.device.address, status)
                        )
                    }

                }

                BluetoothProfile.STATE_CONNECTING -> {
                    LogUtil.d(TAG, "Connecting to device: ${gatt.device.address}")
                    coroutineScope.launch {
                        ConnectionState.Connecting(gatt.device.address)
                    }
                }

                BluetoothProfile.STATE_DISCONNECTING -> {
                    LogUtil.d(TAG, "Disconnecting from device: ${gatt.device.address}")
                    coroutineScope.launch {
                        _connectionState.emit(
                            ConnectionState.Disconnecting(gatt.device.address)
                        )
                    }

                }

                else -> {
                    LogUtil.e(TAG, "Unknown state: $newState")
                    coroutineScope.launch {
                        _connectionState.emit(
                            ConnectionState.Unknown(gatt.device.address, newState)
                        )
                    }

                }
            }
        } else {
            coroutineScope.launch {
                _connectionState.emit(
                    ConnectionState.DFU(gatt.device.address)
                )
            }
        }

    }

    // 服务发现回调
    override fun onServicesDiscovered(gatt: BluetoothGatt, status: Int) {
        BluetoothUtils.logGattStatus("onServicesDiscovered", status)

        if (status == BluetoothGatt.GATT_SUCCESS) {
            LogUtil.d(TAG, "Services discovered for device: ${gatt.device.address}")
            coroutineScope.launch {
                LogUtil.d(TAG, "Emitting ServicesDiscovered state to flow")
                _connectionState.emit(
                    ConnectionState.ServicesDiscovered(
                        gatt.device.address,
                        gatt.services
                    )
                )
                LogUtil.d(TAG, "ServicesDiscovered state emitted successfully")
            }

            // 打印所有服务和特征
            val servicesInfo = BluetoothUtils.printGattServices(gatt)
            LogUtil.d(TAG, "Device services:\n$servicesInfo")
        } else {
            LogUtil.e(TAG, "Service discovery failed: $status")
            coroutineScope.launch {
                _connectionState.emit(
                    ConnectionState.Failed(
                        gatt.device.address,
                        status,
                        "Service discovery failed"
                    )
                )
            }
        }
    }

    // 特征读取回调
    @Suppress("DEPRECATION")
    override fun onCharacteristicRead(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic,
        status: Int
    ) {
        BluetoothUtils.logGattStatus("onCharacteristicRead", status)
        handleCharacteristicRead(gatt, characteristic, characteristic.value, status)
    }

    // Android 13+ 特征读取回调
//    override fun onCharacteristicRead(
//        gatt: BluetoothGatt,
//        characteristic: BluetoothGattCharacteristic,
//        value: ByteArray,
//        status: Int
//    ) {
//        BluetoothUtils.logGattStatus("onCharacteristicRead (API 33+)", status)
//        handleCharacteristicRead(gatt, characteristic, value, status)
//    }

    private fun handleCharacteristicRead(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic,
        value: ByteArray?,
        status: Int
    ) {
        if (status == BluetoothGatt.GATT_SUCCESS) {
            val hexValue = BluetoothUtils.bytesToHex(value)
            LogUtil.d(TAG, "Read characteristic ${characteristic.uuid}: $hexValue")
        } else {
            LogUtil.e(TAG, "Error reading characteristic ${characteristic.uuid}: $status")
        }

        coroutineScope.launch {
            _characteristicRead.emit(
                CharacteristicEvent(
                    deviceAddress = gatt.device.address,
                    serviceUuid = characteristic.service.uuid,
                    characteristicUuid = characteristic.uuid,
                    value = value,
                    status = status
                )
            )
        }
    }

    // 特征写入回调
    @Suppress("DEPRECATION")
    override fun onCharacteristicWrite(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic,
        status: Int
    ) {
        BluetoothUtils.logGattStatus("onCharacteristicWrite", status)
        LogUtil.d(TAG, "onCharacteristicWrite ${characteristic.uuid}")
        if (status == BluetoothGatt.GATT_SUCCESS) {
            LogUtil.d(TAG, "Write to characteristic ${characteristic.uuid}")
        } else {
            LogUtil.e(TAG, "Error writing to characteristic ${characteristic.uuid}: $status")
        }

        coroutineScope.launch {
            _characteristicWrite.emit(
                CharacteristicEvent(
                    deviceAddress = gatt.device.address,
                    serviceUuid = characteristic.service.uuid,
                    characteristicUuid = characteristic.uuid,
                    value = characteristic.value,
                    status = status
                )
            )
        }
    }

    // 特征值变化回调
    @Suppress("DEPRECATION")
    override fun onCharacteristicChanged(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic
    ) {
        handleCharacteristicChanged(gatt, characteristic, characteristic.value)
    }

    // Android 13+ 特征值变化回调
    override fun onCharacteristicChanged(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic,
        value: ByteArray
    ) {
        handleCharacteristicChanged(gatt, characteristic, value)
    }

    private fun handleCharacteristicChanged(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic,
        value: ByteArray?
    ) {
        val hexValue = BluetoothUtils.bytesToHex(value)
        LogUtil.d(TAG, "Characteristic ${characteristic.uuid} changed: $hexValue")

        coroutineScope.launch {
            _characteristicChanged.emit(
                CharacteristicEvent(
                    deviceAddress = gatt.device.address,
                    serviceUuid = characteristic.service.uuid,
                    characteristicUuid = characteristic.uuid,
                    value = value,
                    status = BluetoothGatt.GATT_SUCCESS
                )
            )
        }
    }

    // 描述符读取回调
    override fun onDescriptorRead(
        gatt: BluetoothGatt,
        descriptor: BluetoothGattDescriptor,
        status: Int,
        value: ByteArray
    ) {
        BluetoothUtils.logGattStatus("onDescriptorRead", status)
        val hexValue = BluetoothUtils.bytesToHex(value)
        LogUtil.d(TAG, "Descriptor ${descriptor.uuid} read: $hexValue (status: $status)")
    }

    // 描述符写入回调
    @Suppress("DEPRECATION")
    override fun onDescriptorWrite(
        gatt: BluetoothGatt,
        descriptor: BluetoothGattDescriptor,
        status: Int
    ) {
        BluetoothUtils.logGattStatus("onDescriptorWrite", status)

        val charUuid = descriptor.characteristic?.uuid ?: "unknown characteristic"

        if (status == BluetoothGatt.GATT_SUCCESS) {
            if (descriptor.uuid == DeviceUUID.descriptorUUID) {
                // 客户端特征配置描述符 (CCCD)
                // 写入成功，我们假设状态已按预期设置（启用或禁用）
                // 不再依赖回调中的 descriptor.value，因为它可能不准确
                LogUtil.d(
                    TAG,
                    "CCCD descriptor write operation successful for characteristic $charUuid"
                )
            } else {
                // 其他描述符写入成功
                LogUtil.d(
                    TAG,
                    "Descriptor ${descriptor.uuid} written successfully for characteristic $charUuid"
                )
            }
        } else {
            LogUtil.e(
                TAG,
                "Error writing descriptor ${descriptor.uuid} for characteristic $charUuid: $status"
            )
        }
    }

    // MTU变化回调
    override fun onMtuChanged(gatt: BluetoothGatt, mtu: Int, status: Int) {
        BluetoothUtils.logGattStatus("onMtuChanged", status)

        if (status == BluetoothGatt.GATT_SUCCESS) {
            LogUtil.d(TAG, "MTU changed to $mtu")
            coroutineScope.launch {
                _mtuChanged.emit(mtu)
            }
        } else {
            LogUtil.e(TAG, "MTU change failed: $status")
        }
    }

    /**
     * 设置是否进入 DFU 模式
     */
    fun setDFU(isDFU: Boolean) {
        this._isInDFU.value = isDFU
    }

    // 清理资源
    fun close() {
        gatt?.close()
        gatt = null
    }
}

/**
 * 连接状态封装类
 */
sealed class ConnectionState {
    abstract val deviceAddress: String

    data class Connecting(override val deviceAddress: String) : ConnectionState()
    data class Connected(override val deviceAddress: String, val status: Int) : ConnectionState()
    data class Disconnecting(override val deviceAddress: String) : ConnectionState()
    data class Disconnected(override val deviceAddress: String, val status: Int) : ConnectionState()
    data class ServicesDiscovered(
        override val deviceAddress: String,
        val services: List<BluetoothGattService>
    ) : ConnectionState()

    data class Failed(
        override val deviceAddress: String,
        val status: Int,
        val message: String
    ) : ConnectionState()

    data class DFU(override val deviceAddress: String) : ConnectionState()
    data class Unknown(override val deviceAddress: String, val state: Int) : ConnectionState()
}

/**
 * 特征值操作事件
 */
data class CharacteristicEvent(
    val deviceAddress: String,
    val serviceUuid: UUID,
    val characteristicUuid: UUID,
    val value: ByteArray?,
    val status: Int
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CharacteristicEvent

        if (deviceAddress != other.deviceAddress) return false
        if (serviceUuid != other.serviceUuid) return false
        if (characteristicUuid != other.characteristicUuid) return false
        if (value != null) {
            if (other.value == null) return false
            if (!value.contentEquals(other.value)) return false
        } else if (other.value != null) return false
        if (status != other.status) return false

        return true
    }

    override fun hashCode(): Int {
        var result = deviceAddress.hashCode()
        result = 31 * result + serviceUuid.hashCode()
        result = 31 * result + characteristicUuid.hashCode()
        result = 31 * result + (value?.contentHashCode() ?: 0)
        result = 31 * result + status
        return result
    }
}