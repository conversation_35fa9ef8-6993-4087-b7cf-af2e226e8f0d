package org.aihealth.ineck.bluetooth

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity.RESULT_OK
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothDevice
import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import android.bluetooth.BluetoothGattDescriptor
import android.bluetooth.BluetoothManager
import android.bluetooth.le.ScanCallback
import android.bluetooth.le.ScanFilter
import android.bluetooth.le.ScanResult
import android.bluetooth.le.ScanSettings
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.os.Build
import android.os.ParcelUuid
import androidx.annotation.RequiresPermission
import androidx.core.content.ContextCompat.getSystemService
import com.hjq.permissions.XXPermissions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import no.nordicsemi.android.dfu.DfuServiceInitiator
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import java.util.UUID

/**
 *  蓝牙工具操作类
 */
class BluetoothUtil{
    private var bluetoothAdapter: BluetoothAdapter ?= null
    var address = ""
    init {
        val bluetoothManager: BluetoothManager?  = getSystemService(baseApplication,BluetoothManager::class.java)
        bluetoothAdapter = bluetoothManager?.adapter
    }

    val isScanning = MutableStateFlow(false)

    val foundDevices = MutableStateFlow<List<BluetoothDevice>>(emptyList())

    /**
     * 检查蓝牙权限,没有权限会先获取权限,如果存在所有权限会执行onAllgranted方法
     */
    fun checkPermissions(
        onAllGranted: () -> Unit,
        notAllGranted: () -> Unit
    ) {
        val permissions = when {
            Build.VERSION.SDK_INT < Build.VERSION_CODES.S -> {
                arrayOf(
                    Manifest.permission.ACCESS_COARSE_LOCATION,
                    Manifest.permission.ACCESS_FINE_LOCATION
                )
            }

            else -> arrayOf(
                Manifest.permission.BLUETOOTH_CONNECT,
                Manifest.permission.BLUETOOTH_SCAN
            )
        }
        if (!XXPermissions.isGranted(activity, permissions)) {
            notAllGranted()
        } else {
            onAllGranted()
        }
    }

    /**
     * 检查蓝牙是否已开启，没有开启会先进行蓝牙开启，如果成功开启会执行onBluetoothOpened方法
     */
    fun checkBluetoothEnable(
        onBluetoothOpened: () -> Unit
    ) {
        if (bluetoothAdapter?.isEnabled == false) {
            val enableBtIntent = Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE)
            ActivityResultUtils.startActivity(enableBtIntent) { resultCode, _ ->
                if (resultCode == RESULT_OK) {
                    onBluetoothOpened()
                } else {
                    DialogUtil.showToast(activity.getString(R.string.please_open_bluetooth))
                }
            }
        } else {
            onBluetoothOpened()
        }
    }

    /**
     * Get remote device by address
     */
    fun getRemoteDevice(address: String): BluetoothDevice? {
        return bluetoothAdapter?.getRemoteDevice(address)
    }
    /**
     * 检查蓝牙权限是否已授予
     */
    @SuppressLint("MissingPermission")
    fun isPermissionGranted(): Boolean {
        // 从Android 12 (API 31)开始，需要BLUETOOTH_SCAN和BLUETOOTH_CONNECT权限
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.S) {
            return baseApplication.checkSelfPermission(Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED &&
                    baseApplication.checkSelfPermission(Manifest.permission.BLUETOOTH_CONNECT) ==PackageManager.PERMISSION_GRANTED
        } else {
            // 在Android 12之前，需要BLUETOOTH和BLUETOOTH_ADMIN权限
            return baseApplication.checkSelfPermission(Manifest.permission.BLUETOOTH) == PackageManager.PERMISSION_GRANTED &&
                    baseApplication.checkSelfPermission(Manifest.permission.BLUETOOTH_ADMIN) == PackageManager.PERMISSION_GRANTED
        }
    }
    /**
     * 检查蓝牙是否可用
     */
    fun isBluetoothAvailable(): Boolean {
        return bluetoothAdapter?.isEnabled == true
    }
    /**
     * 检查蓝牙是否已启用
     */
    fun isBluetoothEnabled(): Boolean {
        return bluetoothAdapter?.isEnabled == true
    }

    /**
     * 搜索附近蓝牙设备
     */
    @SuppressLint("MissingPermission")
    fun scanDevice(
        onScanResult: (device: BluetoothDevice) -> Unit,
        onTimeOut: () -> Unit
    ) = CoroutineScope(Dispatchers.IO).launch {
        val scanCallback: ScanCallback = object : ScanCallback() {
            override fun onScanResult(callbackType: Int, result: ScanResult) {
                super.onScanResult(callbackType, result)
                onScanResult(result.device)
            }
        }
        val scanSettings =
            ScanSettings.Builder()
                /**
                 * 三种模式
                 * - SCAN_MODE_LOW_POWER : 低功耗模式，默认此模式，如果应用不在前台，则强制此模式
                 * - SCAN_MODE_BALANCED ： 平衡模式，一定频率下返回结果
                 * - SCAN_MODE_LOW_LATENCY 高功耗模式，建议应用在前台才使用此模式
                 */
                .setScanMode(ScanSettings.SCAN_MODE_BALANCED)
                .build()
        val filters = listOf(ScanFilter.Builder().setServiceUuid(ParcelUuid(DeviceUUID.serviceUUID)).build())
        bluetoothAdapter?.bluetoothLeScanner?.let {
            it.startScan(filters,scanSettings,scanCallback)
            delay(10000)
            it.stopScan(scanCallback)
            onTimeOut()
        }
    }

    private val scanCallback = object : ScanCallback() {
        override fun onScanResult(callbackType: Int, result: ScanResult?) {
            super.onScanResult(callbackType, result)
            result ?: return

            if (!foundDevices.value.contains(result.device)) {
                LogUtil.i("bluetooth scan ${result.device}")
                foundDevices.update { it + result.device }
            }
        }

        override fun onBatchScanResults(results: MutableList<ScanResult>?) {
            super.onBatchScanResults(results)
        }

        override fun onScanFailed(errorCode: Int) {
            super.onScanFailed(errorCode)
            isScanning.value = false
            LogUtil.i("bluetooth scan failed")
        }
    }

    @SuppressLint("MissingPermission")
    fun scanDevice() {
        val scanSettings =
            ScanSettings.Builder()
                /**
                 * 三种模式
                 * - SCAN_MODE_LOW_POWER : 低功耗模式，默认此模式，如果应用不在前台，则强制此模式
                 * - SCAN_MODE_BALANCED ： 平衡模式，一定频率下返回结果
                 * - SCAN_MODE_LOW_LATENCY 高功耗模式，建议应用在前台才使用此模式
                 */
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY)
                .build()
        val filters = listOf(
            ScanFilter.Builder()
                .setServiceUuid(ParcelUuid.fromString("00001523-1212-efde-1523-785feabcd123"))
                .build()
        )
        bluetoothAdapter?.bluetoothLeScanner?.startScan(filters, scanSettings, scanCallback)
        isScanning.value = true
    }

    var serviceConnection: ServiceConnection ?= null

    /**
     * 更新固件版本
     */
    fun updateDFU(address: String) {
        val initiator = DfuServiceInitiator(address)
            .setKeepBond(false)
            .setUnsafeExperimentalButtonlessServiceInSecureDfuEnabled(true)
            .setPrepareDataObjectDelay(300L)
            .setZip(R.raw.ineck6_0_6)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            DfuServiceInitiator.createDfuNotificationChannel(activity)
        }
        initiator.start(activity,DFUService::class.java)
    }



    var deviceRepository: DeviceRepository?= null

    fun close() {
        serviceConnection?.let {
            deviceRepository = null
            activity.unbindService(it)
            activity.stopService(Intent(activity, BluetoothService::class.java))
            serviceConnection = null
        }

    }

    @RequiresPermission(PERMISSION_BLUETOOTH_SCAN)
    fun stopScanDevice() {
        LogUtil.i("blue stop scan device")
        bluetoothAdapter?.bluetoothLeScanner?.stopScan(scanCallback)
        isScanning.value = false
        foundDevices.value = emptyList()
    }
}




/**
 * 蓝牙工具类
 */
object BluetoothUtils {
    private const val TAG = "BluetoothUtils"

    /**
     * 将数据转换为十六进制字符串
     */
    fun bytesToHex(bytes: ByteArray?): String {
        if (bytes == null) return ""
        val hexChars = CharArray(bytes.size * 2)
        for (i in bytes.indices) {
            val v = bytes[i].toInt() and 0xFF
            hexChars[i * 2] = "0123456789ABCDEF"[v ushr 4]
            hexChars[i * 2 + 1] = "0123456789ABCDEF"[v and 0x0F]
        }
        return String(hexChars)
    }

    /**
     * 获取特征值的属性描述
     */
    fun getCharacteristicPropertiesString(characteristic: BluetoothGattCharacteristic): String {
        val properties = characteristic.properties
        val builder = StringBuilder()

        if ((properties and BluetoothGattCharacteristic.PROPERTY_READ) != 0) {
            builder.append("Read ")
        }
        if ((properties and BluetoothGattCharacteristic.PROPERTY_WRITE) != 0) {
            builder.append("Write ")
        }
        if ((properties and BluetoothGattCharacteristic.PROPERTY_WRITE_NO_RESPONSE) != 0) {
            builder.append("WriteNoResponse ")
        }
        if ((properties and BluetoothGattCharacteristic.PROPERTY_NOTIFY) != 0) {
            builder.append("Notify ")
        }
        if ((properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
            builder.append("Indicate ")
        }

        return builder.toString().trim()
    }

    /**
     * 从GATT找到特定服务和特征
     */
    fun findCharacteristic(
        gatt: BluetoothGatt?,
        serviceUuid: UUID,
        characteristicUuid: UUID
    ): BluetoothGattCharacteristic? {
        if (gatt == null) return null

        val service = gatt.getService(serviceUuid) ?: return null
        return service.getCharacteristic(characteristicUuid)
    }

    /**
     * 打印GATT服务和特征值信息
     */
    fun printGattServices(gatt: BluetoothGatt?): String {
        if (gatt == null) return "No GATT connection"

        val result = StringBuilder()
        val services = gatt.services

        if (services.isEmpty()) {
            return "No services discovered"
        }

        for (service in services) {
            val serviceUuid = service.uuid
            result.append("Service: $serviceUuid\n")

            for (characteristic in service.characteristics) {
                val charUuid = characteristic.uuid
                val properties = getCharacteristicPropertiesString(characteristic)
                result.append("  Characteristic: $charUuid\n")
                result.append("    Properties: $properties\n")

                for (descriptor in characteristic.descriptors) {
                    result.append("      Descriptor: ${descriptor.uuid}\n")
                }
            }
        }

        return result.toString()
    }

    /**
     * 记录GATT操作状态
     */
    fun logGattStatus(method: String, status: Int) {
        val statusString = BluetoothConstants.GATT_STATUS[status] ?: "UNKNOWN: $status"
        LogUtil.d(TAG, "$method completed with status: $statusString")
    }

    /**
     * 根据API版本执行特性操作（读/写/开启通知）
     */
    @RequiresPermission(Manifest.permission.BLUETOOTH_CONNECT)
    @Suppress("DEPRECATION")
    fun performCharacteristicOperation(operation: CharacteristicOperation) {
        val characteristic = operation.characteristic ?: return
        val gatt = operation.gatt ?: return

        when (operation.type) {
            CharacteristicOperationType.READ -> {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    gatt.readCharacteristic(characteristic)
                } else {
                    // 用于API 33以下的调用
                    gatt.readCharacteristic(characteristic)
                }
            }

            CharacteristicOperationType.WRITE -> {
                LogUtil.d(
                    TAG,
                    "Characteristic Write${operation.characteristic.uuid},value:${operation.data?.toString()}"
                )
                characteristic.value = operation.data
                // 旧版API设置写入类型
                characteristic.writeType = operation.writeType!!
                gatt.writeCharacteristic(characteristic)
            }

            CharacteristicOperationType.ENABLE_NOTIFICATION -> {
                val descriptor = characteristic.getDescriptor(
                    DeviceUUID.descriptorUUID
                )
                val enabled = operation.enabled ?: true

                gatt.setCharacteristicNotification(characteristic, enabled)

                if (descriptor != null) {
                    val value = if (enabled) {
                        if ((characteristic.properties and BluetoothGattCharacteristic.PROPERTY_INDICATE) != 0) {
                            BluetoothGattDescriptor.ENABLE_INDICATION_VALUE
                        } else {
                            BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE
                        }
                    } else {
                        BluetoothGattDescriptor.DISABLE_NOTIFICATION_VALUE
                    }
                    LogUtil.d(
                        TAG,
                        "set notification:${characteristic.uuid},enable:$enabled,value:$value"
                    )
                    descriptor.value = value
                    gatt.writeDescriptor(descriptor)
                }
            }
        }
    }

    fun hexToByteArray(code: String): ByteArray {
        require(code.length % 2 == 0) { "Unexpected hex string: $this" }
        val result = ByteArray(code.length / 2)
        for (i in result.indices) {
            val d1 = decodeHexDigit(code[i * 2]) shl 4
            val d2 = decodeHexDigit(code[i * 2 + 1])
            result[i] = (d1 + d2).toByte()
        }
        return result
    }

    private fun decodeHexDigit(c: Char): Int {
        return when (c) {
            in '0'..'9' -> c - '0'
            in 'a'..'f' -> c - 'a' + 10
            in 'A'..'F' -> c - 'A' + 10
            else -> throw IllegalArgumentException("Unexpected hex digit: $c")
        }
    }

    /**
     * 特征操作类型
     */
    enum class CharacteristicOperationType {
        READ, WRITE, ENABLE_NOTIFICATION
    }

    /**
     * 特征操作数据类
     */
    data class CharacteristicOperation(
        val type: CharacteristicOperationType,
        val characteristic: BluetoothGattCharacteristic?,
        val gatt: BluetoothGatt?,
        val data: ByteArray? = null,
        val writeType: Int? = null,
        val enabled: Boolean? = null,
        val callback: ((Int) -> Unit)? = null
    ) {
        override fun equals(other: Any?): Boolean {
            if (this === other) return true
            if (javaClass != other?.javaClass) return false

            other as CharacteristicOperation

            if (type != other.type) return false
            if (characteristic != other.characteristic) return false
            if (gatt != other.gatt) return false
            if (data != null) {
                if (other.data == null) return false
                if (!data.contentEquals(other.data)) return false
            } else if (other.data != null) return false
            if (writeType != other.writeType) return false
            if (enabled != other.enabled) return false

            return true
        }

        override fun hashCode(): Int {
            var result = type.hashCode()
            result = 31 * result + (characteristic?.hashCode() ?: 0)
            result = 31 * result + (gatt?.hashCode() ?: 0)
            result = 31 * result + (data?.contentHashCode() ?: 0)
            result = 31 * result + (writeType ?: 0)
            result = 31 * result + (enabled?.hashCode() ?: 0)
            return result
        }
    }
}



