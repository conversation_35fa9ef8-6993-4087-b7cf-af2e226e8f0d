package org.aihealth.ineck.bluetooth

import android.bluetooth.BluetoothGattService
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.hour
import org.aihealth.ineck.util.minute
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.second
import org.aihealth.ineck.util.toHexString
import org.aihealth.ineck.util.year
import java.nio.ByteBuffer
import java.util.Calendar
import java.util.GregorianCalendar
import java.util.UUID

/**
 *  蓝牙通信模块下的功能出入口
 *  包括各个命令的执行与逻辑方法
 */
class DeviceRepository(
    private val bluetoothService: BluetoothService
) {

    /**
     * 监听当前设备角度
     */
    fun setOnAngleChangedListener(
        onAngleChanged: (primaryAngle: Int, angle: Int, maxAngle: Byte, currentTime: Long) -> Unit
    ) {
        bluetoothService.setOnAngleChangedListener(onAngleChanged)
    }

    /**
     * 获取设备版本号
     */
    fun getVersion(callback: (version: String) -> Unit) {
        DeviceUUID.settingsUUID.write("08", DeviceUUID.getVersionUUID) {
            val byteBuffer = ByteBuffer.wrap(it)
            val deviceDFUVer = if (byteBuffer.getShort(0) < 0) {
                Short.MAX_VALUE - (Short.MIN_VALUE - byteBuffer.getShort(0)) + 1
            } else {
                byteBuffer.getShort(0).toInt()
            }
            val version =
                ((deviceDFUVer / 10).toString().replace("0", ".") + deviceDFUVer % 10).replace(
                    "..",
                    ".0"
                )
            callback(version)
        }
    }

    /**
     * 获取设备本地角度数据
     */
    fun getLocalAngle(callback: (angle: Int, timeStamp: Long) -> Unit) {
        LogUtil.d("mytag", "getLocalAngle: start")
        DeviceUUID.settingsUUID.write("04", DeviceUUID.getLocalAngleUUID) { value ->
            if (value.size < 9) {
                return@write
            }
            val year = value[0].toInt() * 100 + value[1].toInt()
            val month = value[2].toInt()
            val day = value[3].toInt()
            val hour = value[4].toInt()
            val minute = value[5].toInt()
            val second = value[6].toInt()
            val angle = value[7].toInt()
//            val maxAngle = value[8].toInt()
            LogUtil.d(
                "mytag",
                "getLocalAngle: ${year}-${month}-${day} ${hour}:${minute}:${second} (angle: ${angle})"
            )
            GregorianCalendar(year, month - 1, day, hour, minute, second).timeInMillis.let {
                if (it > 0) {
                    callback(angle.coerceIn(0, 90), it)
                }
            }
        }
    }

    /**
     * 设置设备振动模式（干预/监测）
     */
    fun setVibration(enabled: Boolean) {
        DeviceUUID.settingsUUID.write(if (enabled) "07" else "06")
    }

    /**
     * 设备校准基准角度(第一次校准)，此方法默认会清空之前的校准数据
     */
    fun startFirstCalibration() {
        // 清空之前的校准数据
        DeviceUUID.settingsUUID.write("0C")
        // 进行第一次校准
        DeviceUUID.settingsUUID.write("01")
    }

    /**
     * 设备设定最大偏转角（第二次校准）,校准完成，此方法默认会振动提醒用户校准完成
     */
    fun startSecondCalibration() {
        // 进行第二次校准
        DeviceUUID.settingsUUID.write("02")
        // 振动提醒用户校准完成
//        DeviceUUID.settingsUUID.write("0F")
    }

    /**
     * 获取当前电量
     */
    fun getPower(callback: (power: Int) -> Unit) {
        DeviceUUID.getPowerUUID.read(bluetoothService.batteryGattService) {
            callback(it[0].toInt())
        }
    }

    /**
     * 获取序列号
     */
    fun getSN(callback: (sn: String) -> Unit) {
        DeviceUUID.settingsUUID.write("0A",DeviceUUID.getVersionUUID) {
            callback(it.toHexString())
        }
    }

    /**
     * 设置设备类型（aiNeck/aiBack）
     */
    fun setDeviceType(deviceType: DeviceType) {
        DeviceUUID.settingsUUID.write(if (deviceType == DeviceType.aiBack) "0002" else "0001")

    }

    /**
     * 设置振动角度
     */
    fun setVibrationAngle(angle: Int) {
        DeviceUUID.setMaxAngleUUID.write(angle.toHexStr())
    }

    /**
     * 清空步数
     */
    fun clearSteps() {
        DeviceUUID.settingsUUID.write("0B")
    }

    /**
     * 设置振动提示间隔时间（单位：秒）
     */
    fun setVibrationFrequency(
        frequency: Int
    ) {
        DeviceUUID.setVibrationTimeUUID.write(frequency.toHexStr())

    }

    /**
     * 设备振动一次提醒用户
     */
    fun vibrate() {
        DeviceUUID.settingsUUID.write("0F")

    }

    /**
     * 设置设备时间
     */
    fun setCurrentTime() {
        val calendar = Calendar.getInstance()
        val hexYear = (calendar.year / 100).toHexStr() + (calendar.year % 100).toHexStr()
        val hexMonth = (calendar.month + 1).toHexStr()
        val hexDate = calendar.date.toHexStr()
        val hexHour = calendar.hour.toHexStr()
        val hexMinute = calendar.minute.toHexStr()
        val hexSecond = calendar.second.toHexStr()
        DeviceUUID.setTimeUUID.write("$hexYear$hexMonth$hexDate$hexHour$hexMinute$hexSecond")
    }

    /**
     * 进入固件更新模式
     */
    fun startDFU() {
        bluetoothService.bluetoothGatt
            ?.getService(DeviceUUID.updateServiceUUID)
            ?.let {
                bluetoothService.setNotification(it.getCharacteristic(DeviceUUID.updateControlUUID))
                DeviceUUID.updateControlUUID.write(value = "0100", service = it)
            }

    }

    /**
     *
     */


    private fun UUID.write(
        value: String,
        callbackUUID: UUID = this,
        service: BluetoothGattService? = bluetoothService.gattService,
        callback: ((value: ByteArray) -> Unit)? = null
    ) {
        service?.getCharacteristic(this)?.let {
            bluetoothService.write(it, value, callbackUUID, callback)
        }
    }

    private fun UUID.read(
        service: BluetoothGattService? = bluetoothService.gattService,
        callback: (value: ByteArray) -> Unit
    ) {
        service?.getCharacteristic(this)?.let {
            bluetoothService.read(it, callback)
        }
    }

    /**
     * 将int转化为16进制格式
     * formatNum: 16进制位数，默认2位数，不足用0补齐
     */

    private fun Int.toHexStr(formatNum: Int = 2): String {
        return String.format("%0${formatNum}X", this)
    }
}