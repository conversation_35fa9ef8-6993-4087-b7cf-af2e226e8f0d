package org.aihealth.ineck.bluetooth

import java.util.UUID

object DeviceUUID {

    /**
     * 设备数据通道服务
     */
    val serviceUUID: UUID = UUID.fromString("00001523-1212-efde-1523-785feabcd123")

    /**
     * 设备电池通道服务
     */
    val batteryServiceUUID: UUID = UUID.fromString("0000180F-0000-1000-8000-00805f9b34fb")

    /**
     * 固件更新服务
     */
    val updateServiceUUID: UUID = UUID.fromString("00001530-1212-efde-1523-785feabcd123")

    /**
     * 写数据
     */
    val descriptorUUID: UUID = UUID.fromString("00002902-0000-1000-8000-00805f9b34fb")

    /**
     * 此通道用于获取设备当前的角度数据。
     * 包括时间、当前角度、最大偏转角、设备类型(aiNeck和aiBack)。
     */
    val getAngleUUID: UUID = UUID.fromString("00008eb3-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于更改设备设置。
     * 包括（校准基准角度，设定最大偏转角，关闭振动提醒，
     * 开启振动提醒，获取版本号请求，系统复位，步数归零，
     * 清除之前的角度校准信息，自动校准完成后的 1 次振动提示，切换至 AINECK，切换至 AIBACK）等
     */
    val settingsUUID: UUID = UUID.fromString("00008eb2-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于设置最大偏转角度，16 进制数设置，如 0x30表示 48 度。
     */
    val setMaxAngleUUID: UUID = UUID.fromString("00008eb4-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于校准设备时间，将手机的当前时间以 16 进制形式发送至设备
     * 比如：2021 年 8 月 31 日 23 时 59 分 00 秒 的 16 进制编码应为：0x07e5081f173b00
     */
    val setTimeUUID: UUID = UUID.fromString("00008eb5-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于获取设备本地存储的角度数据
     * 仅在 APP 发送读取请求且设备内存中存在角度数据时有效
     */
    val getLocalAngleUUID: UUID = UUID.fromString("00008eb6-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于获取设备步数
     */
    val getStepUUID: UUID = UUID.fromString("00008eb7-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于获取设备疼痛记录
     * 方式1: 在用户发送疼痛请求（三击按键）时有效。
     * 方式2：APP 端发送读取请求且设备内存中存在疼痛数据时有效。
     */
    val getPainUUID: UUID = UUID.fromString("00008eb8-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于获取设备版本号。
     * 仅在 APP 发送读取请求时有效
     */
    val getVersionUUID: UUID = UUID.fromString("00008eb9-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于设置振动间隔时间，以 16 进制数输入，如发送 0x02，则表示在用户角度大于设定的最大角度后，间隔 2 秒，开始振动提醒
     */
    val setVibrationTimeUUID: UUID = UUID.fromString("00008eba-1212-efde-1523-785feabcd123")

    /**
     * 此通道用户获取设备电池电量
     */
    val getPowerUUID: UUID = UUID.fromString("00002A19-0000-1000-8000-00805f9b34fb")

    /**
     * 此通道用于固件升级控制
     */
    val updateControlUUID: UUID = UUID.fromString("00001531-1212-efde-1523-785feabcd123")

    /**
     * 此通道用于固件升级传输zip文件
     */
    val updatePackageUUID: UUID = UUID.fromString("00001532-1212-efde-1523-785feabcd123")
}
