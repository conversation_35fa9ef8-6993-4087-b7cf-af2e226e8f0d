package org.aihealth.ineck

import android.annotation.SuppressLint
import android.app.Application
import android.os.StrictMode
import androidx.media3.common.util.UnstableApi
import com.auth0.android.Auth0
import com.auth0.android.authentication.AuthenticationAPIClient
import com.google.firebase.messaging.FirebaseMessaging
import com.jakewharton.threetenabp.AndroidThreeTen
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.umeng.commonsdk.UMConfigure
import org.aihealth.ineck.dao.StepDataDao
import org.aihealth.ineck.dao.TimeStampDao
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.util.DeviceConfigManager
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.NotificationUtils
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.UmInitConfig
import java.util.Locale

lateinit var baseApplication: BaseApplication
var currentLocale: Locale = Locale.CHINESE
var isInChina: Boolean = true
lateinit var auth0Account : Auth0
@SuppressLint("StaticFieldLeak")
lateinit var auth0ApiClient :AuthenticationAPIClient
lateinit var timeStampDao: TimeStampDao
lateinit var stepDataDao: StepDataDao

open class BaseApplication:Application() {
    @UnstableApi
    override fun onCreate() {
        super.onCreate()
        
        // 配置 StrictMode，只在调试模式下生效
        // 注意：使用宽松的策略，避免系统级操作（如字体加载）触发不必要的警告
        if (BuildConfig.DEBUG) {
            StrictMode.setThreadPolicy(
                StrictMode.ThreadPolicy.Builder()
                    // 只检测自定义的慢调用，避免系统字体加载等正常操作的警告
                    .detectCustomSlowCalls()
                    // 不检测磁盘读写，因为会包含很多系统级的正常操作
                    // .detectDiskReads()
                    // .detectDiskWrites()
                    // 不检测网络操作，避免 Firebase 等第三方库触发警告
                    // .detectNetwork()
                    .penaltyLog()
                    .build()
            )
            StrictMode.setVmPolicy(
                StrictMode.VmPolicy.Builder()
                    .detectLeakedSqlLiteObjects()
                    .detectLeakedClosableObjects()
                    // 只检测内存泄漏相关的严重问题
                    .penaltyLog()
                    .build()
            )
        }
        
        instance = this
        baseApplication = this
        currentLocale = if (resources.configuration.locales.get(0).language == "zh") Locale.CHINESE else Locale.ENGLISH
        /* 是否为国内服务 */
        val locale = resources.configuration.locales.get(0).country
        isInChina = SPUtil.getBoolean(SPConstant.IS_IN_CHINA, locale == Locale.CHINA.country)
        LogUtil.i("isInChina:$isInChina, currentLocale:$currentLocale")
//        isInChina = false
        NotificationUtils.createNotificationChannels()
        auth0Account = Auth0.getInstance(
            baseApplication.getString(R.string.com_auth0_client_id),
            baseApplication.getString(R.string.com_auth0_domain)
        )
        auth0ApiClient = AuthenticationAPIClient(auth0Account)
        timeStampDao = database.timeStampDao()
        stepDataDao = database.stepDataDao()
        AndroidThreeTen.init(this)
        // 初始化设备配置
        DeviceConfigManager.initialize()
        initWechat()
        
        // 检查用户是否已同意协议，如果已同意则初始化推送服务
        if (!SPUtil.getBoolean(SPConstant.IS_FIRST_READ_AGREEMENT, true)) {
            if (isInChina) {
                initUmeng()
            } else {
                initFCM()
            }
        }

        if (BuildConfig.DEBUG){
            UMConfigure.setLogEnabled(true)
        }
        UMConfigure.preInit(this,"6824368a55d24d3412d0377f","Umeng")
//        val logPath = getExternalFilesDir(null)?.absolutePath + File.separator + "logs"
//        val time = SimpleDateFormat("HH:mm:ss.SSS", Locale.getDefault()).format(Date())
//        Timber.plant(FileLoggingTree(logPath,time))

//        CrashReport.initCrashReport(getApplicationContext(), "6fd79ddf4c", false);
    }

    fun initFCM() {
        try {
            FirebaseMessaging.getInstance().token.addOnCompleteListener { task ->
                if (!task.isSuccessful) {
                    val exception = task.exception
                    LogUtil.w("FCM Token 获取失败: ${exception?.message}")
                    
                    // 检查是否是服务不可用错误
                    if (exception?.message?.contains("SERVICE_NOT_AVAILABLE") == true) {
                        LogUtil.i("FCM 服务在当前地区不可用，这在中国大陆是正常现象")
                        
                        // 如果 FCM 不可用，可以考虑切换到友盟推送
                        if (isInChina) {
                            LogUtil.i("检测到在中国地区，尝试使用友盟推送服务")
                            try {
                                initUmeng()
                            } catch (e: Exception) {
                                LogUtil.e("友盟推送初始化也失败: ${e.message}")
                            }
                        }
                    }
                    return@addOnCompleteListener
                }

                // Get new FCM registration token
                val token = task.result
                LogUtil.i("FCM Token 获取成功: $token")
                
                // Save token to SharedPreferences
                SPUtil.putString(SPConstant.FCM_TOKEN, token)
                
                // 注意：不在这里调用 checkAndUpdateDeviceToken()，因为用户数据可能还未加载
                // 会在用户数据加载完成后自动检查更新
            }
        } catch (e: Exception) {
            LogUtil.e("FCM 初始化异常: ${e.message}")
            
            // 如果 FCM 初始化失败，在中国地区尝试使用友盟
            if (isInChina) {
                LogUtil.i("FCM 初始化失败，在中国地区使用友盟推送")
                try {
                    initUmeng()
                } catch (umengException: Exception) {
                    LogUtil.e("友盟推送初始化失败: ${umengException.message}")
                }
            }
        }
    }
    fun initUmeng() {
        //友盟正式初始化
        val umInitConfig: UmInitConfig = UmInitConfig()
        umInitConfig.UMinit(getApplicationContext())

    }

    //region 微信
    lateinit var wxapi: IWXAPI

    /**
     * 微信回调
     *
     * 在登录等界面设置
     */
    var processWechatStatusChanged: ((data: BaseResp) -> Unit)? = null

    /**
     * 初始化微信
     */
    private fun initWechat() {
        wxapi = WXAPIFactory.createWXAPI(applicationContext, null)
        wxapi.registerApp(getString(R.string.APP_ID_WX))
    }

    companion object {
        const val TAG = "BaseApplication"

        lateinit var instance: BaseApplication
    }
}