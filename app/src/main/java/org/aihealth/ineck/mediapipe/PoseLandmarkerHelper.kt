package org.aihealth.ineck.mediapipe

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.YuvImage
import android.os.SystemClock
import androidx.annotation.VisibleForTesting
import androidx.camera.core.ImageProxy
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.core.Delegate
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarkerResult
import org.aihealth.ineck.util.LogUtil
import java.io.ByteArrayOutputStream

class PoseLandmarkerHelper(
    var minPoseDetectionConfidence: Float = DEFAULT_POSE_DETECTION_CONFIDENCE,
    var minPoseTrackingConfidence: Float = DEFAULT_POSE_TRACKING_CONFIDENCE,
    var minPosePresenceConfidence: Float = DEFAULT_POSE_PRESENCE_CONFIDENCE,
    var currentModel: Int = MODEL_POSE_LANDMARKER_LITE,
    var currentDelegate: Int = DELEGATE_CPU,
    var runningMode: RunningMode = RunningMode.LIVE_STREAM,
    val context: Context,
    // this listener is only used when running in RunningMode.LIVE_STREAM
    val poseLandmarkerHelperListener: LandmarkerListener? = null
) {

    // For this example this needs to be a var so it can be reset on changes.
    // If the Pose Landmarker will not change, a lazy val would be preferable.
    private var poseLandmarker: PoseLandmarker? = null

    init {
        setupPoseLandmarker()
    }

    @Synchronized
    fun clearPoseLandmarker() {
        try {
            poseLandmarker?.close()
            poseLandmarker = null
            LogUtil.d("PoseLandmarker cleared successfully")
        } catch (e: Exception) {
            LogUtil.e("Error clearing PoseLandmarker: ${e.message}")
            e.printStackTrace()
        }
    }

    // Return running status of PoseLandmarkerHelper
    @Synchronized
    fun isClose(): Boolean {
        return poseLandmarker == null
    }

    // Initialize the Pose landmarker using current settings on the
    // thread that is using it. CPU can be used with Landmarker
    // that are created on the main thread and used on a background thread, but
    // the GPU delegate needs to be used on the thread that initialized the
    // Landmarker
    @Synchronized
    fun setupPoseLandmarker() {
        try {
            // 清理现有的landmarker
            poseLandmarker?.close()
            poseLandmarker = null
            
            // Set general pose landmarker options
            val baseOptionBuilder = BaseOptions.builder()

            // Use the specified hardware for running the model. Default to CPU
            when (currentDelegate) {
                DELEGATE_CPU -> {
                    baseOptionBuilder.setDelegate(Delegate.CPU)
                }

                DELEGATE_GPU -> {
                    baseOptionBuilder.setDelegate(Delegate.GPU)
                }
            }

            val modelName =
                when (currentModel) {
                    MODEL_POSE_LANDMARKER_FULL -> "pose_landmarker_full.task"
                    MODEL_POSE_LANDMARKER_LITE -> "pose_landmarker_lite.task"
                    MODEL_POSE_LANDMARKER_HEAVY -> "pose_landmarker_heavy.task"
                    else -> "pose_landmarker_full.task"
                }

            baseOptionBuilder.setModelAssetPath(modelName)

            val baseOptions = baseOptionBuilder.build()
            // Create an option builder with base options and specific
            // options only use for Pose Landmarker.
            val optionsBuilder =
                PoseLandmarker.PoseLandmarkerOptions.builder()
                    .setBaseOptions(baseOptions)
                    .setMinPoseDetectionConfidence(minPoseDetectionConfidence)
                    .setMinTrackingConfidence(minPoseTrackingConfidence)
                    .setMinPosePresenceConfidence(minPosePresenceConfidence)
                    .setRunningMode(runningMode)

            // The ResultListener and ErrorListener only use for LIVE_STREAM mode.
            if (runningMode == RunningMode.LIVE_STREAM) {
                optionsBuilder
                    .setResultListener(this::returnLivestreamResult)
                    .setErrorListener(this::returnLivestreamError)
            }

            poseLandmarker = PoseLandmarker.createFromOptions(context, optionsBuilder.build())
            LogUtil.d("PoseLandmarker setup completed successfully")
        } catch (e: Exception) {
            LogUtil.e("Error setting up PoseLandmarker: ${e.message}")
            e.printStackTrace()
            poseLandmarker = null
            poseLandmarkerHelperListener?.onError(
                "Failed to create PoseLandmarker: ${e.message}",
                GPU_ERROR
            )
        }
    }

    fun detectLiveStream(
        imageProxy: ImageProxy,
        isFrontCamera: Boolean
    ) {
        try {
            // 检查PoseLandmarker是否可用
            if (isClose()) {
                LogUtil.w("PoseLandmarker is closed, skipping detection")
                imageProxy.close()
                return
            }
            
            val frameTime = SystemClock.uptimeMillis()
            
            // 使用 ImageProxy 的图像格式创建适当的 Bitmap
            val bitmap = if (imageProxy.format == ImageFormat.YUV_420_888) {
                // 当使用 YUV 格式时需要转换
                val yBuffer = imageProxy.planes[0].buffer // Y
                val uBuffer = imageProxy.planes[1].buffer // U
                val vBuffer = imageProxy.planes[2].buffer // V
                
                val ySize = yBuffer.remaining()
                val uSize = uBuffer.remaining()
                val vSize = vBuffer.remaining()
                
                val nv21 = ByteArray(ySize + uSize + vSize)
                
                // U 和 V 是交错的
                yBuffer.get(nv21, 0, ySize)
                vBuffer.get(nv21, ySize, vSize)
                uBuffer.get(nv21, ySize + vSize, uSize)
                
                val yuvImage = YuvImage(nv21, ImageFormat.NV21, imageProxy.width, imageProxy.height, null)
                val out = ByteArrayOutputStream()
                yuvImage.compressToJpeg(Rect(0, 0, imageProxy.width, imageProxy.height), 100, out)
                val imageBytes = out.toByteArray()
                
                BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.size)
            } else {
                // 当使用 RGBA_8888 格式时可以直接转换
                val planes = imageProxy.planes
                val buffer = planes[0].buffer
                val pixelStride = planes[0].pixelStride
                val rowStride = planes[0].rowStride
                val rowPadding = rowStride - pixelStride * imageProxy.width
                
                // 创建具有正确大小的 Bitmap
                val bitmap = Bitmap.createBitmap(
                    imageProxy.width + rowPadding / pixelStride,
                    imageProxy.height,
                    Bitmap.Config.ARGB_8888
                )
                bitmap.copyPixelsFromBuffer(buffer)
                
                // 裁剪以移除填充区域
                if (rowPadding > 0) {
                    Bitmap.createBitmap(bitmap, 0, 0, imageProxy.width, imageProxy.height)
                } else {
                    bitmap
                }
            }
            
            imageProxy.close()
            
            if (bitmap == null) {
                LogUtil.e("Failed to create bitmap from image")
                return
            }

            // 再次检查PoseLandmarker是否可用
            if (isClose()) {
                LogUtil.w("PoseLandmarker closed during bitmap processing")
                return
            }

            val matrix = Matrix().apply {
                // 旋转框架接收自相机，与显示方向一致
                postRotate(imageProxy.imageInfo.rotationDegrees.toFloat())

                // 如果使用前置摄像头，则翻转图像
                if (isFrontCamera) {
                    postScale(
                        -1f,
                        1f,
                        bitmap.width.toFloat(),
                        bitmap.height.toFloat()
                    )
                }
            }
            
            val rotatedBitmap = Bitmap.createBitmap(
                bitmap, 0, 0, bitmap.width, bitmap.height,
                matrix, true
            )

            // 将输入 Bitmap 对象转换为 MPImage 对象以运行推理
            val mpImage = BitmapImageBuilder(rotatedBitmap).build()

            detectAsync(mpImage, frameTime)
        } catch (e: Exception) {
            LogUtil.e("Error in detectLiveStream: ${e.message}")
            e.printStackTrace()
            try {
                imageProxy.close()
            } catch (closeException: Exception) {
                LogUtil.e("Error closing imageProxy: ${closeException.message}")
            }
        }
    }

    // Run pose landmark using MediaPipe Pose Landmarker API
    @VisibleForTesting
    fun detectAsync(mpImage: MPImage, frameTime: Long) {
        try {
            // 添加null检查和同步机制
            val landmarker = poseLandmarker
            if (landmarker != null && !isClose()) {
                landmarker.detectAsync(mpImage, frameTime)
            } else {
                LogUtil.w("PoseLandmarker is null or closed, skipping detection")
            }
        } catch (e: Exception) {
            LogUtil.e("Error in detectAsync: ${e.message}")
            e.printStackTrace()
        }
        // As we're using running mode LIVE_STREAM, the landmark result will
        // be returned in returnLivestreamResult function
    }


    // Return the landmark result to this PoseLandmarkerHelper's caller
    private fun returnLivestreamResult(
        result: PoseLandmarkerResult,
        input: MPImage
    ) {
        poseLandmarkerHelperListener?.onResults(
            ResultBundle(
                listOf(result),
                input.height,
                input.width
            )
        )
    }

    // Return errors thrown during detection to this PoseLandmarkerHelper's
    // caller
    private fun returnLivestreamError(error: RuntimeException) {
        poseLandmarkerHelperListener?.onError(
            error.message ?: "An unknown error has occurred"
        )
    }

    companion object {

        const val DELEGATE_CPU = 0
        const val DELEGATE_GPU = 1
        const val DEFAULT_POSE_DETECTION_CONFIDENCE = 0.5F
        const val DEFAULT_POSE_TRACKING_CONFIDENCE = 0.5F
        const val DEFAULT_POSE_PRESENCE_CONFIDENCE = 0.5F
        const val OTHER_ERROR = 0
        const val GPU_ERROR = 1
        const val MODEL_POSE_LANDMARKER_FULL = 0
        const val MODEL_POSE_LANDMARKER_LITE = 1
        const val MODEL_POSE_LANDMARKER_HEAVY = 2
    }

    data class ResultBundle(
        val results: List<PoseLandmarkerResult>,
        val inputImageHeight: Int,
        val inputImageWidth: Int,
    )

    interface LandmarkerListener {
        fun onError(error: String, errorCode: Int = OTHER_ERROR)
        fun onResults(resultBundle: ResultBundle)
    }
}