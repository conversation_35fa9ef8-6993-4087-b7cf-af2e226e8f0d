package org.aihealth.ineck.network

import android.content.Context
import com.tencent.mm.opensdk.modelpay.PayReq
import com.tencent.mm.opensdk.openapi.IWXAPI
import com.tencent.mm.opensdk.openapi.WXAPIFactory
import com.wechat.pay.java.core.RSAAutoCertificateConfig
import com.wechat.pay.java.core.util.NonceUtil
import com.wechat.pay.java.service.payments.app.AppService
import com.wechat.pay.java.service.payments.app.model.Amount
import com.wechat.pay.java.service.payments.app.model.PrepayRequest
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.wxapi.WXPayEntryActivity
import org.aihealth.ineck.wxapi.WechatCallback
import org.aihealth.ineck.wxapi.WechatPayResult
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import java.util.UUID


class WeChatPayService {
    private lateinit var api: IWXAPI
    private lateinit var merchantId: String
    private lateinit var privateKey: String
    private lateinit var merchantSerialNumber: String
    private lateinit var apiV3Key: String
    private lateinit var appService: AppService
    private lateinit var appId: String
    private lateinit var config: RSAAutoCertificateConfig
   suspend fun init(context: Context) {
        appId = context.getString(R.string.APP_ID_WX)
        merchantId = context.getString(R.string.APP_PARTNER_ID_WX)
        privateKey = "-----BEGIN PRIVATE KEY-----\n" +
                "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDgcHccvijKcUbo\n" +
                "cwyjm/ks3sOFxiIa0mu6VI4OCIxYnMGqXfmdpji2uHeG119Yzqrhgfb9SU3R7QG1\n" +
                "plfE3MwbC2qdvki4emky6W+IEFVjVzqPb6ipt305wmk6Hlf6hKnOIHqYao8m8Jtn\n" +
                "3P/0F6mTMguOACGqjQ75w0R0lHuZjNOjPUvG3VuFBgflCLL/S8AxebDeV6VPPzq1\n" +
                "x7yim6d9+Q85z+ekcMdPp5NsIf4/4gLLI6O5UDOuzxhpRzWDJYagNok3QTDHZa96\n" +
                "grNYEsoyn341Ao37qavfiH4QLd+673sk3d7qT4NQR/IzHL5CFBLoQogcnezAGfuU\n" +
                "Arw4arCtAgMBAAECggEBALrmDNq7pB5JTIIf5IOOTGbILswEnbv2RE2LQix/g9f6\n" +
                "2e90iHx2wj2uyaJZUYYTvAtEfhiYZZyOIGFTXz2JnNJcpHEt10MAdQA3knAdIe4y\n" +
                "4oo1sp96NGbX9Wa3hA4TUZBcTZsFCaaksNVCHUb4OXdhMmwSwfZTtwfYSvZF+9N1\n" +
                "+chpMmLdyxkGdFb4e2TjlaG/idT9s3M/xFCugGHJyQfOeyT9Nd8Sanwl7/YWNQhV\n" +
                "EuZZGoSP0BJQmiQPcv+YllLI0PIu4GlQvrNHfBzpX92zryXozkjI4UOkG4TbkxfI\n" +
                "N/IxTKtpbPKhpSHWZysKXl3WXq9bTuotoiZ4Rnze64ECgYEA+71m5mxwTqMf9Um9\n" +
                "RjwjlESMFRcS45p01qXbKFRzQMnoEQ+xAWPoQvEmltcCufvs/XdabJxR5z/MZJA6\n" +
                "4oNNkZY9Oyrq1ZBTXVG4HGRpDD1tHrFFXCjw4gRPug8UTdIyyIyJ8CycwmHEhXhZ\n" +
                "voOK90xjALf7B2fppH1+km8lIGECgYEA5DzKcdllP+x86aw6roQgeLeHAj8Lo7ap\n" +
                "RDuqqMiwnjKHGj8AoC4A17jN599fNM+7m+692tU2mpdNIkZ+4enegYMrUI7KAnuy\n" +
                "duqRL8Q/CXN4ZxP8XorZRtXajhVz6jPGNQesC4oE9BIIsikO+coiJdHcWJgnwuSd\n" +
                "UKzXEFVSo80CgYB7KusquEmmn/dl6uM5dvktdPBY7UYnqA2ca9+zdsZBH8h0eInx\n" +
                "FTGxdw2bZa2G07TbOsc6Nq14CGX3mf3Fj9qm7awdw9zMCuZO1UnQUmx3OaKnDrLR\n" +
                "6ZIZ82QSs4gKn1qLdv+STDGShYVCjGTUasXWQ0B5PIsMz5GoEmKYQNsYYQKBgHPE\n" +
                "igM5e+vyrmFwZMuEZVwV6zsuc171FeJwSV4Xfc6gXnrnng+hhffl2ztTx9znyYeM\n" +
                "j2xiXbIIMK3ECDPbBmu29s+Dvd+IkrtSu5X4+uh2U4olTws9bnxJmPQc2ZDf9heL\n" +
                "7q5jsaODSjnkLZ5x8uQwEI43j2OBxnHXWbcFMEe5AoGAKfbN79LhU3W3hWNp321r\n" +
                "gozuLELiRnjBNypMEDdAayQFQuorCOG6U5SGi6TQuEjsZTtZLrlpvJ2XE0UgEkgA\n" +
                "I4FbAOMAYpmBpxfDJY6HAQ15TFdsPx0ov25B5AlnvUOtJ87M6MBlwP3bodUm+y3Z\n" +
                "f9EWLHFkCPbo4TErEFMyFHY=\n" +
                "-----END PRIVATE KEY-----\n"
        merchantSerialNumber = context.getString(R.string.WECHAT_CERT)
        apiV3Key = context.getString(R.string.WECHAT_API_V3)

        config = RSAAutoCertificateConfig.Builder()
            .merchantId(merchantId)
            .privateKey(privateKey)
            .merchantSerialNumber(merchantSerialNumber)
            .apiV3Key(apiV3Key)
            .build()
        appService = AppService.Builder().config(config).build()
        api = WXAPIFactory.createWXAPI(context, context.getString(R.string.APP_ID_WX), false)
    }

    suspend fun prepayOrder() {
        var payReq: PrepayRequest = PrepayRequest().apply {
            appid = appId
            mchid = merchantId
            description = "test"
            outTradeNo = UUID.randomUUID().toString().replace("-", "")
            timeExpire = convertToRFC3339(System.currentTimeMillis() + 1000 * 60 * 60)
            notifyUrl = "https://myaih.net/user/membership/wechat/notify"
            amount = Amount().apply {
                total = 1
                currency = "CNY"
            }
        }

        val preRes = appService.prepay(payReq)
        LogUtil.i("prepayOrder: $preRes")
        GlobalScope.launch(Dispatchers.Default) {
            withContext(Dispatchers.Main){
                payWX(preRes.prepayId, object : WechatCallback {
                    override fun call(result: WechatPayResult) {
                        LogUtil.i("wechatCallback: $result")
                        DialogUtil.showToast("PayCallback: $result")
                    }
                })
            }
        }


    }

    fun payWX(prepayId: String,callback: WechatCallback ) {
        WXPayEntryActivity().setWechatCallback(callback)
        api.registerApp(appId)
        LogUtil.i("payWX")
        val appId = appId //appid
        val nonceStr = NonceUtil.createNonce(32); //随机字符串
        val partnerId = merchantId //mchid
        val timeStamp = System.currentTimeMillis().toString() //时间戳
        val signString = appId + "\n" + timeStamp + "\n" + nonceStr + "\n" + prepayId + "\n"
        val signer = config.createSigner()
        val signValue = signer.sign(signString).sign
        val payReq = PayReq()
        payReq.appId = appId
        payReq.partnerId = partnerId //mchid
        payReq.prepayId = prepayId //后端返回
        payReq.packageValue = "Sign=WXPay"
        payReq.nonceStr = nonceStr //随机字符串
        payReq.timeStamp = timeStamp //时间戳
        payReq.sign = signValue
        api.sendReq(payReq)



//        //初始化一个 WXTextObject 对象，填写分享的文本内容
//        //初始化一个 WXTextObject 对象，填写分享的文本内容
//        val textObj = WXTextObject()
//        textObj.text = "123"
//        // 用 WXTextObject 对象初始化一个 WXMediaMessage 对象
//        // 用 WXTextObject 对象初始化一个 WXMediaMessage 对象
//        val msg = WXMediaMessage()
//        msg.mediaObject = textObj
//        msg.description = ""
//
//        val req = SendMessageToWX.Req()
//        req.transaction = System.currentTimeMillis().toString() //transaction字段用与唯一标示一个请求
//
//        req.message = msg
//        req.scene = WXSceneSession
//        api.sendReq(req)
    }

    private fun timeStampToRfc3339(timeStamp: Long): String {
        val simpleDateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZ");
        val formatDate = simpleDateFormat.format(Date(timeStamp));
        return formatDate;
    }
    fun convertToRFC3339(timestamp: Long): String {
        val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
        dateFormat.timeZone = TimeZone.getTimeZone("UTC")
        return dateFormat.format(timestamp)
    }
//    fun sign(message: ByteArray?): String? {
//        val sign: Signature = Signature.getInstance("SHA256withRSA")
//
//        sign.initSign()
//        sign.update(message)
//        return Base64.getEncoder().encodeToString(sign.sign())
//    }

    fun payFromWX(
        prepayId: String,
        nonceStr: String,
        timeStamp: String,
        sign: String,
        callback: WechatCallback
    ) {
        val api = WXAPIFactory.createWXAPI(activity, activity.getString(R.string.APP_ID_WX), false)
        LogUtil.i("prepayId: $prepayId, nonceStr: $nonceStr, timeStamp: $timeStamp, sign: $sign")
        WXPayEntryActivity().setWechatCallback(callback)
        LogUtil.i("payWX")
        val appId = activity.getString(R.string.APP_ID_WX) //appid
        val nonceStr = nonceStr //随机字符串
        val partnerId = activity.getString(R.string.APP_PARTNER_ID_WX) //mchid
        val timeStamp = timeStamp //时间戳
        val payReq = PayReq()
        api.registerApp(appId)
        payReq.appId = appId
        payReq.partnerId = partnerId //mchid
        payReq.prepayId = prepayId //后端返回
        payReq.packageValue = "Sign=WXPay"
        payReq.nonceStr = nonceStr //随机字符串
        payReq.timeStamp = timeStamp //时间戳
        payReq.sign = sign
        api.sendReq(payReq)
    }
}