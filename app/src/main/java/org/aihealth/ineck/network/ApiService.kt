package org.aihealth.ineck.network

import com.google.gson.JsonElement
import com.google.gson.JsonObject
import okhttp3.MultipartBody
import okhttp3.ResponseBody
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.AiNeckQuestionnaireHistoryDetailModel
import org.aihealth.ineck.model.AnswerModel
import org.aihealth.ineck.model.CervicalVertebraeResult
import org.aihealth.ineck.model.Code
import org.aihealth.ineck.model.ExerciseResult
import org.aihealth.ineck.model.FeelingResult
import org.aihealth.ineck.model.MemberShipLevel
import org.aihealth.ineck.model.PayResult
import org.aihealth.ineck.model.TimeCost
import org.aihealth.ineck.model.Utilization
import org.aihealth.ineck.model.WeChatResult
import org.aihealth.ineck.model.angles.AnglesHandle
import org.aihealth.ineck.model.angles.BackNeuralHistoryItem
import org.aihealth.ineck.model.angles.BackPain
import org.aihealth.ineck.model.angles.BackPainHistoryItem
import org.aihealth.ineck.model.angles.BackPainRecord
import org.aihealth.ineck.model.angles.BaseResponse
import org.aihealth.ineck.model.angles.FollowList
import org.aihealth.ineck.model.angles.LineChart
import org.aihealth.ineck.model.angles.MeanList
import org.aihealth.ineck.model.angles.NeckNeuralHistoryItem
import org.aihealth.ineck.model.angles.NeckPain
import org.aihealth.ineck.model.angles.NeckPainHistoryItem
import org.aihealth.ineck.model.angles.NeckPainRecord
import org.aihealth.ineck.model.angles.NeuralRecord
import org.aihealth.ineck.model.angles.Odi
import org.aihealth.ineck.model.angles.OdiHistory
import org.aihealth.ineck.model.angles.Promis
import org.aihealth.ineck.model.angles.PromisHistory
import org.aihealth.ineck.model.angles.Record
import org.aihealth.ineck.model.angles.RecordScales
import org.aihealth.ineck.model.angles.Severity
import org.aihealth.ineck.model.angles.ToEmail
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.model.improvement.ChatItem
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.model.improvement.ProgramModel
import org.aihealth.ineck.model.meet.MeetMsg
import org.aihealth.ineck.model.vitalsigns.BloodGlucose
import org.aihealth.ineck.model.vitalsigns.BloodOxygen
import org.aihealth.ineck.model.vitalsigns.BloodPressure
import org.aihealth.ineck.model.vitalsigns.BodyTemperature
import org.aihealth.ineck.model.vitalsigns.HeartRate
import org.aihealth.ineck.model.vitalsigns.VitalSignGroup
import org.aihealth.ineck.util.token
import org.aihealth.ineck.viewmodel.user
import org.threeten.bp.Instant
import org.threeten.bp.ZoneId
import org.threeten.bp.ZonedDateTime
import org.threeten.bp.format.DateTimeFormatter
import retrofit2.Call
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.Multipart
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Part
import retrofit2.http.Path
import retrofit2.http.Query
import java.util.Locale
import java.util.TimeZone

/**
 * 构建语言标签，确保语言和地区的一致性
 */
fun buildLanguageTag(): String {
    // 根据currentLocale构建完整的语言标签
    return if (currentLocale == Locale.CHINESE) {
        "zh-CN"  // 中文使用中国地区
    } else {
        "en-US"  // 英文使用美国地区
    }
}

interface ApiService {

    val token1: String
        get() = token
//        get() = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1cGRhdGVkX2F0IjoiMjAyNC0wMi0yMVQwNDozNDozMi43MzBaIiwiYWRkcmVzcyI6eyJjb3VudHJ5IjpudWxsLCJwb3N0YWxfY29kZSI6bnVsbCwicmVnaW9uIjpudWxsLCJmb3JtYXR0ZWQiOm51bGx9LCJwaG9uZV9udW1iZXJfdmVyaWZpZWQiOnRydWUsInBob25lX251bWJlciI6IjE4NjY4MDY5Nzc4IiwibG9jYWxlIjpudWxsLCJ6b25laW5mbyI6bnVsbCwiYmlydGhkYXRlIjpudWxsLCJnZW5kZXIiOiJVIiwiZW1haWxfdmVyaWZpZWQiOmZhbHNlLCJlbWFpbCI6bnVsbCwid2Vic2l0ZSI6bnVsbCwicGljdHVyZSI6Imh0dHBzOi8vZmlsZXMuYXV0aGluZy5jby9hdXRoaW5nLWNvbnNvbGUvZGVmYXVsdC11c2VyLWF2YXRhci5wbmciLCJwcm9maWxlIjpudWxsLCJwcmVmZXJyZWRfdXNlcm5hbWUiOm51bGwsIm5pY2tuYW1lIjpudWxsLCJtaWRkbGVfbmFtZSI6bnVsbCwiZmFtaWx5X25hbWUiOm51bGwsImdpdmVuX25hbWUiOm51bGwsIm5hbWUiOm51bGwsInN1YiI6IjY1ZDU3ZDU4NjM0MTUxYTY4NWQ5ZDAwNiIsImV4dGVybmFsX2lkIjpudWxsLCJ1bmlvbmlkIjpudWxsLCJ1c2VybmFtZSI6bnVsbCwiZGF0YSI6eyJ0eXBlIjoidXNlciIsInVzZXJQb29sSWQiOiI2MTdmNTdhNTQwZWI0NDZiMjYwM2RlNjQiLCJhcHBJZCI6IjYxN2Y1N2IxY2NmYjgzMjdhNjVlNTFhMCIsImlkIjoiNjVkNTdkNTg2MzQxNTFhNjg1ZDlkMDA2IiwidXNlcklkIjoiNjVkNTdkNTg2MzQxNTFhNjg1ZDlkMDA2IiwiX2lkIjoiNjVkNTdkNTg2MzQxNTFhNjg1ZDlkMDA2IiwicGhvbmUiOiIxODY2ODA2OTc3OCIsImVtYWlsIjpudWxsLCJ1c2VybmFtZSI6bnVsbCwidW5pb25pZCI6bnVsbCwib3BlbmlkIjpudWxsLCJjbGllbnRJZCI6IjYxN2Y1N2E1NDBlYjQ0NmIyNjAzZGU2NCJ9LCJ1c2VycG9vbF9pZCI6IjYxN2Y1N2E1NDBlYjQ0NmIyNjAzZGU2NCIsImF1ZCI6IjYxN2Y1N2IxY2NmYjgzMjdhNjVlNTFhMCIsImV4cCI6MTcwOTY5OTY3MiwiaWF0IjoxNzA4NDkwMDcyLCJpc3MiOiJodHRwczovL2tmbWhzLWV3cS5hdXRoaW5nLmNuL29pZGMifQ.oNEI-ofo_vmfMuZcXT_X-S9GbE39yDBFcqttdyNjzV4"

    val uuid1: String
        get() = user.uuid
//        get() = "63044178a783499baad7f619"

    /**
     * 日报告请求数据
     */
    @GET("angles_handle/{uuid}/{type}?interval=1d&additionaloffset=0&fill=0&precision=s")
    fun getAnglesHandle(
        @Path("uuid") uuid: String = uuid1,
        @Path("type") type: String,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("timezone") timezone: String = TimeZone.getDefault().id,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<AnglesHandle>>

    /**
     * v2
     * 获取月报告每天填写表单（odi,promise,neck_pain,back_pain）次数
     * @param from_date 开始时间
     * @param to_date 结束时间
     */
    @GET("/v2/profiles/records")
    fun getRecord(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<Record>>>

    /**
     * 月报告页面获取每天角度（轻度，中度，重度）的次数
     */
    @GET("angles_monthly/{uuid}/{type}?interval=1d&additionaloffset=0&fill=0&precision=s")
    fun getAnglesMonthly(
        @Path("uuid") uuid: String = uuid1,
        @Path("type") type: String,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("timezone") timezone: String = TimeZone.getDefault().id,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<Severity>>>

    /**
     * 获取月报告Promis和ODI折线图数据
     */
    @GET("profiles/{uuid}/odi_promis_line_chart")
    fun getLineChart(
        @Path("uuid") uuid: String = uuid1,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<LineChart>>

    /**
     * 获取日，周,，月报告表单（odi,promise,neck_pain,back_pain）数据
     */
    @GET("profiles/{uuid}/record_scales")
    fun getRecordScales(
        @Path("uuid") uuid: String = uuid1,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<RecordScales>>

    /**
     * 获取日报告Neck表单疼痛数据历史记录详情
     */
    @GET("/v2/profiles/neck_pain")
    fun getTargetNeckPain(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<NeckPainRecord>>

    /**
     * 获取日报告Neck表单神经数据历史记录详情
     */
    @GET("/v2/profiles/neck_neural")
    fun getTargetNeckNeural(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<NeuralRecord>>

    /**
     * 获取日报告Back表单疼痛数据历史记录详情
     */
    @GET("/v2/profiles/back_pain")
    fun getTargetBackPain(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<BackPainRecord>>

    /**
     * 获取日报告Back表单数据历史记录详情
     */
    @GET("/v2/profiles/back_neural")
    fun getTargetBackNeural(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<NeuralRecord>>

    /**
     * 获取周报告ODI表单数据历史记录详情
     */
    @GET("profiles/{uuid}/record_scales?types=history")
    fun getOdiHistory(
        @Path("uuid") uuid: String = uuid1,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<OdiHistory>>

    /**
     * 获取周报告PROMIS表单数据历史记录详情
     */
    @GET("profiles/{uuid}/record_scales?types=history")
    fun getPromisHistory(
        @Path("uuid") uuid: String = uuid1,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<PromisHistory>>

    /**
     * 周报告图表数据
     */
    @GET("angles_refactor/{uuid}/{type}?type_fields=mean&interval=1d&additionaloffset=0&precision=s")
    fun getAnglesRefactor(
        @Path("uuid") uuid: String = uuid1,
        @Path("type") type: String,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("timezone") timezone: String = TimeZone.getDefault().id,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<MeanList>

    /**
     * 报告发送到邮箱
     * type: raw or after（原始数据或处理后数据）
     */
    @POST("profiles/{uuid}/report")
    fun sendReportToEmail(
        @Path("uuid") uuid: String = uuid1,
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("type") type: String = "after",
        @Query("language") language: String = if (currentLocale == Locale.CHINESE) "Chinese" else "English",
        @Body toEmail: ToEmail,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<Boolean>>

    /**
     * 上传今日疼痛记录数据(Neck)
     */
    @Deprecated("use v2")
    @POST("profiles/{uuid}/neck_pain")
    fun postNeckPain(
        @Path("uuid") uuid: String = uuid1,
        @Body neckPain: NeckPain,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    @POST("v2/profiles/neck_pain")
    fun postNeckPain(
        @Body neckPain: NeckPainRecord,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传今日疼痛记录数据(Back)
     */
    @Deprecated("use v2")
    @POST("profiles/{uuid}/back_pain")
    fun postBackPain(
        @Path("uuid") uuid: String = uuid1,
        @Body backPain: BackPain,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    @POST("v2/profiles/back_pain")
    fun postBackPain(
        @Body backPain: BackPainRecord,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传今日神经指数(Neck)
     */
    @POST("v2/profiles/neck_neural")
    fun postNeckNeural(
        @Body neural: NeuralRecord,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传今日神经指数(back)
     */
    @POST("v2/profiles/back_neural")
    fun postBackNeural(
        @Body neural: NeuralRecord,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     *  获取 Neck Pain 疼痛历史记录 (一个月内)
     */
//    @Deprecated("use v2")
//    @GET("profiles/{uuid}/neck_pain")
//    fun getNeckPainHistory(
//        @Path("uuid") uuid: String = uuid1,
//        @Query("from_date") from_date: String,
//        @Query("to_date") to_date: String,
//        @Query("types") types: String = "history",
//        @Header("Authorization") authorization: String = "Bearer $token1"
//    ): Call<BaseResponse<List<NeckPainHistoryUnit>>>

    @GET("v2/profiles/neck_pain")
    fun getNeckPainHistory(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<NeckPainHistoryItem>>>

    @GET("v2/profiles/back_pain")
    fun getBackPainHistory(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<BackPainHistoryItem>>>

    @GET("v2/profiles/neck_neural")
    fun getNeckNeuralHistory(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<NeckNeuralHistoryItem>>>

    @GET("v2/profiles/back_neural")
    fun getBackNeuralHistory(
        @Query("from_date") from_date: String,
        @Query("to_date") to_date: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<BackNeuralHistoryItem>>>

    /**
     * 上传promis数据
     */
    @POST("profiles/{uuid}/promise")
    fun postPromis(
        @Path("uuid") uuid: String = uuid1,
        @Body promis: Promis,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传odi数据
     */
    @POST("profiles/{uuid}/oswestry")
    fun postOdi(
        @Path("uuid") uuid: String = uuid1,
        @Body odi: Odi,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传aiNeck角度数据
     */
    @POST("angles_refactor/{uuid}/neck?precision=s")
    fun postNeckAngle(
        @Path("uuid") uuid: String = uuid1,
        @Body body: Any,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传aiNeck视频监测的角度数据
     */
    @POST("angles_refactor/{uuid}/neck_cv?precision=s")
    fun postNeckAngleCV(
        @Path("uuid") uuid: String = uuid1,
        @Body body: Any,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传aiBack角度数据
     */
    @POST("angles_refactor/{uuid}/back?precision=s")
    fun postBackAngle(
        @Path("uuid") uuid: String = uuid1,
        @Body body: Any,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传aiBackCV角度数据
     */
    @POST("angles_refactor/{uuid}/back_cv?precision=s")
    fun postBackAngleCV(
        @Path("uuid") uuid: String = uuid1,
        @Body body: Any,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 上传aiBack角度数据
     */
    @POST("angles_refactor/joints/knee?precision=s")
    fun postKneeAngle(
        @Body body: Any,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * v2 获取用户信息
     */
    @GET("/v2/profiles/info")
    fun getInfo(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<JsonObject>>

    @GET("profiles/info")
    fun getUserInfoWithoutToken(
        @Query("uuid") uuid: String
    ): Call<BaseResponse<JsonObject>>

    /**
     * 个人信息首次上传
     * @path type : 0(用户端) 1(指导端)
     */
    @POST("/v2/profiles/info?type=0")
    fun postInfo(
        @Body body: User,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     * 个人信息修改
     */
    @PATCH("/v2/profiles/info")
    fun updateInfo(
        @Query("uuid") uuid: String = user.uuid,
        @Body body: Any = user,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     *  删除用户
     */
    @DELETE("/v2/profiles/info")
    fun deleteInfo(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     * 获取关注列表
     */
    @GET("/v2/follows/follow")
    fun getFollows(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<FollowList>>

    /**
     * 添加关注对象
     */
    @POST("/v2/follows/follow")
    fun addFollow(
//        @Path("uuid") uuid: String = uuid1,
//        @Query("following_id") following_id: String,
        @Query("following_id") followingId: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     * 删除关注对象
     */
    @PATCH("follows/{uuid}/follow")
    fun deleteFollow(
        @Path("uuid") uuid: String = uuid1,
        @Query("following_id") following_id: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     * 查询历史表单记录
     */
//    @GET("profiles/{uuid}/{type}?types=history")
//    fun getPainRecord(
//        @Path("uuid") uuid: String = uuid1,
//        @Path("")
//    ): Call<BaseResponse<List<>>>\

    /**
     *  获取生命体征数据
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     *  包括：血糖(blood_glucose)、血压(blood_pressure)、血氧含量(blood_oxygen)、体温(Body Temperature)、心率(Heart Rate)
     */
    @GET("/v2/profiles/vital_signs/all")
    fun getVitalSigns(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<VitalSignGroup>>

    /**
     *  上传血氧数据
     *  @param  body    上传数据内容Body
     *  @param  authorization   验证用户token
     */
    @POST("/v2/profiles/vital_signs/blood_oxygen")
    fun postBloodOxygen(
        @Query("date_time") dateTime:String = getCurrentTimeInUTC(),
        @Query("data_sources") dataSources :String = "1",
        @Body body: BloodOxygen,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<Int>>

    /**
     *  获取血氧数据历史记录
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     */
    @GET("/v2/profiles/vital_signs/blood_oxygen")
    fun getHistoryOfBloodOxygen(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  上传体温数据
     *  @param  body    上传数据内容Body
     *  @param  authorization   验证用户token
     */
    @POST("/v2/profiles/vital_signs/body_temperature")
    fun postBodyTemperature(
        @Query("date_time") dateTime:String = getCurrentTimeInUTC(),
        @Query("data_sources") dataSources :String = "1",
        @Body body: BodyTemperature,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  获取体温数据历史记录
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     */
    @GET("/v2/profiles/vital_signs/body_temperature")
    fun getHistoryOfBodyTemperature(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  上传血压数据
     *  @param  body    上传数据内容Body
     *  @param  authorization   验证用户token
     */
    @POST("/v2/profiles/vital_signs/blood_pressure")
    fun postBloodPressure(
        @Query("date_time") dateTime:String = getCurrentTimeInUTC(),
        @Query("data_sources") dataSources :String = "1",
        @Body body: BloodPressure,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  获取血压数据历史记录
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     */
    @GET("/v2/profiles/vital_signs/blood_pressure")
    fun getHistoryOfBloodPressure(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  上传血糖数据
     *  @param  body    上传数据内容Body
     *  @param  authorization   验证用户token
     */
    @POST("/v2/profiles/vital_signs/blood_glucose")
    fun postBloodGlucose(
        @Query("date_time") dateTime:String = getCurrentTimeInUTC(),
        @Query("data_sources") dataSources :String = "1",
        @Body body: BloodGlucose,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  获取血糖数据历史记录
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     */
    @GET("/v2/profiles/vital_signs/blood_glucose")
    fun getHistoryOfBloodGlucose(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  上传心率数据
     *  @param  body    上传数据内容Body
     *  @param  authorization   验证用户token
     */
    @POST("/v2/profiles/vital_signs/heart_rate")
    fun postHeartRate(
        @Query("date_time") dateTime:String = getCurrentTimeInUTC(),
        @Query("data_sources") dataSources :String = "1",
        @Body body: HeartRate,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  获取心率历史数据
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     */
    @GET("/v2/profiles/vital_signs/heart_rate")
    fun getHistoryOfHeartRate(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Query("types") types: String = "history",
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     *  V2
     *  获取改善页项目内容
     *  @param  authorization   验证用户token
     */
    @GET("/v2/user/programs/improvement/match")
    fun getImprovementProgramData(
        @Query("lang") language: String? = buildLanguageTag(),
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<ImprovementProgramsData>>

    /**
     * V2
     * 获取子课程详情
     */
    @GET("/v2/user/programs/section")
    fun getImprovementProgramDetailData(
        @Query("material_id") materialId: Int,
        @Query("lang") language: String? = buildLanguageTag(),
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<ProgramModel>>

    /**
     *  获取当前用户已练习过的改善项目数据
     *  @param  fromDate    时间段开始时间
     *  @param  toDate      时间段结束时间
     *  @param  authorization   验证用户token
     */
    @GET("/v2/profiles/exercise")
    fun getImprovementProgramUserExerciseData(
        @Query("from_date") fromDate: String,
        @Query("to_date") toDate: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     * 上传当前用户已练习过的改善项目数据
     */
    @POST("/v2/profiles/exercise")
    fun postImprovementProgramUserExerciseData(
        @Body body: ExerciseResult,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     * 上传当前用户对已练习过的改善项目的评价
     */
    @PATCH("/v2/profiles/exercise")
    fun patchImprovementProgramUserExerciseData(
        @Body body: FeelingResult,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonObject>>

    /**
     * 提交用户在线时长
     */
    @POST("/v2/user/utilization/time")
    fun postUserUtilizationTime(
        @Body body: Utilization,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<ResponseBody>

    /**
     * update v2 at 2024/02/02
     * 获取用户在线时长和用户跟练总时间
     */
    @GET("/v2/user/utilization/time")
    fun getUserUtilizationTime(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<TimeCost>>

    /**
     * 兑换码使用接口
     */
    @POST("/user/membership/redemption_code/activate")
    fun postRedemptionCodeActivate(
        @Body body : Code,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<JsonElement>>

    /**
     * 会员试用接口
     */
    @POST("/user/membership/free_trial")
    fun postFreeTrial(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<JsonElement>>

    /**
     * 获取吊起微信支付参数
     */
    @POST("/user/membership/wechat_pay")
    fun postWechatPay(
        @Body body : MemberShipLevel,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<WeChatResult>>

    /**
     * 获取微信支付订单
     */
    @GET("/user/membership/wechat_official_pay_transaction")
    fun getWechatBill(
        @Query("out_trade_no") outTradeNo : String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<PayResult>>

    /**
     * 提交颈椎问卷
     */
    @POST("/questionnaire/evaluation/cervical_vertebrae")
    fun postCervicalVertebrae(
        @Body body : AnswerModel,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<JsonElement>>

    /**
     * 获取颈椎问卷结果
     */
    @GET("/questionnaire/evaluation/cervical_vertebrae")
    fun getCervicalVertebraeResult(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<CervicalVertebraeResult>>

    /**
     * 获取 aiNeck 健康问卷链路历史记录
     */
    @GET("/questionnaire/evaluation/cervical_vertebrae?types=history")
    fun getAiNeckHistory(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<JsonElement>

    @GET("/questionnaire/evaluation/cervical_vertebrae")
    fun getAiNeckDetailHistory(
        @Query("types") types: String = "info",
        @Query("obj_id") objId: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<AiNeckQuestionnaireHistoryDetailModel>

    /**
     * 提交背部问卷
     */
    @POST("/questionnaire/evaluation/lumbar_vertebrae")
    fun postLumbarVertebrae(
        @Body body: AnswerModel,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     * 获取背部问卷结果
     */
    @GET("/questionnaire/evaluation/lumbar_vertebrae")
    fun getLumbarVertebraeResult(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<CervicalVertebraeResult>>

    /**
     * 获取 aiback 健康问卷链路历史记录
     */
    @GET("/questionnaire/evaluation/lumbar_vertebrae?types=history")
    fun getAiBackHistory(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<JsonElement>

    @GET("/questionnaire/evaluation/lumbar_vertebrae")
    fun getAiBackDetailHistory(
        @Query("types") types: String = "info",
        @Query("obj_id") objId: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<AiNeckQuestionnaireHistoryDetailModel>

    @GET("/v2/profiles/search?type=1")
    fun getProviderList(
        @Query("text") text: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<JsonObject>>>

    /**
     * 搜索指导 最新的
     */
    @GET("/profiles/search?type=1")
    fun searchProviderList(
        @Query("text") text: String,
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 5,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     * 获取会议列表
     */
    @GET("/v2/profiles/meeting?&type=1")
    fun getMeetingList(
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<MeetMsg>>>

    /**
     * 获取会议链接
     */
    @GET("/v2/profiles/token")
    fun getMeetingToken(
        @Query("tpc") tpc: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     * 上传图片
     */
    @Multipart
    @POST("/v2/profiles/image")
    fun uploadImage(
        @Part file: MultipartBody.Part,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ):Call<BaseResponse<String>>
    /**
     * 获取聊天对话
     */
    @GET("/chats/{rootUuid}/chat_list")
    fun getChatList(
        @Path("rootUuid") routUuid: String = uuid1,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<List<ChatItem>>>

    /**
     * 获取未读聊天数量
     */
    @GET("/v2/chats/{rootUuid}/chat_list")
    fun getUnreadChatCount(
        @Path("rootUuid") routUuid: String = uuid1,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<Int>>


    @GET("/chats/{rootUuid}/chat")
    fun getChatData(
        @Path("rootUuid") routUuid: String = uuid1,
        @Query("to_user") toUuid: String
    ): Call<BaseResponse<JsonElement>>


    /**
     * 上传聊天对话
     * {
     *     "to_user": "628de9fbed396d6bd1982270",
     *     "msg": "71 to 70！"
     * }
     */
    @POST("/chats/{rootUuid}/chat")
    fun postChatData(
        @Path("rootUuid") routUuid: String = uuid1,
        @Body body: JsonObject
    ): Call<BaseResponse<JsonElement>>


    /**
     * 修改聊天消息数据
     *  {
     *     "datetime": 1732690586,
     *     "to_user": "628de9fbed396d6bd1982273",
     *     "msg": "12dsafsasad",      // 原始消息
     *     "new_msg": "12"            // 更新后的消息
     *  }
     */
    @PUT("/chats/{rootUuid}/chat")
    fun putChatData(
        @Path("rootUuid") rootUuid: String = uuid1,
        @Body body: JsonObject,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     * 对消息记录进行已读标记
     */
    @PATCH("/chats/{rootUuid}/chat")
    fun patchChatData(
        @Path("rootUuid") rootUuid: String = uuid1,
        @Body body: JsonObject,
    ): Call<BaseResponse<JsonElement>>

    /**
     * 对全部消息记录进行已读标记
     */
    @POST("/v2/chats/{rootUuid}/chat_list")
    fun markAllMessagesAsRead(
        @Path("rootUuid") rootUuid: String = uuid1,
    ): Call<BaseResponse<JsonElement>>

    @Multipart
    @POST("/v2/material/video/upload")
    suspend fun uploadVideo(
        @Part file: MultipartBody.Part,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Response<ResponseBody>

    /**
     *{
     *    "code": 1,
     *    "message": "Articles retrieved successfully",
     *    "data": {
             "data": [
                 {
                     "date": "2024-12-25T00:00:00",
                     "title": "Strengthens back and core muscles",
                     "image": "https://myaih-net-profile-images.s3.amazonaws.com/e1f5cde1-0241-4f53-ae84-b838293a9a68_1.png",
                     "user_image": "https://myaih-net-profile-images.s3.amazonaws.com/38c0029d-0439-435b-ad38-044b19b0ecca_jpg",
                     "link": "https://www.instagram.com/p/DD_d9MVunrE/?utm_source=ig_web_copy_link&igsh=MzRlODBiNWFlZA==",
                     "author": "AIH",
                     "describe": "Perform abdominal, lumbar and gluteal strength exercises such as planks"
                 }
             ],
             "page": 1,
             "total": 9
         }
     *}
     */
    @GET("/user/upload/article")
    fun getArticle(
        @Query("page") page: Int = 1,
        @Query("page_size") pageSize: Int = 5,
        @Query("lang") lang: String = if (currentLocale == Locale.CHINESE) "cn" else "us",
    ): Call<BaseResponse<JsonElement>>

    @POST("/rtm/record/recording_time")
    fun postRTMRecordingTime(
        @Header("Authorization") authorization: String = "Bearer $token1",
        @Body body: JsonObject
    ): Call<BaseResponse<Int>>

    /**
     * 更新FCM token
     */
    @POST("/profiles/add/user?test=1")
    fun updateFCMToken(
        @Body body: Map<String, String>,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 友盟 token
     */
    @POST("/profiles/add/user?test=1")
    fun updateUPushToken(
        @Body body: Map<String, String>,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<ResponseBody>

    /**
     * 获取用户体能数据(步数等)
     * @param test 测试参数
     * @param dateTime 指定日期时间
     * @param authorization 验证用户token
     */
    @GET("/v3/profiles/vital_signs/body_fitness?test=1")
    fun getBodyFitness(
        @Query("date_time") dateTime: String,
        @Header("Authorization") authorization: String = "Bearer $token1"
    ): Call<BaseResponse<JsonElement>>

    /**
     * 发送验证邮件
     * @param email 需要验证的邮箱地址
     */
    @POST("/profiles/auth/email")
    fun sendVerificationEmail(
        @Query("email") email: String
    ): Call<BaseResponse<JsonElement>>

}

fun convertTimestampToFormattedDate(timestamp: Long): String {
    val instant = Instant.ofEpochSecond(timestamp)
    val zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.systemDefault())
    return zonedDateTime.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME) + ".000000000"
}

fun getCurrentTimeInUTC(): String {
    val currentTimeMillis = System.currentTimeMillis()

    // 转换为 UTC 时间的 Instant
    val instant = Instant.ofEpochMilli(currentTimeMillis)

    // 格式化为无毫秒的 UTC 格式 (统一与API返回格式一致)
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
        .withZone(ZoneId.of("UTC"))

    val result = formatter.format(instant)
    
    // 添加调试日志确认格式
    println("getCurrentTimeInUTC: 生成上传时间格式 = $result")
    
    return result // 输出 2025-06-05T03:30:00Z
}