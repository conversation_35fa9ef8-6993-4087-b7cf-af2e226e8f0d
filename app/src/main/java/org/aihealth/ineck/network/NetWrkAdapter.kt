package org.aihealth.ineck.network

import com.google.gson.JsonDeserializationContext
import com.google.gson.JsonDeserializer
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.gson.JsonSyntaxException
import java.lang.reflect.Type

class GsonIntegerAdapter : JsonSerializer<Int>, JsonDeserializer<Int> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Int {
        try {
            if (json.asString.equals("") || json.asString.equals("null")) { //定义为int类型,如果后台返回""或者null,则返回0
                return 0
            }
        } catch (ignore: java.lang.Exception) {
        }
        return try {
            json.asInt
        } catch (e: NumberFormatException) {
            throw JsonSyntaxException(e)
        }
    }
    override fun serialize(
        src: Int?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src)
    }

}
class GsonLongAdapter : JsonSerializer<Long>, JsonDeserializer<Long> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Long {
        try {
            if (json.asString.equals("") || json.asString.equals("null")) {
                return 0L
            }
        } catch (ignore: java.lang.Exception) {
        }
        return try {
            json.asLong
        } catch (e: NumberFormatException) {
            throw JsonSyntaxException(e)
        }
    }
    override fun serialize(
        src: Long?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src)
    }
}

class GsonFloatAdapter : JsonSerializer<Float>, JsonDeserializer<Float> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Float {
        try {
            if (json.asString.equals("") || json.asString.equals("null")) {
                return 0F
            }
        } catch (ignore: java.lang.Exception) {
        }
        return try {
            json.asFloat
        } catch (e: NumberFormatException) {
            throw JsonSyntaxException(e)
        }
    }
    override fun serialize(
        src: Float?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src)
    }
}

class GsonDoubleAdapter : JsonSerializer<Double>, JsonDeserializer<Double> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Double {
        try {
            if (json.asString.equals("") || json.asString.equals("null")) {
                return 0.0
            }
        } catch (ignore: java.lang.Exception) {
        }
        return try {
            json.asDouble
        } catch (e: NumberFormatException) {
            throw JsonSyntaxException(e)
        }
    }
    override fun serialize(
        src: Double?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src)
    }
}


class GsonBooleanAdapter : JsonSerializer<Boolean>, JsonDeserializer<Boolean> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): Boolean {
        try {
            if (json.asString.equals("") || json.asString.equals("null")) { //定义为Boolean类型,如果后台返回""或者null,则返回false
                return false
            }
        } catch (ignore: java.lang.Exception) {
        }
        return try {
            json.asBoolean
        } catch (e: NumberFormatException) {
            throw JsonSyntaxException(e)
        }
    }
    override fun serialize(
        src: Boolean?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src)
    }
}

class GsonStringAdapter: JsonSerializer<String>, JsonDeserializer<String> {
    override fun deserialize(
        json: JsonElement,
        typeOfT: Type?,
        context: JsonDeserializationContext?
    ): String {
        try {
            if (json.asString.equals("null")) {
                return ""
            }
        } catch (ignore: java.lang.Exception) {
        }
        return try {
            json.asString
        } catch (e: NumberFormatException) {
            throw JsonSyntaxException(e)
        }
    }
    override fun serialize(
        src: String?,
        typeOfSrc: Type?,
        context: JsonSerializationContext?
    ): JsonElement {
        return JsonPrimitive(src)
    }
}