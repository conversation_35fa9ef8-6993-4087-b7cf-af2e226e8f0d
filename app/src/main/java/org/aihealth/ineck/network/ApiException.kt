package org.aihealth.ineck.network

import android.net.ParseException
import com.google.gson.JsonParseException
import org.json.JSONException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

class ApiException(
    e: Throwable
) : Exception(){
    // 未知错误
    private val UNKNOWN_ERROR = 1000
    // 解析错误
    private val PARSE_ERROR = 1001
    // 网络错误/连接错误
    private val NETWORK_ERROR = 1002

    var code: Int
    override var message: String? = null
    init {
        message = e.message
        if (e is JsonParseException || e is JSONException || e is ParseException) {
            code = PARSE_ERROR
        } else if (e is UnknownHostException || e is SocketTimeoutException) {
            code = NETWORK_ERROR
        } else {
            code = UNKNOWN_ERROR
        }
    }
}