package org.aihealth.ineck.network

import okhttp3.ResponseBody
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.TimeStamp
import org.aihealth.ineck.model.Utilization
import org.aihealth.ineck.model.angles.Angle
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.viewmodel.user
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

val apiService by lazy {
    NetworkManager.retrofit.create(ApiService::class.java)
}

object NetWork {

    // 同步请求统一处理异常
    fun <T> Call<T>.mExecute(): Response<T>? {
        try {
            return execute()
        } catch (e: Exception) {
            LogUtil.d(ApiException(e).let { "请求出错：${it.code},原因：${it.message} " })
            return null
        }
    }

    // 更简洁进行调用
    fun <T> Call<T>.enqueue(
        onFailure: (call: Call<T>, throwable: Throwable) -> Unit = { call, throwable -> },
        onResponse: (call: Call<T>, any: T?) -> Unit = { call, any -> }
    ) {
        enqueue(object : Callback<T> {
            override fun onResponse(call: Call<T>, response: Response<T>) {
                onResponse(call, response.body())
            }
            override fun onFailure(call: Call<T>, t: Throwable) {
                onFailure(call,t)
            }
        })
    }

    // 只需要response时更简洁进行调用
    fun <T> Call<T>.enqueueBody(
        onResponse: ( T? ) -> Unit
    ) {
        enqueue(object : Callback<T> {
            override fun onResponse(call: Call<T>, response: Response<T>) {
                LogUtil.d("response success" + response.body().toString())
                onResponse(response.body())
            }
            override fun onFailure(call: Call<T>, t: Throwable) {
                DialogUtil.hideLoading()
                if (t.message == "No Internet Connection"){
//                    onResponse()
                }
                LogUtil.d(("response failure" + t.message) ?: "")
            }
        })
    }

    /**
     * 上传aiNeck角度
     */
    fun postAngle(
        type: DeviceType,
        list: List<Angle>,
        onResponse: ( ResponseBody? ) -> Unit
    ) {

        val angleMap = HashMap<String,Any>()
        val valueMap = HashMap<String,String>()
        list.forEach { angle ->
            valueMap.clear()
            valueMap.put(
                key = when(angle.angle) {
                    in 0..15 -> "1"
                    in 15..30 -> "2"
                    else -> "3"
                },
                value = angle.angle.toString()
            )
            angleMap.put(angle.timestamp.toString(),valueMap)
        }
        val bodyMap = HashMap<String,Any>()
        bodyMap.put("angles",angleMap)
        HashMap<String,Any>().apply {
            put(user.uuid,bodyMap)
        }.let {
            when (type) {
                DeviceType.aiNeck -> {
                    apiService.postNeckAngle(
                        body = it
                    ).enqueueBody(onResponse)
                }
                DeviceType.aiNeckCV -> {
                    apiService.postNeckAngleCV(
                        body = it
                    ).enqueueBody(onResponse)
                }
                DeviceType.aiBack -> {
                    apiService.postBackAngle(
                        body = it
                    ).enqueueBody(onResponse)
                }
                DeviceType.aiBackCV -> {
                    apiService.postBackAngleCV(
                        body = it
                    ).enqueueBody(onResponse)
                }

                DeviceType.KneeJoint -> {
                    apiService.postKneeAngle(
                        body = it
                    ).enqueueBody(onResponse)
                }
                else -> {}
            }
        }

    }
    fun postTimeStamp(
        timeStamp:TimeStamp,
        onResponse: (ResponseBody?) -> Unit
    ){
        if (timeStamp.TimeSpent > 0) {
            val data = Utilization(
                startTime = (timeStamp.StartTime).toString(),
                endTime = (timeStamp.EndTime).toString(),
                timeSpent = timeStamp.TimeSpent,
            )
            LogUtil.i("data: $data")
            apiService.postUserUtilizationTime(
                body = data
            ).enqueueBody(onResponse)
        }
    }
}