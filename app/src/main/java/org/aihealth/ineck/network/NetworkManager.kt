package org.aihealth.ineck.network

import android.annotation.SuppressLint
import android.content.Context
import android.net.ConnectivityManager
import com.chuckerteam.chucker.api.ChuckerInterceptor
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.Response
import okhttp3.logging.HttpLoggingInterceptor
import org.aihealth.ineck.BuildConfig
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.isInChina
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.io.IOException
import java.security.cert.X509Certificate
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.X509TrustManager


object NetworkManager {

    //30秒超时断开连接
    private const val TIME_OUT = 10L

//    private const val base_url = "http://161.189.104.30:5000"
//    private const val base_url = "http://52.82.81.193:5000"
    /**
     * 国内域名
     */
    private const val BASE_URL_CHINESE = "https://aispine.aihnet.cn"

    /**
     * 国外域名
     */
    private const val BASE_URL_ENGLISH = "https://myaih.net"

    // 请求头添加session
    private val mHeadersInterceptor: Interceptor by lazy { HeadersInterceptor() }


    val retrofit by lazy {
        Retrofit.Builder()
            .client(initOkHttpClient())
            .baseUrl(if (isInChina) BASE_URL_CHINESE else BASE_URL_ENGLISH)
            .addConverterFactory(GsonConverterFactory.create(buildGson())) // 数据转换器，解析
            .build()
    }

    private fun initOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .trustAllSSLSocket() // 信任所有证书
            .connectTimeout(TIME_OUT, TimeUnit.SECONDS)
            .addInterceptor(mHeadersInterceptor)
            .addInterceptor(ConnectivityInterceptor(activity))
            .addInterceptor(HttpLoggingInterceptor().apply {
                level =
                    if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE
            })
            .addInterceptor(
                //添加chucker实现应用内显示网络请求信息拦截器
                ChuckerInterceptor.Builder(baseApplication).build()
            )
            .readTimeout(TIME_OUT, TimeUnit.SECONDS)
            .writeTimeout(TIME_OUT, TimeUnit.SECONDS)
            .build()
    }

    fun buildGson(): Gson {

        return GsonBuilder()
            .registerTypeAdapter(Int::class.java, GsonIntegerAdapter())
            .registerTypeAdapter(Float::class.java, GsonFloatAdapter())
            .registerTypeAdapter(Double::class.java, GsonDoubleAdapter())
            .registerTypeAdapter(Long::class.java, GsonLongAdapter())
            .registerTypeAdapter(Boolean::class.java, GsonBooleanAdapter())
            .registerTypeAdapter(String::class.java, GsonStringAdapter())
            .create()
    }

    private fun OkHttpClient.Builder.trustAllSSLSocket(): OkHttpClient.Builder {
        // 信任所有服务器地址
        hostnameVerifier { _, _ -> true }
        val x509TrustManager = object : X509TrustManager {
            override fun checkClientTrusted(
                chain: Array<out X509Certificate>?,
                authType: String?
            ) {
            }

            override fun checkServerTrusted(
                chain: Array<out X509Certificate>?,
                authType: String?
            ) {
            }

            override fun getAcceptedIssuers(): Array<X509Certificate> {
                return emptyArray()
            }
        }
        val trustAllCerts = arrayOf(x509TrustManager)
        try {
            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(null, trustAllCerts, java.security.SecureRandom())
            sslSocketFactory(sslContext.socketFactory, x509TrustManager)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return this
    }

    /**
     * 设置请求头
     */
    private class HeadersInterceptor : Interceptor {
        override fun intercept(chain: Interceptor.Chain): Response {
            val original: Request = chain.request()
            val requestBuilder: Request.Builder = original.newBuilder()
            val request: Request = requestBuilder.build()
//            注意目前全部都是使用测试环境
//            if(BuildConfig.DEBUG){
//                val newHttpUrl = original.url.newBuilder()
//                    .addQueryParameter("test", "1")
//                    .build()
//                return chain.proceed(requestBuilder.url(newHttpUrl).build());
//            }
//            return chain.proceed(request)
            val host =
                if (isInChina) BASE_URL_CHINESE.substring(8) else BASE_URL_ENGLISH.substring(8)
            val newHttpUrl = original.url.newBuilder()
                .host(host)
                .addQueryParameter("test", "1")
                .build()
            return chain.proceed(requestBuilder.url(newHttpUrl).build());

        }
    }
}

class ConnectivityInterceptor(private val context: Context) : Interceptor {

    override fun intercept(chain: Interceptor.Chain): Response {
        if (!isOnline(context)) {
            throw IOException("No Internet Connection")
        }
        return chain.proceed(chain.request())
    }

    @SuppressLint("ServiceCast")
    private fun isOnline(context: Context): Boolean {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val networkInfo = connectivityManager.activeNetworkInfo
        return networkInfo != null && networkInfo.isConnected
    }
}
