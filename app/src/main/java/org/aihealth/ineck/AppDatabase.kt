package org.aihealth.ineck

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import org.aihealth.ineck.dao.AngleDao
import org.aihealth.ineck.dao.StepDataDao
import org.aihealth.ineck.dao.TimeStampDao
import org.aihealth.ineck.model.StepEntity
import org.aihealth.ineck.model.TimeStamp
import org.aihealth.ineck.model.angles.Angle

val database by lazy {
    val DATABASE_NAME = "AIH_User"
    Room.databaseBuilder(baseApplication, AppDatabase::class.java, DATABASE_NAME)
//        .createFromAsset("db1.db")
        .allowMainThreadQueries()
        .fallbackToDestructiveMigration(false)
//        .addMigrations(MIGRATION_1_2)
        .build()
}

@Database(
    entities = [Angle::class,TimeStamp::class,StepEntity::class],
    version = 3,
    exportSchema = false
)
abstract class AppDatabase: RoomDatabase() {
    abstract fun angleDao(): AngleDao

    abstract fun timeStampDao(): TimeStampDao

    abstract fun stepDataDao(): StepDataDao
}

/**
 *  数据库版本1迁移到2
 */
//val MIGRATION_1_2 = object : Migration(1,2) {
//    override fun migrate(database: SupportSQLiteDatabase) {
//        database.apply {
//            execSQL("CREATE TABLE `angleCV` (`id` INTEGER PRIMARY KEY NOT NULL, `type` TEXT NOT NULL, `angle` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL)")
//            execSQL("CREATE UNIQUE INDEX `index_angleCV_timestamp` ON angleCV (timestamp)")
//        }
//    }
//}