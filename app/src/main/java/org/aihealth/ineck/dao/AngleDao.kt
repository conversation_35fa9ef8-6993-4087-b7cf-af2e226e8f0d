package org.aihealth.ineck.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import org.aihealth.ineck.model.angles.Angle

/**
 *  针对Angle储存数据的操作方法
 */
@Dao
interface AngleDao {
    // 如果时间相同替换之前时间数据
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun add(vararg angle: Angle)

    @Update
    fun update(angle: Angle)

    @Query("delete from angle where uuid = :uuid and type = :type and timestamp <= :lastTimeStamp")
    fun clear(uuid: String,type: String,lastTimeStamp: Long)

    @Query("select * from angle where uuid = :uuid and type = :type")
    fun find(uuid: String,type: String): List<Angle>
}
