package org.aihealth.ineck.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import org.aihealth.ineck.model.TimeStamp

@Dao
interface TimeStampDao {

    /**
     * 获取当前用户的未上传的在线时间戳
     */
    @Query("SELECT * FROM TimeStamp where uid = :uid")
    fun getTimeStampByUid(uid: String): List<TimeStamp>


    /**
     * 获取当前用户的未上传的完整的在线时长
     */
    @Query("SELECT * FROM TimeStamp where uid = :uid and time_spent != 0")
    fun getTimeStampByUidWithEnd(uid: String): List<TimeStamp>

    /**
     * 获取当前用户的未上传的在线时长
     */
    @Query("SELECT SUM(time_spent) FROM TimeStamp where uid = :uid")
    fun getTotalTimeSpentByUid(uid: String): Long

    /**
     * 删除已上传的在线时间戳
     */
    @Delete
    fun deleteTimeStamp(timeStamp: TimeStamp)

    /**
     * 添加新的在线时间戳
     */
    @Insert
    fun addTimeStamp(timeStamp: TimeStamp)

    /**
     * 更新在线时间戳
     */
    @Update(onConflict = OnConflictStrategy.REPLACE)
    fun updateTimeStamp(timeStamp: TimeStamp)

}