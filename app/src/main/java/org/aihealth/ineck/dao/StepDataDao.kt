package org.aihealth.ineck.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import org.aihealth.ineck.model.StepEntity

/**
 * 针对用户步数的操作
 */
@Dao
interface StepDataDao {

    /**
     * 添加一条新记录
     * @param stepEntity
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun addNewStepData(stepEntity: StepEntity)

    /**
     * 根据日期查询记录
     *
     * @param curDate
     * @return
     */
    @Query("select * from UserStep where date = :curDate")
    fun getCurDataByDate(curDate: String): StepEntity?

    /**
     * 更新数据
     * @param stepEntity
     */
    @Update(onConflict = OnConflictStrategy.REPLACE)
    fun updateCurData(stepEntity: StepEntity)

    /**
     * 删除指定日期的记录
     *
     * @param stepEntity
     */
    @Delete
    fun deleteCurData(stepEntity: StepEntity)

    /**
     * 查询所有的记录
     *
     * @return
     */
    @Query("select * from UserStep")
    fun getAllDatas(): List<StepEntity>

    /**
     * 删除所有步数记录
     * 用于用户登出时清空数据
     */
    @Query("DELETE FROM UserStep")
    fun deleteAllSteps()

}