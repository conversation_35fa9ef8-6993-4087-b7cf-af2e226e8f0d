package org.aihealth.ineck.service

import android.Manifest
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Binder
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.core.app.ActivityCompat
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import java.util.concurrent.TimeUnit

class CallService : Service() {
    private val binder = CallBinder()
    private var startTime: Long = 0
    private var isTracking = false
    private val handler = Handler(Looper.getMainLooper())
    private var durationInSeconds = 0
    private val updateInterval = 1000L // 1 second
    
    // Callback to notify duration updates
    private var durationCallback: ((Int) -> Unit)? = null
    
    // Companion object to store the last tracked duration (persists after service is stopped)
    companion object {
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "call_service_channel"
        
        const val ACTION_START_TRACKING = "org.aihealth.ineck.service.action.START_TRACKING"
        const val ACTION_STOP_TRACKING = "org.aihealth.ineck.service.action.STOP_TRACKING"
        
        // Store the last tracked duration - this will persist even if the service is destroyed
        var lastTrackedDuration: Int = 0
            private set
        
        // Track if a service instance is currently tracking
        private var isServiceTracking = false
        
        fun startService(context: Context, startTimestamp: Long = 0) {
            val intent = Intent(context, CallService::class.java).apply {
                action = ACTION_START_TRACKING
                if (startTimestamp > 0) {
                    putExtra("start_timestamp", startTimestamp)
                }
            }
            
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(intent)
                } else {
                    context.startService(intent)
                }
                isServiceTracking = true
                LogUtil.d("CallService: Starting service with timestamp: $startTimestamp")
            } catch (e: Exception) {
                LogUtil.e("CallService: Failed to start service: ${e.message}")
                // Try as a regular service without foreground
                try {
                    context.startService(intent)
                    isServiceTracking = true
                } catch (e: Exception) {
                    LogUtil.e("CallService: Failed to start service altogether: ${e.message}")
                }
            }
        }
        
        fun stopService(context: Context) {
            val intent = Intent(context, CallService::class.java).apply {
                action = ACTION_STOP_TRACKING
            }
            try {
                context.startService(intent)
                isServiceTracking = false
                LogUtil.d("CallService: Stopping service")
            } catch (e: Exception) {
                LogUtil.e("CallService: Failed to stop service: ${e.message}")
            }
        }
        
        // Check if service is currently tracking
        fun isTracking(): Boolean {
            return isServiceTracking
        }
    }
    
    inner class CallBinder : Binder() {
        fun getService(): CallService = this@CallService
    }
    
    override fun onBind(intent: Intent?): IBinder {
        return binder
    }
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_TRACKING -> {
                val startTimestamp = intent.getLongExtra("start_timestamp", 0)
                LogUtil.d("Service received START_TRACKING command with timestamp: $startTimestamp")
                startTracking(startTimestamp)
            }
            ACTION_STOP_TRACKING -> {
                LogUtil.d("Service received STOP_TRACKING command")
                stopTracking()
            }
            else -> {
                LogUtil.d("Service received unknown command: ${intent?.action}")
            }
        }
        return START_STICKY
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // Make sure to save the duration before being destroyed
        if (isTracking) {
            lastTrackedDuration = durationInSeconds
            LogUtil.d("Service being destroyed while tracking, saving duration: $lastTrackedDuration seconds")
            isTracking = false
            isServiceTracking = false
            handler.removeCallbacks(durationUpdateRunnable)
        }
        LogUtil.d("CallService destroyed")
    }
    
    private fun startTracking(startTimestamp: Long) {
        if (!isTracking) {
            isTracking = true
            isServiceTracking = true
            
            // 使用传入的时间戳，如果没有则使用当前时间
            startTime = if (startTimestamp > 0) startTimestamp else System.currentTimeMillis()
            
            durationInSeconds = 0
            lastTrackedDuration = 0
            
            // Start as a regular foreground service without specifying phoneCall type
            try {
                startForeground(NOTIFICATION_ID, createNotification(formatDuration(0)))
                LogUtil.d("CallService: Started as foreground service")
            } catch (e: SecurityException) {
                LogUtil.e("CallService: Failed to start as foreground service: ${e.message}")
                // Continue tracking without foreground service if necessary
            }
            
            // Start updating duration
            handler.post(durationUpdateRunnable)
            
            // 添加自动停止机制：如果服务运行超过5分钟，自动停止
            handler.postDelayed({
                if (isTracking) {
                    LogUtil.w("CallService: Auto-stopping after 5 minutes to prevent stuck service")
                    stopTracking()
                }
            }, 5 * 60 * 1000) // 5分钟
            
            LogUtil.d("CallService: Started tracking call duration with start time: $startTime")
        } else {
            LogUtil.d("CallService: Already tracking, ignoring start request")
        }
    }
    
    fun stopTracking(): Int {
        val currentDuration = durationInSeconds // 保存当前时长
        
        if (isTracking) {
            isTracking = false
            isServiceTracking = false
            handler.removeCallbacks(durationUpdateRunnable)
            
            try {
                stopForeground(true)
            } catch (e: Exception) {
                LogUtil.e("CallService: Error stopping foreground: ${e.message}")
            }
            
            stopSelf()
            
            LogUtil.d("CallService: Stopped tracking. Actual duration: $currentDuration seconds")
        } else {
            LogUtil.d("CallService: Already stopped, returning current duration: $currentDuration seconds")
        }
        
        // 保存实际时长，不应用最小计费时长（业务逻辑应在ViewModel处理）
        lastTrackedDuration = currentDuration
        
        return currentDuration
    }
    
    fun setDurationCallback(callback: (Int) -> Unit) {
        durationCallback = callback
    }
    
    fun getCurrentDuration(): Int {
        return durationInSeconds
    }
    
    private val durationUpdateRunnable = object : Runnable {
        override fun run() {
            if (isTracking) {
                val currentTime = System.currentTimeMillis()
                durationInSeconds = ((currentTime - startTime) / 1000).toInt()
                
                // Update static duration value
                lastTrackedDuration = durationInSeconds
                
                // Update notification
                try {
                    val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                    notificationManager.notify(NOTIFICATION_ID, createNotification(formatDuration(durationInSeconds)))
                } catch (e: Exception) {
                    LogUtil.e("CallService: Error updating notification: ${e.message}")
                }
                
                // Notify callback
                durationCallback?.invoke(durationInSeconds)
                
                LogUtil.d("Call tracking - current duration: $durationInSeconds seconds")
                
                // Schedule next update
                handler.postDelayed(this, updateInterval)
            }
        }
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                getString(R.string.call_notification_channel),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.call_notification_channel_desc)
            }
            
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(durationText: String): android.app.Notification {
        val pendingIntent = PendingIntent.getActivity(
            this,
            0,
            Intent(this, MainActivity::class.java),
            PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle(getString(R.string.call_in_progress))
            .setContentText(getString(R.string.call_duration, durationText))
            .setSmallIcon(R.drawable.ic_chat_phone)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    private fun formatDuration(seconds: Int): String {
        val hours = TimeUnit.SECONDS.toHours(seconds.toLong())
        val minutes = TimeUnit.SECONDS.toMinutes(seconds.toLong()) % 60
        val remainingSeconds = seconds % 60
        
        return if (hours > 0) {
            String.format("%02d:%02d:%02d", hours, minutes, remainingSeconds)
        } else {
            String.format("%02d:%02d", minutes, remainingSeconds)
        }
    }
} 