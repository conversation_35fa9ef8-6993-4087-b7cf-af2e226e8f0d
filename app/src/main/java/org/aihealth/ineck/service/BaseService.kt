package org.aihealth.ineck.service

import android.app.Service
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlin.coroutines.CoroutineContext
import kotlin.coroutines.EmptyCoroutineContext

abstract class BaseService: Service() {
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

    fun launch(
        context: CoroutineContext = EmptyCoroutineContext,
        content: suspend () -> Unit
    ) = serviceScope.launch(context) {
        content()
    }

    override fun onDestroy() {
        super.onDestroy()
        serviceScope.cancel()
    }
}