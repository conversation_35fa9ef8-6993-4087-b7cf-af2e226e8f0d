package org.aihealth.ineck.view.screen

import android.Manifest
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import org.aihealth.ineck.R
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.AIHOutlinedTextField
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.dao.RedemptionCodeViewModel

@Composable
fun UseRedemptionCodeScreen(
    viewModel: RedemptionCodeViewModel,
) {

    BasePageView(
        title = stringResource(id = R.string.use_redemption_code),
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true,
        headerContent = {
            Image(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 8.dp)
                    .width(18.dp)
                    .height(18.dp)
                    .clickable {
                        if (ActivityResultUtils.checkPermissions(arrayOf(Manifest.permission.CAMERA))) {
                            viewModel.onScanClick()
                        } else {
                            viewModel.showPowerDialogVisibleState = true
                        }
                    },
                painter = painterResource(id = R.drawable.ic_scan),
                contentDescription = "scan icon"
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 10.dp)
                .padding(horizontal = 12.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Box(
                    Modifier
                        .fillMaxWidth()
                        .clip(shape = RoundedCornerShape(5.dp)),
                ) {
                    Image(
                        modifier = Modifier,
                        painter = painterResource(id = R.drawable.use_redemption_code_screen_bg),
                        contentScale = ContentScale.Crop,
                        contentDescription = "image description",
                    )
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(top = 30.dp, end = 30.dp),
                        painter = painterResource(id = R.drawable.vip_bg_3),
                        contentDescription = "vip"
                    )
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(top = 112.dp),
                        painter = painterResource(id = R.drawable.vip_line),
                        contentDescription = "line",
                    )
                    Column(
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .padding(top = 20.dp, start = 20.dp)
                    ) {
                        Text(
                            text = stringResource(id = R.string.use_redemption),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(600),
                                color = Color(0xFFFFFFFF),
                            )
                        )
                    }
                    Column(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(end = 28.dp, start = 28.dp, bottom = 33.dp)
                    ) {
                        AIHOutlinedTextField(
                            modifier = Modifier
                                .width(293.dp)
                                .height(56.dp),
                            value = viewModel.redeemCode,
                            onValueChange = {
                                viewModel.redeemCode = it
                            },
                            backgroundColor = Color.Transparent,
                            keyboardType = KeyboardType.Text,
                            placeholder = stringResource(id = R.string.input_redemption_code),
                            borderColor = Color(0xFFBABABA),
                        )
                        AIHButton(
                            text = stringResource(id = R.string.redeem),
                            onClick = {
                                if (viewModel.redeemCode.trim().isNotBlank()) {
                                    viewModel.useRedemptionCodeDialogVisible = true
                                }

//                                      mainViewModel.redeemCode()
                            },
                            modifier = Modifier
                                .padding(top = 32.dp)
                                .width(293.dp)
                                .height(56.dp),
                            fontColor = Color.Black,
                            backgroundColor = Color(0xFFFFFFFF),
                            shape = RoundedCornerShape(size = 28.dp)
                        )

                    }
                }
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp),
            ) {
                Text(
                    text = stringResource(id = R.string.line_instruction),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF3775F6),
                    )
                )
                NumberedText(stringResource(id = R.string.line_instruction_1), 1)
                NumberedText(stringResource(id = R.string.line_instruction_2), 2)
                NumberedText(stringResource(id = R.string.line_instruction_3), 3)

            }
        }
        UseRedemptionCodeDialog(viewModel)
    }
    PermissionGrantDialog(viewModel)

}

/**
 * 扫码成功后确认是否使用兑换码对话框
 */
@Composable
fun UseRedemptionCodeDialog(
    viewModel: RedemptionCodeViewModel
) {
    if (viewModel.useRedemptionCodeDialogVisible) {
        Dialog(onDismissRequest = {
            viewModel.useRedemptionCodeDialogVisible = false
        }) {
            Column(
                Modifier
                    .width(250.dp)
                    .background(Color.White, RoundedCornerShape(10.dp))
                    .padding(horizontal = 16.dp, vertical = 30.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.confirm_use_redemption_code) + ":" + viewModel.redeemCode,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(20.dp))
                Row {
                    AIHOutlinedButton(
                        text = stringResource(id = R.string.cancel),
                        onClick = {
                            viewModel.redeemCode = ""
                            viewModel.useRedemptionCodeDialogVisible = false
                        },
                        modifier = Modifier.size(90.dp, 40.dp)
                    )
                    Spacer(modifier = Modifier.width(10.dp))
                    AIHButton(
                        text = stringResource(id = R.string.confirm),
                        onClick = {
                            viewModel.redeemCode()
                            viewModel.redeemCode = ""
                            viewModel.useRedemptionCodeDialogVisible = false
                        },
                        modifier = Modifier.size(90.dp, 40.dp)
                    )
                }
            }
        }
    }
}

@Composable
fun NumberedText(text: String, number: Int) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 2.dp),
        horizontalArrangement = Arrangement.Start
    ) {
        Text(
            text = "$number.  ",
            style = TextStyle(
                fontSize = 12.sp,
                color = Color(0xFF3775F6),
            ),
        )
        Text(
            text = text,
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF3775F6),
            )
        )
    }
}

/**
 * 是否重新校准弹窗
 */
@Composable
private fun PermissionGrantDialog(
    viewModel: RedemptionCodeViewModel
) {
    if (viewModel.showPowerDialogVisibleState) {
        Dialog(onDismissRequest = { viewModel.showPowerDialogVisibleState = false }) {
            Column(
                modifier = Modifier
                    .width(300.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White)
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.camera_permission),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 40.dp),
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                                viewModel.onScanClick()
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}