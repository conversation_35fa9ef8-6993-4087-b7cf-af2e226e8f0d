package org.aihealth.ineck.view.screen.logoff

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.clearAllUserData
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.user

@Composable
fun LogOffRequireScreen() {
    var showDialog by remember { mutableStateOf(false) }
    BasePageView(
        title = stringResource(id = R.string.logoff_account),
        showBackIcon = true
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(horizontal = 12.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    modifier = Modifier.padding(top = 18.dp),
                    text = stringResource(id = R.string.logoff_account_text),
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF444444),
                    )
                )

                Text(
                    modifier = Modifier.padding(top = 12.dp),
                    text = stringResource(id = R.string.logoff_account_text2),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF444444),
                    )
                )
            }

            Button(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 68.dp)
                    .fillMaxWidth(0.8f),
                shape = RoundedCornerShape(size = 21.dp),
                colors = ButtonDefaults.buttonColors(Color(0xFF1E4BDF)),
                onClick = {
                    showDialog = true
                }
            ) {
                Text(
                    text = stringResource(id = R.string.logoff),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }

        }
    }
    ConfirmLogOffDialog(
        showDialog = showDialog,
        onCancel = {
            showDialog = false
        },
        onConfirm = {
            showDialog = false
            apiService.deleteInfo().enqueue(
                onFailure = { _, throwable ->
                    DialogUtil.showToast(throwable.message.toString())
                },
                onResponse = { _, response ->
                    if (response?.code == 1) {
                        // 清空用户的 deviceToken（本地和服务器）
                        clearAllUserData()
                        
                        userSP.all.clear()
                        startScreen(Screen.LogOffSuccess.route, false)
                    } else {
                        DialogUtil.showToast(response?.msg.toString())

                    }
                }
            )
        }
    )
}

@Composable
private fun ConfirmLogOffDialog(
    showDialog: Boolean,
    onCancel: () -> Unit = { },
    onConfirm: () -> Unit = { }
) {
    if (showDialog) {
        Dialog(onDismissRequest = { onCancel() }) {
            Column(
                modifier = Modifier
                    .background(
                        color = Color(0xFFFFFFFF),
                        shape = RoundedCornerShape(size = 17.dp)
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {

                Text(
                    modifier = Modifier
                        .padding(top = 16.dp)
                        .padding(horizontal = 12.dp),
                    text = stringResource(id = R.string.reminder),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 11.dp)
                        .padding(horizontal = 12.dp),
                    text = stringResource(id = R.string.logoff_account_text3),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                    ),
                )
                Column(
                    modifier = Modifier
                        .padding(top = 25.dp, bottom = 12.dp)
                        .padding(horizontal = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Button(
                        modifier = Modifier
                            .fillMaxWidth(0.8f)
                            .padding(vertical = 12.dp),
                        shape = RoundedCornerShape(size = 21.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = Color(0xFF1E4BDF)
                        ),
                        onClick = {
                            onConfirm()
                        }
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF444444),
                            )
                        )
                    }
                    Button(
                        modifier = Modifier.fillMaxWidth(0.8f),
                        shape = RoundedCornerShape(size = 21.dp),
                        colors = ButtonDefaults.buttonColors(Color(0xFF1E4BDF)),
                        onClick = {
                            onCancel()
                        }
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color.White,
                            )
                        )
                    }

                }
            }

        }
    }
}
