package org.aihealth.ineck.view.screen.login

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.AIHTextField
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.LoginChinaViewModel

@Composable
fun ForgetPasswordScreen(
    email:String = "",
) {
    val viewModel = viewModel<LoginChinaViewModel>()
    LaunchedEffect(Unit) {
        viewModel.forgetEmail = email
    }
    ForgetPasswordRoute(
        email = viewModel.forgetEmail,
        countDown = viewModel.forgetTimeLogin,
        code = viewModel.forgetCode,
        sendCode = {
            viewModel.sendForgetPasswordCode()
        },
        changeEmail = {
            viewModel.forgetEmail = it
        },
        changeCode = {
            viewModel.forgetCode = it
        },
        password = viewModel.forgetPassword,
        changePassword = {
            viewModel.forgetPassword = it
        },
        newPassword = viewModel.checkForgetPassword,
        changeNewPassword = {
            viewModel.checkForgetPassword = it
        },
        upload = {
            viewModel.resetPassword()
        }
    )
}

@Composable
fun ForgetPasswordRoute(
    email: String,
    changeEmail: (String) -> Unit,
    countDown: Int = 0,
    code:String = "",
    sendCode: () -> Unit = {},
    changeCode: (String) -> Unit ,
    password: String,
    changePassword: (String) -> Unit,
    newPassword:String,
    changeNewPassword: (String) -> Unit,
    upload: () -> Unit
) {
    val context = LocalContext.current
    val focusManager = LocalFocusManager.current
    val (showError, setShowError) = remember { mutableStateOf("") }

    fun validateEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    fun validateSendCode() {
        if (email.isEmpty()) {
            setShowError(context.getString(R.string.please_enter_email_account))
            return
        }
        if (!validateEmail(email)) {
            setShowError(context.getString(R.string.please_enter_corrent_email_account))
            return
        }
        setShowError("")
        sendCode()
    }

    fun validateUpload() {
        if (email.isEmpty() || !validateEmail(email)) {
            setShowError(context.getString(R.string.please_enter_corrent_email_account))
            return
        }
        if (code.isEmpty()) {
            setShowError(context.getString(R.string.enter_verification_code))
            return
        }
        if (password.isEmpty()) {
            setShowError(context.getString(R.string.please_enter_new_password))
            return
        }
        if (newPassword.isEmpty()) {
            setShowError(context.getString(R.string.please_enter_new_password_again))
            return
        }
        if (password != newPassword) {
            setShowError(context.getString(R.string.two_password_not_same))
            return
        }
        setShowError("")
        upload()
    }

    BasePageView(
        modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures(onTap = {
                    focusManager.clearFocus()
                })
            }
            .windowInsetsPadding(WindowInsets.ime),
        title = stringResource(id = R.string.forget_password),
        showBackIcon = true
    ){
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .imePadding()
                .padding(horizontal = 16.dp)
                .padding(top = 16.dp)
        ) {
            Text(
                text = stringResource(id = R.string.email),
                color = Color(0XFF444444),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp, vertical = 16.dp)
            )
            AIHTextField(
                value = email,
                onValueChange = {
                    changeEmail(it)
                },
                keyboardType = KeyboardType.Email,
                placeholder = stringResource(id = R.string.please_enter_email_account)
            )
            Text(
                text = stringResource(id = R.string.code),
                color = Color(0XFF444444),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp, vertical = 16.dp)
            )
            Box {
                AIHTextField(
                    value = code,
                    onValueChange = {
                        changeCode(it)
                    },
                    keyboardType = KeyboardType.Number,
                    maxLength = 4,
                    placeholder = stringResource(id = R.string.enter_verification_code)
                )
                Text(text = if (countDown > 0) stringResource(
                    id = R.string.get_code_after_time,
                    countDown
                ) else stringResource(
                    id = R.string.get_code
                ),
                    color = Color(0xFF1E4BDF),
                    fontSize = 14.sp,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = 16.dp)
                        .pointerInput(Unit) {
                            detectTapGestures {
                                validateSendCode()
                            }
                        }
                )
            }

            Text(
                text = stringResource(id = R.string.new_password),
                color = Color(0XFF444444),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp, vertical = 16.dp)
            )
            AIHTextField(
                value = password,
                onValueChange = {
                    changePassword(it)
                },
                keyboardType = KeyboardType.Password,
                placeholder = stringResource(id = R.string.please_enter_new_password),
            )
            Text(
                text = stringResource(id = R.string.new_password_check),
                color = Color(0XFF444444),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 10.dp, vertical = 16.dp)
            )
            AIHTextField(
                value = newPassword,
                onValueChange = {
                    changeNewPassword(it)
                },
                keyboardType = KeyboardType.Password,
                placeholder = stringResource(id = R.string.please_enter_new_password_again),
            )

            if (showError.isNotEmpty()) {
                Text(
                    text = showError,
                    color = Color.Red,
                    fontSize = 14.sp,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 10.dp, vertical = 8.dp)
                )
            }

            AIHTextButton(
                text = stringResource(id = R.string.confirm),
                onClick = {
                    validateUpload()
                },
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(top = 30.dp),
                style = TextStyle(
                    fontWeight = FontWeight(400),
                    color = Color(0xFFF7F7F7),
                    fontSize = 20.sp,
                    textAlign = TextAlign.Center
                ),
            )
        }
    }
}