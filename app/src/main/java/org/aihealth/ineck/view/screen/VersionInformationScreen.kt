package org.aihealth.ineck.view.screen

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun VersionInformationScreen() {
    BasePageView(title = stringResource(id = R.string.version_information), showBackIcon = true) {
        Column(Modifier.fillMaxSize()) {
            Item(title = stringResource(id = R.string.version_update)){
                
            }
            Item(title = stringResource(id = R.string.function_introduction)){
                startScreen(Screen.FunctionIntroduction.route)
            }
        }
        Row(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 48.dp)
                .clickable {
                    val intent = Intent(Intent.ACTION_VIEW)
                    intent.data = android.net.Uri.parse("https://beian.miit.gov.cn/")
                    activity.startActivity(intent)
                },
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "浙ICP备2021023293号-2A",
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                )
            )
            Image(
                modifier = Modifier.size(12.dp),
                painter = painterResource(id = R.drawable.small_arrow),
                contentDescription = "small icon"
            )

        }
    }
}

@Composable
private fun Item(
    title: String,
    onClick: () -> Unit = {}
) {
    Row(
        Modifier
            .fillMaxWidth()
            .height(52.dp)
            .clickable {
                onClick()
            }
            .padding(horizontal = 24.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Image(
            painter = painterResource(id = R.drawable.img_version_information),
            contentDescription = null,
            modifier = Modifier.size(22.dp)
        )
        Spacer(modifier = Modifier.width(29.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        Icon(
            painter = painterResource(id = R.drawable.img_next),
            contentDescription = null,
            tint = Color(0XFF999999),
            modifier = Modifier.size(20.dp)
        )
    }
    AIHDivider()
}