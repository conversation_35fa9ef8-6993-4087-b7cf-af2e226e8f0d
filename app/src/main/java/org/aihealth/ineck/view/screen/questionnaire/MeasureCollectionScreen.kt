package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.PagerState
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.BasePageView


@Composable
fun MeasureCollectionScreen(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    pagerCount: Int,
    onSkip: () -> Unit,
    content: @Composable (Int) -> Unit,
) {
    BasePageView(
        modifier = modifier,
        showBackIcon = true,
        title = stringResource(id = R.string.scale_collection),
        headerContent = {
            Text(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 5.dp)
                    .clickable {
                        onSkip()
                    },
                text = stringResource(id = R.string.skip),
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF3888FF),
                )
            )
        },
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            QuestionnaireTagHeader(2)
            HorizontalPager(
                state = pagerState,
                count = pagerCount,
                modifier = modifier.fillMaxWidth(),
                userScrollEnabled = false
            ) { page ->
                content(page)

            }
        }
    }
}