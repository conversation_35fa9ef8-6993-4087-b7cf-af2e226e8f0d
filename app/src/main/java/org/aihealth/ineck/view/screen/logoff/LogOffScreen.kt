package org.aihealth.ineck.view.screen.logoff

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun LogOffScreen() {
    var showDialog by remember { mutableStateOf(false) }
    BasePageView(
        title = stringResource(id = R.string.logoff_account),
        showBackIcon = true
    ) {
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Column(
                modifier = Modifier
                    .padding(horizontal = 12.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_warnning),
                    contentDescription = "image description",
                    contentScale = ContentScale.None,
                    modifier = Modifier.padding(top = 24.dp)
                )
                Text(
                    modifier = Modifier.padding(top = 4.dp),
                    text = stringResource(id = R.string.account_logoff),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF444444),
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 22.dp)
                        .fillMaxWidth(),
                    text = stringResource(id = R.string.logout_text_1),
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF444444),
                    ),
                )
                Text(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    text = stringResource(id = R.string.logout_text_2),
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF444444),
                        textAlign = TextAlign.Justify,
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    text = stringResource(id = R.string.logout_text_3),
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF444444),
                        textAlign = TextAlign.Justify,
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 4.dp)
                        .fillMaxWidth(),
                    text = stringResource(id = R.string.logout_text_4),
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF444444),
                        textAlign = TextAlign.Justify,
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 2.dp)
                        .fillMaxWidth(),
                    text = stringResource(id = R.string.logout_text_5),
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF444444),
                        textAlign = TextAlign.Justify,
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    text = stringResource(id = R.string.logout_text_6),
                    style = TextStyle(
                        fontSize = 13.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF444444),
                        textAlign = TextAlign.Justify,
                    )
                )
            }
            Button(
                modifier = Modifier
                    .padding(top = 200.dp, bottom = 68.dp)
                    .fillMaxWidth(0.8f),
                shape = RoundedCornerShape(size = 21.dp),
                colors = ButtonDefaults.buttonColors(Color(0xFF1E4BDF)),
                onClick = {
                    showDialog = true

                }
            ) {
                Text(
                    text = stringResource(id = R.string.logoff_account_btn),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
        }
    }
    ConfirmLogOffDialog(
        showDialog = showDialog,
        onCancel = {
            showDialog = false
        },
        onConfirm = {
            showDialog = false
            startScreen(Screen.LogOffRequireScreen.route, false)
        }
    )
}

@Composable
private fun ConfirmLogOffDialog(
    showDialog: Boolean,
    onCancel: () -> Unit = { },
    onConfirm: () -> Unit = { }
) {
    if (showDialog) {
        Dialog(onDismissRequest = { onCancel() }) {
            Column(
                modifier = Modifier
                    .background(
                        color = Color(0xFFFFFFFF),
                        shape = RoundedCornerShape(size = 17.dp)
                    ),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {

                Text(
                    modifier = Modifier
                        .padding(top = 16.dp)
                        .padding(horizontal = 12.dp),
                    text = stringResource(id = R.string.reminder),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                        letterSpacing = 0.6.sp,
                    )
                )
                Text(
                    modifier = Modifier
                        .padding(top = 11.dp)
                        .padding(horizontal = 20.dp),
                    text = stringResource(id = R.string.logoff_account_text3),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                        letterSpacing = 0.48.sp,
                    ),
                    textAlign = TextAlign.Start,
                )
                Column(
                    modifier = Modifier
                        .padding(top = 25.dp, bottom = 12.dp)
                        .padding(horizontal = 12.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Button(
                        modifier = Modifier.fillMaxWidth(0.8f),
                        shape = RoundedCornerShape(size = 21.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color.White
                        ),
                        border = BorderStroke(
                            width = 1.dp,
                            color = Color(0xFF1E4BDF)
                        ),
                        onClick = {
                            onConfirm()
                        }
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF444444),
                            )
                        )
                    }
                    Button(
                        modifier = Modifier
                            .fillMaxWidth(0.8f)
                            .padding(top = 12.dp),
                        shape = RoundedCornerShape(size = 21.dp),
                        colors = ButtonDefaults.buttonColors(Color(0xFF1E4BDF)),
                        onClick = {
                            onCancel()
                        }
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color.White,
                            )
                        )
                    }

                }
            }

        }
    }
}
