package org.aihealth.ineck.view.custom

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.Sizes
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.finish

@Composable
fun ActionBar(
    title: String = "",
    showBackIcon: Boolean = false,
    headerHeight: Dp = Sizes.actionBarHeight,
    headerColor: Color = Color(0xFF333333),
    backgroundColor: Color = Color.Transparent,
    statusBarDarkContentEnabled: Boolean = true,
    content: @Composable BoxScope.() -> Unit = {},
    centerTitle: Boolean = false,
    onBackPressed: () -> Unit = { finish() }
) {
    val tx = rememberUpdatedState(newValue = title)
    val systemUiController = rememberSystemUiController()
    DisposableEffect(Unit) {
        systemUiController.statusBarDarkContentEnabled = statusBarDarkContentEnabled
        onDispose {  }
    }
    Box(modifier = Modifier
        .fillMaxWidth()
        .background(backgroundColor)
        .statusBarsPadding()


    ){
        if (tx.value.isNotBlank()) {
            Box(modifier = Modifier
                .height(headerHeight)
                .fillMaxWidth()
                .padding(horizontal = Sizes.actionHorizontalPadding)){
                Text(
                    text = tx.value,
                    style = if (showBackIcon) Typography.displayMedium else Typography.displayLarge,
                    modifier = Modifier.align(if (showBackIcon || centerTitle) Alignment.Center else Alignment.CenterStart),
                    color = headerColor
                )
            }
            HorizontalDivider(
                Modifier
                    .fillMaxWidth()
                    .height(0.2.dp)
                    .align(Alignment.BottomCenter), color = Color(0xFFD6D6D6)
            )
        }
        content()
        if (showBackIcon) {
            var lastClickTime by remember {
                mutableLongStateOf(0L)
            }
            Icon(painter = painterResource(id = R.drawable.img_back),
                contentDescription = null,
                tint = headerColor,
                modifier = Modifier
                    .padding(start = Sizes.actionHorizontalPadding)
                    .align(
                        Alignment.CenterStart
                    )
                    .size(24.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            val currentTime = System.currentTimeMillis()
                            if (currentTime - lastClickTime >= 2000) {
                                lastClickTime = currentTime
                                onBackPressed()
                            }
                        }
                    }
            )
        }

    }
}
