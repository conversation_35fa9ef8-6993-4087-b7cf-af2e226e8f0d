package org.aihealth.ineck.view.custom

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.Shapes
import org.aihealth.ineck.util.phoneFilter
import kotlin.math.min

@Composable
fun AIHTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    keyboardType: KeyboardType = KeyboardType.Text,
    singleLine: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    maxLength: Int = Int.MAX_VALUE,
    placeholder: String = "",
    morePlaceholder: @Composable BoxScope.() -> Unit = {},
    backgroundColor: Color = Color(0XFFF0F0F0),
    shape: Shape = RoundedCornerShape(12.dp),
    enabled: Boolean = true
) {
    var passwordVisible by remember {
        mutableStateOf(false)
    }
    Box(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .height(40.dp)

            .background(backgroundColor, shape)
            .padding(horizontal = 12.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        BasicTextField(
            value = value,
            onValueChange = { s ->
                when (keyboardType) {
                    KeyboardType.Number -> {
                        onValueChange(s.filter { it.isDigit() }.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                    KeyboardType.Phone -> {
                        onValueChange(s.filter { it.isDigit() }
                            .let { if (it.length > 11) it.substring(0, 11) else it })
                    }
                    KeyboardType.Password -> {
                        val length = if (maxLength == Int.MAX_VALUE) 20 else maxLength
                        onValueChange(s.filter { it.code <= 127 }
                            .let { if (it.length > length) it.substring(0, length) else it })
                    }
                    KeyboardType.Email -> {
                        val length = if (maxLength == Int.MAX_VALUE) 128 else maxLength
                        onValueChange(s.filter { it.isLetterOrDigit() || it == '@' || it == '.' }
                            .let { if (it.length > length) it.substring(0, length) else it })
                    }
                    KeyboardType.Ascii -> {
                        onValueChange(s.filter { it.code <= 127 }.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                    else -> {
                        onValueChange(s.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                }
            },
            decorationBox = {
                if (value.isEmpty() && placeholder.isNotEmpty()) {
                    Text(text = placeholder, fontSize = 14.sp, color = Color(0XFF838383))
                }
                Box {
                    if (keyboardType == KeyboardType.Password && value.isNotEmpty()) {
                        Image(
                            painter = painterResource(id = if (passwordVisible) R.drawable.img_password_show else R.drawable.img_password_hide),
                            contentDescription = null,
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .padding(end = 4.dp)
                                .size(16.dp)
                                .pointerInput(Unit) {
                                    detectTapGestures {
                                        passwordVisible = !passwordVisible
                                    }
                                }
                        )
                    }
                    morePlaceholder()
                    it()
                }

            },
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
            visualTransformation = if (keyboardType == KeyboardType.Phone) {
                VisualTransformation {
                    phoneFilter(it)
                }
            } else if (keyboardType == KeyboardType.Password && !passwordVisible) {
                PasswordVisualTransformation()
            } else {
                VisualTransformation.None
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = singleLine,
            maxLines = maxLines,
            enabled = enabled,

            )
    }

}

@Composable
fun AIHPasswordTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    keyboardType: KeyboardType = KeyboardType.Text,
    singleLine: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    maxLength: Int = Int.MAX_VALUE,
    //hint
    placeholder: String = "",
    backgroundColor: Color = Color(0XFFF0F0F0),
    shape: Shape = RoundedCornerShape(10.dp),
    enabled: Boolean = true,
    ) {
    var passwordVisible by remember {
        mutableStateOf(false)
    }
    var isDropdownVisible by remember { mutableStateOf(false) }
    Column(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .height(40.dp)
            .background(backgroundColor, shape)
            .padding(horizontal = 12.dp),
        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.Center
    ) {
        BasicTextField(
            value = value,
            onValueChange = { s ->
                when (keyboardType) {
                    KeyboardType.Number -> {
                        onValueChange(s.filter { it.isDigit() }.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                    KeyboardType.Phone -> {
                        onValueChange(s.filter { it.isDigit() }
                            .let { if (it.length > 11) it.substring(0, 11) else it })
                    }
                    KeyboardType.Password -> {
                        val length = if (maxLength == Int.MAX_VALUE) 20 else maxLength
                        onValueChange(s.filter { it.code <= 127 }
                            .let { if (it.length > length) it.substring(0, length) else it })
                    }
                    KeyboardType.Email -> {
                        val length = if (maxLength == Int.MAX_VALUE) 128 else maxLength
                        onValueChange(s.filter { it.isLetterOrDigit() || it == '@' || it == '.' }
                            .let { if (it.length > length) it.substring(0, length) else it })
                    }
                    KeyboardType.Ascii -> {
                        onValueChange(s.filter { it.code <= 127 }.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                    else -> {
                        onValueChange(s.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                }
            },
            decorationBox = {
                if (value.isEmpty() && placeholder.isNotEmpty()) {
                    Text(text = placeholder, fontSize = 14.sp, color = Color(0XFF838383))
                }
                Box {
                    if (keyboardType == KeyboardType.Password) {
                        if (value.isEmpty()) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_msg),
                                contentDescription = null,
                                modifier = Modifier
                                    .align(Alignment.CenterEnd)
                                    .padding(end = 4.dp)
                                    .size(16.dp)
                                    .pointerInput(Unit) {
                                        detectTapGestures {
                                            isDropdownVisible = true
                                        }
                                    }
                            )
                        } else {
                            Image(
                                painter = painterResource(id = if (passwordVisible) R.drawable.img_password_show else R.drawable.img_password_hide),
                                contentDescription = null,
                                modifier = Modifier
                                    .align(Alignment.CenterEnd)
                                    .padding(end = 4.dp)
                                    .size(16.dp)
                                    .pointerInput(Unit) {
                                        detectTapGestures {
                                            passwordVisible = !passwordVisible
                                        }
                                    }
                            )
                        }
                    }
                    it()
                }

            },
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
            visualTransformation = if (keyboardType == KeyboardType.Phone) {
                VisualTransformation {
                    phoneFilter(it)
                }
            } else if (keyboardType == KeyboardType.Password && !passwordVisible) {
                PasswordVisualTransformation()
            } else {
                VisualTransformation.None
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = singleLine,
            maxLines = maxLines,
            enabled = enabled,

            )
        DropdownMenu(
            expanded = isDropdownVisible,
            onDismissRequest = { isDropdownVisible = false },
            modifier = Modifier
                .background(color = Color(0x99000000), shape = RoundedCornerShape(size = 8.dp))
                .padding(start = 10.dp, top = 10.dp, end = 10.dp, bottom = 10.dp)
        ) {
            Column(
                modifier = Modifier.padding(8.dp)
            ) {
                Text(
                    text = stringResource(R.string.password_requirements),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
        }
    }

}

@Composable
fun AIHOutlinedTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    keyboardType: KeyboardType = KeyboardType.Text,
    singleLine: Boolean = false,
    maxLines: Int = Int.MAX_VALUE,
    maxLength: Int = Int.MAX_VALUE,
    placeholder: String = "",
    backgroundColor: Color = Color.Transparent,
    borderColor: Color = Color(0XFF888888),
    shape: Shape = Shapes.large,
    enabled: Boolean = true

) {
    var passwordVisible by remember {
        mutableStateOf(false)
    }
    Box(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .height(40.dp)

            .background(backgroundColor, shape)
            .border(1.dp, borderColor, shape)
            .padding(horizontal = 12.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        BasicTextField(
            value = value,
            onValueChange = { s ->
                when (keyboardType) {
                    KeyboardType.Number -> {
                        onValueChange(s.filter { it.isDigit() }.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                    KeyboardType.Phone -> {
                        onValueChange(s.filter { it.isDigit() }
                            .let { if (it.length > 11) it.substring(0, 11) else it })
                    }
                    KeyboardType.Password -> {
                        val length = if (maxLength == Int.MAX_VALUE) 20 else maxLength
                        onValueChange(s.filter { it.code <= 127 }
                            .let { if (it.length > length) it.substring(0, length) else it })
                    }
                    KeyboardType.Email -> {
                        val length = if (maxLength == Int.MAX_VALUE) 128 else maxLength
                        onValueChange(s.filter { it.isLetterOrDigit() || it == '@' || it == '.' }
                            .let { if (it.length > length) it.substring(0, length) else it })
                    }
                    KeyboardType.Ascii -> {
                        onValueChange(s.filter { it.code <= 127 }.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                    else -> {
                        onValueChange(s.let {
                            if (maxLength == Int.MAX_VALUE) it else it.substring(
                                0,
                                min(it.length, maxLength)
                            )
                        })
                    }
                }
            },
            decorationBox = {
                if (value.isEmpty()) {
                    Text(text = placeholder, fontSize = 14.sp, color = Color(0xFFE8E8E9))
                }
                Box {
                    if (keyboardType == KeyboardType.Password) {
                        Image(
                            painter = painterResource(id = if (passwordVisible) R.drawable.img_password_show else R.drawable.img_password_hide),
                            contentDescription = null,
                            modifier = Modifier
                                .align(Alignment.CenterEnd)
                                .padding(end = 4.dp)
                                .size(16.dp)
                                .pointerInput(Unit) {
                                    detectTapGestures {
                                        passwordVisible = !passwordVisible
                                    }
                                }
                        )
                    }
                    it()
                }
            },
            keyboardOptions = KeyboardOptions(keyboardType = keyboardType),
            visualTransformation = if (keyboardType == KeyboardType.Phone) {
                VisualTransformation {
                    phoneFilter(it)
                }
            } else if (keyboardType == KeyboardType.Password && !passwordVisible) {
                PasswordVisualTransformation()
            } else {
                VisualTransformation.None
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = singleLine,
            maxLines = maxLines,
            enabled = enabled
        )
    }

}