package org.aihealth.ineck.view.screen

import android.content.Context
import android.view.ViewTreeObserver
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.mediapipe.tasks.vision.core.RunningMode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.mediapipe.PoseLandmarkerHelper
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDialog
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.viewmodel.AibackVCGuideViewModel
import org.aihealth.ineck.viewmodel.DetectedResult
import org.aihealth.ineck.viewmodel.dao.AngleWithTimestamp
import org.aihealth.ineck.viewmodel.dao.VCGuideProcessState
import org.aihealth.ineck.viewmodel.dao.compareEnqueue
import org.aihealth.ineck.viewmodel.dao.getTimestampNow
import org.aihealth.ineck.viewmodel.dao.verifyDiff
import java.util.Locale
import java.util.concurrent.Executors
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.acos
import kotlin.math.sqrt


@Composable
fun AiBackVCGuideScreen(
    visible: Boolean,
    viewModel: AibackVCGuideViewModel,
    context: Context = LocalContext.current,
    lifecycleOwner: LifecycleOwner = LocalContext.current as LifecycleOwner,
    onDismissEvent: (DetectedResult) -> Unit,
    requestCameraPermissionEvent: () -> Unit
) {
    AnimatedVisibility(visible = visible) {
        AIHDialog(
            onDismissRequest = { onDismissEvent(DetectedResult.None) }
        ) {
            /* 协程域 */
            val coroutineScope = rememberCoroutineScope()

            /** 开始监测时间戳 - 从有效数据开始计算 */
            val startTimestampForEffectDetecting =
                viewModel.startTimestampForEffectDetecting.collectAsState()

            /** 垂直检测完成状态 */
            val finishVerticalDetectedState =
                viewModel.finishedVerticalDetectedState.collectAsState()

            /** 当前检测步骤状态 */
            val detectProcessState = viewModel.currentVCGuideProcessState.collectAsState()

            /** 重力矢量状态 */
            val gravityState = viewModel.verticalGravity.collectAsState()

            /** 进入重力校准状态 */
            val gravityMeasureProcess = remember {
                derivedStateOf { gravityState.value > 9.2f }
            }
            /* 是否捕捉到背部 */
            val capturedPose = viewModel.capturedPose.collectAsState()
            /* 是否处于平视状态（不晃动） */
            val verticalPose = viewModel.verticalPose.collectAsState()

            /* 当前静音状态 */
            val isMuteState = viewModel.isMuteState.collectAsState()

            /* 全检测步骤完成状态 */
            val totalState = viewModel.totalState.collectAsState()

            /** 检测结果状态 */
            val detectResultState = viewModel.detectedResult.collectAsState()

            /* 摄像头相关 */
            val cameraProvider = ProcessCameraProvider.getInstance(context).get()

            /* 图像分析 */
            val poseLandmarkerListener = object : PoseLandmarkerHelper.LandmarkerListener {
                override fun onError(error: String, errorCode: Int) {
                    viewModel.capturedPose.update { false }
                    viewModel.verticalPose.update { false }
                    viewModel.totalState.update { false }
                    viewModel.bodyTiltAngle.update { 0f }
                    viewModel.startTimestampForEffectDetecting.update { 0L }
                    viewModel.writeDetectedResult(DetectedResult.DetectedFailure)

                }

                override fun onResults(resultBundle: PoseLandmarkerHelper.ResultBundle) {
//                            LogUtil.d("width: ${resultBundle.inputImageWidth} | height: ${resultBundle.inputImageWidth}")
                    resultBundle.results.first().let { poseLandmarkerResult ->
                        for (landmark in poseLandmarkerResult.worldLandmarks()) {
                            val currentTime = System.currentTimeMillis()
                            if (landmark.get(11).visibility().get() < 0.5f
                                || landmark.get(12).visibility().get() < 0.5f
                                || landmark.get(23).visibility().get() < 0.5f
                                || landmark.get(24).visibility().get() < 0.5f
                            ) {
                                viewModel.capturedPose.update { false }
                                viewModel.verticalPose.update { false }
                                viewModel.bodyTiltAngle.update { 0f }
                                viewModel.startTimestampForEffectDetecting.update { 0L }
                            } else {


                                // 人体存在
                                viewModel.capturedPose.update { true }

                                val x1 = landmark.get(11).x() / 2 + landmark.get(12).x() / 2
                                val y1 = landmark.get(11).y() / 2 + landmark.get(12).y() / 2
                                val z1 = landmark.get(11).z() / 2 + landmark.get(12).z() / 2
                                val x2 = landmark.get(23).x() / 2 + landmark.get(24).x() / 2
                                val y2 = landmark.get(23).y() / 2 + landmark.get(24).y() / 2
                                val z2 = landmark.get(23).z() / 2 + landmark.get(24).z() / 2
                                val abX = x2 - x1
                                val abY = y2 - y1
                                val abZ = z2 - z1
                                val cX = 0.0
                                val cY = 1.0
                                val cZ = 0.0
                                // 计算向量AB和向量C的点积
                                val dotProduct = abX * cX + abY * cY + abZ * cZ

                                // 计算向量AB和向量C的模长
                                val abMagnitude = sqrt(abX * abX + abY * abY + abZ * abZ)
                                val cMagnitude = sqrt(cX * cX + cY * cY + cZ * cZ)

                                // 计算夹角的余弦值
                                val cosTheta = dotProduct / (abMagnitude * cMagnitude)

                                val angle = (acos(cosTheta) * (180.0 / PI)).toFloat()
                                viewModel.bodyTiltAngle.update { angle }
                                if (detectProcessState.value == VCGuideProcessState.DetectingPage) {
                                    /** 检测中 */
                                    viewModel.angleWithTimestampQueueForDetecting.value.compareEnqueue(
                                        AngleWithTimestamp(angle, getTimestampNow())
                                    )
                                    /** 设备未垂直情况， 检测开始时间戳被初始化 */
                                    if (!gravityMeasureProcess.value) {
                                        viewModel.startTimestampForEffectDetecting.update { getTimestampNow() }
                                    }
                                    /* 若校准时间超过 5sec 以上，在当前拥有足够样本容量的情况下，判断队列差是否小于10度 */
                                    if (getTimestampNow() - startTimestampForEffectDetecting.value > 5000L &&
                                        viewModel.angleWithTimestampQueueForDetecting.value.verifyDiff(
                                            10f
                                        )
                                    ) {
                                        coroutineScope.launch {
                                            viewModel.totalState.update { true }
                                        }
                                        coroutineScope.launch {
                                            /* 队列中最近一次的取值角度作为矫正角度 */
                                            val lastAngle =
                                                viewModel.angleWithTimestampQueueForDetecting.value.last().angle
                                            delay(1000)
                                            viewModel.writeDetectedResult(
                                                DetectedResult.DetectedSuccess(
                                                    reviseValue = lastAngle
                                                )
                                            )
                                        }
                                    }
                                    /* 直立状态判断 */
                                    if (
                                        abs(viewModel.bodyTiltAngle.value) < 20f
                                    ) {
                                        /* 在范围内，确认为 平视状态 */
                                        viewModel.verticalPose.update { true }
                                    } else {
                                        viewModel.verticalPose.update { false }
                                        /* 检测开始时间戳被初始化 */
                                        viewModel.startTimestampForEffectDetecting.update { getTimestampNow() }
                                    }

                                }
                            }

                        }
                    }
                }


            }

            ConstraintLayout(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = Color.Transparent)
            ) {
                val (contentCard, bottomButton) = createRefs()
                val centerGuideline = createGuidelineFromTop(.5f)
                Card(
                    modifier = Modifier
                        .fillMaxWidth(.9f)
                        .height(if (currentLocale == Locale.CHINESE) 485.dp else 516.dp)
                        .constrainAs(contentCard) {
                            start.linkTo(parent.start, 16.dp)
                            top.linkTo(centerGuideline)
                            end.linkTo(parent.end, 16.dp)
                            bottom.linkTo(centerGuideline)
                        },
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF4F4F4),
                    ),
                    shape = RoundedCornerShape(10.dp),
                ) {
                    Box {
                        /* 包含内容有： 语言播报开关、文本提示框、视频检测内容框，Button按钮选项框 */
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .animateContentSize(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            TextContentBlock(
                                viewModel = viewModel,
                                currentVCGuideProcessPageState = detectProcessState,
                                gravityMeasureProcess = finishVerticalDetectedState,
                                capturePose = capturedPose,
                                verticalPose = verticalPose,
                                finishVerticalDetectedState = finishVerticalDetectedState,
                                totalState = totalState,
                            )
                            VCBlock(
                                viewModel = viewModel,
                                currentVCGuideProcessPageState = detectProcessState,
                                gravityMeasureProcess = gravityMeasureProcess,
                                capturePose = capturedPose,
                                verticalPose = verticalPose,
                                lifecycleOwner = lifecycleOwner,
                                cameraProvider = cameraProvider,
                                listener = poseLandmarkerListener,
                            )
                            ButtonGroupBlock(
                                currentVCGuideProcessPageState = detectProcessState,
                                onIgnoreEvent = {
                                    /* 直接跳转至校准中页 */
//                                    viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                                    /* 无事发生 */
                                    onDismissEvent(DetectedResult.None)
                                },
                                onNextEvent = {
                                    viewModel.changeGuideDetectProcessState(VCGuideProcessState.PreviousDetectPage)
                                }
                            )
                        }
                        /* 语音播报开关 */
                        if (detectProcessState.value != VCGuideProcessState.PreviousGuidePage) {
                            MuteButton(
                                isMuteState = isMuteState,
                                changeMuteState = { viewModel.changeCurrentMuteState(!isMuteState.value) },
                                modifier = Modifier
                                    .padding(16.dp)
                                    .size(30.dp)
                                    .align(Alignment.TopEnd)
                            )
                        }
                    }
                }

                /**
                 * 退出按钮
                 */
                if (detectProcessState.value is VCGuideProcessState.PreviousGuidePage) {
                    // UNDO
                } else {
                    IconButton(
                        modifier = Modifier
                            .constrainAs(bottomButton) {
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                                top.linkTo(contentCard.bottom, 34.dp)
                            }
                            .size(22.dp),
                        onClick = { onDismissEvent(DetectedResult.None) }
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.img_dialog_close),
                            contentDescription = stringResource(id = R.string.close_dialog),
                            tint = Color.White,
                            modifier = Modifier.background(Color.Transparent)
                        )

                    }
                }


            }
            /* 监听结果返回状态 */
            LaunchedEffect(detectResultState.value) {
                if (detectResultState.value != DetectedResult.None) {
                    /* 向主页返回引导检测结果 */
                    onDismissEvent(detectResultState.value)
                }
            }
            /* 监听垂直校准结束变化 */
            LaunchedEffect(finishVerticalDetectedState.value) {
                if (finishVerticalDetectedState.value) {
                    coroutineScope.launch {
                        delay(1500)
                        viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                    }
                }
            }
            LaunchedEffect(detectProcessState.value) {
                if (detectProcessState.value is VCGuideProcessState.DetectingPage) {
                    /* 校准中 过程超时判断 - 15 sec */
                    repeat(30) { count ->
                        delay(500)
                        if (count >= 29) viewModel.writeDetectedResult(DetectedResult.DetectedTimeout)
                    }
                } else if (detectProcessState.value is VCGuideProcessState.PreviousDetectPage) {
                    /* 请求前置摄像头权限 */
                    requestCameraPermissionEvent()
                }
            }
            DisposableEffect(lifecycleOwner) {
                val observer = LifecycleEventObserver { _, event ->
                    if (event == Lifecycle.Event.ON_START) {
                        viewModel.clearAllState()
                    }
                }
                lifecycleOwner.lifecycle.addObserver(observer)
                onDispose {
                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }
        }
    }
}

/**
 *  引导框中文本信息框（标题、引导提示内容等）
 *  @param  viewModel   视频检测引导视图模型
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  gravityMeasureProcess   重力矢量垂直状态
 *  @param  finishVerticalDetectedState     是否已完成设备垂直校准状态
 */
@Composable
private fun TextContentBlock(
    viewModel: AibackVCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    gravityMeasureProcess: State<Boolean>,
    capturePose: State<Boolean>,
    verticalPose: State<Boolean>,
    finishVerticalDetectedState: State<Boolean>,
    totalState: State<Boolean>
) {
    Box(
        modifier = Modifier.fillMaxWidth(.8f)
    ) {
/* 文本显示内容 */
        when (currentVCGuideProcessPageState.value) {
            /* 检测中 */
            VCGuideProcessState.DetectingPage -> {
                TextContentForDetectingPage(
                    viewModel = viewModel,
                    currentVCGuideProcessPageState = currentVCGuideProcessPageState,
                    gravityMeasureProcess = gravityMeasureProcess,
                    totalState = totalState,
                    capturePose = capturePose,
                    verticalPose = verticalPose
                )
            }
            /* 检测前垂直校准 */
            VCGuideProcessState.PreviousDetectPage -> {
                TextContentForPreviousDetectPage(
                    currentVCGuideProcessPageState = currentVCGuideProcessPageState,
                    finishVerticalDetectedState = finishVerticalDetectedState,
                    gravityMeasureProcess = gravityMeasureProcess,
                    verticalPose = verticalPose,
                    viewModel = viewModel
                )
            }
            /* 检测前引导提示 */
            VCGuideProcessState.PreviousGuidePage -> {
                TextContentForPreviousGuidePage(currentVCGuideProcessPageState)
            }
        }
    }
}

/**
 *  检测中步骤提示内容
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  gravityMeasureProcess   重力矢量垂直状态
 *  @param  viewModel   视频检测引导视图模型
 */
@Composable
private fun TextContentForDetectingPage(
    viewModel: AibackVCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    gravityMeasureProcess: State<Boolean>,
    capturePose: State<Boolean>,
    verticalPose: State<Boolean>,
    totalState: State<Boolean>,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    /** 检测引导完成动画 */
    val finishComposition by rememberLottieComposition(spec = LottieCompositionSpec.RawRes(R.raw.vertical_calibration_finish_animation))
    AnimatedVisibility(
        visible = currentVCGuideProcessPageState.value is VCGuideProcessState.DetectingPage,
        enter = slideInHorizontally { fullWidth -> -fullWidth },
        exit = slideOutHorizontally { fullWidth -> 2 * fullWidth }
    ) {
        Box(
            modifier = Modifier
                .height(180.dp)
                .background(color = Color.Transparent)
                .animateContentSize(),
            contentAlignment = Alignment.Center
        ) {
            Crossfade(targetState = totalState.value, label = "") { state ->
                if (state) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        /* 校准完成 */
                        LottieAnimation(
                            composition = finishComposition,
                            iterations = 1,
                            modifier = Modifier.size(60.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = stringResource(id = R.string.passed),
                            style = TextStyle(
                                fontSize = 22.sp,
                                fontWeight = FontWeight.Normal,
                                color = Color(0xFF333333),
                            ),
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                } else {
                    Text(
                        text = if (!gravityMeasureProcess.value) {
                            stringResource(id = R.string.detect_guide_keep_phone_upright)
                        } else if (!capturePose.value) {
                            stringResource(id = R.string.detect_guide_please_show_your_body)
                        } else if (!verticalPose.value) {
                            stringResource(id = R.string.detect_guide_dont_shake_body)
                        } else {
                            stringResource(id = R.string.precautions_of_detect_guide_line_3)
                        },
                        style = TextStyle(
                            fontSize = if (currentLocale == Locale.CHINESE) 22.sp else 20.sp,
                            fontWeight = FontWeight.W400,
                            color = if (gravityMeasureProcess.value && capturePose.value && verticalPose.value) Color(
                                0xFF333333
                            ) else Color(0xFFFC7349),
                        ),
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .animateContentSize()
                    )
                }
            }
        }
        /* 启用检测计时 */
        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                if (event == Lifecycle.Event.ON_START) {
                    getTimestampNow().let { startTimestamp ->
                        viewModel.apply {
                            startTimestampForEffectDetecting.update { startTimestamp }
                        }
                    }
                } else if (event == Lifecycle.Event.ON_STOP) {
                    viewModel.apply {
                        startTimestampForEffectDetecting.update { 0L }
                    }
                }
            }
            lifecycleOwner.lifecycle.addObserver(observer)
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
    }
}

/**
 *  检测垂直检测步骤提示内容
 *  @param  viewModel   视频检测引导视图模型
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  finishVerticalDetectedState     当前垂直检测是否完成状态
 *  @param  gravityMeasureProcess   重力矢量垂直状态
 *  @param  transitionCountdown     过渡倒计时，默认为 2 sec
 */
@Composable
private fun TextContentForPreviousDetectPage(
    viewModel: AibackVCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    finishVerticalDetectedState: State<Boolean>,
    gravityMeasureProcess: State<Boolean>,
    verticalPose: State<Boolean>,
    transitionCountdown: Int = 2,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val coroutineScope = rememberCoroutineScope()
    /* 标题从”请将手机置于脸部正前方“ 到 ”请保持手机固定且竖直状态“ 延时 2sec*/
    val count = remember { mutableIntStateOf(transitionCountdown) }
    AnimatedVisibility(
        visible = currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousDetectPage,
        enter = slideInHorizontally { fullWidth -> -fullWidth },
        exit = slideOutHorizontally { fullWidth -> 2 * fullWidth }
    ) {
        /** 垂直校准完成动画 */
        val finishComposition by rememberLottieComposition(spec = LottieCompositionSpec.RawRes(R.raw.vertical_calibration_finish_animation))
        Box(
            modifier = Modifier
                .height(180.dp)
                .background(color = Color.Transparent)
        ) {
            if (count.intValue != 0) {
                Text(
                    text = stringResource(id = R.string.detect_guide_keep_phone_upright),
                    style = TextStyle(
                        fontSize = 22.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFF333333),
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                )
            } else {
                Crossfade(
                    targetState = finishVerticalDetectedState.value,
                    modifier = Modifier.align(Alignment.Center), label = ""
                ) { isVertical ->
                    if (isVertical) {
                        /* 手机接近垂直状态 */
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 20.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            /* 校准完成 */
                            LottieAnimation(
                                composition = finishComposition,
                                iterations = 1,
                                modifier = Modifier.size(60.dp)
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = stringResource(id = R.string.passed),
                                style = TextStyle(
                                    fontSize = 22.sp,
                                    fontWeight = FontWeight.Normal,
                                    color = Color(0xFF333333),
                                ),
                                textAlign = TextAlign.Center,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }

                    } else {
                        /* 手机未接近垂直状态 */
                        Text(
                            text = stringResource(id = R.string.detect_guide_keep_phone_three_time),
                            style = TextStyle(
                                fontSize = 22.sp,
                                fontWeight = FontWeight.W400,
                                color = if (gravityMeasureProcess.value) Color(0xFF6181E9) else Color(
                                    0xFFFC7349
                                ),
                            ),
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.Center)
                                .animateContentSize(),
                            maxLines = 4,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
        /* 生命周期开始与结束时针对过渡倒计时进行初始化逻辑与重置逻辑 */
        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                if (event == Lifecycle.Event.ON_START) {
                    coroutineScope.launch {
                        repeat(transitionCountdown) {
                            delay(1000)
                            count.intValue = count.intValue - 1
                        }
                        /* 重力传感器开始监听 */
                        viewModel.gravityStartListening()
                    }
                } else if (event == Lifecycle.Event.ON_STOP) {
                    count.intValue = transitionCountdown
                    /* 重力传感器停止监听 */
                    viewModel.gravityStopListening()
                    coroutineScope.cancel()
                }
            }
            lifecycleOwner.lifecycle.addObserver(observer)
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
    }
}

/**
 *  检测引导步骤提示内容
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 */
@Composable
private fun TextContentForPreviousGuidePage(
    currentVCGuideProcessPageState: State<VCGuideProcessState>
) {
    AnimatedVisibility(
        visible = currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousGuidePage,
        enter = slideInHorizontally { fullWidth -> -fullWidth },
        exit = slideOutHorizontally { fullWidth -> 2 * fullWidth }
    ) {
        Column(
            modifier = Modifier.padding(vertical = 24.dp),
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Text(
                text = stringResource(id = R.string.precautions_of_detect_guide_title),
                fontWeight = FontWeight.W400,
                color = Color(0xFF333333),
                fontSize = 22.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.height(10.dp))
            /* 第一点 */
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 5.dp)
            ) {
                /* 序号 */
                Box(
                    modifier = Modifier
                        .size(width = 14.dp, height = 20.dp)
                        .background(color = Color.Transparent)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 13.dp, height = 4.dp)
                            .background(
                                color = Color(0xFFBCCCFF),
                                shape = RoundedCornerShape(size = 2.dp)
                            )
                            .align(Alignment.BottomCenter),
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_serial_number_1),
                        contentDescription = "",
                        modifier = Modifier
                            .size(13.dp)
                            .align(Alignment.Center)
                    )
                }
                /* 间隔 */
                Spacer(modifier = Modifier.width(9.dp))
                /* 文本内容 */
                Text(
                    text = stringResource(id = R.string.back_guide_1),
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start
                )
            }
            /* 第二点 */
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 5.dp)
            ) {
                /* 序号 */
                Box(
                    modifier = Modifier
                        .size(width = 14.dp, height = 20.dp)
                        .background(color = Color.Transparent)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 13.dp, height = 4.dp)
                            .background(
                                color = Color(0xFFBCCCFF),
                                shape = RoundedCornerShape(size = 2.dp)
                            )
                            .align(Alignment.BottomCenter),
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_serial_number_2),
                        contentDescription = "",
                        modifier = Modifier
                            .size(13.dp)
                            .align(Alignment.Center)
                    )
                }
                /* 间隔 */
                Spacer(modifier = Modifier.width(9.dp))
                /* 文本内容 */
                Text(
                    text = stringResource(id = R.string.back_guide_2),
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start
                )
            }
            /* 第三点 */
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 5.dp)
            ) {
                /* 序号 */
                Box(
                    modifier = Modifier
                        .size(width = 14.dp, height = 20.dp)
                        .background(color = Color.Transparent)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 13.dp, height = 4.dp)
                            .background(
                                color = Color(0xFFBCCCFF),
                                shape = RoundedCornerShape(size = 2.dp)
                            )
                            .align(Alignment.BottomCenter),
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_serial_number_3),
                        contentDescription = "",
                        modifier = Modifier
                            .size(13.dp)
                            .align(Alignment.Center)
                    )
                }
                /* 间隔 */
                Spacer(modifier = Modifier.width(9.dp))
                /* 文本内容 */
                Text(
                    text = stringResource(id = R.string.back_guide_3),
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start
                )
            }
        }
    }
}

/**
 *  视频检测框
 *  @param  viewModel   视频检测引导视图模型
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  gravityMeasureProcess   是否已进入垂直校准过程标识状态
 *  @param  lifecycleOwner  生命周期拥有者
 *  @param  cameraProvider  相机类提供者
 *  @param  imageAnalyzer   图像分析对象
 */
@Composable
private fun VCBlock(
    viewModel: AibackVCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    gravityMeasureProcess: State<Boolean>,
    capturePose: State<Boolean>,
    verticalPose: State<Boolean>,
    lifecycleOwner: LifecycleOwner,
    cameraProvider: ProcessCameraProvider,
    listener: PoseLandmarkerHelper.LandmarkerListener,
) {
    /** 上下文 */
    val context = LocalContext.current

    val backgroundExecutor = remember { Executors.newSingleThreadExecutor() }
    val poseLandmarkerHelper = remember {
        PoseLandmarkerHelper(
            context = context,
            runningMode = RunningMode.LIVE_STREAM,
            poseLandmarkerHelperListener = listener
        )
    }
    val frameBlockColor: Color by animateColorAsState(
        targetValue = if ((currentVCGuideProcessPageState.value != VCGuideProcessState.DetectingPage)
            || (gravityMeasureProcess.value && capturePose.value && verticalPose.value && currentVCGuideProcessPageState.value is VCGuideProcessState.DetectingPage)
        )
            Color(0xFFDDE4f1)
        else Color(0xFFFC7349), label = ""
    )
    Box(
        modifier = Modifier
            .size(220.dp)
            .background(color = Color.Transparent)
    ) {
        if (currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousGuidePage) {
            /* 示例人像 */
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
                shape = RoundedCornerShape(8.dp)
            ) {
                Image(
                    painter = painterResource(id = R.drawable.img_detect_guide_example_aiback),
                    contentDescription = stringResource(id = R.string.text_portrait_example),
                    contentScale = ContentScale.Fit,
                )
            }
        } else {
            val circleProgress = remember { Animatable(0f) }
            /* 外围检测框 */
//            Icon(
//                painter = painterResource(id = R.drawable.img_vc_detect_block),
//                contentDescription = if (currentLocale == Locale.CHINESE) "检测框" else "Detection frame",
//                modifier = Modifier
//                    .size(300.dp)
//                    .align(Alignment.Center),
//                tint = frameBlockColor
//            )
            /* 检测框周边圆圈进度条 */
            CircularProgressIndicator(
                progress = { circleProgress.value },
                modifier = Modifier
                    .size(300.dp)
                    .clip(CircleShape)
                    .background(color = Color(0x80CCCCCC))
                    .align(Alignment.Center),
                color = frameBlockColor,
                strokeWidth = 10.dp,
            )
            /* 实时人像检测框 */
            Surface(
                modifier = Modifier
                    .size(280.dp)
                    .align(Alignment.Center),
                shape = RoundedCornerShape(8.dp)
            ) {
                AndroidView(
                    factory = { context ->
                        val previewView = PreviewView(context)
                        val preview = Preview.Builder()
                            .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                            .build()
                        previewView.viewTreeObserver.addOnGlobalLayoutListener(object :
                            ViewTreeObserver.OnGlobalLayoutListener {
                            override fun onGlobalLayout() {
                                preview.setTargetRotation(previewView.display.rotation)
                                previewView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                            }
                        })
                        /* 摄像头选择 */
                        val selector = CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                            .build()

                        /* 自定义图像分析对象 */
                        val backAnalyzer =
                            ImageAnalysis.Builder()
                                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                                .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888)
                                .build().also {
                                    it.setAnalyzer(backgroundExecutor) { image ->
                                        poseLandmarkerHelper.detectLiveStream(
                                            imageProxy = image,
                                            isFrontCamera = true
                                        )
                                    }
                                }

                        try {
                            cameraProvider.unbindAll()
                            cameraProvider.bindToLifecycle(
                                lifecycleOwner, selector, preview, backAnalyzer
                            )
                            preview.setSurfaceProvider(previewView.surfaceProvider)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        previewView
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
            LaunchedEffect(gravityMeasureProcess.value) {
                if (gravityMeasureProcess.value) {
                    launch(Dispatchers.IO) {
                        viewModel.verifyVerticalDetection(4, 500)
                    }
                    launch(Dispatchers.Main) {
                        (1..4).forEach { i ->
                            circleProgress.animateTo(
                                i * (1f / 4f),
                                animationSpec = tween(500, 0, LinearEasing)
                            )
                        }
                    }
                } else {
                    cancel()
                    launch(Dispatchers.Main) { circleProgress.animateTo(0f) }
                    viewModel.finishedVerticalDetectedState.update { false }
                }
            }
        }
    }
}

/**
 *  Button按键组
 *  @param  currentVCGuideProcessPageState  当前检测引导页进程状态
 *  @param  onIgnoreEvent   忽视引导提示按键触发事件
 *  @param  onNextEvent     下一步按键触发事件
 */
@Composable
private fun ButtonGroupBlock(
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    onIgnoreEvent: () -> Unit,
    onNextEvent: () -> Unit
) {
    if (currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousGuidePage) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 34.dp)
                .padding(end = 10.dp)
        ) {
            val (ignoreButton, nextButton) = createRefs()
            val verticalCenterGuideline = createGuidelineFromStart(.4f)
            AIHOutlinedButton(
                text = stringResource(id = R.string.ignore),
                shape = RoundedCornerShape(4.dp),
                onClick = onIgnoreEvent,
                backgroundColor = Color(0xFFEFEFEF),
                borderColor = Color(0xFFCCCCCC),
                modifier = Modifier
                    .height(34.dp)
                    .constrainAs(ignoreButton) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top, 20.dp)
                        end.linkTo(verticalCenterGuideline, 10.dp)
                        bottom.linkTo(parent.bottom, 20.dp)
                        width = Dimension.fillToConstraints
                    }
            )
            AIHButton(
                text = stringResource(id = R.string.i_know_and_go_next),
                shape = RoundedCornerShape(4.dp),
                onClick = onNextEvent,
                modifier = Modifier
                    .height(34.dp)
                    .constrainAs(nextButton) {
                        start.linkTo(verticalCenterGuideline, 10.dp)
                        top.linkTo(parent.top, 20.dp)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom, 20.dp)
                        width = Dimension.fillToConstraints
                    }
            )
        }
    } else {
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
        )
    }
}