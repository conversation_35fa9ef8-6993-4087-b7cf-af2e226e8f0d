package org.aihealth.ineck.view.screen.membershipcenter

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.improvement.ImprovementProgramsLoadState
import org.aihealth.ineck.util.Limit_Time
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.preview.MemberShipAgreementDialog
import org.aihealth.ineck.view.preview.MemberShipCenterContent
import org.aihealth.ineck.view.preview.MemberShipCenterHeaderNormal
import org.aihealth.ineck.view.preview.MemberShipCenterHeaderSpecial
import org.aihealth.ineck.view.preview.OrderListType
import org.aihealth.ineck.view.preview.VipBenefitObject
import org.aihealth.ineck.view.preview.VipBenefitsInMemberShipCenter
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardFailureContent
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardSuccessContentWithVipZhOnly
import org.aihealth.ineck.view.screen.exercise.UserExerciseCardLoadingContent
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.MemberShipCenterViewModel
import org.aihealth.ineck.viewmodel.user
import java.util.Locale


@Composable
fun MemberShipCenterScreen(
    mainViewModel: MainViewModel,
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }

    val memberShipCenterViewModel: MemberShipCenterViewModel = viewModel()
    LogUtil.i("MemberShipCenterScreen memberShipCenterViewModel:${memberShipCenterViewModel}")
    val improvementRecommendProgramDataState =
        mainViewModel.improvementScreen.improvementDataState.collectAsState()
//    LaunchedEffect(true) {
//        memberShipCenterViewModel.getClient()
//    }
    val vipBenefitsListWithSilver = mutableListOf<VipBenefitObject>().apply {
        add(VipBenefitObject(R.drawable.ic_practiced_first, activity.getString(R.string.the_new_lesson_is_practiced_first)))
        add(VipBenefitObject(R.drawable.ic_vip_select_class, activity.getString(R.string.vip_select_class)))
        add(VipBenefitObject(R.drawable.ic_24_hours_training, activity.getString(R.string._24_hours_training)))
        add(VipBenefitObject(R.drawable.ic_member_benefits, activity.getString(R.string.member_benefits)))
    }.toList()
    BasePageView(
        title = stringResource(id = R.string.membership_center),
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true,
        headerContent = {
            Text(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 4.dp)
                    .clickable {
                        startScreen(Screen.UseRedemptionCodeScreen.route, false)
                    },
                text = stringResource(id = R.string.redeem_code),
                style = TextStyle(
                    fontSize = fontSize14,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF2B56D7),
                )
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .padding(vertical = 20.dp)
                .padding(horizontal = 12.dp),
            verticalArrangement = Arrangement.Top,
        ) {
            if (currentLocale == Locale.CHINESE) {
                // 如果是会员 并且离到期还有3天以上
                if (user.vipStatus && user.vipActiveTime > Limit_Time && (user.membershipId != -1)) {
                    MemberShipCenterHeaderNormal(
                        user.photo,
                        user.name,
                        stringResource(id = R.string.membership_valid_until) + TimeUtil.timestampToDate(
                            user.vipEndDate,
                            "yyyy.MM.dd"
                        )
                    ) {
                        VipBenefitsInMemberShipCenter(
                            vipBenefitsList = vipBenefitsListWithSilver,
                            onClick = { (LogUtil.i("it")) },
                        )
                    }
                    Spacer(modifier = Modifier.size(5.dp))

                    LaunchedEffect(Unit) {
                        mainViewModel.improvementScreen.loadImprovementProgramsData(
                            loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                        )
                    }
                    when (improvementRecommendProgramDataState.value) {
                        is ImprovementProgramsLoadState.InitLoading -> {
                            /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                            repeat(3) { count ->
                                UserExerciseCardLoadingContent(count = count)
                            }
                        }

                        is ImprovementProgramsLoadState.Loading -> {
                            /* 非初次加载，直接从服务器发送请求 */
                            repeat(3) { count ->
                                UserExerciseCardLoadingContent(count = count)
                            }
                        }

                        is ImprovementProgramsLoadState.Success -> {
                            val data =
                                (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                            ImprovementProgramCardSuccessContentWithVipZhOnly(
                                programsData = data
                            )
                        }

                        is ImprovementProgramsLoadState.Failure -> {
                            LogUtil.d("ImprovementProgramsLoadState Failure")
                            ImprovementProgramCardFailureContent {
                                mainViewModel.improvementScreen.loadImprovementProgramsData(
                                    loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                )
                            }
                        }
                    }
                }
                // 如果是会员 并且离到期不到3天
                else if (user.vipStatus && user.vipActiveTime <= Limit_Time && (user.membershipId != -1)) {
                    MemberShipCenterHeaderSpecial(
                        user.photo,
                        user.name,
                        stringResource(id = R.string.membership_valid_until) + TimeUtil.timestampToDate(
                            user.vipEndDate,
                            "yyyy.MM.dd"
                        ),
                        onClick = {
                            // 跳转到续费
                            startScreen(Screen.MemberShipCenter.route+"?renewal=true",false)
                        },
                        content = {
                            VipBenefitsInMemberShipCenter(
                                vipBenefitsList = vipBenefitsListWithSilver,
                                onClick = { (LogUtil.i("it")) },
                            )
                        }
                    )
                    Spacer(modifier = Modifier.size(5.dp))
                    if (!memberShipCenterViewModel.renewal.collectAsState().value) {
                        LaunchedEffect(Unit) {
                            mainViewModel.improvementScreen.loadImprovementProgramsData(
                                loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                            )
                        }
                        when (improvementRecommendProgramDataState.value) {
                            is ImprovementProgramsLoadState.InitLoading -> {
                                /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                                repeat(3) { count ->
                                    UserExerciseCardLoadingContent(count = count)
                                }
                            }

                            is ImprovementProgramsLoadState.Loading -> {
                                /* 非初次加载，直接从服务器发送请求 */
                                repeat(3) { count ->
                                    UserExerciseCardLoadingContent(count = count)
                                }
                            }

                            is ImprovementProgramsLoadState.Success -> {
                                val data =
                                    (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                                ImprovementProgramCardSuccessContentWithVipZhOnly(
                                    programsData = data
                                )
                            }

                            is ImprovementProgramsLoadState.Failure -> {
                                LogUtil.d("ImprovementProgramsLoadState Failure")
                                ImprovementProgramCardFailureContent {
                                    mainViewModel.improvementScreen.loadImprovementProgramsData(
                                        loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                    )
                                }
                            }
                        }
                    }
                    else {
                        val tagChoose = memberShipCenterViewModel.currentOrderListType.value
                        MemberShipCenterContent(
                            tagChoose,
                            memberShipCenterViewModel.orderDataListWithOldOne,
                            {
                                LogUtil.i(it.name)
                                memberShipCenterViewModel.clearCheck()
                                memberShipCenterViewModel.currentOrderListType.value = it
                            },
                            // 支付相关
                            {
                                if (!memberShipCenterViewModel.isChecked) {
                                    return@MemberShipCenterContent
                                }
                                if (tagChoose == OrderListType.Wearable) {
                                    memberShipCenterViewModel.orderDataListWithOldOne.last().isChoose =
                                        true
                                } else {
                                    memberShipCenterViewModel.orderDataListWithOldOne.forEach {
                                        if (it.isChoose) {
                                            LogUtil.i("choose the ${it.orderName}")
                                        }
                                    }
                                }
                                memberShipCenterViewModel.orderOlder()

                            },
                            memberShipCenterViewModel.isChecked,
                            {
                                memberShipCenterViewModel.isChecked =
                                    !memberShipCenterViewModel.isChecked
                            },
                            {
                                MemberShipAgreementDialog(
                                    memberShipCenterViewModel.dialogVisible,
                                    onConfirm = {
                                        memberShipCenterViewModel.isChecked = true
                                        memberShipCenterViewModel.dialogVisible = false
                                    },
                                    onCancel = {
                                        memberShipCenterViewModel.dialogVisible = false
                                    }
                                )

                            }

                        )
                    }


                }
                // 如果不是会员，不能试用
                else if (!user.vipStatus && user.membershipId == -1 && user.memberTrial) {
                    MemberShipCenterHeaderNormal(
                        user.photo,
                        user.name,
                        stringResource(R.string.new_person),
                    ) {
                        VipBenefitsInMemberShipCenter(
                            vipBenefitsList = memberShipCenterViewModel.vipBenefitsListWithSilver,
                            onClick = { (LogUtil.i("it")) },
                        )
                    }
                    Spacer(modifier = Modifier.size(5.dp))
                    val tagChoose = memberShipCenterViewModel.currentOrderListType.value
                    MemberShipCenterContent(
                        tagChoose,
                        memberShipCenterViewModel.orderDataListWithOldOne,
                        {
                            LogUtil.i(it.name)
                            memberShipCenterViewModel.clearCheck()
                            memberShipCenterViewModel.currentOrderListType.value = it
                        },
                        // 支付相关
                        {
                            LogUtil.i("bug the order")
                            if (tagChoose == OrderListType.Wearable) {
                                memberShipCenterViewModel.orderDataListWithNewOne.last().isChoose = true
                            } else {
                                memberShipCenterViewModel.orderDataListWithOldOne.forEach {
                                    if (it.isChoose) {
                                        LogUtil.i("choose the ${it.orderName}")
                                    }
                                }
                            }

                        },
                        memberShipCenterViewModel.isChecked,
                        {
                            memberShipCenterViewModel.isChecked =
                                !memberShipCenterViewModel.isChecked
                        },
                        {
                            MemberShipAgreementDialog(
                                memberShipCenterViewModel.dialogVisible,
                                onConfirm = {
                                    memberShipCenterViewModel.isChecked = true
                                    memberShipCenterViewModel.dialogVisible = false
                                },
                                onCancel = {
                                    memberShipCenterViewModel.dialogVisible = false
                                }
                            )

                        }
                    )
                }
                else {
                    MemberShipCenterHeaderNormal(
                        user.photo,
                        user.name,
                        stringResource(R.string.new_person),
                    ) {
                        VipBenefitsInMemberShipCenter(
                            vipBenefitsList = vipBenefitsListWithSilver,
                            onClick = { (LogUtil.i("it")) },
                        )
                    }
                    Spacer(modifier = Modifier.size(5.dp))
                    val tagChoose = memberShipCenterViewModel.currentOrderListType.value
                    MemberShipCenterContent(
                        tagChoose,
                        memberShipCenterViewModel.orderDataListWithNewOne,
                        {
                            LogUtil.i(it.name)
                            memberShipCenterViewModel.clearCheck()
                            memberShipCenterViewModel.currentOrderListType.value = it
                        },
                        // 支付相关
                        {
                            memberShipCenterViewModel.order()
                        },
                        memberShipCenterViewModel.isChecked,
                        {
                            memberShipCenterViewModel.isChecked =
                                !memberShipCenterViewModel.isChecked
                        },
                        {
                            MemberShipAgreementDialog(
                                memberShipCenterViewModel.dialogVisible,
                                onConfirm = {
                                    memberShipCenterViewModel.isChecked = true
                                    memberShipCenterViewModel.dialogVisible = false
                                },
                                onCancel = {
                                    memberShipCenterViewModel.dialogVisible = false
                                }
                            )

                        }

                    )
                }

            }
            else {
                // 如果是会员 并且离到期还有3天以上
                if (user.vipStatus && user.vipActiveTime > Limit_Time && (user.membershipId != -1)) {
                    MemberShipCenterHeaderNormal(
                        user.photo,
                        user.name,
                        "Membership deadline:" + TimeUtil.timestampToDate(
                            user.vipEndDate,
                            "yyyy.MM.dd"
                        )
                    ) {
                        VipBenefitsInMemberShipCenter(
                            vipBenefitsList = vipBenefitsListWithSilver,
                            onClick = { (LogUtil.i("it")) },
                        )
                    }
                    Spacer(modifier = Modifier.size(5.dp))

                    LaunchedEffect(Unit) {
                        mainViewModel.improvementScreen.loadImprovementProgramsData(
                            loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                        )
                    }
                    when (improvementRecommendProgramDataState.value) {
                        is ImprovementProgramsLoadState.InitLoading -> {
                            /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                            repeat(3) { count ->
                                UserExerciseCardLoadingContent(count = count)
                            }
                        }

                        is ImprovementProgramsLoadState.Loading -> {
                            /* 非初次加载，直接从服务器发送请求 */
                            repeat(3) { count ->
                                UserExerciseCardLoadingContent(count = count)
                            }
                        }

                        is ImprovementProgramsLoadState.Success -> {
                            val data =
                                (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                            if(currentLocale == Locale.CHINESE){
                                ImprovementProgramCardSuccessContentWithVipZhOnly(
                                    programsData = data
                                )
                            }
                            else{
                                ImprovementProgramCardSuccessContentWithVipEnOnly(programsData = data)
                            }

                        }

                        is ImprovementProgramsLoadState.Failure -> {
                            LogUtil.d("ImprovementProgramsLoadState Failure")
                            ImprovementProgramCardFailureContent {
                                mainViewModel.improvementScreen.loadImprovementProgramsData(
                                    loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                )
                            }
                        }
                    }


                }
                // 如果是会员 并且离到期不到3天
                else if (user.vipStatus && user.vipActiveTime <= Limit_Time && (user.membershipId != -1)) {
                    MemberShipCenterHeaderSpecial(
                        user.photo,
                        user.name,
                        "Membership deadline:" + TimeUtil.timestampToDate(
                            user.vipEndDate,
                            "yyyy.MM.dd"
                        ),
                        onClick = {
                            // 跳转到续费
                            startScreen(Screen.MemberShipCenter.route+"?renewal=true",false)
                        },
                        content = {
                            VipBenefitsInMemberShipCenter(
                                vipBenefitsList = vipBenefitsListWithSilver,
                                onClick = { (LogUtil.i("it")) },
                            )
                        }
                    )
                    Spacer(modifier = Modifier.size(5.dp))
                    if (!memberShipCenterViewModel.renewal.collectAsState().value) {
                        LaunchedEffect(Unit) {
                            mainViewModel.improvementScreen.loadImprovementProgramsData(
                                loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                            )
                        }
                        when (improvementRecommendProgramDataState.value) {
                            is ImprovementProgramsLoadState.InitLoading -> {
                                /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                                repeat(3) { count ->
                                    UserExerciseCardLoadingContent(count = count)
                                }
                            }

                            is ImprovementProgramsLoadState.Loading -> {
                                /* 非初次加载，直接从服务器发送请求 */
                                repeat(3) { count ->
                                    UserExerciseCardLoadingContent(count = count)
                                }
                            }

                            is ImprovementProgramsLoadState.Success -> {
                                val data =
                                    (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                                ImprovementProgramCardSuccessContentWithVipZhOnly(
                                    programsData = data
                                )
                            }

                            is ImprovementProgramsLoadState.Failure -> {
                                LogUtil.d("ImprovementProgramsLoadState Failure")
                                ImprovementProgramCardFailureContent {
                                    mainViewModel.improvementScreen.loadImprovementProgramsData(
                                        loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                    )
                                }
                            }
                        }
                    } else {
                        val tagChoose = memberShipCenterViewModel.currentOrderListType.value
                        MemberShipCenterContent(
                            tagChoose,
                            memberShipCenterViewModel.orderDataListWithOldOne,
                            {
                                LogUtil.i(it.name)
                                memberShipCenterViewModel.clearCheck()
                                memberShipCenterViewModel.currentOrderListType.value = it
                            },
                            // 支付相关
                            {
                                if (!memberShipCenterViewModel.isChecked) {

                                }
                                if (tagChoose == OrderListType.Wearable) {
                                    memberShipCenterViewModel.orderDataListWithNewOne.last().isChoose = true
                                } else {
                                    memberShipCenterViewModel.orderDataListWithOldOne.forEach {
                                        if (it.isChoose) {
                                            LogUtil.i("choose the ${it.orderName}")
                                        }
                                    }
                                }

                            },
                            memberShipCenterViewModel.isChecked,
                            {
                                memberShipCenterViewModel.isChecked =
                                    !memberShipCenterViewModel.isChecked
                            },
                            {
                                MemberShipAgreementDialog(
                                    memberShipCenterViewModel.dialogVisible,
                                    onConfirm = {
                                        memberShipCenterViewModel.isChecked = true
                                        memberShipCenterViewModel.dialogVisible = false
                                    },
                                    onCancel = {
                                        memberShipCenterViewModel.dialogVisible = false
                                    }
                                )

                            }

                        )
                    }


                }
                // 如果不是会员，不能试用
                else if (!user.vipStatus && user.membershipId == -1 && user.memberTrial) {
                    MemberShipCenterHeaderNormal(
                        user.photo,
                        user.name,
                        "New user promotion, limited time limited Up to 20% off",
                    ) {
                        VipBenefitsInMemberShipCenter(
                            vipBenefitsList = vipBenefitsListWithSilver,
                            onClick = { (LogUtil.i("it")) },
                        )
                    }
                    Spacer(modifier = Modifier.size(5.dp))
                    val tagChoose = memberShipCenterViewModel.currentOrderListType.value
                    MemberShipCenterContent(
                        tagChoose,
                        memberShipCenterViewModel.orderDataListWithOldOne,
                        {
                            LogUtil.i(it.name)
                            memberShipCenterViewModel.clearCheck()
                            memberShipCenterViewModel.currentOrderListType.value = it
                        },
                        // 支付相关
                        {
                            LogUtil.i("bug the order")
                            if (tagChoose == OrderListType.Wearable) {
                                memberShipCenterViewModel.orderDataListWithNewOne.last().isChoose = true
                            } else {
                                memberShipCenterViewModel.orderDataListWithOldOne.forEach {
                                    if (it.isChoose) {
                                        LogUtil.i("choose the ${it.orderName}")
                                    }
                                }
                            }

                        },
                        memberShipCenterViewModel.isChecked,
                        {
                            memberShipCenterViewModel.isChecked =
                                !memberShipCenterViewModel.isChecked
                        },
                        {
                            MemberShipAgreementDialog(
                                memberShipCenterViewModel.dialogVisible,
                                onConfirm = {
                                    memberShipCenterViewModel.isChecked = true
                                    memberShipCenterViewModel.dialogVisible = false
                                },
                                onCancel = {
                                    memberShipCenterViewModel.dialogVisible = false
                                }
                            )
                        }
                    )
                }
                else {
                    MemberShipCenterHeaderNormal(
                        user.photo,
                        user.name,
                        "New user promotion, limited time limited Up to 20% off",
                    ) {
                        VipBenefitsInMemberShipCenter(
                            vipBenefitsList = vipBenefitsListWithSilver,
                            onClick = { (LogUtil.i("it")) },
                        )
                    }
                    Spacer(modifier = Modifier.size(5.dp))
                    val tagChoose = memberShipCenterViewModel.currentOrderListType.value
                    MemberShipCenterContent(
                        tagChoose,
                        memberShipCenterViewModel.orderDataListWithNewOne,
                        {
                            LogUtil.i(it.name)
                            memberShipCenterViewModel.clearCheck()
                            memberShipCenterViewModel.currentOrderListType.value = it
                        },
                        // 支付相关
                        {
                            memberShipCenterViewModel.order()
                        },
                        memberShipCenterViewModel.isChecked,
                        {
                            memberShipCenterViewModel.isChecked =
                                !memberShipCenterViewModel.isChecked
                        },
                        {
                            MemberShipAgreementDialog(
                                memberShipCenterViewModel.dialogVisible,
                                onConfirm = {
                                    memberShipCenterViewModel.isChecked = true
                                    memberShipCenterViewModel.dialogVisible = false
                                },
                                onCancel = {
                                    memberShipCenterViewModel.dialogVisible = false
                                }
                            )

                        }

                    )
                }

            }


        }
    }

}

@Composable
fun OrderItem(
    @DrawableRes resId: Int = 0,
    choose: Boolean = false,
    orderData: OrderData,
    onClick: () -> Unit,
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }
    val fontSize18 = with(density) { 18.sp / fontScale }

    Box(
        modifier =
        if (choose) Modifier
            .padding(horizontal = 5.dp)
            .width(98.dp)
            .height(132.dp)
            .background(color = Color(0xFF8EB1E5), shape = RoundedCornerShape(size = 3.dp))
            .clickable {
                onClick()
            }
        else Modifier
            .padding(horizontal = 5.dp)
            .width(98.dp)
            .height(122.dp)
            .background(color = Color(0xFFEBF4FF), shape = RoundedCornerShape(size = 3.dp))
            .clickable {
                onClick()
            },

        ) {
        if (resId != 0) {
            Column(modifier = Modifier.align(Alignment.TopStart))
            {
                Image(
                    painterResource(id = resId),
                    contentDescription = "recommended",
                )
            }

        }

        Column(
            modifier = Modifier.align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                modifier = Modifier
                    .padding(top = 20.dp, bottom = 5.dp),
                text = orderData.orderName,
                style = TextStyle(
                    fontSize = fontSize14,
                    fontWeight = FontWeight(400),
                    color = if (choose) Color(0xFFFFFFFF) else Color(0xFF6492D8),
                    textAlign = TextAlign.Center,
                )
            )
            val salePriceString = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight.Normal,
                        color = if (choose) Color(0xFFFFFFFF) else Color(0xFF3A67AB),
                    )
                ) {
                    append("¥")
                }
                withStyle(
                    SpanStyle(
                        fontSize = fontSize18,
                        fontWeight = FontWeight.Normal,
                        color = if (choose) Color(0xFFFFFFFF) else Color(0xFF3A67AB),
                    )
                ) {
                    append(orderData.salePrice)
                }

            }
            Text(
                modifier = Modifier.padding(bottom = 5.dp),
                text = salePriceString,
            )
            val originPriceString = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight.Normal,
                        color = if (choose) Color(0xFFFFFFFF) else Color(0xFF6492D8),
                        textDecoration = TextDecoration.LineThrough,
                    )
                ) {
                    append("¥")
                }
                withStyle(
                    SpanStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight.Normal,
                        color = if (choose) Color(0xFFFFFFFF) else Color(0xFF6492D8),
                        textDecoration = TextDecoration.LineThrough,
                    )
                ) {
                    append(orderData.originPrice)
                }

            }

            Text(text = originPriceString)
        }

    }
}

data class OrderData(
    var orderName: String,
    var salePrice: String,
    var originPrice: String,
) {
    var index by mutableIntStateOf(0)
    var isChoose by mutableStateOf(false)
}