package org.aihealth.ineck.view.screen.meeting

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.google.gson.Gson
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.meet.MeetMsg
import org.aihealth.ineck.model.meet.MeetingListLoadState
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.view.custom.AIHCard
import org.aihealth.ineck.view.screen.exercise.UserExerciseCardLoadingContent
import org.aihealth.ineck.viewmodel.user
import us.zoom.sdk.ZoomVideoSDK
import us.zoom.sdk.ZoomVideoSDKSessionContext
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

@Composable
fun MeetingListCard(
    modifier: Modifier = Modifier,
    meetingListLoadState: MeetingListLoadState,
) {
    AIHCard {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .fillMaxWidth()
                .animateContentSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            /* 卡片表头Tab选择 */
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Text(
                    text = stringResource(R.string.meeting),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                )
            }

            when (meetingListLoadState) {
                is MeetingListLoadState.InitLoading -> {
                    repeat(3) { count ->
                        UserExerciseCardLoadingContent(count = count)
                    }
                }

                is MeetingListLoadState.Loading -> {

                }

                is MeetingListLoadState.Success -> {
                    val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US)
                    dateFormat.timeZone = TimeZone.getTimeZone("UTC")
                    val data = try {
                        meetingListLoadState.dataList.sortedByDescending {
                            dateFormat.parse(it.time)
                        }
                    } catch (e: Exception) {
                        meetingListLoadState.dataList
                    }

//                    LogUtil.i("data:$data")
                    UserMeetingCardSuccessContent(data)
                }

                is MeetingListLoadState.Failure -> {

                }

                is MeetingListLoadState.EmptyData -> {
                    UserMeetingCardEmptyContent()
                }
            }
        }
    }
}


@Composable
fun UserMeetingCardSuccessContent(
    meetingList: List<MeetMsg>,
) {
    val context = LocalContext.current
    meetingList.forEachIndexed { index, meetMsg ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    XXPermissions.with(context)
                        .permission(Permission.CAMERA, Permission.RECORD_AUDIO)
                        .request(object : OnPermissionCallback {
                            override fun onGranted(p0: MutableList<String>, allGranted: Boolean) {
                                if (allGranted) {
                                    apiService
                                        .getMeetingToken(meetMsg.tpc)
                                        .enqueueBody { response ->
                                            response?.let {
                                                if (it.code == 1) {
                                                    val gson = Gson()
                                                    val jsonString = gson.fromJson(it.data, String::class.java)
                                                    val data = jsonString
                                                    val params = ZoomVideoSDKSessionContext().apply {
                                                        token = data
                                                        sessionName = meetMsg.tpc
                                                        userName = user.name
                                                    }
                                                    LogUtil.i("get meeting token success:${params.toJson()}")
                                                    val session = ZoomVideoSDK
                                                        .getInstance()
                                                        .joinSession(params)
                                                    if (null != session) {
                                                        startScreen(Screen.MeetingScreen.route)
                                                    } else {
                                                        DialogUtil.showToast(context.getString(R.string.join_meetin_fail))
                                                    }
                                                } else {
                                                    LogUtil.i("get meeting token failed:${it.msg}")
                                                    DialogUtil.showToast(it.msg)
                                                }
                                            }
                                        }
                                } else {
                                    DialogUtil.showToast(context.getString(R.string.please_grant_permission))
                                }
                            }

                            override fun onDenied(
                                permissions: MutableList<String>,
                                doNotAskAgain: Boolean
                            ) {
                                super.onDenied(permissions, doNotAskAgain)
                                if (doNotAskAgain) {
                                    DialogUtil.showToast(context.getString(R.string.please_grant_permission_manual))
                                    XXPermissions.startPermissionActivity(context, permissions)
                                } else {
                                    DialogUtil.showToast(context.getString(R.string.please_grant_permission))
                                }
                            }

                        })
                }
        ) {
            if (index != 0) {
                HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
            }
            val context = LocalContext.current
            val meetingName = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        fontWeight = FontWeight.Medium,
                        fontSize = 14.sp,
                    )
                ) {
                    append(
                        context.getString(R.string.meeting_name, meetMsg.meetingName)
                    )
                }
            }
            Text(
                modifier = Modifier
                    .padding(horizontal = 4.dp),
                text = meetingName,
                overflow = TextOverflow.Ellipsis
            )

            /* 练习时间 */
            val descriptionStringOfExerciseTime = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        fontSize = 14.sp
                    )
                ) {
                    append(
                        context.getString(R.string.meeting_time)
                    )
                }
                withStyle(
                    SpanStyle(
                        color = Color(0xFF6181E9),
                        fontSize = 14.sp,
                    )
                ) {
                    append(convertUtcToLocalTime(meetMsg.time))
                }
            }

            Text(
                text = stringResource(R.string.meeting_creator, meetMsg.creatorName),
                modifier = Modifier
                    .padding(horizontal = 4.dp),
                fontSize = 14.sp
            )

            Text(
                text = descriptionStringOfExerciseTime,
                modifier = Modifier
                    .padding(horizontal = 4.dp)
            )


        }
    }
}

@Composable
private fun UserMeetingCardEmptyContent() {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(150.dp),
    ) {
        val (icon, text) = createRefs()
        Icon(
            painter = painterResource(id = R.drawable.icon_empty_record_here),
            contentDescription = stringResource(id = R.string.empty_your_meeting),
            tint = Color(0xFF666666),
            modifier = Modifier
                .size(42.dp)
                .constrainAs(icon) {
                    start.linkTo(parent.start, 12.dp)
                    top.linkTo(parent.top, 24.dp)
                    end.linkTo(parent.end, 24.dp)
                    bottom.linkTo(parent.bottom, 24.dp)
                }
        )
        Text(
            text = stringResource(id = R.string.empty_your_meeting),
            fontWeight = FontWeight.Light,
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            color = Color(0xFF666666),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(text) {
                start.linkTo(parent.start, 12.dp)
                top.linkTo(icon.bottom, 12.dp)
                end.linkTo(parent.end, 12.dp)
            }
        )
    }
}

fun convertUtcToLocalTime(utcTime: String): String {
    // Define the input format for the UTC timestamp
    try {
        val utcFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
        utcFormat.timeZone = TimeZone.getTimeZone("UTC") // Set the time zone to UTC

        // Parse the UTC time string to a Date object
        val date: Date = utcFormat.parse(utcTime)

        // Define the output format for the local time
        val localFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        localFormat.timeZone = TimeZone.getDefault() // Set the time zone to the local time zone

        // Format the Date object to a string in local time
        return localFormat.format(date)

    } catch (e: Exception) {
        return utcTime
    }
}