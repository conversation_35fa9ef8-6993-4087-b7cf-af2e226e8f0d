package org.aihealth.ineck.view.screen

import android.content.Context
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.Crossfade
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.mlkit.vision.face.FaceDetectorOptions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.analyzer.FaceImageAnalyzer
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDialog
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.viewmodel.DetectedResult
import org.aihealth.ineck.viewmodel.VCGuideViewModel
import org.aihealth.ineck.viewmodel.dao.AngleWithTimestamp
import org.aihealth.ineck.viewmodel.dao.VCGuideProcessState
import org.aihealth.ineck.viewmodel.dao.compareEnqueue
import org.aihealth.ineck.viewmodel.dao.getTimestampNow
import org.aihealth.ineck.viewmodel.dao.verifyDiff
import java.util.Locale
import kotlin.math.abs

@Composable
fun VCGuideScreen(
    visible: Boolean,
    viewModel: VCGuideViewModel,
    context: Context = LocalContext.current,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onDismissEvent: (DetectedResult) -> Unit,
    requestCameraPermissionEvent: () -> Unit
) {
    AnimatedVisibility(visible = visible) {
        AIHDialog(
            onDismissRequest = { onDismissEvent(DetectedResult.None) }
        ) {
            /* 协程域 */
            val coroutineScope = rememberCoroutineScope()

            /** 开始监测时间戳 - 从有效数据开始计算 */
            val startTimestampForEffectDetecting =
                viewModel.startTimestampForEffectDetecting.collectAsState()

            /** 垂直检测完成状态 */
            val finishVerticalDetectedState =
                viewModel.finishedVerticalDetectedState.collectAsState()

            /** 当前检测步骤状态 */
            val detectProcessState = viewModel.currentVCGuideProcessState.collectAsState()

            /** 重力矢量状态 */
            val gravityState = viewModel.verticalGravity.collectAsState()

            /** 进入重力校准状态 */
            val gravityMeasureProcess = remember {
                derivedStateOf { gravityState.value > 9.2f }
            }
            /* 是否捕捉到面部 */
            val isCapturedFace = viewModel.isCapturedFace.collectAsState()
            /* 是否处于平视状态（不晃动） */
            val isSightLineHorizontal = viewModel.isSightLineHorizontal.collectAsState()

            /* 当前静音状态 */
            val isMuteState = viewModel.isMuteState.collectAsState()

            /* 全检测步骤完成状态 */
            val totalState = viewModel.totalState.collectAsState()

            /** 检测结果状态 */
            val detectResultState = viewModel.detectedResult.collectAsState()

            /* 摄像头相关 */
            val cameraProvider = ProcessCameraProvider.getInstance(context).get()
            /* 图像分析器 */
            val imageAnalyzer = FaceImageAnalyzer(
                opts = FaceDetectorOptions.Builder()
                    .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_ACCURATE)
                    .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
                    .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
                    .build(),
                onUpdateAngle = { angleX, angleY, angleZ, isEffect ->
                    if (isEffect) {
                        /* 面部存在，更新头部各欧拉角参数 */
                        viewModel.isCapturedFace.update { true }
                        viewModel.headEulerAngleX.update { angleX }
                        viewModel.headEulerAngleY.update { angleY }
                        viewModel.headEulerAngleZ.update { angleZ }
                        if (detectProcessState.value == VCGuideProcessState.DetectingPage) {
                            /* 检测中 */
                            viewModel.angleWithTimestampQueueForDetecting.value.compareEnqueue(
                                AngleWithTimestamp(angleX, getTimestampNow())
                            )
                            /* 设备未垂直情况， 检测开始时间戳被初始化 */
                            if (!gravityMeasureProcess.value) {
                                viewModel.startTimestampForEffectDetecting.update { getTimestampNow() }
                            }
                            /* 若校准时间超过 5sec 以上，在当前拥有足够样本容量的情况下，判断队列差是否小于10度 */
                            if (getTimestampNow() - startTimestampForEffectDetecting.value > 5000L &&
                                viewModel.angleWithTimestampQueueForDetecting.value.verifyDiff(10f)
                            ) {
                                coroutineScope.launch {
                                    viewModel.totalState.update { true }
                                }
                                coroutineScope.launch {
                                    /* 队列中最近一次的取值角度作为矫正角度 */
                                    val lastAngle =
                                        viewModel.angleWithTimestampQueueForDetecting.value.last().angle
                                    delay(1000)
                                    viewModel.writeDetectedResult(
                                        DetectedResult.DetectedSuccess(
                                            reviseValue = lastAngle
                                        )
                                    )
                                }
                            }
                            /* 平视状态判断 */
                            if ((abs(viewModel.headEulerAngleX.value) < 15f) &&
                                abs(viewModel.headEulerAngleY.value) < 15f &&
                                abs(viewModel.headEulerAngleX.value) < 10f
                            ) {
                                /* 在范围内，确认为 平视状态 */
                                viewModel.isSightLineHorizontal.update { true }
                            } else {
                                viewModel.isSightLineHorizontal.update { false }
                                /* 检测开始时间戳被初始化 */
                                viewModel.startTimestampForEffectDetecting.update { getTimestampNow() }
                            }
                        }
                    } else {
                        /* 面部不存在 */
                        viewModel.isCapturedFace.update { false }
                        viewModel.isSightLineHorizontal.update { false }
                        viewModel.headEulerAngleX.update { 0f }
                        viewModel.headEulerAngleY.update { 0f }
                        viewModel.headEulerAngleZ.update { 0f }
                        /* 重置面部开始检测时间  */
                        viewModel.startTimestampForEffectDetecting.update { 0L }
                        /* 若在检测中（记录面部角度）状态时，发生面部消失情况，则判定为检测失败 */
//                        if (detectProcessState.value == VCGuideProcessState.DetectingPage) {
//                            viewModel.writeDetectedResult(DetectedResult.DetectedFailure)
//                        }
                    }
                }
            )

            ConstraintLayout(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = Color.Transparent)
            ) {
                val (contentCard, bottomButton) = createRefs()
                val centerGuideline = createGuidelineFromTop(.5f)
                Card(
                    modifier = Modifier
                        .fillMaxWidth(.9f)
                        .height(if (currentLocale == Locale.CHINESE) 485.dp else 516.dp)
                        .constrainAs(contentCard) {
                            start.linkTo(parent.start, 16.dp)
                            top.linkTo(centerGuideline)
                            end.linkTo(parent.end, 16.dp)
                            bottom.linkTo(centerGuideline)
                        },
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF4F4F4),
                    ),
                    shape = RoundedCornerShape(10.dp),
                ) {
                    Box {
                        /* 包含内容有： 语言播报开关、文本提示框、视频检测内容框，Button按钮选项框 */
                        Column(
                            modifier = Modifier
                                .fillMaxSize()
                                .animateContentSize(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            TextContentBlock(
                                viewModel = viewModel,
                                currentVCGuideProcessPageState = detectProcessState,
                                finishVerticalDetectedState = finishVerticalDetectedState,
                                gravityMeasureProcess = gravityMeasureProcess,
                                totalState = totalState,
                                isCapturedFace = isCapturedFace,
                                isSightLineHorizontal = isSightLineHorizontal
                            )
                            VCBlock(
                                viewModel = viewModel,
                                currentVCGuideProcessPageState = detectProcessState,
                                gravityMeasureProcess = gravityMeasureProcess,
                                isCapturedFace = isCapturedFace,
                                isSightLineHorizontal = isSightLineHorizontal,
                                lifecycleOwner = lifecycleOwner,
                                cameraProvider = cameraProvider,
                                imageAnalyzer = imageAnalyzer
                            )
                            ButtonGroupBlock(
                                currentVCGuideProcessPageState = detectProcessState,
                                onIgnoreEvent = {
                                    /* 直接跳转至校准中页 */
//                                    viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                                    /* 无事发生 */
                                    onDismissEvent(DetectedResult.None)
                                },
                                onNextEvent = {
                                    viewModel.changeGuideDetectProcessState(VCGuideProcessState.PreviousDetectPage)
                                }
                            )
                        }
                        /* 语音播报开关 */
                        if (detectProcessState.value != VCGuideProcessState.PreviousGuidePage) {
                            MuteButton(
                                isMuteState = isMuteState,
                                changeMuteState = { viewModel.changeCurrentMuteState(!isMuteState.value) },
                                modifier = Modifier
                                    .padding(16.dp)
                                    .size(30.dp)
                                    .align(Alignment.TopEnd)
                            )
                        }
                    }
                }
                /**
                 * 退出按钮
                 */
                if (detectProcessState.value is VCGuideProcessState.PreviousGuidePage) {
                    // UNDO
                } else {
                    IconButton(
                        modifier = Modifier
                            .constrainAs(bottomButton) {
                                start.linkTo(parent.start)
                                end.linkTo(parent.end)
                                top.linkTo(contentCard.bottom, 34.dp)
                            }
                            .size(22.dp),
                        onClick = { onDismissEvent(DetectedResult.None) }
                    ) {
                        Icon(
                            painter = painterResource(id = R.drawable.img_dialog_close),
                            contentDescription = "Close Dialog",
                            tint = Color.White,
                            modifier = Modifier.background(Color.Transparent)
                        )

                    }
                }


            }
            /* 监听结果返回状态 */
            LaunchedEffect(detectResultState.value) {
                if (detectResultState.value != DetectedResult.None) {
                    /* 向主页返回引导检测结果 */
                    onDismissEvent(detectResultState.value)
                }
            }
            /* 监听垂直校准结束变化 */
            LaunchedEffect(finishVerticalDetectedState.value) {
                if (finishVerticalDetectedState.value) {
                    coroutineScope.launch {
                        delay(1500)
                        viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                    }
                }
            }
            LaunchedEffect(detectProcessState.value) {
                if (detectProcessState.value is VCGuideProcessState.DetectingPage) {
                    /* 校准中 过程超时判断 - 15 sec */
                    repeat(30) { count ->
                        delay(500)
                        if (count >= 29) viewModel.writeDetectedResult(DetectedResult.DetectedTimeout)
                    }
                } else if (detectProcessState.value is VCGuideProcessState.PreviousDetectPage) {
                    /* 请求前置摄像头权限 */
                    requestCameraPermissionEvent()
                }
            }
            DisposableEffect(lifecycleOwner) {
                val observer = LifecycleEventObserver { _, event ->
                    if (event == Lifecycle.Event.ON_START) {
                        viewModel.clearAllState()
                    }
                }
                lifecycleOwner.lifecycle.addObserver(observer)
                onDispose {
                    lifecycleOwner.lifecycle.removeObserver(observer)
                }
            }
        }
    }
}

/**
 *  引导框中文本信息框（标题、引导提示内容等）
 *  @param  viewModel   视频检测引导视图模型
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  gravityMeasureProcess   重力矢量垂直状态
 *  @param  finishVerticalDetectedState     是否已完成设备垂直校准状态
 */
@Composable
private fun TextContentBlock(
    viewModel: VCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    gravityMeasureProcess: State<Boolean>,
    isCapturedFace: State<Boolean>,
    isSightLineHorizontal: State<Boolean>,
    finishVerticalDetectedState: State<Boolean>,
    totalState: State<Boolean>
) {
    Box(
        modifier = Modifier.fillMaxWidth(.8f)
    ) {
/* 文本显示内容 */
        when (currentVCGuideProcessPageState.value) {
            /* 检测中 */
            VCGuideProcessState.DetectingPage -> {
                TextContentForDetectingPage(
                    viewModel = viewModel,
                    currentVCGuideProcessPageState = currentVCGuideProcessPageState,
                    gravityMeasureProcess = gravityMeasureProcess,
                    totalState = totalState,
                    isCapturedFace = isCapturedFace,
                    isSightLineHorizontal = isSightLineHorizontal
                )
            }
            /* 检测前垂直校准 */
            VCGuideProcessState.PreviousDetectPage -> {
                TextContentForPreviousDetectPage(
                    currentVCGuideProcessPageState = currentVCGuideProcessPageState,
                    finishVerticalDetectedState = finishVerticalDetectedState,
                    gravityMeasureProcess = gravityMeasureProcess,
                    viewModel = viewModel
                )
            }
            /* 检测前引导提示 */
            VCGuideProcessState.PreviousGuidePage -> {
                TextContentForPreviousGuidePage(currentVCGuideProcessPageState)
            }
        }
    }
}

/**
 *  检测引导步骤提示内容
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 */
@Composable
private fun TextContentForPreviousGuidePage(
    currentVCGuideProcessPageState: State<VCGuideProcessState>
) {
    AnimatedVisibility(
        visible = currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousGuidePage,
        enter = slideInHorizontally { fullWidth -> -fullWidth },
        exit = slideOutHorizontally { fullWidth -> 2 * fullWidth }
    ) {
        Column(
            modifier = Modifier.padding(vertical = 24.dp),
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Text(
                text = stringResource(id = R.string.precautions_of_detect_guide_title),
                fontWeight = FontWeight.W400,
                color = Color(0xFF333333),
                fontSize = 22.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Spacer(modifier = Modifier.height(10.dp))
            /* 第一点 */
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 5.dp)
            ) {
                /* 序号 */
                Box(
                    modifier = Modifier
                        .size(width = 14.dp, height = 20.dp)
                        .background(color = Color.Transparent)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 13.dp, height = 4.dp)
                            .background(
                                color = Color(0xFFBCCCFF),
                                shape = RoundedCornerShape(size = 2.dp)
                            )
                            .align(Alignment.BottomCenter),
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_serial_number_1),
                        contentDescription = "No.1",
                        modifier = Modifier
                            .size(13.dp)
                            .align(Alignment.Center)
                    )
                }
                /* 间隔 */
                Spacer(modifier = Modifier.width(9.dp))
                /* 文本内容 */
                Text(
                    text = stringResource(id = R.string.precautions_of_detect_guide_line_1),
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start
                )
            }
            /* 第二点 */
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 5.dp)
            ) {
                /* 序号 */
                Box(
                    modifier = Modifier
                        .size(width = 14.dp, height = 20.dp)
                        .background(color = Color.Transparent)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 13.dp, height = 4.dp)
                            .background(
                                color = Color(0xFFBCCCFF),
                                shape = RoundedCornerShape(size = 2.dp)
                            )
                            .align(Alignment.BottomCenter),
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_serial_number_2),
                        contentDescription = "No.2",
                        modifier = Modifier
                            .size(13.dp)
                            .align(Alignment.Center)
                    )
                }
                /* 间隔 */
                Spacer(modifier = Modifier.width(9.dp))
                /* 文本内容 */
                Text(
                    text = stringResource(id = R.string.precautions_of_detect_guide_line_2),
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start
                )
            }
            /* 第三点 */
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.padding(vertical = 5.dp)
            ) {
                /* 序号 */
                Box(
                    modifier = Modifier
                        .size(width = 14.dp, height = 20.dp)
                        .background(color = Color.Transparent)
                ) {
                    Box(
                        modifier = Modifier
                            .size(width = 13.dp, height = 4.dp)
                            .background(
                                color = Color(0xFFBCCCFF),
                                shape = RoundedCornerShape(size = 2.dp)
                            )
                            .align(Alignment.BottomCenter),
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_serial_number_3),
                        contentDescription = "No.3",
                        modifier = Modifier
                            .size(13.dp)
                            .align(Alignment.Center)
                    )
                }
                /* 间隔 */
                Spacer(modifier = Modifier.width(9.dp))
                /* 文本内容 */
                Text(
                    text = stringResource(id = R.string.precautions_of_detect_guide_line_3),
                    color = Color(0xFF666666),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.W400,
                    textAlign = TextAlign.Start
                )
            }
        }
    }
}

/**
 *  检测垂直检测步骤提示内容
 *  @param  viewModel   视频检测引导视图模型
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  finishVerticalDetectedState     当前垂直检测是否完成状态
 *  @param  gravityMeasureProcess   重力矢量垂直状态
 *  @param  transitionCountdown     过渡倒计时，默认为 2 sec
 */
@Composable
private fun TextContentForPreviousDetectPage(
    viewModel: VCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    finishVerticalDetectedState: State<Boolean>,
    gravityMeasureProcess: State<Boolean>,
    transitionCountdown: Int = 2,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val coroutineScope = rememberCoroutineScope()
    /* 标题从”请将手机置于脸部正前方“ 到 ”请保持手机固定且竖直状态“ 延时 2sec*/
    val count = remember { mutableIntStateOf(transitionCountdown) }
    AnimatedVisibility(
        visible = currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousDetectPage,
        enter = slideInHorizontally { fullWidth -> -fullWidth },
        exit = slideOutHorizontally { fullWidth -> 2 * fullWidth }
    ) {
        /** 垂直校准完成动画 */
        val finishComposition by rememberLottieComposition(spec = LottieCompositionSpec.RawRes(R.raw.vertical_calibration_finish_animation))
        Box(
            modifier = Modifier
                .height(180.dp)
                .background(color = Color.Transparent)
        ) {
            if (count.intValue != 0) {

                val speechText = stringResource(id = R.string.hold_phone_in_front_of_face)
                LaunchedEffect(Unit) {
                    TextToSpeech.ttsSpeaking(speechText)
                }
                Text(
                    text = speechText,
                    style = TextStyle(
                        fontSize = 22.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFF333333),
                    ),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.Center)
                )
            } else {
                Crossfade(
                    targetState = finishVerticalDetectedState.value,
                    modifier = Modifier.align(Alignment.Center), label = ""
                ) { isVertical ->
                    if (isVertical) {
                        /* 手机接近垂直状态 */
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 20.dp),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            /* 校准完成 */
                            LottieAnimation(
                                composition = finishComposition,
                                iterations = 1,
                                modifier = Modifier.size(60.dp)
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = stringResource(id = R.string.passed),
                                style = TextStyle(
                                    fontSize = 22.sp,
                                    fontWeight = FontWeight.Normal,
                                    color = Color(0xFF333333),
                                ),
                                textAlign = TextAlign.Center,
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                        }

                    } else {
                        /* 手机未接近垂直状态 */
                        Text(
                            text = if (gravityMeasureProcess.value) stringResource(id = R.string.keep_phone_fixed_and_upright_true) else stringResource(
                                id = R.string.keep_phone_fixed_and_upright_false
                            ),
                            style = TextStyle(
                                fontSize = 22.sp,
                                fontWeight = FontWeight.W400,
                                color = if (gravityMeasureProcess.value) Color(0xFF6181E9) else Color(
                                    0xFFFC7349
                                ),
                            ),
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .align(Alignment.Center)
                                .animateContentSize(),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
        /* 生命周期开始与结束时针对过渡倒计时进行初始化逻辑与重置逻辑 */
        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                if (event == Lifecycle.Event.ON_START) {
                    coroutineScope.launch {
                        repeat(transitionCountdown) {
                            delay(1000)
                            count.intValue = count.intValue - 1
                        }
                        /* 重力传感器开始监听 */
                        viewModel.gravityStartListening()
                    }
                } else if (event == Lifecycle.Event.ON_STOP) {
                    count.intValue = transitionCountdown
                    /* 重力传感器停止监听 */
                    viewModel.gravityStopListening()
                    coroutineScope.cancel()
                }
            }
            lifecycleOwner.lifecycle.addObserver(observer)
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
    }
}

/**
 *  检测中步骤提示内容
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  gravityMeasureProcess   重力矢量垂直状态
 *  @param  viewModel   视频检测引导视图模型
 */
@Composable
private fun TextContentForDetectingPage(
    viewModel: VCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    gravityMeasureProcess: State<Boolean>,
    isCapturedFace: State<Boolean>,
    isSightLineHorizontal: State<Boolean>,
    totalState: State<Boolean>,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    /** 检测引导完成动画 */
    val finishComposition by rememberLottieComposition(spec = LottieCompositionSpec.RawRes(R.raw.vertical_calibration_finish_animation))
    AnimatedVisibility(
        visible = currentVCGuideProcessPageState.value is VCGuideProcessState.DetectingPage,
        enter = slideInHorizontally { fullWidth -> -fullWidth },
        exit = slideOutHorizontally { fullWidth -> 2 * fullWidth }
    ) {
        Box(
            modifier = Modifier
                .height(180.dp)
                .background(color = Color.Transparent)
                .animateContentSize(),
            contentAlignment = Alignment.Center
        ) {
            Crossfade(targetState = totalState.value, label = "") { state ->
                if (state) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 20.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        /* 校准完成 */
                        LottieAnimation(
                            composition = finishComposition,
                            iterations = 1,
                            modifier = Modifier.size(60.dp)
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = stringResource(id = R.string.passed),
                            style = TextStyle(
                                fontSize = 22.sp,
                                fontWeight = FontWeight.Normal,
                                color = Color(0xFF333333),
                            ),
                            textAlign = TextAlign.Center,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    }

                } else {
                    val speechText =
                        if (!gravityMeasureProcess.value) {
                            stringResource(id = R.string.detect_guide_keep_phone_upright)
                        } else if (!isCapturedFace.value) {
                            stringResource(id = R.string.detect_guide_please_show_your_face)
                        } else if (!isSightLineHorizontal.value) {
                            stringResource(id = R.string.detect_guide_dont_shake_body)
                        } else {
                            stringResource(id = R.string.precautions_of_detect_guide_line_3)
                        }
                    LaunchedEffect(Unit) {
                        TextToSpeech.ttsSpeaking(speechText)
                    }
                    Text(
                        text = speechText,
                        style = TextStyle(
                            fontSize = if (currentLocale == Locale.CHINESE) 22.sp else 20.sp,
                            fontWeight = FontWeight.W400,
                            color = if (gravityMeasureProcess.value && isCapturedFace.value && isSightLineHorizontal.value) Color(
                                0xFF333333
                            ) else Color(0xFFFC7349),
                        ),
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .animateContentSize()
                    )
                }
            }
        }
        /* 启用检测计时 */
        DisposableEffect(lifecycleOwner) {
            val observer = LifecycleEventObserver { _, event ->
                if (event == Lifecycle.Event.ON_START) {
                    getTimestampNow().let { startTimestamp ->
                        viewModel.apply {
                            startTimestampForEffectDetecting.update { startTimestamp }
                        }
                    }
                } else if (event == Lifecycle.Event.ON_STOP) {
                    viewModel.apply {
                        startTimestampForEffectDetecting.update { 0L }
                    }
                }
            }
            lifecycleOwner.lifecycle.addObserver(observer)
            onDispose {
                lifecycleOwner.lifecycle.removeObserver(observer)
            }
        }
    }
}

/**
 *  视频检测框
 *  @param  viewModel   视频检测引导视图模型
 *  @param  currentVCGuideProcessPageState 当前视频检测引导页进程状态
 *  @param  gravityMeasureProcess   是否已进入垂直校准过程标识状态
 *  @param  lifecycleOwner  生命周期拥有者
 *  @param  cameraProvider  相机类提供者
 *  @param  imageAnalyzer   图像分析对象
 */
@Composable
private fun VCBlock(
    viewModel: VCGuideViewModel,
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    gravityMeasureProcess: State<Boolean>,
    isCapturedFace: State<Boolean>,
    isSightLineHorizontal: State<Boolean>,
    lifecycleOwner: LifecycleOwner,
    cameraProvider: ProcessCameraProvider,
    imageAnalyzer: FaceImageAnalyzer,
) {
    val frameBlockColor: Color by animateColorAsState(
        targetValue = if ((currentVCGuideProcessPageState.value != VCGuideProcessState.DetectingPage)
            || (gravityMeasureProcess.value && isCapturedFace.value && isSightLineHorizontal.value && currentVCGuideProcessPageState.value is VCGuideProcessState.DetectingPage)
        )
            Color(0xFFDDE4f1)
        else Color(0xFFFC7349), label = ""
    )
    Box(
        modifier = Modifier
            .size(220.dp)
            .background(color = Color.Transparent)
    ) {
        if (currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousGuidePage) {
            /* 示例人像 */
            Surface(
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
                shape = RoundedCornerShape(8.dp)
            ) {
                Image(
                    painter = painterResource(id = R.drawable.img_detect_guide_example_2),
                    contentDescription = stringResource(id = R.string.text_portrait_example),
                    contentScale = ContentScale.Crop,
                )
            }
        } else {
            val circleProgress = remember { Animatable(0f) }
            /* 外围检测框 */
            Icon(
                painter = painterResource(id = R.drawable.img_vc_detect_block),
                contentDescription = "Detection frame",
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
                tint = frameBlockColor
            )
            /* 检测框周边圆圈进度条 */
            CircularProgressIndicator(
                progress = { circleProgress.value },
                modifier = Modifier
                    .size(196.dp)
                    .clip(CircleShape)
                    .background(color = Color(0x80CCCCCC))
                    .align(Alignment.Center),
                color = frameBlockColor,
                strokeWidth = 10.dp,
            )
            /* 实时人像检测框 */
            Surface(
                modifier = Modifier
                    .size(180.dp)
                    .align(Alignment.Center),
                shape = CircleShape
            ) {
                AndroidView(
                    factory = { context ->
                        val previewView = PreviewView(context)
                        val preview = Preview.Builder().build()
                        /* 摄像头选择 */
                        val selector = CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                            .build()

                        preview.setSurfaceProvider(previewView.surfaceProvider)
                        /* 自定义图像分析对象 */
                        val faceDetectAnalysis = ImageAnalysis.Builder()
                            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                            .build()

                        faceDetectAnalysis.setAnalyzer(
                            ContextCompat.getMainExecutor(context),
                            imageAnalyzer
                        )
                        try {
                            /* 接触之前所有对摄像头单例的绑定 */
                            cameraProvider.unbindAll()
                            /* 针对当前生命周期的绑定行为 */
                            cameraProvider.bindToLifecycle(
                                lifecycleOwner, selector, preview, faceDetectAnalysis
                            )
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        previewView
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
            LaunchedEffect(gravityMeasureProcess.value) {
                if (gravityMeasureProcess.value) {
                    launch(Dispatchers.IO) {
                        viewModel.verifyVerticalDetection(4, 500)
                    }
                    launch(Dispatchers.Main) {
                        (1..4).forEach { i ->
                            circleProgress.animateTo(
                                i * (1f / 4f),
                                animationSpec = tween(500, 0, LinearEasing)
                            )
                        }
                    }
                } else {
                    cancel()
                    launch(Dispatchers.Main) { circleProgress.animateTo(0f) }
                    viewModel.finishedVerticalDetectedState.update { false }
                }
            }
        }
    }
}

/**
 *  Button按键组
 *  @param  currentVCGuideProcessPageState  当前检测引导页进程状态
 *  @param  onIgnoreEvent   忽视引导提示按键触发事件
 *  @param  onNextEvent     下一步按键触发事件
 */
@Composable
private fun ButtonGroupBlock(
    currentVCGuideProcessPageState: State<VCGuideProcessState>,
    onIgnoreEvent: () -> Unit,
    onNextEvent: () -> Unit
) {
    if (currentVCGuideProcessPageState.value is VCGuideProcessState.PreviousGuidePage) {
        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 34.dp)
                .padding(end = 10.dp)
        ) {
            val (ignoreButton, nextButton) = createRefs()
            val verticalCenterGuideline = createGuidelineFromStart(.4f)
            AIHOutlinedButton(
                text = stringResource(id = R.string.ignore),
                shape = RoundedCornerShape(4.dp),
                onClick = onIgnoreEvent,
                backgroundColor = Color(0xFFEFEFEF),
                borderColor = Color(0xFFCCCCCC),
                modifier = Modifier
                    .height(34.dp)
                    .constrainAs(ignoreButton) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top, 20.dp)
                        end.linkTo(verticalCenterGuideline, 10.dp)
                        bottom.linkTo(parent.bottom, 20.dp)
                        width = Dimension.fillToConstraints
                    }
            )
            AIHButton(
                text = stringResource(id = R.string.i_know_and_go_next),
                shape = RoundedCornerShape(4.dp),
                onClick = onNextEvent,
                modifier = Modifier
                    .height(34.dp)
                    .constrainAs(nextButton) {
                        start.linkTo(verticalCenterGuideline, 10.dp)
                        top.linkTo(parent.top, 20.dp)
                        end.linkTo(parent.end)
                        bottom.linkTo(parent.bottom, 20.dp)
                        width = Dimension.fillToConstraints
                    }
            )
        }
    } else {
        Spacer(
            modifier = Modifier
                .fillMaxWidth()
                .height(80.dp)
        )
    }
}

/**
 *  静音按钮
 *  @param  modifier    修饰符参数
 *  @param  isMuteState 当前静音状态
 *  @param  changeMuteState 变更静音状态事件
 */
@Composable
fun MuteButton(
    modifier: Modifier = Modifier,
    isMuteState: State<Boolean>,
    changeMuteState: () -> Unit,
) {
    Box(
        modifier = Modifier
            .then(modifier)
            .clip(CircleShape)
            .background(color = Color(0xFFD9D9D9))
            .clickable { changeMuteState() },
        contentAlignment = Alignment.Center
    ) {
        Crossfade(targetState = isMuteState.value, label = "") { isMute ->
            if (isMute) {
                Icon(
                    painter = painterResource(id = R.drawable.img_voice_close),
                    contentDescription = "Mute",
                    modifier = Modifier.size(20.dp),
                    tint = Color(0xFFF4F4F4)
                )
            } else {
                Icon(
                    painter = painterResource(id = R.drawable.img_voice_open),
                    contentDescription = "UnMute",
                    modifier = Modifier.size(20.dp),
                    tint = Color(0xFFF4F4F4)
                )
            }
        }
    }
}