package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import android.graphics.Bitmap
import android.graphics.Paint
import android.os.Build
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.AnglesHandle
import org.aihealth.ineck.model.angles.BackPain
import org.aihealth.ineck.model.angles.ChartPoint
import org.aihealth.ineck.model.angles.Mean
import org.aihealth.ineck.model.angles.NeckPain
import org.aihealth.ineck.model.angles.RecordScales
import org.aihealth.ineck.model.angles.SeverityProportion
import org.aihealth.ineck.model.angles.SummaryValue
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.dayOfWeek
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.roundToPx
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.timeInSecond
import org.aihealth.ineck.util.toPx
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHCard
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.AIHOutlinedTextField
import org.aihealth.ineck.view.custom.AIHSelectButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.custom.CaptureView
import org.aihealth.ineck.view.custom.DayCalendar
import org.aihealth.ineck.view.custom.MonthCalendar
import org.aihealth.ineck.view.custom.Triangle
import org.aihealth.ineck.view.custom.WeekMonthCalendarShowPanel
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshIndicator
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshLayout
import org.aihealth.ineck.view.directions.HistoryIndexStatusDirections
import org.aihealth.ineck.view.directions.HistoryPainDirections
import org.aihealth.ineck.viewmodel.MainViewModel
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale

/**
 * 报告页面UI
 */
@SuppressLint("SoonBlockedPrivateApi")
@Composable
fun ReportScreen(
    viewModel: MainViewModel
) {

    val currentDeviceType = viewModel.homeScreen.currentDeviceType
    val coroutineScope = rememberCoroutineScope()
    /* 周报告月历选择显示信号状态 */
    val isSelectWeekState = viewModel.reportScreen.isWeekMonthCalendarVisible.collectAsState()
    /* 周报告月历回到被选日期月份信号 */
    val isBackToSelectCalendarSignal =
        viewModel.reportScreen.isBackToSelectCalendarSignal.collectAsState()
    LaunchedEffect(Unit) {
        // 初始化加载数据
        viewModel.reportScreen.isHasConnected = true
        viewModel.reportScreen.onDaySelectedChanged(currentDeviceType)
    }
    BasePageView(
        headerContent = {
            Text(
                text = (when (viewModel.homeScreen.currentDeviceType.netWorkName) {
                    DeviceType.aiNeck.netWorkName -> {
                        stringResource(id = R.string.ai_neck)
                    }

                    DeviceType.aiBack.netWorkName -> {
                        stringResource(id = R.string.ai_back)

                    }

                    DeviceType.aiNeckCV.netWorkName -> {
                        stringResource(id = R.string.ai_neck_cv)
                    }

                    DeviceType.aiBackCV.netWorkName -> {
                        stringResource(id = R.string.ai_back_cv)
                    }

                    DeviceType.KneeJoint.netWorkName -> {
                        stringResource(id = R.string.joint_knee)
                    }

                    DeviceType.KneeJoint.netWorkName -> {
                        stringResource(id = R.string.joint_knee)

                    }

                    DeviceType.ElbowJoint.netWorkName -> {
                        stringResource(id = R.string.joint_elbow)
                    }

                    DeviceType.HipJoint.netWorkName -> {
                        stringResource(id = R.string.joint_hip)
                    }

                    DeviceType.ShoulderJoint.netWorkName -> {
                        stringResource(id = R.string.joint_shoulder)
                    }

                    DeviceType.aiJointCV.netWorkName -> {
                        stringResource(id = R.string.joint_knee)
                    }

                    else -> stringResource(id = R.string.ai_neck_cv)
                })
                        + " " + stringResource(id = R.string.report),
                style = MaterialTheme.typography.displayLarge,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 12.dp),
                color = Color.White
            )
            Row(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 12.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            viewModel.reportScreen.sendEmailDialogVisible = true
                        }
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.img_report_download),
                    contentDescription = null,
                    tint = Color.White,
                    modifier = Modifier.size(16.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = stringResource(id = R.string.download_report),
                    fontSize = 12.sp,
                    color = Color.White,
                    fontWeight = FontWeight.Medium
                )
            }
        },
        background = {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0XFF3654CD), Color(0XFF3F6CE3), Color(0XFF789AF7)
                            )
                        )
                    )
            )
        },
        statusBarDarkContentEnabled = false
    ) {
        AIHRefreshLayout(
            state = viewModel.reportScreen.refreshState,
            onRefresh = {
                coroutineScope.launch {
                    viewModel.reportScreen.onRefresh(currentDeviceType)
                }
            },
            indicator = {
                AIHRefreshIndicator(
                    state = viewModel.reportScreen.refreshState,
                    fontColor = Color.White,
                    iconColor = Color(0XFFF1F1F1)
                )
            }
        ) {
            CaptureView(captureController = viewModel.reportScreen.captureController) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                ) {
                    AIHCard(
                        enabled = true
                    ) {
                        Column(
                            Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            AIHSelectButton(
                                selectedIndex = viewModel.reportScreen.periodIndex,
                                array = stringArrayResource(id = R.array.period),
                                onClick = { viewModel.reportScreen.periodIndex = it },
                                modifier = Modifier
                                    .padding()
                                    .fillMaxWidth(),
                                padding = PaddingValues(5.dp)
                            )

                            if (viewModel.reportScreen.periodIndex == 0) {
                                DayCalendar(
                                    calendarState = viewModel.reportScreen.dayCalendarState,
                                    isHasConnected = true,
                                    onClick = {
                                        coroutineScope.launch {
                                            viewModel.reportScreen.dayCalendarState.selectedDay = it
                                            viewModel.reportScreen.onDaySelectedChanged(currentDeviceType)
                                        }
                                    }
                                )
                                when(currentDeviceType){
                                    DeviceType.aiNeck,DeviceType.aiBack-> {
                                        AngleReport(
                                            viewModel.reportScreen.periodIndex,
                                            viewModel.reportScreen.dayAnglesData
                                        )
                                    }
                                    DeviceType.aiNeckCV,DeviceType.aiBackCV ->{
                                        AngleCVReport(
                                            viewModel.reportScreen.periodIndex,
                                            viewModel.reportScreen.dayAnglesData
                                        )
                                    }
                                    else -> {
                                    }
                                }

                            } else if (viewModel.reportScreen.periodIndex == 1) {
                                WeekDateTime(
                                    selectedDay = viewModel.reportScreen.dayCalendarState.selectedDay,
                                    isHasConnected = true,
                                    onShowWeekSelectEvent = {
                                        viewModel.reportScreen.isWeekMonthCalendarVisible.update { true }
                                        viewModel.reportScreen.isBackToSelectCalendarSignal.update { true }
                                    }
                                )
                                when(currentDeviceType){
                                    DeviceType.aiNeck,DeviceType.aiBack-> {
                                        AngleReport(
                                            viewModel.reportScreen.periodIndex,
                                            viewModel.reportScreen.weekAnglesData
                                        )
                                    }
                                    DeviceType.aiNeckCV,DeviceType.aiBackCV ->{
                                        AngleCVReport(
                                            viewModel.reportScreen.periodIndex,
                                            viewModel.reportScreen.weekAnglesData
                                        )
                                    }
                                    else -> {
                                    }
                                }

                            } else {
                                MonthCalendar(
                                    calendarTime = SimpleDateFormat(stringResource(id = R.string.dateFormat)).format(
                                        viewModel.reportScreen.monthCalendarTime
                                    ),
                                    calendarState = viewModel.reportScreen.monthCalendarState,
                                    isHasConnected = viewModel.reportScreen.isHasConnected,
                                    onClick = {
                                        coroutineScope.launch {
                                            viewModel.reportScreen.monthCalendarState.selectedDay =
                                                it
                                            viewModel.reportScreen.dayCalendarState.selectedDay = it
                                            viewModel.reportScreen.periodIndex = 0
                                            viewModel.reportScreen.onDaySelectedChanged(currentDeviceType)
                                        }
                                    }
                                )
                            }

                        }

                    }
                    val monthArray = stringArrayResource(id = R.array.report_month)
                    val month by remember {
                        derivedStateOf {
                            monthArray[viewModel.reportScreen.monthCalendarMonth - 1]
                        }
                    }
                    when (viewModel.reportScreen.periodIndex) {
                        0 -> {
                            when (viewModel.homeScreen.currentDeviceType) {
                                DeviceType.aiNeck, DeviceType.aiNeckCV -> TodayIndexStatusCard(
                                    painHistory = {
                                        val model = HistoryPainDirections.HistoryPainModel(
                                            painTypeNumberState = 1
                                        )
                                        startScreen(
                                            route = HistoryPainDirections.actionToHistoryPain(model)
                                        )
                                    },
                                    neuralHistory = {
                                        val model = HistoryPainDirections.HistoryPainModel(
                                            painTypeNumberState = 3
                                        )
                                        startScreen(
                                            route = HistoryPainDirections.actionToHistoryPain(model)
                                        )
                                    },
                                    neckPain = viewModel.reportScreen.dayRecordScales.neck_pain
                                )

                                DeviceType.aiBack, DeviceType.aiBackCV -> TodayIndexStatusCard(
                                    backPain = viewModel.reportScreen.dayRecordScales.back_pain,
                                    painHistory = {
                                        val model = HistoryPainDirections.HistoryPainModel(
                                            painTypeNumberState = 2
                                        )
                                        startScreen(
                                            route = HistoryPainDirections.actionToHistoryPain(model)
                                        )
                                    },
                                    neuralHistory = {
                                        val model = HistoryPainDirections.HistoryPainModel(
                                            painTypeNumberState = 4
                                        )
                                        startScreen(
                                            route = HistoryPainDirections.actionToHistoryPain(model)
                                        )
                                    },
                                )

                                else -> {}
                            }

                            TodayHealthIndexStatusCard(
                                viewModel.reportScreen.dayRecordScales.promis,
                                viewModel.reportScreen.dayRecordScales.odi,
                                toPromisHistory = {
                                    val calendar =
                                        viewModel.reportScreen.dayCalendarState.selectedDay.clone() as Calendar
                                    calendar.add(Calendar.DATE, 1)
                                    val endTime = calendar.timeInSecond
                                    calendar.add(Calendar.DATE, -7)
                                    val startTime = calendar.timeInSecond
                                    val model =
                                        HistoryIndexStatusDirections.HistoryIndexStatusModel(
                                            startTime = startTime,
                                            endTime = endTime,
                                            isPromis = true
                                        )
                                    startScreen(
                                        route = HistoryIndexStatusDirections.actionToHistoryIndexStatus(
                                            model = model
                                        )
                                    )
                                },
                                toODIHistory = {
                                    val calendar =
                                        viewModel.reportScreen.dayCalendarState.selectedDay.clone() as Calendar
                                    calendar.add(Calendar.DATE, 1)
                                    val endTime = calendar.timeInSecond
                                    calendar.add(Calendar.DATE, -7)
                                    val startTime = calendar.timeInSecond
                                    val model =
                                        HistoryIndexStatusDirections.HistoryIndexStatusModel(
                                            startTime = startTime,
                                            endTime = endTime,
                                            isPromis = false,
                                        )
                                    startScreen(
                                        route = HistoryIndexStatusDirections.actionToHistoryIndexStatus(
                                            model = model
                                        )
                                    )
                                }
                            )
                            if (!viewModel.reportScreen.isHasConnected) {
                                NeverConnectedCard(0)
                            }

                        }

                        1 -> {
                            if (viewModel.reportScreen.isHasConnected) {
                                WeekSpineStatusCard(
                                    deviceType = viewModel.homeScreen.currentDeviceType,
                                    dayOfWeek = viewModel.reportScreen.dayCalendarState.selectedDay.dayOfWeek,
                                    viewModel.reportScreen.weekMeanList
                                )
                            }
                            WeekHealthIndexStatusCard(
                                viewModel.reportScreen.weekRecordScales.promis,
                                viewModel.reportScreen.weekRecordScales.odi,
                                toPromisHistory = {
                                    //                        startScreen(Screen.OdiPromisRecord.route)
                                    val calendar =
                                        viewModel.reportScreen.dayCalendarState.selectedDay.clone() as Calendar
                                    calendar.add(Calendar.DATE, 1)
                                    val endTime = calendar.timeInSecond
                                    calendar.add(Calendar.DATE, -7)
                                    val startTime = calendar.timeInSecond
                                    val model =
                                        HistoryIndexStatusDirections.HistoryIndexStatusModel(
                                            startTime = startTime,
                                            endTime = endTime,
                                            isPromis = true
                                        )
                                    startScreen(
                                        route = HistoryIndexStatusDirections.actionToHistoryIndexStatus(
                                            model = model
                                        )
                                    )
                                },
                                toODIHistory = {
//                                    startScreen(Screen.OdiPromisRecord.route, bundle = Bundle().apply { putBoolean("isPromis",true) })
                                    val calendar =
                                        viewModel.reportScreen.dayCalendarState.selectedDay.clone() as Calendar
                                    calendar.add(Calendar.DATE, 1)
                                    val endTime = calendar.timeInSecond
                                    calendar.add(Calendar.DATE, -7)
                                    val startTime = calendar.timeInSecond
                                    val model =
                                        HistoryIndexStatusDirections.HistoryIndexStatusModel(
                                            startTime = startTime,
                                            endTime = endTime,
                                        )
                                    startScreen(
                                        route = HistoryIndexStatusDirections.actionToHistoryIndexStatus(
                                            model = model
                                        )
                                    )
                                }
                            )
                            if (!viewModel.reportScreen.isHasConnected) {
                                NeverConnectedCard(1)
                            }
                        }

                        2 -> {
                            if (viewModel.reportScreen.isHasConnected) {
                                MonthPostureData(
                                    month = month,
                                    anglesHandle = viewModel.reportScreen.monthAnglesHandle
                                )
                            }
                            MonthPromisIndexStatus(
                                month = month,
                                recordScales = viewModel.reportScreen.monthRecordScales,
                                list = viewModel.reportScreen.monthLineChart.promis_line_chart_data
                            )
                            MonthOdiIndexStatus(
                                month = month,
                                recordScales = viewModel.reportScreen.monthRecordScales,
                                list = viewModel.reportScreen.monthLineChart.odi_line_chart
                            )
                        }
                    }
                    Spacer(modifier = Modifier.height(14.dp))
                }
            }
        }

    }
    SendEmailBottomDrawer(viewModel)
    WeekMonthCalendarShowPanel(
        isVisible = isSelectWeekState,
        isBackSelected = isBackToSelectCalendarSignal,
        selectDay = viewModel.reportScreen.dayCalendarState.selectedDay,
        today = viewModel.reportScreen.dayCalendarState.today,
        onSelectedEvent = { dateUnit ->
            coroutineScope.launch {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    viewModel.reportScreen.dayCalendarState.selectedDay = Calendar.Builder()
                        .setDate(dateUnit.currentYear, dateUnit.currentMonth - 1, dateUnit.date)
                        .build()
                } else {
                    viewModel.reportScreen.dayCalendarState.selectedDay =
                        Calendar.getInstance().apply {
                            set(Calendar.YEAR, dateUnit.currentYear)
                            set(Calendar.MONTH, dateUnit.currentMonth - 1)
                            set(Calendar.DAY_OF_MONTH, dateUnit.date)
                        }
                }
                viewModel.reportScreen.onDaySelectedChanged(currentDeviceType)
                viewModel.reportScreen.isWeekMonthCalendarVisible.update { !it }
            }
        },
        onBackSelectedEvent = { signal ->
            viewModel.reportScreen.isBackToSelectCalendarSignal.update { signal }
        },
        onDismissEvent = {
            viewModel.reportScreen.isWeekMonthCalendarVisible.update { !it }
            viewModel.reportScreen.isBackToSelectCalendarSignal.update { true }
        },
        onReCompose = {
            /* 忽闪忽现(肉眼感知不强) + 延时 为的是使组件重组刷新 */
            coroutineScope.launch {
                viewModel.reportScreen.isWeekMonthCalendarVisible.update { false }
                delay(50)
                viewModel.reportScreen.isWeekMonthCalendarVisible.update { true }
            }
        },
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0x80000000))
            .padding(horizontal = 24.dp, vertical = 12.dp)
    )
}

@Composable
private fun SendEmailBottomDrawer(
    viewModel: MainViewModel,
) {
    val reportImpl = viewModel.reportScreen
    /* 底部对话框抽屉打开显示状态 */
//    val bottomDrawerState: BottomDrawerState by remember {
//        derivedStateOf {
//            if (reportImpl.sendEmailDialogVisible) BottomDrawerState(initialValue = BottomDrawerValue.Expanded)
//                else BottomDrawerState(initialValue = BottomDrawerValue.Closed)
//        }
//    }
//    /* 当抽屉关闭时，将ViewModel层 sendEmailDialogVisible 设置维false */
//    if (bottomDrawerState.isClosed) {
//        LaunchedEffect(key1 = bottomDrawerState.currentValue, block = {
//            reportImpl.sendEmailDialogVisible = false
//        })
//    }
//
//    BottomDrawer(
//        gesturesEnabled = true,
//        drawerShape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp),
//        drawerElevation = 16.dp,
//        drawerBackgroundColor = Color.White,
//        scrimColor = Color(0X50000000),
//        drawerState = bottomDrawerState,
//        drawerContent = {
//
//        },
//        content = {
//            /* outside */
//        }
//    )

    if (reportImpl.sendEmailDialogVisible) {
        AIHBottomSheet(
            onDismissRequest = { reportImpl.sendEmailDialogVisible = false }
        ) {
            Column(
                modifier = Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .height(314.dp)
                    .background(
                        Color.White,
                        RoundedCornerShape(topStart = 20.dp, topEnd = 20.dp)
                    )
                    .padding(horizontal = 17.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = stringResource(id = R.string.report_sent_to_doctor_in_email),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color(0XFF2B56D7),
                    modifier = Modifier.padding(bottom = 30.dp)
                )
                AIHOutlinedTextField(
                    value = reportImpl.email,
                    onValueChange = { reportImpl.email = it },
                    placeholder = stringResource(id = R.string.please_enter),
                    keyboardType = KeyboardType.Email,
                    shape = CircleShape
                )
                Spacer(modifier = Modifier.height(24.dp))
                AIHButton(
                    text = stringResource(id = R.string.next_step),
                    onClick = {
                        /* 向指定邮箱发送报告 */
                        reportImpl.sendToEmail {
                            if (it) {
                                reportImpl.sendEmailDialogVisible = false
                            }
                        }
                    },
                    modifier = Modifier.size(340.dp, 40.dp)
                )
                Spacer(modifier = Modifier.height(16.dp))
                AIHOutlinedButton(
                    text = stringResource(id = R.string.createReportImage),
                    onClick = {
                        /* 生成报告图片保存到设备本地 */
                        reportImpl.createReportImage()
                    },
                    modifier = Modifier.size(340.dp, 40.dp)
                )
            }
        }
    }
}

/**
 * 应用从未连接过设备的卡片
 */

@Composable
private fun NeverConnectedCard(
    periodIndex: Int = 0
) {
    AIHCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.report_neverconnected_tip),
                color = Color(0XFF333333),
                fontSize = 16.sp,
            )
            Spacer(modifier = Modifier.height(10.dp))
            Row(
                Modifier
                    .fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier.weight(1f)
                ) {
                    Text(
                        text = "**",
                        fontSize = 16.sp,
                        color = Color(0XFF66D7D6),
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = stringResource(id = if (periodIndex == 0) R.string.wearing_time_this_day else R.string.wearing_time_this_week),
                        color = Color(0XFF666666),
                        fontSize = 12.sp
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(1f),
                ) {
                    Text(
                        text = "**",
                        fontSize = 16.sp,
                        color = Color(0XFF66D7D6),
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = stringResource(id = R.string.number_of_vibrations),
                        color = Color(0XFF666666),
                        fontSize = 12.sp
                    )
                }
            }
            Spacer(modifier = Modifier.height(30.dp))
            Box(
                modifier = Modifier
                    .fillMaxWidth(0.8f)
                    .aspectRatio(1f)
                    .border(
                        width = 23.dp,
                        color = Color(0XFFD9D9D9),
                        shape = CircleShape
                    )
            ) {
                Column(
                    modifier = Modifier.align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "**",
                        fontSize = 16.sp,
                        color = Color(0XFF999999),
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(2.dp))
                    Text(
                        text = stringResource(id = R.string.average_angle),
                        fontSize = 12.sp,
                        color = Color(0XFF999999)
                    )
                }
            }
        }
    }
}

/**
 * 日，周角度报告状态，圆环平均角度，角度比例
 */
@Composable
private fun AngleReport(
    periodIndex: Int = 0,
    anglesHandle: AnglesHandle = AnglesHandle()
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(20.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = anglesHandle.wearing_time ?: "00:00:00",
                    fontSize = 16.sp,
                    color = Color(0XFF5170DD)
                )
                Text(
                    text = stringResource(id = if (periodIndex == 0) R.string.wearing_time_this_day else R.string.wearing_time_this_week),
                    color = Color(0XFF666666),
                    fontSize = 12.sp
                )
            }
            Spacer(modifier = Modifier.weight(0.01f))
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = anglesHandle.number_of_vibrations.toString(),
                    fontSize = 16.sp,
                    color = Color(0XFF5170DD)
                )
                Text(
                    text = stringResource(id = R.string.number_of_vibrations),
                    color = Color(0XFF666666),
                    fontSize = 12.sp
                )
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
        val todaySummaryList = stringArrayResource(id = R.array.report_status_summary_today)
        val weekSummaryList = stringArrayResource(id = R.array.report_status_summary_week)
        Text(
            text = if (periodIndex == 0) todaySummaryList[anglesHandle.body_posture_state - 1] else weekSummaryList[anglesHandle.body_posture_state - 1],
            fontSize = 16.sp,
            color = Color(0XFF5170DD),
            fontWeight = FontWeight.Medium,
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = stringResource(id = R.string.please_keep_it_up),
            fontSize = 12.sp,
            color = Color(0XFF444444)
        )
        AverageAngleCircle(anglesHandle.degree_list, anglesHandle.average_angle)
        RadioTable(anglesHandle.degree_list)
    }
}

/**
 * 日，周角度报告状态，圆环平均角度，摄像头角度比例
 */
@Composable
private fun AngleCVReport(
    periodIndex: Int = 0,
    anglesHandle: AnglesHandle = AnglesHandle()
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(20.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = anglesHandle.wearing_time ?: "00:00:00",
                    fontSize = 16.sp,
                    color = Color(0XFF5170DD)
                )
                Text(
                    text = stringResource(id = if (periodIndex == 0) R.string.monitor_time_this_day else R.string.monitor_time_this_week),
                    color = Color(0XFF666666),
                    fontSize = 12.sp
                )
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
        val todaySummaryList = stringArrayResource(id = R.array.report_status_summary_today)
        val weekSummaryList = stringArrayResource(id = R.array.report_status_summary_week)
        Text(
            text = if (periodIndex == 0) todaySummaryList[anglesHandle.body_posture_state - 1] else weekSummaryList[anglesHandle.body_posture_state - 1],
            fontSize = 16.sp,
            color = Color(0XFF5170DD),
            fontWeight = FontWeight.Medium,
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = stringResource(id = R.string.please_keep_it_up),
            fontSize = 12.sp,
            color = Color(0XFF444444)
        )
        AverageAngleCircle(anglesHandle.degree_list, anglesHandle.average_angle)
        RadioTable(anglesHandle.degree_list)
    }
}
/**
 * 今日PROMIS健康量表和ODI健康量表数值状态
 */

@Composable
private fun TodayHealthIndexStatusCard(
    promisValue: SummaryValue = SummaryValue(),
    odiValue: SummaryValue = SummaryValue(),
    toPromisHistory: () -> Unit = {},
    toODIHistory: () -> Unit = {}
) {
    AIHCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
        ) {
            /* PROMIS 数据 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.promis_value_of_the_day),
                    fontSize = 14.sp,
                    color = Color(0XFF444444),
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier
                        .weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = { toPromisHistory() },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0xFF6BC5C6),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            PromisHealthView(promisValue = promisValue)
            Spacer(modifier = Modifier.height(16.dp))
            /* ODI 数据 */
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.odi_value_of_the_day),
                    fontSize = 14.sp,
                    color = Color(0XFF444444),
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier
                        .weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        toODIHistory()

//                        startScreen(Screen.OdiPromisRecord.route)
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0xFF6BC5C6),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            OdiHealthView(summaryValueValue = odiValue)
        }
    }
}

/**
 * 本周PROMIS健康量表和ODI健康量表数值状态
 */
@Composable
private fun WeekHealthIndexStatusCard(
    promisValue: SummaryValue = SummaryValue(),
    odiValue: SummaryValue = SummaryValue(),
    toPromisHistory: () -> Unit = {},
    toODIHistory: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }
    AIHCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
        ) {
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.promis_value_of_the_week),
                    fontSize = 14.sp,
                    color = Color(0XFF444444),
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier
                        .weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        toPromisHistory()
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0xFF6BC5C6),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            PromisHealthView(promisValue = promisValue)
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                Modifier
                    .fillMaxWidth()
            ) {
                Text(
                    text = "${stringResource(id = R.string.average_value)}: ${promisValue.mean}",
                    fontSize = fontSize14,
                    color = Color(0XFFFED132),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
                AIHDivider(isVertical = true)
                Text(
                    text = "${stringResource(id = R.string.maximum_value)}: ${promisValue.max}",
                    fontSize = fontSize14,
                    color = Color(0XFF5ECCB1),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
                AIHDivider(isVertical = true)

                Text(
                    text = "${stringResource(id = R.string.minimum_value)}: ${promisValue.min}",
                    fontSize = fontSize14,
                    color = Color(0XFFF57E43),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
            }

            Spacer(modifier = Modifier.height(24.dp))
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.odi_value_of_the_week),
                    fontSize = 14.sp,
                    color = Color(0XFF444444),
                    fontWeight = FontWeight.Medium,
                    modifier = Modifier
                        .weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        toODIHistory()
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0xFF6BC5C6),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            OdiHealthView(summaryValueValue = odiValue)
            Spacer(modifier = Modifier.height(10.dp))
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(20.dp)
            ) {
                Text(
                    text = "${stringResource(id = R.string.average_value)}: ${odiValue.mean}",
                    fontSize = fontSize14,
                    color = Color(0XFFFED132),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
                AIHDivider(isVertical = true)

                Text(
                    text = "${stringResource(id = R.string.maximum_value)}: ${odiValue.max}",
                    fontSize = fontSize14,
                    color = Color(0XFF5ECCB1),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
                AIHDivider(isVertical = true)

                Text(
                    text = "${stringResource(id = R.string.minimum_value)}: ${odiValue.min}",
                    fontSize = fontSize14,
                    color = Color(0XFFF57E43),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

/**
 *  周报告 星期日期卡片
 *  @param  selectedDay 选择日期
 *  @param  onShowWeekSelectEvent   打开月历选择面板事件
 */
@SuppressLint("SimpleDateFormat")
@Composable
private fun WeekDateTime(
    selectedDay: Calendar,
    isHasConnected: Boolean,
    onShowWeekSelectEvent: () -> Unit
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        val calender = selectedDay.clone() as Calendar
        calender.add(Calendar.DATE, -7)

        Text(
            text = SimpleDateFormat(stringResource(id = R.string.dateFormat)).format(calender.time),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = if (isHasConnected) Color(0XFF444444) else Color.White
        )
        /* 选择星期触发月历 */
        IconButton(
            onClick = { onShowWeekSelectEvent() }
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_report_calender),
                contentDescription = null,
                modifier = Modifier
                    .padding(horizontal = 3.dp)
                    .size(24.dp, 26.dp)
            )
        }
        calender.add(Calendar.DATE, 7)
        Text(
            text = SimpleDateFormat(stringResource(id = R.string.dateFormat)).format(calender.time),
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            color = if (isHasConnected) Color(0xFF444444) else Color.White
        )
    }
}

/**
 * 今天颈部疼痛指数状态和神经指数状态
 */
@Preview()
@Composable
private fun TodayIndexStatusCard(
    neckPain: NeckPain = NeckPain(),
    painHistory: () -> Unit = {},
    neuralHistory: () -> Unit = {}
) {
    LogUtil.d(
        "TodayIndexStatusCard, neckPain: hand:${neckPain.hand}, neck:${neckPain.neck}" +
                "muscle:${neckPain.muscle}, balance:${neckPain.balance}, numb:${neckPain.numb}"
    )
    AIHCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
        ) {
            Spacer(modifier = Modifier.height(8.dp))
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.pain_index_status_today),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.weight(3f)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        painHistory()
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0XFF999999),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            /* 疼痛指数表头信息 */
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 14.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.report_body),
                    fontSize = 14.sp,
                    color = Color(0XFFB0C3FF),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = stringResource(id = R.string.report_scale),
                    fontSize = 14.sp,
                    color = Color(0XFFB0C3FF),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
                Text(
                    text = stringResource(id = R.string.level),
                    fontSize = 14.sp,
                    color = Color(0XFFB0C3FF),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.End
                )
            }
            /* 颈部 疼痛数据 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 14.dp, vertical = 12.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.neck),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = neckPain.neck.toString(),
                    fontSize = 14.sp,
                    color = if (neckPain.neck > 7) {
                        Color(0XFFF57E43)
                    } else if (neckPain.neck > 3) {
                        Color(0xFFFED132)
                    } else {
                        Color(0xFF5ECCB1)
                    },
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = if (neckPain.neck > 7) {
                        stringResource(id = R.string.severe)
                    } else if (neckPain.neck > 3) {
                        stringResource(id = R.string.moderate)
                    } else {
                        stringResource(id = R.string.mild)
                    },
                    fontSize = 14.sp,
                    color = if (neckPain.neck > 7) {
                        Color(0XFFF57E43)
                    } else if (neckPain.neck > 3) {
                        Color(0xFFFED132)
                    } else {
                        Color(0xFF5ECCB1)
                    },
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.End,
                    fontWeight = FontWeight.Medium
                )
            }
            AIHDivider()
            /* 上肢 疼痛数据 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 14.dp, vertical = 12.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.hand_arm),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = neckPain.hand.toString(),
                    fontSize = 14.sp,
                    color = if (neckPain.hand > 7) {
                        Color(0XFFF57E43)
                    } else if (neckPain.hand > 3) {
                        Color(0xFFFED132)
                    } else {
                        Color(0xFF5ECCB1)
                    },
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = if (neckPain.hand > 7) {
                        stringResource(id = R.string.severe)
                    } else if (neckPain.hand > 3) {
                        stringResource(id = R.string.moderate)
                    } else {
                        stringResource(id = R.string.mild)
                    },
                    fontSize = 14.sp,
                    color = if (neckPain.hand > 7) {
                        Color(0XFFF57E43)
                    } else if (neckPain.hand > 3) {
                        Color(0xFFFED132)
                    } else {
                        Color(0xFF5ECCB1)
                    },
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.End,
                    fontWeight = FontWeight.Medium
                )
            }
            AIHDivider()
            Spacer(modifier = Modifier.height(8.dp))
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.neurological_index_status_today),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        neuralHistory()
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0XFF999999),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            /* 肌肉力量数据 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 14.dp)
                    .padding(bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.muscle_strength),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = stringResource(id = R.string.number_level, neckPain.muscle),
                    fontSize = 14.sp,
                    color = if (neckPain.muscle >= 4) {
                        Color(0XFFF57E43)
                    } else if (neckPain.muscle >= 2) {
                        Color(0xFFFED132)
                    } else {
                        Color(0xFF5ECCB1)
                    },
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.width(10.dp))
//                Image(
//                    painter = painterResource(id = R.drawable.img_explain),
//                    contentDescription = null,
//                    modifier = Modifier.size(14.dp)
//                )
            }
            AIHDivider()
            /* 是否麻木 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 14.dp, top = 12.dp, bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.numbness_tingling),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(3F)
                )
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(Color(0XFF1E4BDF), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier.padding(vertical = 4.dp),
                        text = if (neckPain.numb == 1) stringResource(id = R.string.report_none) else stringResource(
                            id = R.string.report_numb_yes
                        ),
                        fontSize = 8.sp,
                        color = Color.White
                    )
                }
            }
            AIHDivider()
            /* 是否平衡 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 14.dp, top = 12.dp, bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.balance),
                    fontSize = 16.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(3F)
                )
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(Color(0XFF1E4BDF), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier.padding(vertical = 4.dp),
                        text = if (neckPain.balance == 1) stringResource(id = R.string.normal) else stringResource(
                            id = R.string.unbalance
                        ),
                        fontSize = 8.sp,
                        color = Color.White
                    )
                }
            }
            AIHDivider()
            Spacer(modifier = Modifier.height(5.dp))
        }
    }
}

/**
 * 今天背部疼痛指数状态和神经指数状态
 */
@Composable
private fun TodayIndexStatusCard(
    backPain: BackPain = BackPain(),
    painHistory: () -> Unit,
    neuralHistory: () -> Unit
) {
    LogUtil.d("TodayIndexStatusCard, backPain: back:${backPain.back}, neck:${backPain.leg}")
    AIHCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
        ) {
            Spacer(modifier = Modifier.height(8.dp))
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.pain_index_status_today),
                    fontSize = 14.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        painHistory()
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0XFF999999),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 14.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.report_body),
                    fontSize = 14.sp,
                    color = Color(0XFFB0C3FF),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = stringResource(id = R.string.report_scale),
                    fontSize = 14.sp,
                    color = Color(0XFFB0C3FF),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center
                )
                Text(
                    text = stringResource(id = R.string.level),
                    fontSize = 14.sp,
                    color = Color(0XFFB0C3FF),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.End
                )
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 14.dp, vertical = 12.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.back),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = backPain.back.toString(),
                    fontSize = 14.sp,
                    color = Color(0XFFFED132),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = stringResource(id = R.string.moderate),
                    fontSize = 14.sp,
                    color = Color(0XFFFED132),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.End,
                    fontWeight = FontWeight.Medium
                )
            }
            AIHDivider()
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 14.dp, vertical = 12.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.leg),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = backPain.leg.toString(),
                    fontSize = 14.sp,
                    color = Color(0XFFF57E43),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
                Text(
                    text = stringResource(id = R.string.moderate),
                    fontSize = 14.sp,
                    color = Color(0XFFF57E43),
                    modifier = Modifier.weight(1F),
                    textAlign = TextAlign.End,
                    fontWeight = FontWeight.Medium
                )
            }
            AIHDivider()
            Spacer(modifier = Modifier.height(8.dp))
            Row(Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = stringResource(id = R.string.neurological_index_status_today),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.weight(3F)
                )
                AIHButton(
                    text = stringResource(id = R.string.details),
                    onClick = {
                        neuralHistory()
                    },
                    backgroundColor = Color(0XFFEBEEF8),
                    fontSize = 8.sp,
                    fontColor = Color(0XFF999999),
                    modifier = Modifier.weight(1f)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 14.dp, bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.muscle_strength),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(1F)
                )
                Text(
                    text = stringResource(id = R.string.number_level, backPain.muscle),
                    fontSize = 14.sp,
                    color = Color(0XFFFED132),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.width(10.dp))
//                Image(
//                    painter = painterResource(id = R.drawable.img_explain),
//                    contentDescription = null,
//                    modifier = Modifier.size(14.dp)
//                )
            }
            AIHDivider()

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 14.dp, top = 12.dp, bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.numbness_tingling),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(3F)
                )
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(Color(0XFF1E4BDF), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier.padding(vertical = 4.dp),
                        text = if (backPain.numb == 1) stringResource(id = R.string.report_none) else stringResource(
                            id = R.string.report_numb_yes
                        ),
                        fontSize = 8.sp,
                        color = Color.White
                    )
                }
            }
            AIHDivider()

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 14.dp, top = 12.dp, bottom = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.balance),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    modifier = Modifier.weight(3F)
                )
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .background(Color(0XFF1E4BDF), CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier.padding(vertical = 4.dp),

                        text = if (backPain.balance == 1) stringResource(id = R.string.normal) else stringResource(
                            id = R.string.unbalance
                        ),
                        fontSize = 8.sp,
                        color = Color.White
                    )
                }

            }
            AIHDivider()
            Spacer(modifier = Modifier.height(5.dp))
        }
    }
}

/**
 * 日，周平均角度圆环
 */
@Composable
private fun AverageAngleCircle(
    degreeList: SeverityProportion,
    averageAngle: Int
) {
    Box(
        modifier = Modifier
            .padding(top = 28.dp)
            .fillMaxWidth()
            .height(180.dp)
    ) {
        Canvas(
            Modifier
                .fillMaxWidth()
                .height(180.dp)
        ) {
            if (degreeList.moderate.proportion == 0 && degreeList.slight.proportion == 0 && degreeList.severe.proportion == 0) {
                drawArc(
                    color = Color(0XFF75CFC1),
                    startAngle = 0F,
                    sweepAngle = 360F,
                    useCenter = false,
                    topLeft = Offset((size.width - 180.dp.toPx()) / 2, 0F),
                    size = Size(size.height, size.height),
                    style = Stroke(width = 20.dp.toPx(), cap = StrokeCap.Round)
                )
            } else {
                drawArc(
                    color = Color(0XFF75CFC1),
                    startAngle = 0F,
                    sweepAngle = degreeList.slight.proportion * 3.6F,
                    useCenter = false,
                    topLeft = Offset((size.width - 180.dp.toPx()) / 2, 0F),
                    size = Size(size.height, size.height),
                    style = Stroke(width = 20.dp.toPx(), cap = StrokeCap.Round)
                )

                drawArc(
                    color = Color(0XFFF57E43),
                    startAngle = degreeList.slight.proportion * 3.6F + degreeList.moderate.proportion * 3.6F,
                    sweepAngle = degreeList.severe.proportion * 3.6F,
                    useCenter = false,
                    topLeft = Offset((size.width - 180.dp.toPx()) / 2, 0F),
                    size = Size(size.height, size.height),
                    style = Stroke(width = 20.dp.toPx(), cap = StrokeCap.Round)
                )

                drawArc(
                    color = Color(0XFFFED132),
                    startAngle = degreeList.slight.proportion * 3.6F,
                    sweepAngle = degreeList.moderate.proportion * 3.6F,
                    useCenter = false,
                    topLeft = Offset((size.width - 180.dp.toPx()) / 2, 0F),
                    size = Size(size.height, size.height),
                    style = Stroke(width = 20.dp.toPx(), cap = StrokeCap.Round)
                )
            }

        }
        Column(
            modifier = Modifier.align(Alignment.Center),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(text = "${averageAngle}°", fontSize = 18.sp, color = Color(0XFF999999))
            Spacer(modifier = Modifier.height(2.dp))
            Text(
                text = stringResource(id = R.string.average_angle),
                fontSize = 12.sp,
                color = Color(0XFF999999)
            )
        }
    }
}

/**
 * PROMIS健康量表
 */
@Composable
private fun PromisHealthView(
    promisValue: SummaryValue = SummaryValue()
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val value = promisValue.mean.coerceIn(0, 80)
    var explainVisible by remember {
        mutableStateOf(false)
    }
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .padding(horizontal = 12.dp)
    ) {
        val containerMaxWidth = this.maxWidth
        val mWidth = containerMaxWidth / 8
        Column(Modifier.fillMaxWidth()) {
            /* PROMIS刻度 */
            Box(Modifier.fillMaxWidth()) {
                Text(text = "80", fontSize = fontSize10, color = Color(0XFF999999))
                for (i in 0..6) {
                    Text(
                        text = ((7 - i) * 10).toString(), modifier = Modifier
                            .padding(start = mWidth * i)
                            .width(mWidth * 2), textAlign = TextAlign.Center,
                        fontSize = fontSize10, color = Color(0XFF999999)
                    )
                }
                Text(
                    text = "0",
                    modifier = Modifier.align(Alignment.CenterEnd),
                    fontSize = fontSize10,
                    color = Color(0XFF999999)
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
            /* PROMIS渐变进度条 */
            ConstraintLayout(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(12.dp)
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0XE5FB593F),
                                Color(0XE5F17216),
                                Color(0XE5F1C114),
                                Color(0XE5D5ED28),
                                Color(0XE5A0E14F),
                                Color(0XE56EA944)
                            ).reversed()
                        )
                    )
            ) {
                val unreached = createRef()
                val dataProgressEndPoint = createGuidelineFromStart(mWidth * 8 * (80 - value) / 80)
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .background(
                            Color(0XFFE7E7E5)
                        )
                        .constrainAs(unreached) {
                            start.linkTo(dataProgressEndPoint)
                            top.linkTo(parent.top)
                            end.linkTo(parent.end)
                            bottom.linkTo(parent.bottom)
                            width = Dimension.fillToConstraints
                        }
                )
            }
            Spacer(modifier = Modifier.height(4.dp))
            Row(Modifier.fillMaxWidth()) {
                val path = Path()
                Box(
                    modifier = Modifier
                        .width(mWidth * 3)
                        .height(6.dp)
                        .drawBehind {
                            path.moveTo(0F, 0F)
                            path.lineTo(0F, size.height)
                            path.lineTo(size.width, size.height)
                            path.lineTo(size.width, 0F)
                            drawPath(
                                path,
                                color = Color(0XFF6FAA45),
                                style = Stroke(width = 0.5.dp.toPx())
                            )
                        }
                )

                Box(
                    modifier = Modifier
                        .width(mWidth)
                        .height(6.dp)
                        .drawBehind {
                            path.reset()
                            path.moveTo(0F, size.height)
                            path.lineTo(size.width, size.height)
                            path.lineTo(size.width, 0F)
                            drawPath(
                                path,
                                color = Color(0XFFFFD209),
                                style = Stroke(width = 0.5.dp.toPx())
                            )
                        }
                )

                Box(
                    modifier = Modifier
                        .width(mWidth)
                        .height(6.dp)
                        .drawBehind {
                            path.reset()
                            path.moveTo(0F, size.height)
                            path.lineTo(size.width, size.height)
                            path.lineTo(size.width, 0F)
                            drawPath(
                                path,
                                color = Color(0XFFFD6C0B),
                                style = Stroke(width = 0.5.dp.toPx())
                            )
                        }

                )

                Box(
                    modifier = Modifier
                        .width(mWidth * 3)
                        .height(6.dp)
                        .drawBehind {
                            path.reset()
                            path.moveTo(0F, size.height)
                            path.lineTo(size.width, size.height)
                            path.lineTo(size.width, 0F)
                            drawPath(
                                path,
                                color = Color(0XFFFE2B09),
                                style = Stroke(width = 0.5.dp.toPx())
                            )
                        }
                )
            }
            Spacer(modifier = Modifier.height(3.dp))
            Box(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = stringResource(id = R.string.normal),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    modifier = Modifier.width(mWidth * 3),
                    textAlign = TextAlign.Center
                )
                Text(
                    text = stringResource(id = R.string.mild),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    modifier = Modifier
                        .padding(start = mWidth * 2)
                        .width(mWidth * 3),
                    textAlign = TextAlign.Center
                )
                Text(
                    text = stringResource(id = R.string.moderate),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    modifier = Modifier
                        .padding(start = mWidth * 3)
                        .width(mWidth * 3),
                    textAlign = TextAlign.Center
                )
                Text(
                    text = stringResource(id = R.string.severe),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    modifier = Modifier
                        .padding(start = mWidth * 5)
                        .width(mWidth * 3),
                    textAlign = TextAlign.Center
                )

                Box(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .size(14.dp)
                        .pointerInput(Unit) {
                            detectTapGestures {
                                explainVisible = true
                            }
                        }
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.img_explain),
                        contentDescription = null,
                        tint = Color(0xFF6BC5C6),
                        modifier = Modifier
                            .size(14.dp)

                    )
                    var height by remember {
                        mutableIntStateOf(0)
                    }
                    if (explainVisible) {
                        Popup(
                            onDismissRequest = {
                                explainVisible = false
                            },
                            alignment = Alignment.TopEnd,
                            offset = IntOffset(14.dp.roundToPx(), -height - 1.dp.roundToPx()),
                            properties = PopupProperties(focusable = true)
                        ) {
                            Column(
                                modifier = Modifier.onSizeChanged {
                                    height = it.height
                                }
                            ) {
                                Column(
                                    modifier = Modifier
                                        .align(Alignment.CenterHorizontally)
                                        .fillMaxWidth(0.8f)
                                        .background(Color(0xFFE9E9E9), RoundedCornerShape(12.dp))
                                        .padding(horizontal = 14.dp, vertical = 24.dp)
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.promis_scoring_guideline_title),
                                        fontSize = 14.sp,
                                        color = Color(0XFF333333),
                                        modifier = Modifier.align(Alignment.CenterHorizontally)
                                    )
                                    Spacer(modifier = Modifier.height(20.dp))
                                    Text(
                                        text = stringResource(id = R.string.promis_scoring_guideline_content),
                                        fontSize = 12.sp,
                                        color = Color(0XFF666666),
                                        lineHeight = 16.8.sp
                                    )
                                }
                                Triangle(
                                    color = Color(0xFFE9E9E9), modifier = Modifier
                                        .align(Alignment.End)
                                        .padding(end = 14.dp)
                                        .rotate(180F)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * ODI健康量表
 */
@Suppress("UnusedBoxWithConstraintsScope")
@Composable
private fun OdiHealthView(
    summaryValueValue: SummaryValue = SummaryValue()
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }

    val value = summaryValueValue.mean.coerceIn(0, 100)
    var explainVisible by remember {
        mutableStateOf(false)
    }
    Row(
        Modifier
            .fillMaxWidth()
            .height(50.dp)
            .padding(horizontal = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = "0", fontSize = fontSize10, color = Color(0XFF999999))
        BoxWithConstraints(
            Modifier
                .weight(1F)
                .height(44.dp)
        ) {
            val mWidth = (maxWidth - 16.dp) / 5
            Box(
                modifier = Modifier
                    .padding(horizontal = 8.dp)
                    .fillMaxHeight()
            ) {
                Text(
                    text = "20",
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.TopStart
                        )
                        .width(mWidth * 2)
                )
                Text(
                    text = "40",
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.TopStart
                        )
                        .padding(start = mWidth)
                        .width(mWidth * 2)
                )
                Text(
                    text = "60",
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.TopStart
                        )
                        .padding(start = mWidth * 2)
                        .width(mWidth * 2)
                )
                Text(
                    text = "80",
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.TopStart
                        )
                        .padding(start = mWidth * 3)
                        .width(mWidth * 2)
                )
                Text(
                    text = stringResource(id = R.string.mild),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.BottomStart
                        )
                        .width(mWidth)
                )
                Text(
                    text = stringResource(id = R.string.moderate),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.BottomStart
                        )
                        .width(mWidth * 3)
                )
                Text(
                    text = stringResource(id = R.string.severe),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.BottomStart
                        )
                        .padding(start = mWidth)
                        .width(mWidth * 3)
                )
                Text(
                    text = stringResource(id = R.string.serious),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .align(
                            Alignment.BottomStart
                        )
                        .padding(start = mWidth * 2)
                        .width(mWidth * 3)
                )
                /* ODI进度条 */
                ConstraintLayout(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .fillMaxWidth()
                        .wrapContentHeight()
                ) {
                    val (backBar, progressBar, progressDot) = createRefs()
                    val ODIProgressData = createGuidelineFromStart(mWidth * value / 20)
                    Box(
                        modifier = Modifier
                            .height(4.dp)
                            .background(Color(0XFFCECECE))
                            .constrainAs(backBar) {
                                start.linkTo(parent.start)
                                top.linkTo(parent.top)
                                end.linkTo(parent.end)
                                bottom.linkTo(parent.bottom)
                                width = Dimension.fillToConstraints
                            }
                    )
                    Box(
                        modifier = Modifier
                            .height(4.dp)
                            .background(Color(0XFFFFBC58))
                            .constrainAs(progressBar) {
                                start.linkTo(parent.start)
                                top.linkTo(parent.top)
                                end.linkTo(ODIProgressData)
                                bottom.linkTo(parent.bottom)
                                width = Dimension.fillToConstraints
                            }
                    )
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .clip(CircleShape)
                            .shadow(2.dp)
                            .background(Color(0XFFFFBC58))
                            .constrainAs(progressDot) {
                                start.linkTo(ODIProgressData)
                                top.linkTo(parent.top)
                                end.linkTo(ODIProgressData)
                                bottom.linkTo(parent.bottom)
                            }
                    )
                }
            }
            Text(
                text = stringResource(id = R.string.bed_bound),
                fontSize = fontSize10,
                color = Color(0XFF999999),
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .align(
                        Alignment.BottomStart
                    )
                    .padding(start = mWidth * 4 + 8.dp)
                    .width(mWidth + 10.dp)
            )
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .offset(16.dp, 0.dp)
                    .size(14.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            explainVisible = true
                        }
                    }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.img_explain),
                    contentDescription = null,
                    tint = Color(0XFF6BC5C6),
                    modifier = Modifier
                        .size(14.dp)

                )
                var height by remember {
                    mutableIntStateOf(0)
                }
                if (explainVisible) {
                    Popup(
                        onDismissRequest = {
                            explainVisible = false
                        },
                        alignment = Alignment.TopEnd,
                        offset = IntOffset(14.dp.roundToPx(), -height - 1.dp.roundToPx()),
                        properties = PopupProperties(focusable = true)
                    ) {
                        Column(
                            modifier = Modifier.onSizeChanged {
                                height = it.height
                            }
                        ) {
                            Column(
                                modifier = Modifier
                                    .align(Alignment.CenterHorizontally)
                                    .fillMaxWidth(0.8f)
                                    .background(Color(0xFFE9E9E9), RoundedCornerShape(12.dp))
                                    .padding(horizontal = 14.dp, vertical = 24.dp)
                            ) {
                                Text(
                                    text = stringResource(id = R.string.odi_scoring_guideline_title),
                                    fontSize = 14.sp,
                                    color = Color(0XFF333333),
                                    modifier = Modifier.align(Alignment.CenterHorizontally)
                                )
                                Spacer(modifier = Modifier.height(20.dp))
                                Text(
                                    text = stringResource(id = R.string.odi_scoring_guideline_content),
                                    fontSize = 12.sp,
                                    color = Color(0XFF666666),
                                    lineHeight = 16.8.sp
                                )
                            }
                            Triangle(
                                color = Color(0xFFE9E9E9), modifier = Modifier
                                    .align(Alignment.End)
                                    .padding(end = 14.dp)
                                    .rotate(180F)
                            )
                        }
                    }
                }
            }
        }
        Text(text = "100", fontSize = fontSize10, color = Color(0XFF999999))
    }
}


/**
 * 本周脊柱状态
 */
@Preview()
@Composable
private fun WeekSpineStatusCard(
    deviceType: DeviceType = DeviceType.aiNeck,
    dayOfWeek: Int = 1,
    angleList: SnapshotStateList<Mean> = remember { mutableStateListOf() }
) {
    val density = LocalDensity.current
    val fontSize8 = with(density) { 8.sp / fontScale }
    AIHCard {
        Column(
            Modifier
                .fillMaxWidth()
                .padding(top = 12.dp)
                .padding(horizontal = 8.dp),
        ) {
            Text(
                text = stringResource(id = if (deviceType == DeviceType.aiBack || deviceType == DeviceType.aiBackCV) R.string.spine_status_this_week else R.string.neck_status_this_week),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0XFF505050),
                modifier = Modifier
            )
            Column(
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(12.dp))
                /* 柱状图宽间距 */
                val mWidth = with(density) {
                    36.dp.toPx()
                }
                Box(
                    modifier = Modifier
                        .size(301.5.dp, 118.dp)
                        .fillMaxWidth(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = R.string.average_angle),
                        fontSize = fontSize8,
                        color = Color(0XFF999999),
                        modifier = Modifier
                            .align(Alignment.TopStart)
                            .offset((-10).dp)
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_report_week_spine_status),
                        contentDescription = null,
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(top = 16.dp),
                        contentScale = ContentScale.FillBounds
                    )

                    Canvas(
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(start = 44.dp, top = 16.dp, bottom = 8.5.dp)
                            .height(98.dp)
                    ) {
                        var x = 0F
                        angleList.forEach {
                            if (it.mean > 60) {
                                drawLine(
                                    Color(0XFFE97E60),
                                    start = Offset(x - 20f, size.height),
                                    end = Offset(x - 20f, 0F),
                                    strokeWidth = 8.dp.toPx(),
                                    cap = StrokeCap.Round
                                )
                            } else if (it.mean > 0) {
                                drawLine(
                                    Color(0XFFC4C4C4),
                                    start = Offset(x - 20f, size.height),
                                    end = Offset(
                                        x - 20f,
                                        size.height - 29.dp.toPx() * it.mean / 20
                                    ),
                                    strokeWidth = 8.dp.toPx(),
                                    cap = StrokeCap.Round
                                )
                            }
                            x += mWidth
                        }
                    }
                }
                Box(modifier = Modifier.padding(start = 30.dp)) {
                    val weeks = stringArrayResource(id = R.array.week)
                    val firstWeekDay by remember {
                        derivedStateOf { dayOfWeek }
                    }
                    weeks.forEachIndexed { index, _ ->
                        Text(
                            text = weeks[(index + firstWeekDay) % 7],
                            fontSize = fontSize8,
                            color = Color(0XFF666666),
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .padding(start = 40.dp * index)
                                .width(80.dp)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
        }
    }
}

/**
 * 日，周各程度占比表
 */
@Composable
private fun RadioTable(
    degreeList: SeverityProportion = SeverityProportion()
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.dp.toSp() }
    Column(
        Modifier
            .fillMaxWidth()
            .padding(top = 32.dp, bottom = 16.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp)
        ) {
            Spacer(modifier = Modifier.weight(1f))
            Text(
                text = stringResource(id = R.string.level),
                fontSize = fontSize14,
                color = Color(0XFF999999),
                fontWeight = FontWeight.Medium,
                modifier = Modifier
//                    .padding(start = 36.dp)
//                    .width(60.dp)
                    .weight(3f),
            )
            Text(
                text = stringResource(id = R.string.proportion_of),
                fontSize = fontSize14,
                color = Color(0XFF999999),
                fontWeight = FontWeight.Medium,
                modifier = Modifier
//                    .padding(start = 10.dp)
//                    .width(60.dp)
                    .weight(5f),
            )
            Text(
                text = stringResource(id = R.string.wearing_time),
                fontSize = fontSize14,
                color = Color(0XFF999999),
                fontWeight = FontWeight.Medium,
                modifier = Modifier
//                    .width(98.dp)
                    .weight(4f),
            )
            Text(
                text = stringResource(id = R.string.report_angle),
                fontSize = fontSize14,
                color = Color(0XFF999999),
                fontWeight = FontWeight.Medium,
                modifier = Modifier
//                    .width(52.dp)
                    .weight(3f),
            )
        }
        RadioTableItem(
            Color(0XFF5ECCB1),
            stringResource(id = R.string.mild),
            degreeList.slight.proportion,
            degreeList.slight.duration,
            "0°-15°"
        )
        AIHDivider(modifier = Modifier.padding(start = 32.dp, end = 29.dp))
        RadioTableItem(
            Color(0XFFFED132),
            stringResource(id = R.string.moderate),
            degreeList.moderate.proportion,
            degreeList.moderate.duration,
            "15°-30°"
        )
        AIHDivider(modifier = Modifier.padding(start = 32.dp, end = 29.dp))
        RadioTableItem(
            Color(0XFFF57E43),
            stringResource(id = R.string.severe),
            degreeList.severe.proportion,
            degreeList.severe.duration,
            "30°-90°"
        )
    }
}

/* 角度量表子项 */
@Composable
private fun RadioTableItem(
    color: Color,
    level: String,
    radio: Int,
    time: String,
    angle: String
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.dp.toSp() }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Canvas(
            modifier = Modifier
                .wrapContentSize()
                .weight(1f),
            onDraw = {
                drawCircle(
                    color = color,
                    radius = 12f
                )
            }
        )
        Text(
            text = level,
            fontSize = fontSize14,
            fontWeight = FontWeight.Medium,
            color = color,
            modifier = Modifier
                .padding(horizontal = 12.dp)
                .weight(4f)
        )
        Text(
            text = radio.toString(),
            fontSize = fontSize14,
            fontWeight = FontWeight.Medium,
            color = color,
            modifier = Modifier
                .weight(2f),
            textAlign = TextAlign.Center
        )
        Text(
            text = time,
            fontSize = fontSize14,
            fontWeight = FontWeight.Medium,
            color = Color(0XFF333333),
            modifier = Modifier
                .weight(4f),
            textAlign = TextAlign.Center
        )

        Text(
            text = angle,
            fontSize = fontSize14,
            fontWeight = FontWeight.Medium,
            color = color,
            modifier = Modifier
                .weight(4f),
            textAlign = TextAlign.Center,
            maxLines = 1
        )
    }
}

/**
 * 日报日历组件
 */
@Composable
private fun DayCalendar(
    todayCalendar: Calendar,
    selectCalendar: Calendar,
    onClick: (Int) -> Unit
) {
    /**
    var dayPageIndex by remember {
    mutableIntStateOf(0)
    }
    val state = rememberSwipeableState(initialValue = CalendarStateValue.Normal) {
    if (it == CalendarStateValue.Next) {

    dayPageIndex++
    } else if (it == CalendarStateValue.Pre) {
    dayPageIndex--
    }
    return@rememberSwipeableState true
    }
    val bitmap = Bitmap.createBitmap(290.dp.roundToPx(), 50.dp.roundToPx(), Bitmap.Config.ARGB_8888)
    val preBitmap = getDayCalendarBitmap(bitmap, selectCalendar, todayCalendar, dayPageIndex - 1)
    val currentBitmap = getDayCalendarBitmap(bitmap, selectCalendar, todayCalendar, dayPageIndex)
    val nextBitmap = getDayCalendarBitmap(bitmap, selectCalendar, todayCalendar, dayPageIndex + 1)
    val anchors = mapOf(
    Pair(290.dp.toPx(), CalendarStateValue.Pre),
    Pair(0F, CalendarStateValue.Normal),
    Pair(-290.dp.toPx(), CalendarStateValue.Next)
    )
    Row(
    Modifier
    .fillMaxWidth()
    .padding(horizontal = 16.dp), verticalAlignment = Alignment.CenterVertically
    ) {
    Image(
    painter = painterResource(id = R.drawable.img_report_calender_pre),
    contentDescription = null,
    modifier = Modifier.size(10.dp)
    )
    Image(
    painter = painterResource(id = R.drawable.img_report_calender_next),
    contentDescription = null,
    modifier = Modifier.size(10.dp)
    )
    }
     */
}


private enum class CalendarStateValue {
    Pre, Normal, Next
}

private fun getDayCalendarBitmap(
    bitmap: Bitmap,
    selectedDay: Calendar,
    todayCalendar: Calendar,
    pageIndex: Int
): ImageBitmap {
    val mBitmap = Bitmap.createBitmap(bitmap)
    val canvas = android.graphics.Canvas(mBitmap)
    val calendar = todayCalendar.clone() as Calendar
    calendar.add(Calendar.DATE, pageIndex * 7 - calendar.firstDayOfWeek)
    val week = localeResources.getStringArray(R.array.week2)
    val paint = Paint()
    paint.textSize = 10.dp.toPx()
    paint.textAlign = Paint.Align.CENTER
    paint.isAntiAlias = true

    val backgroundPaint = Paint().apply { color = android.graphics.Color.parseColor("#FFBDCDE6") }
    val mWidth = 290.dp.toPx() / 7
    var x = mWidth / 2
    repeat(7) {
        if (selectedDay.timeInMillis == calendar.timeInMillis) {
            canvas.drawRoundRect(
                x - 13.dp.toPx(),
                0F,
                x + 13.dp.toPx(),
                50.dp.toPx(),
                13.dp.toPx(),
                13.dp.toPx(),
                backgroundPaint
            )
            paint.color = android.graphics.Color.WHITE
            paint.isFakeBoldText = true
        } else if (calendar.timeInMillis == todayCalendar.timeInMillis) {
            paint.color = android.graphics.Color.parseColor("#FF3A57CE")
            paint.isFakeBoldText = false
        } else {
            paint.color = android.graphics.Color.parseColor("#FF999999")
            paint.isFakeBoldText = false
        }
        canvas.drawText(week[it % 7], x, 18.dp.toPx(), paint)
        canvas.drawText(calendar.get(Calendar.DATE).toString(), x, 36.dp.toPx(), paint)
        x += mWidth
        calendar.add(Calendar.DATE, 1)
    }
    return mBitmap.asImageBitmap()
}

/**
 * 当月体姿监测数据
 */
@Composable
private fun MonthPostureData(
    month: String,
    anglesHandle: AnglesHandle
) {
    AIHCard {
        Column(
            Modifier.padding(vertical = 16.dp, horizontal = 28.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(
                    id = R.string.posture_data,
                    month
                ),
                fontSize = 20.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0XFF333333)
            )
            Spacer(modifier = Modifier.height(20.dp))
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = anglesHandle.wearing_time ?: "00:00:00",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF444444)
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = stringResource(id = R.string.time_worning_month),
                        fontSize = if (currentLocale == Locale.ENGLISH) 11.sp else 12.sp,
                        color = Color(0XFF666666)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = anglesHandle.average_angle.toString() + "°",
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF444444)
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = stringResource(id = R.string.average_angle),
                        fontSize = if (currentLocale == Locale.ENGLISH) 11.sp else 12.sp,
                        color = Color(0XFF666666)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(horizontalAlignment = Alignment.CenterHorizontally) {
                    Text(
                        text = stringArrayResource(id = R.array.report_month_status)[anglesHandle.body_posture_state - 1],
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Medium,
                        color = when (anglesHandle.body_posture_state) {
                            1 -> {
                                Color(0xFF5ECCB1)
                            }

                            2 -> {
                                Color(0xFFFED132)
                            }

                            3 -> {
                                Color(0XFFF57E43)
                            }

                            else -> {
                                Color(0XFF444444)
                            }
                        }
                    )
                    Spacer(modifier = Modifier.height(10.dp))
                    Text(
                        text = stringResource(id = R.string.status_of_month),
                        fontSize = if (currentLocale == Locale.ENGLISH) 11.sp else 12.sp,
                        color = Color(0XFF666666)
                    )
                }
            }
        }
    }
}

/**
 * 当月Promis健康量表数值状态
 */
@Preview()
@Composable
private fun MonthPromisIndexStatus(
    month: String = "9",
    recordScales: RecordScales = RecordScales(),
    list: List<ChartPoint> = listOf()
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    AIHCard {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp)
                .padding(top = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.month_promis_health_status, month),
                fontSize = 16.sp,
                color = Color(0XFF333333),
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
            )
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 25.dp, bottom = 15.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .padding(start = 3.dp, end = 10.dp)
                        .size(6.dp)
                        .background(Color(0XFFEECA6E), CircleShape)
                )
                Text(
                    text = stringResource(id = R.string.month_promis_health_index_status),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    fontWeight = FontWeight.Bold,
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
            ) {
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(3F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = recordScales.promis.mean.toString(),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF1D1D1D)
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.average_value),
                        fontSize = 12.sp,
                        color = Color(0XFF999999)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(3F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = recordScales.promis.max.toString(),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF1D1D1D)
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.maximum_value),
                        fontSize = 12.sp,
                        color = Color(0XFF999999)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(3F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = recordScales.promis.min.toString(),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF1D1D1D)
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.minimum_value),
                        fontSize = 12.sp,
                        color = Color(0XFF999999)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
            }
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 25.dp, bottom = 15.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .padding(start = 3.dp, end = 10.dp)
                        .size(6.dp)
                        .background(Color(0XFFEECA6E), CircleShape)
                )
                Text(
                    text = stringResource(id = R.string.weekly_health_tren_chart),
                    fontSize = 16.sp,
                    color = Color(0XFF666666),
                    fontWeight = FontWeight.Bold,
                )
            }
            Text(
                text = stringResource(id = R.string.numerical_interval),
                fontSize = 12.sp,
                color = Color(0XFF999999)
            )
//            Text(text = "......", fontSize = fontSize10, color = Color(0XFF666666))
            Row(
                modifier = Modifier
                    .height(180.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                Column(horizontalAlignment = Alignment.End) {
                    Text(text = "80", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "60", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "40", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "20", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "0", fontSize = fontSize12, color = Color(0XFF666666))
                }
                Spacer(modifier = Modifier.width(3.dp))
                Row(
                    Modifier
                        .align(Alignment.CenterVertically)
                        .height(168.dp)
                ) {
                    Box(modifier = Modifier.size(260.dp, 168.dp)) {
                        Image(
                            painter = painterResource(id = R.drawable.img_weekly_health_tren_chart_promis),
                            contentDescription = null,
                            modifier = Modifier
                                .size(260.dp, 168.dp)
                        )
                        val textMeasured = rememberTextMeasurer()
                        Canvas(modifier = Modifier.fillMaxSize()) {
                            val offset1 =
                                Offset(
                                    52.dp.toPx(),
                                    ((80 - (list.getOrNull(0)?.mean_ ?: 0)) * 168.dp.toPx() / 80)
                                )
                            val offset2 =
                                Offset(
                                    104.dp.toPx(),
                                    (80 - (list.getOrNull(1)?.mean_ ?: 0)) * 168.dp.toPx() / 80
                                )
                            val offset3 =
                                Offset(
                                    156.dp.toPx(),
                                    (80 - (list.getOrNull(2)?.mean_ ?: 0)) * 168.dp.toPx() / 80
                                )
                            val offset4 =
                                Offset(
                                    208.dp.toPx(),
                                    (80 - (list.getOrNull(3)?.mean_ ?: 0)) * 168.dp.toPx() / 80
                                )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset1
                            )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset2
                            )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset3
                            )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset4
                            )
                            val path = Path()
                            path.moveTo(offset1.x, offset1.y)
                            path.lineTo(offset2.x, offset2.y)
                            path.lineTo(offset3.x, offset3.y)
                            path.lineTo(offset4.x, offset4.y)
                            drawPath(
                                path = path,
                                color = Color.White,
                                style = Stroke(width = 2.dp.toPx())
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(0)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset1.x - 14f, offset1.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(1)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset2.x - 14f, offset2.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(2)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset3.x - 14f, offset3.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(3)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset4.x - 14f, offset4.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                        }
                    }

                    Spacer(modifier = Modifier.width(3.dp))
                    Column(Modifier.fillMaxHeight()) {
                        Box(
                            modifier = Modifier.height(63.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(id = R.string.normal),
                                fontSize = fontSize10,
                                color = Color(0XFF7AAF55)
                            )

                        }
                        Box(
                            modifier = Modifier.height(21.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(id = R.string.mild),
                                fontSize = fontSize10,
                                color = Color(0XFFC0C326),
                            )
                        }
                        Box(
                            modifier = Modifier.height(21.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(id = R.string.moderate),
                                fontSize = fontSize10,
                                color = Color(0XFFE9BA16),
                            )
                        }
                        Box(
                            modifier = Modifier.height(63.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(id = R.string.severe),
                                fontSize = fontSize10,
                                color = Color(0XFFF0902B),
                            )

                        }
                    }
                }

            }
            Box(
                modifier = Modifier
                    .padding(start = 17.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.first_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.width(104.dp)
                )
                Text(
                    text = stringResource(id = R.string.second_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(start = 52.dp)
                        .width(104.dp)
                )
                Text(
                    text = stringResource(id = R.string.third_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(start = 104.dp)
                        .width(104.dp)
                )
                Text(
                    text = stringResource(id = R.string.fourth_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(start = 156.dp)
                        .width(104.dp)
                )
            }
        }
    }
}

/**
 * 当月Odi健康量表数值状态
 */
@Preview
@Composable
private fun MonthOdiIndexStatus(
    month: String = "9",
    recordScales: RecordScales = RecordScales(),
    list: List<ChartPoint> = listOf()
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    AIHCard {
        Column(
            modifier = Modifier
                .padding(horizontal = 8.dp)
                .padding(top = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.month_odi_health_status, month),
                fontSize = 16.sp,
                color = Color(0XFF333333),
                fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally),
            )
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 25.dp, bottom = 15.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .padding(start = 3.dp, end = 10.dp)
                        .size(6.dp)
                        .background(Color(0XFF6BC5C6), CircleShape)
                )
                Text(
                    text = stringResource(id = R.string.month_odi_health_index_status),
                    fontSize = 14.sp,
                    color = Color(0XFF666666),
                    fontWeight = FontWeight.Bold,
                )
            }
            Row(
                verticalAlignment = Alignment.CenterVertically, modifier = Modifier
                    .fillMaxWidth()
            ) {
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(3F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = recordScales.odi.mean.toString(),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF1D1D1D)
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.average_value),
                        fontSize = fontSize12,
                        color = Color(0XFF999999)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(3F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = recordScales.odi.max.toString(),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF1D1D1D)
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.maximum_value),
                        fontSize = fontSize12,
                        color = Color(0XFF999999)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
                Column(
                    modifier = Modifier.weight(3F),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = recordScales.odi.min.toString(),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF1D1D1D)
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Text(
                        text = stringResource(id = R.string.minimum_value),
                        fontSize = fontSize12,
                        color = Color(0XFF999999)
                    )
                }
                Spacer(modifier = Modifier.weight(1F))
            }
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 25.dp, bottom = 15.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .padding(start = 3.dp, end = 10.dp)
                        .size(6.dp)
                        .background(Color(0XFF6BC5C6), CircleShape)
                )
                Text(
                    text = stringResource(id = R.string.weekly_health_tren_chart),
                    fontSize = 16.sp,
                    color = Color(0XFF666666),
                    fontWeight = FontWeight.Bold,
                )
            }
            Text(
                text = stringResource(id = R.string.numerical_interval),
                fontSize = fontSize12,
                color = Color(0XFF999999)
            )
//            Text(text = "......", fontSize = fontSize10, color = Color(0XFF666666))
            Row(
                modifier = Modifier
//                    .fillMaxWidth()
                    .height(180.dp)
            ) {
                Column(horizontalAlignment = Alignment.End) {
                    Text(text = "100", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "80", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "60", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "40", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "20", fontSize = fontSize12, color = Color(0XFF666666))
                    Spacer(modifier = Modifier.weight(1F))
                    Text(text = "0", fontSize = fontSize12, color = Color(0XFF666666))
                }
                Spacer(modifier = Modifier.width(3.dp))
                Row(
                    Modifier
                        .align(Alignment.CenterVertically)
//                        .fillMaxWidth()
                        .height(160.dp)
                ) {
                    Box(modifier = Modifier.size(260.dp, 160.dp)) {
                        Image(
                            painter = painterResource(id = R.drawable.img_weekly_health_tren_chart_odi),
                            contentDescription = null,
                            modifier = Modifier
                                .size(260.dp, 160.dp)
                        )
                        val textMeasured = rememberTextMeasurer()
                        Canvas(modifier = Modifier.fillMaxSize()) {
                            val offset1 =
                                Offset(
                                    52.dp.toPx(),
                                    (100 - (list.getOrNull(0)?.mean_ ?: 0)) * 160.dp.toPx() / 100
                                )
                            val offset2 =
                                Offset(
                                    104.dp.toPx(),
                                    (100 - (list.getOrNull(1)?.mean_ ?: 0)) * 160.dp.toPx() / 100
                                )
                            val offset3 =
                                Offset(
                                    156.dp.toPx(),
                                    (100 - (list.getOrNull(2)?.mean_ ?: 0)) * 160.dp.toPx() / 100
                                )
                            val offset4 =
                                Offset(
                                    208.dp.toPx(),
                                    (100 - (list.getOrNull(3)?.mean_ ?: 0)) * 160.dp.toPx() / 100
                                )

                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset1
                            )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset2
                            )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset3
                            )
                            drawCircle(
                                color = Color.White,
                                radius = 3.dp.toPx(),
                                center = offset4
                            )
                            val path = Path()
                            path.moveTo(offset1.x, offset1.y)
                            path.lineTo(offset2.x, offset2.y)
                            path.lineTo(offset3.x, offset3.y)
                            path.lineTo(offset4.x, offset4.y)
                            drawPath(
                                path = path,
                                color = Color.White,
                                style = Stroke(width = 2.dp.toPx())
                            )
                            /* 在各点上绘制数据文字 */
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(0)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset1.x - 14f, offset1.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(1)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset2.x - 14f, offset2.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(2)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset3.x - 14f, offset3.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                            drawText(
                                textMeasurer = textMeasured,
                                text = (list.getOrNull(3)?.mean_
                                    ?: 0).let { if (it < 0) "N/A" else it.toString() },
                                topLeft = Offset(offset4.x - 14f, offset4.y - 48f),
                                style = TextStyle(
                                    color = Color(0x80333333),
                                    fontSize = fontSize10,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Light
                                )
                            )
                        }
                    }

                    Spacer(modifier = Modifier.width(3.dp))
                    Column(Modifier.fillMaxHeight()) {
                        Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                            Text(
                                text = stringResource(id = R.string.bed_bound),
                                fontSize = fontSize10,
                                color = Color(0XFFF96750)
                            )
                        }
                        Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                            Text(
                                text = stringResource(id = R.string.serious),
                                fontSize = fontSize10,
                                color = Color(0XFFF0902B),
                                lineHeight = 21.sp
                            )
                        }
                        Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                            Text(
                                text = stringResource(id = R.string.severe),
                                fontSize = fontSize10,
                                color = Color(0XFFF0902B),
                                lineHeight = 21.sp
                            )
                        }
                        Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                            Text(
                                text = stringResource(id = R.string.moderate),
                                fontSize = fontSize10,
                                color = Color(0XFFC0C326),
                                lineHeight = 63.sp
                            )
                        }
                        Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                            Text(
                                text = stringResource(id = R.string.mild),
                                fontSize = fontSize10,
                                color = Color(0XFF7AAF55),
                                lineHeight = 63.sp
                            )
                        }
                    }
                }

            }
            Box(
                modifier = Modifier
                    .padding(start = 20.dp)
//                    .fillMaxWidth()
            ) {
                Text(
                    text = stringResource(id = R.string.first_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.width(104.dp)
                )
                Text(
                    text = stringResource(id = R.string.second_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(start = 52.dp)
                        .width(104.dp)
                )
                Text(
                    text = stringResource(id = R.string.third_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(start = 104.dp)
                        .width(104.dp)
                )
                Text(
                    text = stringResource(id = R.string.fourth_week),
                    fontSize = fontSize10,
                    color = Color(0XFF999999),
                    textAlign = TextAlign.Center,
                    modifier = Modifier
                        .padding(start = 156.dp)
                        .width(104.dp)
                )
            }
        }
    }
}