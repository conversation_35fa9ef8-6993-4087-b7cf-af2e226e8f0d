package org.aihealth.ineck.view.custom

import android.view.View
import android.view.ViewTreeObserver
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mediapipe.tasks.vision.core.RunningMode
import org.aihealth.ineck.R
import org.aihealth.ineck.mediapipe.PoseLandmarkerHelper
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit

/**
 *  摄像头画面浮动窗口
 *  @param  modifier    修饰符参数
 *  @param  isShowMask  遮罩显示状态
 *  @param  isBootCameraPreview 开启Camera Preview画面输出状态
 *  @param  lifecycleOwner  生命周期拥有者
 *  @param  imageAnalysis   图像分析类
 *  @param  onChangeBootCameraPreviewEvent  Camera Preview画面输出状态变更事件
 *  @param  onCloseEvent    关闭当前浮动窗口事件
 *
 */
@Composable
fun CameraFloatWidget(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
    isShowMask: Boolean,
    isBootCameraPreview: Boolean,
    lifecycleOwner: LifecycleOwner,
    imageAnalysis: ImageAnalysis.Analyzer,
    onChangeBootCameraPreviewEvent: () -> Unit,
    onCloseEvent: () -> Unit,
) {
    val context = LocalContext.current

    /** 摄像头相关 */
    val cameraProvider = ProcessCameraProvider.getInstance(context).get()
    DisposableEffect(Unit) {
        onDispose {
            cameraProvider.unbindAll()
        }
    }
    Box(
        modifier = modifier
            .size(120.dp, 180.dp)
            .clip(RoundedCornerShape(8.dp))
            .shadow(2.dp)
    ) {
        Image(
            painter = painterResource(id = R.drawable.float_window_background_for_aineck),
            contentDescription = "window of floating background",
            modifier = Modifier
                .padding(6.dp)
                .fillMaxSize()
                .clip(RoundedCornerShape(8.dp))
                .align(Alignment.Center)
        )
        AndroidView(
            factory = { context ->
                val previewView = PreviewView(context)
                val preview = Preview.Builder().build()
                /* 摄像头选择 */
                val selector = CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                    .build()

                preview.setSurfaceProvider(previewView.surfaceProvider)
                /* 自定义图像分析对象 */
                val faceDetectAnalysis = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                    .build()

                faceDetectAnalysis.setAnalyzer(
                    ContextCompat.getMainExecutor(context),
                    imageAnalysis
                )
                try {
                    cameraProvider.unbindAll()
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner, selector, preview, faceDetectAnalysis
                    )
                } catch (e : Exception) {
                    e.printStackTrace()
                }
                previewView
            },
            update = { view ->
                /* 由isBootCameraPreview的值判断是否对 */
                view.apply {
                    if (isBootCameraPreview) {
                        this.visibility = View.VISIBLE
                    } else this.visibility = View.INVISIBLE
                }
            },
            modifier = Modifier
                .padding(6.dp)
                .clip(RoundedCornerShape(8.dp))
        )
        /** 自定义内容 */
        content()
        AnimatedVisibility(
            visible = isShowMask,
            enter = fadeIn(tween(400)),
            exit = fadeOut(tween(400))
        ) {
            /* 遮罩 */
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(6.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color.Black.copy(alpha = .4f))
                    .align(Alignment.Center),
                contentAlignment = Alignment.Center
            ) {
                Button(
                    onClick = { onChangeBootCameraPreviewEvent() },
                    modifier = Modifier
                        .height(24.dp)
                        .background(Color.Transparent),
                    contentPadding = PaddingValues(0.dp),
                    colors = ButtonDefaults.buttonColors(Color.White),
                    shape = RoundedCornerShape(18.dp)
                ) {
                    Text(
                        text = if (isBootCameraPreview) {
                            stringResource(id = R.string.close_video)
                        } else {
                            stringResource(id = R.string.open_video)
                        },
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight.Light,
                            textAlign = TextAlign.Center,
                            color = Color.Black
                        ),
                        modifier = Modifier.padding(horizontal = 4.dp)
                    )
                }
            }
        }
        if (isShowMask) {
            IconButton(
                onClick = { onCloseEvent() },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .offset(4.dp, (-4).dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_window_close),
                    contentDescription = "Close Floating Window",
                    tint = Color(0x80FFFFFF),
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}


@Composable
fun AiBackCameraFloatWidget(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
    isShowMask: Boolean,
    isBootCameraPreview: Boolean,
    lifecycleOwner: LifecycleOwner,
    listener: PoseLandmarkerHelper.LandmarkerListener,
    onChangeBootCameraPreviewEvent: () -> Unit,
    onCloseEvent: () -> Unit,
) {
    /** 上下文 */
    val context = LocalContext.current

    /** 摄像头相关 */
    val cameraProvider = ProcessCameraProvider.getInstance(context).get()
    val backgroundExecutor = remember { Executors.newSingleThreadExecutor() }
    val poseLandmarkerHelper = remember {
        PoseLandmarkerHelper(
            context = context,
            runningMode = RunningMode.LIVE_STREAM,
            poseLandmarkerHelperListener = listener
        )
    }
    DisposableEffect(Unit) {
        onDispose {
            cameraProvider.unbindAll()
            backgroundExecutor.shutdown()
            backgroundExecutor.awaitTermination(
                Long.MAX_VALUE, TimeUnit.NANOSECONDS
            )
        }
    }
    Box(
        modifier = modifier
            .size(120.dp, 180.dp)
            .clip(RoundedCornerShape(8.dp))
            .shadow(2.dp)
    ) {
        Image(
            painter = painterResource(id = R.drawable.float_window_background_for_aineck),
            contentDescription = "window of floating background",
            modifier = Modifier
                .padding(6.dp)
                .fillMaxSize()
                .clip(RoundedCornerShape(8.dp))
                .align(Alignment.Center)
        )
        AndroidView(
            factory = { context ->
                val previewView = PreviewView(context)
                val preview = Preview.Builder()
                    .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                    .build()
                previewView.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        preview.setTargetRotation(previewView.display.rotation)
                        previewView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                    }
                })
                /* 摄像头选择 */
                val selector = CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                    .build()

                /* 自定义图像分析对象 */
                val backAnalyzer =
                    ImageAnalysis.Builder()
                        .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                        .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                        .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888)
                        .build().also {
                            it.setAnalyzer(backgroundExecutor) { image ->
                                poseLandmarkerHelper.detectLiveStream(
                                    imageProxy = image,
                                    isFrontCamera = true
                                )
                            }
                        }

                try {
                    cameraProvider.unbindAll()
                    cameraProvider.bindToLifecycle(
                        lifecycleOwner, selector, preview, backAnalyzer
                    )
                    preview.setSurfaceProvider(previewView.surfaceProvider)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                previewView
            },
            update = { view ->
                /* 由isBootCameraPreview的值判断是否对 */
                view.apply {
                    if (isBootCameraPreview) {
                        this.visibility = View.VISIBLE
                    } else this.visibility = View.INVISIBLE
                }
            },
            modifier = Modifier
                .padding(6.dp)
                .clip(RoundedCornerShape(8.dp))
        )
        /** 自定义内容 */
        content()

    }
}