package org.aihealth.ineck.view.directions

import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.angles.Odi

class OdiRecordDirections {
    data class OdiRecordArgs(
        val model: OdiRecordModel
    )

    @Parcelize
    data class OdiRecordModel(
        val isPromis: Boolean = false,
        val showToggleButton: Boolean = true,
        val baseTime: Odi?
    ) : Parcelable

    companion object {
        val route = "${Screen.OdiHisRecord.route}?model={model}"
        val gson = Gson()

        val argumentsList:MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model"){
                   type = object : NavType<OdiRecordModel>(false){
                       override val name: String
                           get() = "OdiRecordModel"

                       override fun get(bundle: Bundle, key: String): OdiRecordModel? {
                            return bundle.getParcelable(key)
                       }

                       override fun parseValue(value: String): OdiRecordModel {
                           return gson.fromJson(value, object : TypeToken<OdiRecordModel>(){}.type)
                       }

                       override fun put(bundle: Bundle, key: String, value: OdiRecordModel) {
                           bundle.putParcelable(key,value)
                       }
                   }
                }
            )
        fun parseArguments(backStackEntry: androidx.navigation.NavBackStackEntry): OdiRecordArgs {
            return OdiRecordArgs(
                model = backStackEntry.arguments?.getParcelable<OdiRecordModel>("model")!!
            )
        }
        fun actionToOdiPromisRecord(model: OdiRecordModel):String{
            return Screen.OdiHisRecord.route+"?model=${android.net.Uri.encode(gson.toJson(model))}"
        }

    }
}