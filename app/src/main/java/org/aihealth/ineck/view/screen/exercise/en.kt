package org.aihealth.ineck.view.screen.exercise

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SuggestionChip
import androidx.compose.material3.SuggestionChipDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.model.improvement.toImprovementDetailModel
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.view.custom.AIHCard
import org.aihealth.ineck.view.directions.ImprovementDetailDirections
import org.aihealth.ineck.viewmodel.impl.ImprovementCardProgramsType

val textStyleEnThin = TextStyle(
    fontSize = 10.sp,
    fontWeight = FontWeight.Normal,
    color = Color(0xFF6B4B14),

    letterSpacing = 0.35.sp,
)
val textStyleEnSmall = TextStyle(
    fontSize = 12.sp,
    fontWeight = FontWeight.Normal,
    color = Color(0xFF3C2200),
    letterSpacing = 0.42.sp,
)
val textStyleEnNormal = TextStyle(
    fontSize = 13.sp,
    fontWeight = FontWeight.Normal,
    color = Color(0xFF333333),
    letterSpacing = 0.46.sp,
)
val textStyleEnMedium = TextStyle(
    fontSize = 14.sp,
    fontWeight = FontWeight.SemiBold,
    color = Color(0xFF333333),
    letterSpacing = 0.49.sp,
)

/**
 * en
 * 锻炼页的会员信息模块，在用户已经是会员时显示
 * @param vipHelloString ex:Hi，aiSpine会员
 * @param vipDescriptionString ex:开启健康生活，随时随地改善预防颈椎病
 * @param descriptionString ex: 会员有效期至:2023.11.2
 * @param nextDescriptionString 跳转说明
 * @param onclick:()->Unit 点击跳转到会员页面
 */
@Composable
fun VipMessageCardEn(
    vipHelloString: String,
    vipDescriptionString: String,
    descriptionString: String,
    nextDescriptionString: String,
    onclick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .paint(
                painterResource(id = R.drawable.vip_header_bg)
            ),
        verticalArrangement = Arrangement.Top,
    ) {
        Text(
            modifier = Modifier.padding(top = 18.dp, start = 16.dp),
            text = vipHelloString,
            style = textStyleEnMedium,
        )

        Text(
            modifier = Modifier.padding(start = 16.dp),
            text = vipDescriptionString,
            style = textStyleEnNormal,
            maxLines = 3,
        )
        Spacer(modifier = Modifier.size(8.dp))
        Box(
            modifier = Modifier
                .padding(start = 10.dp, end = 15.5.dp)
                .fillMaxWidth()
                .clickable(
                    onClick = { onclick() },
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ),
        ) {
            Text(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = 10.dp),
                text = descriptionString,
                style = textStyleEnSmall,
            )
            Row(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .paint(
                        painterResource(id = R.drawable.vip_in_bg),
                    ),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Text(
                    modifier = Modifier.padding(start = 18.dp),
                    text = nextDescriptionString,
                    style = textStyleEnThin,
                )
                Image(
                    modifier = Modifier.padding(end = 11.dp),
                    painter = painterResource(id = R.drawable.ic_next_step),
                    contentDescription = "next step",
                )
            }

        }
    }
}

/**
 * en
 * 锻炼页的会员信息模块，在用户没有会员时显示
 * @param vipHelloString ex:Hi,成为会员
 * @param vipDescriptionString ex:开启健康生活，随时随地改善预防颈椎病
 * @param descriptionString ex: 新人特惠，海量课程30天免费试用
 * @param onclick:()->Unit 点击跳转到会员页面
 */
@Composable
fun VipMessageCardNoVipEn(
    vipHelloString: String,
    vipDescriptionString: String,
    descriptionString: String,
    onclick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()

    ) {
        Image(
            modifier = Modifier.fillMaxWidth(),
            painter = painterResource(id = R.drawable.vip_header_bg),
            contentScale = ContentScale.Crop,
            contentDescription = "background image"
        )
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            verticalArrangement = Arrangement.Top,
        ) {

            Text(
                modifier = Modifier
                    .padding(top = 18.dp, start = 16.dp)
                    .background(Color.Unspecified),
                text = vipHelloString,
                style = textStyleEnMedium,
            )

            Text(
                modifier = Modifier
                    .padding(start = 16.dp)
                    .background(Color.Unspecified),
                text = vipDescriptionString,
                style = textStyleEnNormal,
            )
            Spacer(modifier = Modifier.size(13.dp))
            Row(
                modifier = Modifier
                    .padding(start = 10.dp, end = 10.dp)
                    .fillMaxWidth()
                    .background(Color.Unspecified)
                    .clickable(
                        onClick = { onclick() },
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Text(
                    modifier = Modifier
                        .width(247.dp)
                        .padding(start = 10.dp, top = 6.dp)
                        .background(Color.Unspecified),
                    text = descriptionString,
                    style = textStyleEnSmall
                )
                Image(
                    modifier = Modifier
                        .padding(end = 50.dp, top = 12.dp),
                    painter = painterResource(id = R.drawable.ic_next_step),
                    contentDescription = "next step",
                )
            }
        }
    }

}

/**
 * en
 * 改善方案项目列表卡片
 * @param vipContent: @Composable () -> Unit 会员信息
 * @param lessonContent: @Composable () -> Unit 课程信息展示
 */
@Composable
fun ImprovementProgramCardsWithVipEn(
    vipContent: @Composable () -> Unit,
    lessonContent: @Composable () -> Unit,
) {
    val context = LocalContext.current
    /* “Vip 专享” 与 ”针对性改善“ Tab字体颜色 */
    val vipExclusive = buildAnnotatedString {
        withStyle(
            SpanStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight.SemiBold,
                color = Color(0xFFF8C847),
            )
        ) {
            append("VIP exclusive · ")
        }
    }
    val tagExclusive = buildAnnotatedString {
        withStyle(
            SpanStyle(
                fontSize = 12.sp,
                color = Color(0xFF999999),
                fontWeight = FontWeight.SemiBold,
            )
        ) {
            append(context.getString(R.string.targeted_improvement))
        }

    }
    Column(
        modifier = Modifier
            .fillMaxWidth(),
        verticalArrangement = Arrangement.Top
    ) {
        // Vip 会员信息
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color.Transparent)
                .padding(top = 18.dp, start = 16.dp, end = 18.dp, bottom = 16.dp)
                .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
        ) {
            vipContent()
        }
        AIHCard {
            Column(
                modifier = Modifier
                    .padding(top = 18.dp, start = 16.dp, end = 18.dp, bottom = 16.dp)
                    .fillMaxWidth()
                    .animateContentSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                /* 卡片表头Tab选择 */
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(bottom = 6.dp),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Image(
                        painter = painterResource(R.drawable.ic_vip_tag),
                        contentDescription = "vip"
                    )
                    Text(
                        text = vipExclusive,
                        modifier = Modifier
                            .padding(start = 8.dp)
                    )
                    Text(
                        text = tagExclusive,
                    )
                }
                lessonContent()
            }

        }
    }
}

/**
 * en
 *  改善推荐页数据请求成功 卡片显示VIP内容
 *  @param  programsData    改善方案项目数据
 */
@Composable
fun ImprovementProgramCardSuccessContentWithVipEn(
    programsData: ImprovementProgramsData,
    onclick: (model: ImprovementDetailDirections.ImprovementDetailModel) -> Unit,
) {
//    LogUtil.e("JSON:${programsData.toJson()}")
    val context = LocalContext.current
    programsData.programs.forEachIndexed { index, program ->
        if (index != 0) {
            HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp, horizontal = 8.dp))
        }
        Card(
            elevation = CardDefaults.cardElevation(0.dp),
            colors = CardDefaults.cardColors(Color.White),
            shape = RoundedCornerShape(16.dp),
            // 点击进入具体跟练页
            modifier = Modifier
                .clickable {
                    val model = program.toImprovementDetailModel()
                    onclick(model)
                }
        ) {
            // 相对布局
            ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
                val (programCover, programTitle, showDetail, enableDevice) = createRefs()
                /* 改善项目方案封面 */
                Surface(
                    modifier = Modifier
                        .size(96.dp)
                        .constrainAs(programCover) {
                            start.linkTo(parent.start, 10.dp)
                            top.linkTo(parent.top, 10.dp)
                            bottom.linkTo(parent.bottom, 10.dp)
                        },
                    shape = RoundedCornerShape(6.dp)
                ) {
                    AsyncImage(
                        model = program.cover,
                        contentDescription = program.title,
                        placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                        modifier = Modifier.fillMaxSize()
                    )
                }
                /* 改善项目方案标题 */
                Text(
                    text = program.title,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF666666),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.constrainAs(programTitle) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(parent.top, 10.dp)
                        end.linkTo(parent.end, 0.dp)
                        width = Dimension.fillToConstraints
                    }
                )
                /* 改善项目方案关键属性， 时长、已练习人数等 */
                Column(
                    modifier = Modifier.constrainAs(showDetail) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(programTitle.bottom, 8.dp)
                        end.linkTo(parent.end, 10.dp)
                        width = Dimension.fillToConstraints
                    },
                ) {
                    // Vip 文字
                    val vipString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFFF8C847),
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Medium,
                            )
                        ) {
                            append(context.getString(R.string.vip_exclusive))
                        }

                    }

                    // 时长
                    val durationString = TimeUtil.convertSecondsToAnnotatedString(
                        program.duration,
                        LocalContext.current
                    )

                    // 锻炼人数
                    val numberOfPeopleString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(program.frequency.toString())
                            append(" ")
                        }
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.unit_participated))
                        }
                    }
                    if (program.isMembershipRequired) {
                        Row(
                            Modifier,
                            horizontalArrangement = Arrangement.Start,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_vip),
                                contentDescription = "Duration Time",
                                modifier = Modifier
                                    .size(14.dp)
                                    .padding(4.dp)
                            )
                            Text(
                                text = vipString
                            )
                        }
                    }
                    /* 请求体响应结构中提供的“持续时间”参数是以秒为单位，这里需要再显示上做转换计算 */
                    Text(
                        text = durationString,
                        maxLines = 2,
                        modifier = Modifier
                            .padding(horizontal = 4.dp)
                            .padding(bottom = 4.dp),
                    )
                    Text(
                        text = numberOfPeopleString,
                        maxLines = 2,
                        modifier = Modifier
                            .padding(horizontal = 4.dp)
                    )
                }
                /* 是否需要配对设备 Chip */
                if (program.isDevicesRequired) {
                    SuggestionChip(
                        enabled = false,
                        onClick = { /* empty */ },
                        modifier = Modifier
                            .height(36.dp)
                            .padding(8.dp)
                            .constrainAs(enableDevice) {
                                start.linkTo(programCover.end, 6.dp)
                                top.linkTo(showDetail.bottom, 4.dp)
                            },
                        border = SuggestionChipDefaults.suggestionChipBorder(
                            enabled = true,
                            borderWidth = 1.dp,
                            borderColor = Color(0xFF5777D4)
                        ),
                        colors = SuggestionChipDefaults.suggestionChipColors(
                            containerColor = Color(0xFFE1E7FB),
                            disabledContainerColor = Color(0xFFE1E7FB)
                        ),
                        shape = RoundedCornerShape(17.dp),
                        label = {
                            Text(
                                text = stringResource(id = R.string.is_need_of_devices),
                                color = Color(0xFF5777D4),
                                fontSize = 12.sp,
                            )
                        }
                    )
                }
            }
        }
    }
}

/**
 *  改善方案项目列表卡片
 *  @param  improvementCardRecommendDataType    改善页内容卡片推荐类型
 *  @param  content 卡片内容
 */
@Composable
fun ImprovementProgramCardsEn(
    improvementCardRecommendDataType: ImprovementCardProgramsType,
    content: @Composable () -> Unit,
) {
    /* “专家推荐” 与 ”为你打造“ Tab字体颜色 */
    val recommendFromExpertsTabColor: Color by animateColorAsState(
        if (improvementCardRecommendDataType == ImprovementCardProgramsType.RecommendFromExperts) Color(
            0xFFD2B27B
        ) else Color(0xFF666666), label = ""
    )
    val tailoredForYouTabColor: Color by animateColorAsState(
        if (improvementCardRecommendDataType == ImprovementCardProgramsType.TailoredForYou) Color(
            0xFFD2B27B
        ) else Color(0xFF666666), label = ""
    )
    /* “专家推荐” 与 ”为你打造“ Tab选中字体大小变化动画 */
    val recommendFromExpertsTabFontSize: Float by animateFloatAsState(
        targetValue = if (improvementCardRecommendDataType == ImprovementCardProgramsType.RecommendFromExperts) 15f else 14f,
        label = ""
    )
    val tailoredForYouTabColorTabFontSize: Float by animateFloatAsState(
        targetValue = if (improvementCardRecommendDataType == ImprovementCardProgramsType.TailoredForYou) 15f else 14f,
        label = ""
    )
    AIHCard {
        Column(
            modifier = Modifier
                .padding(top = 18.dp, start = 16.dp, end = 18.dp, bottom = 16.dp)
                .fillMaxWidth()
                .animateContentSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            /* 卡片表头Tab选择 */
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 6.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Text(
                    text = stringResource(id = R.string.recommend_from_experts),
                    fontSize = recommendFromExpertsTabFontSize.sp,
                    fontWeight = FontWeight.Medium,
                    color = recommendFromExpertsTabColor,
                    modifier = Modifier
                        .padding(horizontal = 8.dp)
                )
                Box(
                    modifier = Modifier
                        .size(height = 2.dp, width = 2.dp)
                        .clip(RoundedCornerShape(1.dp))
                        .background(color = Color.Black)
                )
                Text(
                    text = stringResource(id = R.string.tailored_for_you),
                    fontSize = tailoredForYouTabColorTabFontSize.sp,
                    fontWeight = FontWeight.Medium,
                    color = tailoredForYouTabColor,
                    modifier = Modifier
                        .padding(horizontal = 8.dp)
                )
            }
            content()
        }
    }
}