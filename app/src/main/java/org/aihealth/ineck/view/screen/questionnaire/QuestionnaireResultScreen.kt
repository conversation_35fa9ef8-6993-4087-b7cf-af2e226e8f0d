package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.Record

@Preview
@Composable
fun QuestionnaireResultScreen(
    modifier: Modifier = Modifier,
    type: DeviceType = DeviceType.aiNeck,
    data: List<Record> = listOf(
        Record(2),
        Record(3),
        Record(2),
        Record(2),
        Record(2)
    ),
    onStart: () -> Unit = {},
    onBack: () -> Unit = {},
) {
    val density = LocalDensity.current
    val fontSize24 = with(density) { 24.sp / fontScale }
    val fontSize20 = with(density) { 20.sp / fontScale }
    val fontSize18 = with(density) { 18.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    val list = remember(data) {
        data
    }
    val hasFinishedQuestionnaireTypeOne = !list[0].data.value.contains(false)
    val hasFinishedQuestionnaireTypeTwo = !list[1].data.value.contains(false)
    val hasFinishedQuestionnaireTypeThree = !list[2].data.value.contains(false)
    val hasFinishedQuestionnaireTypeFour = !list[3].data.value.contains(false)
    LogUtil.i("record:${list.toJson()}")

    val isFinishALl =
        hasFinishedQuestionnaireTypeOne && hasFinishedQuestionnaireTypeTwo && hasFinishedQuestionnaireTypeThree && hasFinishedQuestionnaireTypeFour

    BasePageView (
        showBackIcon = true,
    ){
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 8.dp)
                .verticalScroll(rememberScrollState()),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Image(
                painter = painterResource(id = R.drawable.q_res_ok),
                contentDescription = "image description",
                contentScale = ContentScale.Fit,
                modifier = Modifier.size(72.dp)
            )
            Text(
                modifier = Modifier
                    .padding(top=22.dp, bottom = 60.dp),
                text = stringResource(id = R.string.questionnaire_thank),
                style = TextStyle(
                    fontSize = fontSize24,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF6D6D6D),
                )
            )
            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    painter = if(hasFinishedQuestionnaireTypeOne) painterResource(id = R.drawable.q_res_y) else painterResource(id = R.drawable.q_res_n),
                    contentDescription = "image description",
                    contentScale = ContentScale.None,
                )
                Text(
                    modifier = Modifier.padding(start = 21.dp),
                    text = stringResource(id = R.string.health_questionnaire),
                    style = TextStyle(
                        fontSize = fontSize20,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF3888FF),
                        textAlign = TextAlign.Justify,
                    )
                )
            }
            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    painter = if (hasFinishedQuestionnaireTypeTwo) painterResource(
                        id = R.drawable.q_res_y
                    ) else painterResource(id = R.drawable.q_res_n),
                    contentDescription = "image description",
                    contentScale = ContentScale.None,
                )
                Text(
                    modifier = Modifier.padding(start = 21.dp),
                    text = stringResource(id = R.string.scale_collection),
                    style = TextStyle(
                        fontSize = fontSize20,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF3888FF),
                        textAlign = TextAlign.Justify,
                    )
                )
            }
            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    painter = if(hasFinishedQuestionnaireTypeThree) painterResource(id = R.drawable.q_res_y) else painterResource(id = R.drawable.q_res_n),
                    contentDescription = "image description",
                    contentScale = ContentScale.None,
                )

                val des = when (type) {
                    DeviceType.aiNeck -> stringResource(id = R.string.neck_detection)
                    DeviceType.aiNeckCV -> stringResource(id = R.string.neck_detection)
                    DeviceType.aiBackCV -> stringResource(id = R.string.back_detection)
                    DeviceType.aiBack -> stringResource(id = R.string.back_detection)
                    else -> stringResource(id = R.string.neck_detection)
                }

                Text(
                    modifier = Modifier.padding(start = 21.dp),
                    text = des,
                    style = TextStyle(
                        fontSize = fontSize20,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF3888FF),
                        textAlign = TextAlign.Justify,
                    )
                )
            }
            Row(
                modifier = Modifier
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    painter = if(hasFinishedQuestionnaireTypeFour) painterResource(id = R.drawable.q_res_y) else painterResource(id = R.drawable.q_res_n),
                    contentDescription = "image description",
                    contentScale = ContentScale.None,
                )
                Text(
                    modifier = Modifier.padding(start = 21.dp),
                    text = stringResource(id = R.string.montoring),
                    style = TextStyle(
                        fontSize = fontSize20,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF3888FF),
                        textAlign = TextAlign.Justify,
                    )
                )
            }

            if(isFinishALl){
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 50.dp),
                    text = stringResource(id = R.string.questionnaire_finish),
                    style = TextStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF3888FF),
                        textAlign = TextAlign.Justify,
                    )
                )

                Button(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3888FF)),
                    shape = RoundedCornerShape(size = 21.5.dp),
                    onClick = {
                        onStart()
                    },
                ) {
                    Text(
                        text = stringResource(id = R.string.generate_report),
                        style = TextStyle(
                            fontSize = fontSize18,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFFFFFFF),
                        ),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                }
            }
            else{
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 12.dp, vertical = 50.dp),
                    text = stringResource(id = R.string.prompt_text),
                    style = TextStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF3888FF),
                        textAlign = TextAlign.Justify,
                    )
                )

                Button(
                    modifier = Modifier.fillMaxWidth(0.8f),
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3888FF)),
                    shape = RoundedCornerShape(size = 21.5.dp),
                    onClick = {
                        onBack()
                    }
                ) {
                    Text(
                        text = stringResource(id = R.string.continue_to_examine),
                        style = TextStyle(
                            fontSize = fontSize18,
                            fontWeight = FontWeight(500),
                            color = Color(0xFFFFFFFF),
                        )
                    )
                }
                Text(
                    modifier = Modifier
                        .padding(vertical = 16.dp)
                        .clickable(
                            indication = null,
                            interactionSource = remember { MutableInteractionSource() }
                        ) {
                            onStart()
                        },
                    text = stringResource(id = R.string.generate_report),
                    style = TextStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF999999),
                    )
                )

            }
        }

    }
}