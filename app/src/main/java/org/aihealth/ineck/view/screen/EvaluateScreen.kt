package org.aihealth.ineck.view.screen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.BasePageView

@Preview
@Composable
fun EvaluateScreen() {
    BasePageView(
        title = stringResource(id = R.string.improve_cervical_spine),
        showBackIcon = true,
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
        ) {
            Column {
                Text(
                    text = "恭喜你完成本次颈椎改善练习！",
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                    )
                )
            }
            Column {
                Row {

                }
            }

        }

    }
}
data class Emoji(val name: String,val imgUrl: Int)