package org.aihealth.ineck.view.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.BackPain
import org.aihealth.ineck.model.angles.BackPainRecord
import org.aihealth.ineck.model.angles.JointPain
import org.aihealth.ineck.model.angles.NeckPain
import org.aihealth.ineck.model.angles.NeckPainRecord
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHSlider
import org.aihealth.ineck.view.custom.AIHSliderState
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.HistoryPainDirections
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun PainRecordScreen(
    viewModel: MainViewModel,
    isHistory: Boolean
) {
    LogUtil.d("PainRecordScreen,${viewModel.homeScreen.currentDeviceType.name}")
    BasePageView(
        title = stringResource(id = R.string.pain_record),
        showBackIcon = true,
        headerContent = {
            /* 疼痛历史记录查兰 */
            IconButton(
                onClick = {
                    val model = HistoryPainDirections.HistoryPainModel(
                        when (viewModel.homeScreen.currentDeviceType) {
                            DeviceType.aiNeck, DeviceType.aiNeckCV -> 1
                            DeviceType.aiBack, DeviceType.aiBackCV -> 2
                            else -> 0
                        }
                    )

                    startScreen(
                        route = HistoryPainDirections.actionToHistoryPain(model)
                    )
                },
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.CenterEnd)
                    .padding(end = 8.dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_records_history),
                    contentDescription = "Pain Records History",
                    tint = Color.Black.copy(alpha = .8f),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            when (viewModel.homeScreen.currentDeviceType) {
                DeviceType.aiNeck, DeviceType.aiNeckCV -> NeckPainView(
                    isHistory,
                    neckPain = viewModel.reportScreen.dayRecordScales.neck_pain
                )

                DeviceType.aiBack, DeviceType.aiBackCV -> BackPainView(
                    isHistory,
                    backPain = viewModel.reportScreen.dayRecordScales.back_pain
                )

                DeviceType.aiJointCV, DeviceType.aiJoint -> {
                    JointPainView(
                        isHistory,
                        jointPain = viewModel.reportScreen.dayRecordScales.joint_pain
                    )
                }

                else -> {}
            }
        }

    }
}

@Composable
private fun NeckPainView(
    isHistory: Boolean = true,
    neckPain: NeckPain = NeckPain()
) {
    /* 颈部疼痛数据 */
    val neckPainState by remember {
        mutableStateOf(AIHSliderState(neckPain.neck))
    }
    /* 手部疼痛数据 */
    val handPainState by remember {
        mutableStateOf(AIHSliderState(neckPain.hand))
    }
    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 40.dp, vertical = 20.dp)
    ) {
        PainTitle(titleResId = R.string.pain_record_neck_pain, scale = "0-10")
        Spacer(modifier = Modifier.height(10.dp))
        PainView(state = neckPainState)
        Spacer(modifier = Modifier.height(25.dp))
        PainTitle(titleResId = R.string.pain_record_hand_pain, scale = "0-10")
        Spacer(modifier = Modifier.height(10.dp))
        PainView(state = handPainState)

        if (!isHistory) {
            AIHButton(
                text = stringResource(id = R.string.finish),
                onClick = {
                    apiService.postNeckPain(
                        neckPain = NeckPainRecord(
                            neck = neckPainState.value,
                            hand = handPainState.value
                        )
                    ).enqueueBody {
                        DialogUtil.showToast(localeResources.getString(R.string.pain_record_added_successfully))
                        finish()
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 35.dp, vertical = 30.dp)
                    .fillMaxWidth(),
                fontSize = 18.sp
            )
        }
    }
}

@Composable
private fun BackPainView(
    isHistory: Boolean,
    backPain: BackPain
) {
    val backPainState by remember {
        mutableStateOf(AIHSliderState(backPain.back))
    }
    val legPainState by remember {
        mutableStateOf(AIHSliderState(backPain.leg))
    }
    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 16.dp, vertical = 20.dp)
    ) {
        PainTitle(titleResId = R.string.pain_record_back_pain, scale = "0-10")
        Spacer(modifier = Modifier.height(10.dp))
        PainView(state = backPainState)
        Spacer(modifier = Modifier.height(25.dp))
        PainTitle(titleResId = R.string.pain_record_leg_pain, scale = "0-10")
        Spacer(modifier = Modifier.height(10.dp))
        PainView(state = legPainState)
        Spacer(modifier = Modifier.height(25.dp))
        if (!isHistory) {
            AIHButton(
                text = stringResource(id = R.string.finish),
                onClick = {
                    apiService.postBackPain(
                        backPain = BackPainRecord(
                            back = backPainState.value,
                            leg = legPainState.value
                        )
                    ).enqueueBody {
                        DialogUtil.showToast(localeResources.getString(R.string.pain_record_added_successfully))
                        finish()
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 35.dp, vertical = 30.dp)
                    .fillMaxWidth()
                    .height(42.dp),
                fontSize = 18.sp
            )
        }
    }
}

@Composable
private fun JointPainView(
    isHistory: Boolean = false,
    jointPain: JointPain = JointPain(),
) {
    val jointPainState by remember {
        mutableStateOf(AIHSliderState(jointPain.joint))
    }
    val pain by remember {
        mutableStateOf(JointPain())
    }
    var swelling by remember {
        mutableIntStateOf(0)
    }
    var redness by remember {
        mutableIntStateOf(0)
    }
    var noise by remember {
        mutableIntStateOf(0)
    }
    var click by remember {
        mutableIntStateOf(0)
    }
    var friction by remember {
        mutableIntStateOf(0)
    }
    var crack by remember {
        mutableIntStateOf(0)
    }

    Column(
        modifier = Modifier
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 16.dp, vertical = 20.dp)
    ) {

        PainTitle(titleResId = R.string.pain_record_joint_pain, scale = "0-10")
        Spacer(modifier = Modifier.height(10.dp))
        PainView(state = jointPainState)

        Spacer(modifier = Modifier.height(30.dp))
        PainTitle(titleResId = R.string.pain_record_joint_swelling)

        Spacer(modifier = Modifier.height(16.dp))
        PainBottom(
            modifier = Modifier,
            had = swelling,
            enable = !isHistory,
            onClick = {
                swelling = it
                LogUtil.d("PainBottom,onClick(),$it,pain:${pain.swelling}")
            }
        )

        Spacer(modifier = Modifier.height(30.dp))
        PainTitle(titleResId = R.string.pain_record_joint_redness)
        Spacer(modifier = Modifier.height(16.dp))
        PainBottom(
            modifier = Modifier,
            had = redness,
            enable = !isHistory,
            onClick = {
                redness = it
            }
        )

        Spacer(modifier = Modifier.height(30.dp))
        PainTitle(titleResId = R.string.pain_record_joint_voice)
        Spacer(modifier = Modifier.height(16.dp))
        PainTitleLeft(titleResId = R.string.pain_record_joint_noise)
        Spacer(modifier = Modifier.height(10.dp))
        PainBottom(
            modifier = Modifier,
            had = noise,
            enable = !isHistory,
            onClick = {
                noise = it
            }
        )

        Spacer(modifier = Modifier.height(20.dp))
        PainTitleLeft(titleResId = R.string.pain_record_joint_click)
        Spacer(modifier = Modifier.height(10.dp))
        PainBottom(
            modifier = Modifier,
            had = click,
            enable = !isHistory,
            onClick = {
                click = it
            }
        )

        Spacer(modifier = Modifier.height(20.dp))
        PainTitleLeft(titleResId = R.string.pain_record_joint_friction)
        Spacer(modifier = Modifier.height(10.dp))
        PainBottom(
            modifier = Modifier,
            had = friction,
            enable = !isHistory,
            onClick = {
                friction = it
            }
        )

        Spacer(modifier = Modifier.height(20.dp))
        PainTitleLeft(titleResId = R.string.pain_record_joint_crack)
        Spacer(modifier = Modifier.height(10.dp))
        PainBottom(
            modifier = Modifier,
            had = crack,
            enable = !isHistory,
            onClick = {
                crack = it
            }
        )
        Spacer(modifier = Modifier.height(25.dp))
        if (!isHistory) {
            AIHButton(
                text = stringResource(id = R.string.finish),
                onClick = {
                    //TODO
                },
                modifier = Modifier
                    .padding(horizontal = 35.dp, vertical = 30.dp)
                    .fillMaxWidth()
                    .height(42.dp),
                fontSize = 18.sp
            )
        }

    }

}

@Composable
private fun PainTitle(
    modifier: Modifier = Modifier,
    titleResId: Int,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(id = titleResId),
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF333333),
            )
        )
    }
}

@Composable
private fun PainTitleLeft(
    modifier: Modifier = Modifier,
    titleResId: Int,
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Start,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = stringResource(id = titleResId),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF666666),

                )
        )
    }
}

@Composable
private fun PainTitle(
    titleResId: Int,
    scale: String
) {
    val density = LocalDensity.current

    // 忽略系统字体缩放
    val fixedFontSize18 = with(density) {
        18.sp / fontScale
    }
    // 忽略系统字体缩放
    val fixedFontSize15 = with(density) {
        15.sp / fontScale
    }

    Row(Modifier.fillMaxWidth()) {
        Text(
            text = stringResource(id = titleResId),
            fontSize = fixedFontSize18,
            color = Color(0XFF333333)
        )
        Spacer(modifier = Modifier.weight(1F))
        Text(
            text = stringResource(id = R.string.pain_record_scale, scale),
            fontSize = fixedFontSize15,
            color = Color(0XFF666666)
        )
    }
}

/**
 * 疼痛控件
 */
@Composable
private fun PainView(
    state: AIHSliderState
) {
    val density = LocalDensity.current

    // 忽略系统字体缩放
    val fixedFontSize = with(density) {
        12.sp / fontScale
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 7.dp)
    ) {
        Text(text = "0", fontSize = fixedFontSize, color = Color.Black)
        Spacer(modifier = Modifier.width(4.dp))
        Column(
            modifier = Modifier
                .padding(horizontal = 3.dp)
                .weight(1F)
        ) {
            AIHSlider(
                state = state
            )
            Column(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 5.dp)
            ) {
                BoxWithConstraints(
                    Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    val mWidth = maxWidth / 5
                    repeat(4) {
                        Text(
                            text = "${(it + 1) * 2}",
                            fontSize = 10.sp,
                            color = Color.Black,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .padding(start = mWidth * it)
                                .width(mWidth * 2)
                        )
                    }
                }
                Row(modifier = Modifier.fillMaxWidth()) {
                    Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.img_pain_record_pain1),
                            contentDescription = null,
                            modifier = Modifier.size(22.dp)
                        )
                    }
                    Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.img_pain_record_pain2),
                            contentDescription = null,
                            modifier = Modifier.size(22.dp)
                        )
                    }
                    Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.img_pain_record_pain3),
                            contentDescription = null,
                            modifier = Modifier.size(22.dp)
                        )
                    }
                    Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.img_pain_record_pain4),
                            contentDescription = null,
                            modifier = Modifier.size(22.dp)
                        )
                    }
                    Box(modifier = Modifier.weight(1F), contentAlignment = Alignment.Center) {
                        Image(
                            painter = painterResource(id = R.drawable.img_pain_record_pain5),
                            contentDescription = null,
                            modifier = Modifier.size(22.dp)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
                BoxWithConstraints(Modifier.fillMaxWidth()) {
                    val mWidth = maxWidth / 5
                    Text(
                        text = stringResource(id = R.string.pain_record_no_pain),
                        fontSize = fixedFontSize,
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .width(mWidth * 3)
                            .offset(-mWidth, 0.dp)
                    )
                    Text(
                        text = stringResource(id = R.string.pain_record_moderate),
                        fontSize = fixedFontSize,
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    Text(
                        text = stringResource(id = R.string.pain_record_unbearable_pain),
                        fontSize = fixedFontSize,
                        color = Color.Black,
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .width(mWidth * 3)
                            .offset(mWidth * 3, 0.dp)
                    )
                }

            }
        }
        Spacer(modifier = Modifier.width(4.dp))
        Text(text = "10", fontSize = fixedFontSize, color = Color.Black)
    }

}

@Composable
fun PainBottom(
    modifier: Modifier = Modifier,
    had: Int,
    enable: Boolean = true,
    onClick: (Int) -> Unit
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .weight(1f)
                .height(40.dp),
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() },
                        enabled = enable
                    ) {
                        onClick(0)
                    },
                painter = if (had == 0) painterResource(id = R.drawable.bg_pain_joint_left_choosen) else painterResource(
                    id = R.drawable.bg_pain_joint_left
                ),
                contentDescription = ""
            )
            Text(
                text = stringResource(id = R.string.have),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFFFFFFF),
                )
            )
        }

        Box(
            modifier = Modifier
                .weight(1f)
                .height(40.dp),
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() },
                        enabled = enable
                    ) { onClick(1) },
                painter = if (had == 1) painterResource(id = R.drawable.bg_paint_joint_right_choosen) else painterResource(
                    id = R.drawable.bg_paint_joint_right
                ),
                contentDescription = ""
            )
            Text(
                text = stringResource(id = R.string.no),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFFFFFFF),
                )
            )
        }
    }
}