package org.aihealth.ineck.view.screen.my

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import java.util.Locale

@Composable
fun VipMessageNoVipZh(
    @DrawableRes id: Int,
    vipHelloString: String,
    vipDescriptionString: String,
    nextDescriptionString: String,
    onClick: () -> Unit,
    context: @Composable () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(64.dp)
            .clip(shape = RoundedCornerShape(12.dp))
            .paint(
                painterResource(id = R.drawable.vip_my_bg_no),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .paint(
                    painterResource(id = R.drawable.vip_in_bg_2),
                )
                .clickable { onClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = 13.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF6B4B14),
                    letterSpacing = 0.46.sp,
                )
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = id),
                contentDescription = "ic_vip"
            )
            Text(
                text = vipHelloString,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFFFFFFFF),
                    letterSpacing = 0.56.sp,
                )
            )
        }
        Text(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 40.dp, start = 21.dp),
            text = vipDescriptionString,
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFF1F1F1),
                letterSpacing = 0.42.sp,
            )
        )
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(top = 7.dp, bottom = 9.dp, start = 8.dp, end = 8.dp)
                .background(Color.White, shape = RoundedCornerShape(6.dp)),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            context()
        }
    }
}

@Preview
@Composable
fun VipMessageNoVipZhSliver(
    vipHelloString: String = "Hi，成为 aiSpine会员",
    vipDescriptionString: String = "科学专业“预防”、“矫正”、“锻炼”，让您拥有健康颈椎腰椎",
    nextDescriptionString: String = "立即开通",
    onClick: () -> Unit = {},
) {
    val density = LocalDensity.current
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize13 = with(density) { 13.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(64.dp)
            .clip(shape = RoundedCornerShape(6.dp))
            .paint(
                painterResource(id = R.drawable.vip_my_bg_no),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .clip(shape = RoundedCornerShape(0.dp, 4.dp, 0.dp, 0.dp))
                .paint(
                    painterResource(id = R.drawable.vip_my_bg_no_sliver),
                    contentScale = ContentScale.Crop,
                )
                .clickable { onClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = fontSize13,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF535353)
                ),
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = R.drawable.vip_silver_badge),
                contentDescription = "ic_vip"
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(38.dp)
                    .padding(start = 8.dp, end = 8.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = vipHelloString,
                    style = TextStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight(600),
                        color = Color(0XFF535353)
                    )
                )
                Text(
                    modifier = Modifier.fillMaxWidth(),
                    text = vipDescriptionString,
                    style = TextStyle(
                        fontSize = fontSize12,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF7B8092),
                    )
                )
            }

        }

    }
}

@Composable
fun VipMessageZh(
    @DrawableRes id: Int,
    vipHelloString: String,
    vipDescriptionString: String,
    nextDescriptionString: String,
    onclick: () -> Unit,
    context: @Composable () -> Unit,
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize13 = with(density) { 13.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(64.dp)
            .clip(shape = RoundedCornerShape(12.dp))
            .paint(
                painterResource(id = R.drawable.vip_bg_1),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .paint(
                    painterResource(id = R.drawable.vip_in_bg_2),
                )
                .clickable { onclick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = fontSize13,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF6B4B14),
                    letterSpacing = 0.46.sp,
                )
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = id),
                contentDescription = "ic_vip"
            )
            Text(
                text = vipHelloString,
                style = TextStyle(
                    fontSize = fontSize16,
                    fontWeight = FontWeight(600),
                    color = Color(0xFFFFFFFF),
                    letterSpacing = 0.56.sp,
                )
            )
        }
        Text(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 40.dp, start = 21.dp),
            text = vipDescriptionString,
            style = TextStyle(
                fontSize = fontSize12,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFF1F1F1),
                letterSpacing = 0.42.sp,
            )
        )
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(top = 7.dp, bottom = 9.dp, start = 8.dp, end = 8.dp)
                .background(Color.White, shape = RoundedCornerShape(6.dp)),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            context()
        }
    }
}

@Preview()
@Composable
fun VipMessageZhGold(
    vipHelloString: String = "Hi，aiSpine会员",
    vipDescriptionString: String = "4",
    nextDescriptionString: String = "我的会员",
    onclick: () -> Unit = {},
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize13 = with(density) { 13.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }

    val dayString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                fontSize = fontSize10,
                fontWeight = FontWeight.Normal,
                color = Color(0XFFE9E9E9),
            )
        ) {
            if (currentLocale == Locale.CHINESE)
                append("距离上次跟练已过去")
            else
                append("It's been ")
        }
        withStyle(
            SpanStyle(
                fontSize = fontSize12,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFEEC570)
            )
        ) {
            append(" $vipDescriptionString ")
        }
        withStyle(
            SpanStyle(
                fontSize = fontSize10,
                fontWeight = FontWeight.Normal,
                color = Color(0XFFE9E9E9)
            )
        ) {
            if (currentLocale == Locale.CHINESE)
                append("天")
            else
                append("days")
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(64.dp)
            .clip(shape = RoundedCornerShape(6.dp))
            .paint(
                painterResource(id = R.drawable.vip_my_bg_gold),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .clip(shape = RoundedCornerShape(0.dp, 4.dp, 0.dp, 0.dp))
                .paint(
                    painterResource(id = R.drawable.vip_in_bg_2),
                    contentScale = ContentScale.Crop
                )
                .clickable { onclick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = fontSize13,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF6B4B14)
                )
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = R.drawable.vip_gold_badge),
                contentDescription = "ic_vip"
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(38.dp)
                    .padding(start = 8.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = vipHelloString,
                    style = TextStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight(600),
                        color = Color(0xFFFFFFFF),
                    )
                )
                Text(
                    modifier = Modifier,
                    text = dayString,
                )
            }


        }
    }
}