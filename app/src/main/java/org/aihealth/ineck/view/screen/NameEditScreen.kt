package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.viewmodel.PersonalDataViewModel
import org.aihealth.ineck.viewmodel.user

@Composable
fun NameEditRoute(
    viewModel: PersonalDataViewModel = viewModel()
) {
    // 解析当前用户的姓名，这里假设姓名格式为 "First Last" 或只有一个名字
    val fullName = user.name
    val nameParts = fullName.split(" ", limit = 2)
    val initialSurname = if (nameParts.size > 1) nameParts.first() else ""
    val initialGivenName = if (nameParts.size > 1) nameParts.drop(1).joinToString(" ") else fullName

    NameEditScreen(
        initialSurname = initialSurname,
        initialGivenName = initialGivenName,
        onSave = { surname, givenName ->
            val newName = if (surname.isNotBlank() && givenName.isNotBlank()) {
                "$surname $givenName"
            } else if (surname.isNotBlank()) {
                surname
            } else if (givenName.isNotBlank()) {
                givenName
            } else {
                ""
            }

            if (newName.isNotBlank()) {
                viewModel.setName(newName)
                finish()
            } else {
                DialogUtil.showToast(localeResources.getString(R.string.input_name))
            }
        },
        onCancel = { finish() }
    )
}

@Composable
fun NameEditScreen(
    initialSurname: String = "",
    initialGivenName: String = "",
    onSave: (String, String) -> Unit = { _, _ -> },
    onCancel: () -> Unit = {}
) {
    var surname by remember { mutableStateOf(initialSurname) }
    var givenName by remember { mutableStateOf(initialGivenName) }

    BaseEditScreen(
        onSave = {
            onSave(surname.trim(), givenName.trim())
        },
        onCancel = onCancel
    ) {
        // Name Fields Container
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(16.dp)
        ) {
            NameField(
                label = stringResource(R.string.first_name),
                value = givenName,
                onValueChange = { givenName = it }
            )

            AIHDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp)
            )

            NameField(
                label = stringResource(R.string.last_name),
                value = surname,
                onValueChange = { surname = it }
            )
        }
    }
}

@Composable
private fun NameField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 16.sp,
            color = Color(0xFF666666),
            fontWeight = FontWeight.Normal,
            modifier = Modifier.width(100.dp)
        )

        BasicTextField(
            value = value,
            onValueChange = { newValue ->
                // Limit to reasonable name length
                if (newValue.length <= 50) {
                    onValueChange(newValue)
                }
            },
            modifier = Modifier.weight(1f),
            decorationBox = { innerTextField ->
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    if (value.isEmpty()) {
                        Text(
                            text = "",
                            fontSize = 16.sp,
                            color = Color(0xFFC7C7CC)
                        )
                    }
                    innerTextField()
                }
            },
            textStyle = androidx.compose.ui.text.TextStyle(
                fontSize = 16.sp,
                color = Color(0xFF333333),
                textAlign = TextAlign.End
            ),
            singleLine = true
        )
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun PreviewNameEditScreen() {
    AIH_UserTheme {
        NameEditScreen()
    }
}