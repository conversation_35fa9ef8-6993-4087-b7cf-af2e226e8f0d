package org.aihealth.ineck.view.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import coil.compose.AsyncImage
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.angles.Follow
import org.aihealth.ineck.model.angles.Gender
import org.aihealth.ineck.ui.theme.Sizes
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHCard
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.dialog.AddAttentionDialog
import org.aihealth.ineck.viewmodel.MyAttentionViewModel

@Composable
fun AddAttentionRoute(
    viewModel: MyAttentionViewModel
) {
    val isLoading = viewModel.isLoading.collectAsState().value
    val hasMoreData = viewModel.hasMoreData.collectAsState().value
    AddAttentionScreen(
        data = viewModel.searchData,
        isLoading = isLoading,
        hasMoreData = hasMoreData,
        search = {
            viewModel.search(it)
        },
        loadMore = {
            viewModel.loadMore()
        },
        addFollow = {
            DialogUtil.showLoading()
            viewModel.addFollowInSearch(it)
        },
        toCamera = {
            if (XXPermissions.isGranted(activity, Permission.CAMERA)) {
                viewModel.onScanClick()
            } else {
                viewModel.showPowerDialogVisibleState = true
                viewModel.theClickType = 1
            }
        }
    )
    PermissionGrantDialog(viewModel)
    AddAttentionDialog(
        isShowAddAttentionDialog = viewModel.addAttentionDialogVisible,
        name = viewModel.userInfoName,
        onDismissRequest = {
            viewModel.addAttentionDialogVisible = false
        },
        onConfirm = {
            viewModel.addFollow(viewModel.scanUUID)
            viewModel.addAttentionDialogVisible = false
        },
    )
}

@Preview()
@Composable
fun AddAttentionScreen(
    data: List<Follow> = listOf(),
    isLoading: Boolean = false,
    hasMoreData: Boolean = true,
    search: (String) -> Unit = {},
    loadMore: () -> Unit = {},
    addFollow: (String) -> Unit = {},
    toCamera: () -> Unit = {}
) {
    BasePageView(
        title = stringResource(id = R.string.my_provider),
        showBackIcon = true,
        headerColor = Color.White,
        headerHeight = 60.dp,
        headerBackgroundColor = Color(0XFF3C59CF),
        statusBarDarkContentEnabled = false,
        headerContent = {
            Icon(
                painter = painterResource(id = R.drawable.img_scanner),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = Sizes.actionHorizontalPadding)
                    .size(20.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            toCamera()
                        }
                    }
            )
        }
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0XFFF5F6F8))
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .offset(0.dp, (-24).dp)
        ) {
            AIHCard(modifier = Modifier) {
                SearchBarWithButton(
                    placeholderText = stringResource(id = R.string.myattention_search_tip),
                    onSearch = { text ->
                        search(text)
                    }
                )
            }
            Spacer(modifier = Modifier.height(4.dp))
            if (data.isNotEmpty()) {
                val listState = rememberLazyListState()
                
                // 监听滚动状态，到达底部时加载更多
                val shouldLoadMore = remember {
                    derivedStateOf {
                        val lastVisibleItem = listState.layoutInfo.visibleItemsInfo.lastOrNull()?.index ?: 0
                        val totalItemsCount = listState.layoutInfo.totalItemsCount
                        lastVisibleItem >= totalItemsCount - 2
                    }
                }
                
                LaunchedEffect(shouldLoadMore.value) {
                    if (shouldLoadMore.value && !isLoading && hasMoreData) {
                        loadMore()
                    }
                }
                
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    state = listState
                ) {
                    items(data) {
                        Item(
                            follow = it,
                            toFollow = {
                                addFollow(it.uuid)
                            }
                        )
                    }
                    
                    // 添加底部加载状态指示器
                    item {
                        if (isLoading) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                androidx.compose.material3.CircularProgressIndicator(
                                    modifier = Modifier.size(24.dp),
                                    color = Color(0XFF1E4BDF)
                                )
                            }
                        } else if (!hasMoreData && data.isNotEmpty()) {
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = stringResource(id = R.string.no_more_data),
                                    color = Color(0XFF999999),
                                    fontSize = 14.sp
                                )
                            }
                        }
                    }
                    
                    // 添加底部间距
                    item {
                        Spacer(modifier = Modifier.height(16.dp))
                    }
                }
            } else if (!isLoading) {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Text(
                        text = stringResource(id = R.string.no_data),
                        color = Color(0XFF666666),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            } else {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    androidx.compose.material3.CircularProgressIndicator(
                        color = Color(0XFF1E4BDF)
                    )
                }
            }
        }
    }
}

@Preview()
@Composable
fun SearchBarWithButton(
    placeholderText: String = "Please enter the doctor ID",
    onSearch: (String) -> Unit = {}
) {
    var text by remember { mutableStateOf(TextFieldValue("")) }
    Card {
        Surface(
            color = Color.White,
            shape = RoundedCornerShape(2.dp)
        ) {
            val keyboardController = LocalSoftwareKeyboardController.current
            Row(
                verticalAlignment = Alignment.CenterVertically,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp, bottom = 8.dp, start = 8.dp)
            ) {
                OutlinedTextField(
                    value = text,
                    onValueChange = { text = it },
                    singleLine = true,
                    placeholder = {
                        Text(
                            text = stringResource(id = R.string.myattention_search_tip),
                            style = TextStyle(
                                color = Color(0XFF999999),
                                fontSize = 12.sp,
                            ),
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )
                    },
                    modifier = Modifier
                        .weight(1f)  // 使输入框占据剩余空间
                        .height(56.dp),  // 与 Material3
                    shape = RoundedCornerShape(8.dp),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            onSearch(text.text)
                            keyboardController?.hide()
                        }
                    )
                )
                Icon(
                    imageVector = Icons.Filled.Search,
                    contentDescription = "Search Icon",
                    modifier = Modifier
                        .clickable { onSearch(text.text) }
                        .padding(horizontal = 10.dp)
                )
            }
        }

    }
}

@Preview()
@Composable
private fun Item(
    follow: Follow = Follow(name = "asdasdasd", gender = "M"),
    toFollow: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }

    AIHCard(
        modifier = Modifier
            .height(70.dp),
        paddingValues = PaddingValues(start = 18.dp, end = 18.dp, bottom = 4.dp)
    ) {
        Box {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (follow.photo == "") {
                    Image(
                        painter = painterResource(id = R.drawable.img_myattention_photo),
                        contentDescription = null,
                        modifier = Modifier
                            .size(54.dp)
                            .clip(CircleShape)
                    )
                } else {
                    AsyncImage(
                        model = follow.photo,
                        contentDescription = null,
                        modifier = Modifier
                            .size(54.dp)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop,
                        error = painterResource(id = R.drawable.img_myattention_photo),
                    )
                }
                Column(
                    modifier = Modifier
                        .weight(1F)
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp)
                ) {
                    Row(
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {
                        var isPopupVisible by remember { mutableStateOf(false) }
                        val clipboardManager = LocalClipboardManager.current
                        Box(
                            modifier = Modifier.weight(0.7f)
                        ) {
                            Text(
                                modifier = Modifier.clickable { isPopupVisible = true },
                                text = follow.name,
                                color = Color(0XFF666666),
                                fontSize = fontSize16,
                                fontWeight = FontWeight.Medium,
                                overflow = TextOverflow.Ellipsis
                            )
                            // 当 isPopupVisible 为 true 时，显示悬浮的 Popup
                            if (isPopupVisible) {
                                Popup(
                                    alignment = Alignment.TopCenter,
                                    offset = IntOffset(0, -50), // 调整 Popup 的位置
                                    onDismissRequest = { isPopupVisible = false },
                                    properties = PopupProperties(
                                        focusable = true,
                                        dismissOnClickOutside = true,
                                        dismissOnBackPress = true
                                    )
                                ) {
                                    Surface(
                                        modifier = Modifier
                                            .padding(16.dp)
                                    ) {
                                        Text(
                                            text = follow.name,
                                            modifier = Modifier.padding(16.dp)
                                        )
                                    }
                                }
                            }
                        }

                        Text(
                            modifier = Modifier
                                .padding(start = 2.dp),
                            text = stringResource(
                                id = when (follow.gender) {
                                    "M" -> Gender.M.text
                                    "F" -> Gender.F.text
                                    else -> Gender.U.text
                                }
                            ),
                            color = Color(0XFF666666),
                            fontSize = fontSize16,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                Color(0XFFF4F4F4),
                                RoundedCornerShape(3.dp)
                            )
                    ) {
                        Text(
                            text = follow.userId,
                            color = Color(0XFF999999),
                            fontSize = fontSize10,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }

                AIHButton(
                    text = if (follow.followed) stringResource(R.string.followed) else stringResource(
                        id = R.string.follow
                    ),
                    enabled = !follow.followed,
                    onClick = {
                        toFollow()
                    },
                    modifier = Modifier
                        .width(70.dp)
                        .height(30.dp),
                    shape = RoundedCornerShape(3.dp),
                    fontSize = fontSize10
                )
            }
        }
    }
}

/**
 * 权限说明弹窗
 */
@Composable
private fun PermissionGrantDialog(
    viewModel: MyAttentionViewModel
) {
    if (viewModel.showPowerDialogVisibleState) {
        Dialog(onDismissRequest = { viewModel.showPowerDialogVisibleState = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White)
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.camera_permission),
                    modifier = Modifier.padding(horizontal = 8.dp),
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                            }, contentAlignment = Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                                XXPermissions
                                    .with(activity)
                                    .permission(Permission.CAMERA)
                                    .request(object : OnPermissionCallback {
                                        override fun onGranted(
                                            p0: MutableList<String>,
                                            allGranted: Boolean
                                        ) {
                                            if (allGranted) {
                                                /* 触发视频检测前对话框 */
                                                if (viewModel.theClickType == 1) {
                                                    viewModel.onScanClick()
                                                }
                                            } else {
                                                DialogUtil.showToast(activity.getString(R.string.please_grant_permission))
                                            }
                                        }

                                        override fun onDenied(
                                            permissions: MutableList<String>,
                                            doNotAskAgain: Boolean
                                        ) {
                                            super.onDenied(permissions, doNotAskAgain)
                                            if (doNotAskAgain) {
                                                DialogUtil.showToast(activity.getString(R.string.please_grant_permission_manual))
                                                XXPermissions.startPermissionActivity(
                                                    activity,
                                                    permissions
                                                )
                                            } else {
                                                DialogUtil.showToast(activity.getString(R.string.please_grant_permission))
                                            }
                                        }

                                    })
                            }, contentAlignment = Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}