package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.google.accompanist.web.WebView
import com.google.accompanist.web.rememberWebViewNavigator
import com.google.accompanist.web.rememberWebViewState
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.view.custom.BasePageView
import java.util.Locale

/**
 * 隐私条款UI
 */
@SuppressLint("SetJavaScriptEnabled")
@Composable
fun PrivateTermScreen() {
    BasePageView(
        title = stringResource(id = R.string.privateterm),
        showBackIcon = true

    ) {
        var hasError by remember { mutableStateOf(false) }
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            if (hasError) {
                // Display an error screen
                Box(
                    modifier = Modifier.fillMaxSize(),
                ) {
                    Text(text = "Connection timed out. Please try again later.")
                }
            } else {
                // Display the WebView
                val state = rememberWebViewState(
                    url = if (currentLocale == Locale.CHINESE) "https://health.aihnet.cn/?page_id=6747#/privacy"
                    else "https://medical.aihnet.com/privacy-policy-all/"
                )
                val navigator = rememberWebViewNavigator()
                WebView(
                    state = state,
                    navigator = navigator,
                    onCreated = { webView ->
                        webView.settings.javaScriptEnabled = true
                    }
                )
            }
        }
    }
}