package org.aihealth.ineck.view.dialog

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Velocity
import org.aihealth.ineck.R
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.toStringList
import org.aihealth.ineck.util.year
import org.aihealth.ineck.view.custom.AIHWheel
import org.aihealth.ineck.view.custom.AIHWheelState
import org.aihealth.ineck.view.dialog.picker.BasePicker
import java.util.Calendar
import java.util.GregorianCalendar

/**
 *  月历手动剥轮选择器， 在[onConfirmClick]方法参数中回调获得最终选择月份的第一天 Calendar 对象
 *  @param  modifier    修饰参数
 *  @param  initialCalendar 初始化日期
 *  @param  minCalendar 最小月份
 *  @param  maxCalendar 最大月份
 *  @param  onConfirmClick  月份确认事件，将最终选择的日期回调
 *  @param  onCancelClick   取消事件
 */
@Composable
fun MonthPickerDialog(
    modifier: Modifier = Modifier,
    initialCalendar: Calendar = Calendar.getInstance(),
    minCalendar: Calendar = GregorianCalendar(1900,1,1),
    maxCalendar: Calendar = GregorianCalendar(2100,11,31),
    onConfirmClick: (calendar: Calendar) -> Unit,
    onCancelClick: () -> Unit = {}
) {
    val yearState = remember {
        AIHWheelState(initialIndex = initialCalendar.year - minCalendar.year)
    }
    val monthState = remember {
        AIHWheelState(initialIndex = initialCalendar.month)
    }
    BasePicker(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth(),
        title = stringResource(id = R.string.select_date),
        onConfirmClick = {
            /* 选取每个月的4号为游标日期，可以确保横跨两个月份的星期里，中位数所在的那一天属于所选择的月份 */
            onConfirmClick(GregorianCalendar(yearState.selectedIndex + minCalendar.year,monthState.selectedIndex , 4))
        },
        onCancelClick = onCancelClick
    ) {
        Row(
            Modifier
            .fillMaxWidth()
            .nestedScroll(
                object : NestedScrollConnection {
                    override fun onPostScroll(
                        consumed: Offset,
                        available: Offset,
                        source: NestedScrollSource
                    ): Offset {
                        return available
                    }

                    override suspend fun onPostFling(
                        consumed: Velocity,
                        available: Velocity
                    ): Velocity {
                        return available
                    }
                }

            )) {
            AIHWheel(state = yearState, list = (minCalendar.year .. maxCalendar.year).toStringList(), modifier = Modifier.weight(1F))
            AIHWheel(state = monthState, list = (1..12).toStringList(), modifier = Modifier
                .weight(1F))
        }
    }
}