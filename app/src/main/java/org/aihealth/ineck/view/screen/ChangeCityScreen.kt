package org.aihealth.ineck.view.screen

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun ChangeCityScreen() {
    BasePageView(
        title = stringResource(id = R.string.location),
        showBackIcon = true
    ){
        var selectedIndex by remember {
            mutableIntStateOf(if (isInChina) 0 else 1)
        }
        Column(Modifier.fillMaxSize()) {
            Text(
                text = stringResource(id = R.string.select_location),
                modifier = Modifier.padding(
                    vertical = 10.dp,
                    horizontal = 30.dp
                ),
                color = Color(0XFF666666),
                fontSize = 12.sp
            )
            HorizontalDivider()
            Column {
                Item(
                    isSelected = selectedIndex == 0,
                    drawableId = R.drawable.img_chinese,
                    country = stringResource(id = R.string.mainland),
                    onClick = {
                        selectedIndex = 0
                    }
                )
                Item(
                    isSelected = selectedIndex == 1,
                    drawableId = R.drawable.img_usa,
                    country = stringResource(id = R.string.united_states),
                    onClick = {
                        selectedIndex = 1
                    }
                )
            }

        }
        AIHTextButton(
            text = stringResource(id = R.string.save),
            onClick = {
                isInChina = selectedIndex == 0
                LogUtil.i("isInChina = $isInChina")
                SPUtil.putBoolean(SPConstant.IS_IN_CHINA, isInChina)
                finish()
            },
            style = TextStyle(
                fontWeight = FontWeight(400),
                color = Color(0xFFF7F7F7),
                fontSize = 20.sp,
                textAlign = TextAlign.Center
            ),
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(40.dp)
                .fillMaxWidth()
                .fillMaxWidth(0.9f)
        )
    }
}

@Composable
private fun Item(
    isSelected: Boolean,
    @DrawableRes drawableId: Int,
    country: String,
    onClick:() -> Unit = {}
) {
    Column(
        Modifier
            .clickable { onClick() }
            .padding(horizontal = 40.dp)
            .fillMaxWidth()
            .height(52.dp)) {
        Row(modifier = Modifier
            .fillMaxSize()
            .weight(1F)
            , verticalAlignment = Alignment.CenterVertically) {
            Image(painter = painterResource(id = drawableId), contentDescription = null)
            Spacer(modifier = Modifier.width(16.dp))
            Text(text = country, fontSize = 20.sp)
            Spacer(modifier = Modifier.weight(1F))
            if (isSelected) {
                Image(
                    painter = painterResource(id = R.drawable.img_checked),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 5.dp)
                        .size(24.dp)
                )
            }
        }
        HorizontalDivider()
    }

}