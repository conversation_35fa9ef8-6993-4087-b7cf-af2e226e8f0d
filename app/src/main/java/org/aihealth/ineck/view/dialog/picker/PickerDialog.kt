package org.aihealth.ineck.view.dialog.picker

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import org.aihealth.ineck.view.custom.AIHWheel
import org.aihealth.ineck.view.custom.AIHWheelState

@Composable
fun PickDialog(
    list: List<String>,
    modifier: Modifier =Modifier,
    title: String = "",
    initialIndex: Int = 0,
    onConfirmClick: (index: Int) -> Unit,
    onCancelClick: () -> Unit = {}
) {
    val state = remember {
        AIHWheelState(initialIndex = initialIndex)
    }
    BasePicker(
        modifier = modifier,
        title = title,
        onConfirmClick = {
            onConfirmClick(state.selectedIndex)
        },
        onCancelClick = onCancelClick
    ) {
        AIHWheel(state = state, list = list)
    }
}