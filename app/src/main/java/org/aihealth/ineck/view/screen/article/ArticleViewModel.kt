package org.aihealth.ineck.view.screen.article

import androidx.annotation.DrawableRes
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.annotations.SerializedName
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.viewmodel.BaseViewModel

/**
 * 推文模块的ViewModel
 * 
 */
class ArticleViewModel : BaseViewModel() {

    // 健康建议文章列表
    private val _article = MutableStateFlow<List<Article>>(emptyList())
    val articleList: StateFlow<List<Article>> = _article

    // 分页相关状态
    private val _currentPage = MutableStateFlow(1)
    private val _isArticleLoading = MutableStateFlow(false)
    private val _hasMoreArticlePages = MutableStateFlow(true)
    val isArticleLoading: StateFlow<Boolean> = _isArticleLoading
    val hasMoreArticlePages: StateFlow<Boolean> = _hasMoreArticlePages
    private val pageSize = 5
    // 添加总文章数量状态
    private val _totalArticleCount = MutableStateFlow(0)

    /**
     * 获取推文 - 初始加载或刷新
     */
    fun fetchArticleList(refresh: Boolean = true) {
        if (_isArticleLoading.value) return

        if (refresh) {
            _currentPage.value = 1
            _hasMoreArticlePages.value = true
        }

        _isArticleLoading.value = true
        viewModelScope.launch {
            apiService.getArticle(
                page = _currentPage.value,
                pageSize = pageSize
            ).enqueue(
                onResponse = { _, response ->
                    _isArticleLoading.value = false
                    response?.apply {
                        if (code == 1) {
                            val result = data?.let { parseArticleData(it) }
                            val newArticles = result?.articles ?: emptyList()
                            
                            // 更新总文章数
                            result?.totalCount?.let { _totalArticleCount.value = it }

                            if (newArticles.isEmpty()) {
                                _hasMoreArticlePages.value = false
                            } else {
                                if (refresh) {
                                    _article.value = newArticles
                                } else {
                                    // 将新数据添加到现有列表中
                                    _article.value = _article.value + newArticles
                                }
                                
                                // 根据总数和当前页码判断是否还有更多页
                                val totalPages = if (_totalArticleCount.value % pageSize == 0) {
                                    _totalArticleCount.value / pageSize
                                } else {
                                    _totalArticleCount.value / pageSize + 1
                                }
                                _hasMoreArticlePages.value = _currentPage.value < totalPages
                            }
                            LogUtil.d("Loaded Article page ${_currentPage.value}, items: ${newArticles.size}, total: ${_totalArticleCount.value}")
                        } else {
                            LogUtil.e("Failed to fetch Article: ${msg ?: "Unknown error"}")
                        }
                    }
                },
                onFailure = { _, throwable ->
                    _isArticleLoading.value = false
                    _article.value = emptyList()
                    LogUtil.e("Error fetching Article: ${throwable.message}")
                }
            )
        }
    }

    /**
     * 加载更多推文
     */
    fun loadMoreArticle() {
        if (_isArticleLoading.value || !_hasMoreArticlePages.value) return
        _currentPage.value += 1
        LogUtil.d("load more article page ${_currentPage.value}")
        fetchArticleList(refresh = false)
    }

    /**
     * 解析文章数据
     */
    private fun parseArticleData(data: Any): ArticleResult? {
        return try {
            val gson = Gson()
            val jsonObject = gson.toJson(data)
            val json = gson.fromJson(jsonObject, JsonObject::class.java)

            var articles = emptyList<Article>()
            var totalCount = 0
            var currentPage = 1

            // 从response中提取总数量和当前页码
            if (json.has("total") && !json.get("total").isJsonNull) {
                totalCount = json.get("total").asInt
            }
            
            if (json.has("current_page") && !json.get("current_page").isJsonNull) {
                currentPage = json.get("current_page").asInt
            }

            // 从data字段中提取articles数组
            if (json.has("data") && json.get("data").isJsonArray) {
                val articlesArray = json.getAsJsonArray("data")
                val type = object : TypeToken<List<Article>>() {}.type
                articles = gson.fromJson(articlesArray, type)
            } else {
                LogUtil.e("Unexpected API response format: missing data array")
            }
            
            return ArticleResult(articles, totalCount, currentPage)
        } catch (e: Exception) {
            LogUtil.e("Error parsing Article data: ${e.message}")
            null
        }
    }


}

/**
 * 文章数据解析结果
 */
data class ArticleResult(
    val articles: List<Article> = emptyList(),
    val totalCount: Int = 0,
    val currentPage: Int = 1
)

data class Article(
    val title: String = "",
    val link: String = "",
    val image: String = "",
    val describe: String = "",
    val date: String = "",
    val author: String = "",
    @SerializedName("user_image")
    val authorAvatar: String = "",
    // 本地使用，非API返回
    @DrawableRes val placeholderImage: Int = R.drawable.header_2
)