package org.aihealth.ineck.view.screen.info

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.flow.distinctUntilChanged
import org.aihealth.ineck.R
import org.aihealth.ineck.model.Constants.HEIGHT_CM_MAX_LIMIT
import org.aihealth.ineck.model.Constants.HEIGHT_CM_MIN_LIMIT
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.AIHTextButton
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * 编辑身高
 * height 仅保持公制，在显示的时候根据"M","I"来判断是否转换为英制。
 * @param modifier Modifier
 * @param height Double 公制的数值
 * @param onHeightChange Function1<Double, Unit>
 *
 */
@Composable
fun EditHeight(
    modifier: Modifier = Modifier,
    height: Double = 0.0,
    selectedUnit: String,
    onHeightChange: (HeightUnit) -> Unit,
) {
    val locale = LocalConfiguration.current.locales[0]
    var showDialog by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    val heightString = when (selectedUnit) {
        "M" -> {
            if (height >= HEIGHT_CM_MIN_LIMIT) String.format(
                locale,
                "%.1f %s",
                height,
                stringResource(R.string.height_unit_cm)
            )
            else ""

        }

        "I" -> {
            if (height >= HEIGHT_CM_MIN_LIMIT) {
                val tmp = (height * 0.393701)
                String.format(
                    locale,
                    "%.1f %s",
                    tmp,
                    stringResource(R.string.inch)
                )
            } else {
                ""
            }
        }

        else -> ""
    }

    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Text(
                text = stringResource(id = R.string.height),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF444444)
                ),
                modifier = Modifier.padding(end = 4.dp)
            )
        }
        TextField(
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth()
                .clickable {
                    showDialog = !showDialog
                },
            shape = RoundedCornerShape(24.dp),
            placeholder = {
                Text(
                    text = stringResource(id = R.string.input_height),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFDEDEDE)
                    )
                )
            },
            trailingIcon = {
                Icon(
                    modifier = Modifier,
                    imageVector = if (showDialog) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    tint = Color(0xffDCDCDC),
                    contentDescription = ""
                )
            },
            colors = TextFieldDefaults.colors().copy(
                focusedTextColor = Color.Black,
                focusedContainerColor = Color(0x1FC7CBD6),
                disabledTextColor = Color.Black,
                unfocusedContainerColor = Color(0x1FC7CBD6),
                disabledIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledContainerColor = Color(0x1FC7CBD6),
            ),
            enabled = false,
            interactionSource = interactionSource, // 将interactionSource传递给TextField
            value = heightString,
            onValueChange = {},
        )
    }

    HeightVerticalPickerWithInputDialog(
        visible = showDialog,
        initialHeight = height,
        selectedUnit = selectedUnit,
        onHeightSelected = {
            onHeightChange(it)
            showDialog = false
        },
        onDismiss = { showDialog = false }
    )

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HeightPickerSheet(
    initialHeight: Double = 0.0,
    selectedUnit: String,
    onHeightSelected: (HeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val sheetState = rememberModalBottomSheetState()
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = sheetState,
        containerColor = Color.White,
    ) {
        InputHeight(
            modifier = Modifier.fillMaxWidth(),
            initialHeight = initialHeight,
            selectedUnit = selectedUnit,
            onHeightSelected = onHeightSelected
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HeightInputDialog(
    visible: Boolean = true,
    initialHeight: Double = 0.0,
    selectedUnit: String = "I",
    onHeightSelected: (HeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {

    val context = LocalContext.current
    var selected by remember(selectedUnit) {
        mutableStateOf(selectedUnit)
    }
    val locale = LocalConfiguration.current.locales[0]
    var expanded by remember { mutableStateOf(false) }
    var heightInput by remember(initialHeight) {
        mutableStateOf(
            String.format(locale, "%.2f", initialHeight)
        )
    }
    var selectedHeightInch by remember(initialHeight) {
        mutableStateOf(
            ((initialHeight * 0.393701).toInt() % 12).toString()
        )
    }
    var selectedHeightFeet by remember(initialHeight) {
        mutableStateOf(
            ((initialHeight * 0.393701).toInt() / 12).toString()
        )
    }
    if (visible) {
        AlertDialog(
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false,
            ),
            modifier = Modifier.fillMaxWidth(0.9f),
            containerColor = Color.White,
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = stringResource(R.string.height),
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
            },
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ExposedDropdownMenuBox(
                        modifier = Modifier
                            .align(Alignment.End)
                            .background(Color.Transparent),
                        expanded = expanded,
                        onExpandedChange = { expanded = !it }
                    ) {
                        Card(
                            modifier = Modifier
                                .wrapContentHeight()
                                .menuAnchor(),
                            shape = RoundedCornerShape(size = 12.dp),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = 1.dp,
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .heightIn(min = 32.dp)
                                    .clickable { expanded = !expanded }
                                    .align(Alignment.CenterHorizontally),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    modifier = Modifier.padding(start = 16.dp),
                                    text = when (selected) {
                                        "M" -> stringResource(R.string.centimeters)
                                        else -> stringResource(R.string.feet_inches)
                                    },
                                    color = Color.Black,
                                    fontWeight = FontWeight.Light,
                                    fontSize = 12.sp,
                                    textAlign = TextAlign.Center
                                )
                                Icon(
                                    imageVector = Icons.Default.KeyboardArrowDown,
                                    contentDescription = null,
                                    tint = Color.Black,
                                    modifier = Modifier
                                        .padding(end = 16.dp)
                                        .size(16.dp)
                                )
                            }
                        }
                        // 下拉菜单
                        DropdownMenu(
                            modifier = Modifier,
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = {
                                    Text(
                                        text = stringResource(R.string.centimeters),
                                        color = Color.Black,
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    selected = "M"
                                },
                                colors = MenuDefaults.itemColors().copy(textColor = Color.Black)
                            )
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = { Text(stringResource(R.string.feet_inches)) },
                                onClick = {
                                    expanded = false
                                    selected = "I"
                                }
                            )
                        }
                    }
                    when (selected) {
                        "M" -> {
                            OutlinedTextField(
                                value = heightInput,
                                onValueChange = { input ->
                                    if (input == "") {
                                        heightInput = input
                                    }
                                    if (input.all { char -> char.isDigit() || char == '.' } &&
                                        (input.count { it == '.' } <= 1) &&
                                        (input == "." || input.toDoubleOrNull() != null)) {
                                        heightInput = input
                                    }
                                },
                                label = { Text(text = stringResource(R.string.height_unit_cm)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedTextColor = Color.Black,
                                    disabledTextColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    focusedBorderColor = Color(0xFF244CD2),
                                    unfocusedBorderColor = Color(0xFF999999),
                                    disabledBorderColor = Color.Transparent,
                                ),
                                singleLine = true,
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Done
                                ),
                            )
                        }

                        "I" -> {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp)
                            ) {
                                OutlinedTextField(
                                    value = selectedHeightFeet,
                                    onValueChange = {
                                        if (it.all { char -> char.isDigit() }) {
                                            selectedHeightFeet = it
                                        }
                                    },
                                    label = { Text(text = stringResource(R.string.feet_word)) },
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(end = 8.dp)
                                        .fillMaxWidth(),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedTextColor = Color.Black,
                                        disabledTextColor = Color.Transparent,
                                        focusedContainerColor = Color.Transparent,
                                        focusedBorderColor = Color(0xFF244CD2),
                                        unfocusedBorderColor = Color(0xFF999999),
                                        disabledBorderColor = Color.Transparent,
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Number,
                                        imeAction = ImeAction.Done
                                    ),
                                )
                                OutlinedTextField(
                                    value = selectedHeightInch,
                                    onValueChange = {
                                        if (it.all { char -> char.isDigit() }) {
                                            if (it.isNotEmpty()) {
                                                if (it.toInt() < 12) {
                                                    selectedHeightInch = it
                                                }
                                            } else {
                                                selectedHeightInch = it
                                            }
                                        }

                                    },
                                    label = { Text(text = stringResource(R.string.inches_word)) },
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .weight(1f),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedTextColor = Color.Black,
                                        disabledTextColor = Color.Transparent,
                                        focusedContainerColor = Color.Transparent,
                                        focusedBorderColor = Color(0xFF244CD2),
                                        unfocusedBorderColor = Color(0xFF999999),
                                        disabledBorderColor = Color.Transparent,
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Number,
                                        imeAction = ImeAction.Done
                                    ),
                                )
                            }

                        }

                        else -> {}
                    }
                }
            },
            confirmButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AIHTextButton(
                        text = stringResource(id = R.string.confirm),
                        onClick = {
                            when (selected) {
                                "M" -> {
                                    if (heightInput.isEmpty()) {
                                        DialogUtil.showToast(context.getString(R.string.height_cannot_be_0))
                                    } else if (heightInput.toDouble() < HEIGHT_CM_MIN_LIMIT || heightInput.toDouble() > HEIGHT_CM_MAX_LIMIT) {
                                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_metric))
                                    } else {
                                        onHeightSelected(
                                            HeightUnit(
                                                value = heightInput.toDouble(),
                                                unit = "M"
                                            )
                                        )
                                    }
                                }

                                else -> {
                                    if (selectedHeightFeet.isEmpty() && selectedHeightInch.isEmpty()) {
                                        DialogUtil.showToast(context.getString(R.string.height_cannot_be_0))
                                    } else {
                                        var value =
                                            selectedHeightFeet.toDoubleOrNull()?.times(12) ?: 0.0
                                        value += selectedHeightInch.toDoubleOrNull() ?: 0.0
                                        value *= 2.54

                                        LogUtil.i("height: ${value}, selectedHeightFeet:${selectedHeightFeet}, selectedHeightInch:${selectedHeightInch}")
                                        if (value < HEIGHT_CM_MIN_LIMIT || value > HEIGHT_CM_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                                        } else {
                                            onHeightSelected(
                                                HeightUnit(
                                                    value = value,
                                                    unit = "I"
                                                )
                                            )
                                        }
                                    }
                                }
                            }
                        },
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color.White
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                    )
                }
            },
            dismissButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.cancel),
                        color = Color(0x70CECECE),
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .clickable { onDismiss() }
                    )
                }
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InputHeight(
    modifier: Modifier = Modifier,
    initialHeight: Double = 170.00,
    selectedUnit: String = "M",
    onHeightSelected: (HeightUnit) -> Unit = { _ -> }
) {
    val context = LocalContext.current
    val heightData = (50..300).toList()
    val heightDataInch = (0..11).toList()
    val heightDataFeet = (1..8).toList()
    var selectedHeight by remember(initialHeight) { mutableIntStateOf(initialHeight.toInt()) }
    var selectedHeightInch by remember(initialHeight) {
        mutableIntStateOf(
            (initialHeight * 0.393701).toInt() % 12
        )
    }
    var selectedHeightFeet by remember(initialHeight) {
        mutableIntStateOf(
            (initialHeight * 0.393701).toInt() / 12
        )
    }
    var expanded by remember { mutableStateOf(false) }
    var selected by remember(selectedUnit) {
        mutableStateOf(
            when (selectedUnit) {
                "M" -> context.getString(R.string.centimeters)
                "I" -> context.getString(R.string.feet_inches)
                else -> ""
            }
        )
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.height),
            style = TextStyle(
                fontSize = 24.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
                textAlign = TextAlign.Center,
            )
        )

        ExposedDropdownMenuBox(
            modifier = Modifier
                .align(Alignment.End)
                .background(Color.White),
            expanded = expanded,
            onExpandedChange = { expanded = !it }
        ) {
            Card(
                modifier = Modifier
                    .height(32.dp)
                    .menuAnchor(),
                shape = RoundedCornerShape(size = 12.dp),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 1.dp,
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxHeight()
                        .clickable { expanded = !expanded }
                        .align(Alignment.CenterHorizontally),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.padding(start = 16.dp),
                        text = selected,
                        color = Color.Black,
                        fontWeight = FontWeight.Light,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        tint = Color.Black,
                        modifier = Modifier
                            .padding(end = 16.dp)
                            .size(16.dp)
                    )
                }
            }
            // 下拉菜单
            DropdownMenu(
                modifier = Modifier,
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                DropdownMenuItem(
                    modifier = Modifier,
                    text = {
                        Text(
                            text = stringResource(R.string.centimeters),
                            color = Color.Black,
                        )
                    },
                    onClick = {
                        expanded = false
                        selected = context.getString(R.string.centimeters)
                    },
                    colors = MenuDefaults.itemColors().copy(textColor = Color.Black)
                )
                DropdownMenuItem(
                    modifier = Modifier,
                    text = { Text(stringResource(R.string.feet_inches)) },
                    onClick = {
                        expanded = false
                        selected = context.getString(R.string.feet_inches)
                    }
                )
            }
        }

        if (selected == stringResource(R.string.centimeters)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                ListNumberPicker(
                    data = heightData,
                    selectIndex = heightData.indexOf(selectedHeight),
                    visibleCount = 3,
                    modifier = Modifier
                        .height(150.dp),
                    onSelect = { _, item ->
                        selectedHeight = item
                    },
                ) {
                    //判断是否是选中的状态，选中要展示的样式和非选中的样式
                    if (it == selectedHeight) {
                        val annotatedString = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 30.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF333333)
                                )
                            ) {
                                append("$it")
                            }
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF333333)
                                )
                            ) {
                                append(stringResource(id = R.string.cm))
                            }
                        }
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            HorizontalDivider(
                                modifier = Modifier
                                    .width(89.dp),
                                color = Color.Gray
                            )
                            Text(
                                text = annotatedString
                            )
                            HorizontalDivider(
                                modifier = Modifier
                                    .width(89.dp),
                                color = Color.Gray
                            )
                        }
                    } else {
                        val annotatedString = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 26.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF999999)
                                )
                            ) {
                                append("$it")
                            }
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF999999)
                                )
                            ) {
                                append(stringResource(id = R.string.cm))
                            }
                        }
                        Text(
                            text = annotatedString
                        )
                    }
                }
            }
            AIHTextButton(
                text = stringResource(id = R.string.next_step),
                onClick = {
                    if (selectedHeight.toDouble() < HEIGHT_CM_MIN_LIMIT || selectedHeight.toDouble() > HEIGHT_CM_MAX_LIMIT) {
                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                    } else {
                        onHeightSelected(
                            HeightUnit(
                                value = selectedHeight.toDouble(),
                                unit = "M"
                            )
                        )
                    }

                },
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 32.dp)
                    .fillMaxWidth(0.9f)
            )
        } else {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                ) {
                    ListNumberPicker(
                        data = heightDataFeet,
                        selectIndex = heightDataFeet.indexOf(selectedHeightFeet),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedHeightFeet = item
                        },
                        align = Alignment.CenterEnd
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedHeightFeet) {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append(stringResource(id = R.string.feet))
                                }
                            }
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = annotatedString
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append(stringResource(id = R.string.feet))
                                }
                            }
                            Column(
                                modifier = Modifier
                                    .width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = annotatedString,
                                )
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.width(4.dp))
                Row(
                    modifier = Modifier.weight(1f)
                ) {
                    ListNumberPicker(
                        data = heightDataInch,
                        selectIndex = heightDataInch.indexOf(selectedHeightInch),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedHeightInch = item
                        },
                        align = Alignment.CenterStart
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedHeightInch) {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append(stringResource(id = R.string.inch))
                                }
                            }
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = annotatedString
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append(stringResource(id = R.string.inch))
                                }
                            }
                            Column(
                                modifier = Modifier
                                    .width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = annotatedString
                                )
                            }
                        }
                    }
                }

            }
            AIHTextButton(
                text = stringResource(id = R.string.next_step),
                onClick = {
                    val value =
                        (selectedHeightFeet.toDouble() * 12 + selectedHeightInch.toDouble()) * 2.54
                    LogUtil.i("height: ${value}, selectedHeightFeet:${selectedHeightFeet.toDouble()}, selectedHeightInch:${selectedHeightInch.toDouble()}")
                    if (value < HEIGHT_CM_MIN_LIMIT || value > HEIGHT_CM_MAX_LIMIT) {
                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                    } else {
                        onHeightSelected(
                            HeightUnit(
                                value = value,
                                unit = "I"
                            )
                        )
                    }

                },
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 32.dp)
                    .fillMaxWidth(0.9f)
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HeightPickerDialog(
    visible: Boolean = true,
    initialHeight: Double = 0.0,
    selectedUnit: String = "I",
    onHeightSelected: (HeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val context = LocalContext.current
    val locale = LocalConfiguration.current.locales[0]
    var selected by remember(selectedUnit) {
        mutableStateOf(selectedUnit)
    }
    var expanded by remember { mutableStateOf(false) }
    val heightData = (50..300).toList()
    val heightDataInch = (0..11).toList()
    val heightDataFeet = (1..8).toList()
    var selectedHeight by remember(initialHeight) { mutableIntStateOf(initialHeight.toInt()) }

    var heightInput by remember(initialHeight) {
        mutableStateOf(
            String.format(locale, "%.2f", initialHeight)
        )
    }
    var selectedHeightInch by remember(initialHeight) {
        mutableStateOf(
            ((initialHeight * 0.393701).toInt() % 12)
        )
    }
    var selectedHeightFeet by remember(initialHeight) {
        mutableStateOf(
            ((initialHeight * 0.393701).toInt() / 12)
        )
    }
    if (visible) {
        AlertDialog(
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false,
            ),
            modifier = Modifier.fillMaxWidth(0.9f),
            containerColor = Color.White,
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = stringResource(R.string.height),
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
            },
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ExposedDropdownMenuBox(
                        modifier = Modifier
                            .align(Alignment.Start)
                            .background(Color.Transparent),
                        expanded = expanded,
                        onExpandedChange = { expanded = !it }
                    ) {
                        Card(
                            modifier = Modifier
                                .wrapContentHeight()
                                .menuAnchor(),
                            shape = RoundedCornerShape(size = 12.dp),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = 1.dp,
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .heightIn(min = 32.dp)
                                    .clickable { expanded = !expanded }
                                    .align(Alignment.CenterHorizontally),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    modifier = Modifier.padding(start = 16.dp),
                                    text = when (selected) {
                                        "M" -> stringResource(R.string.centimeters)
                                        else -> stringResource(R.string.feet_inches)
                                    },
                                    color = Color.Black,
                                    fontWeight = FontWeight.Light,
                                    fontSize = 12.sp,
                                    textAlign = TextAlign.Center
                                )
                                Icon(
                                    imageVector = Icons.Default.KeyboardArrowDown,
                                    contentDescription = null,
                                    tint = Color.Black,
                                    modifier = Modifier
                                        .padding(end = 16.dp)
                                        .size(16.dp)
                                )
                            }
                        }
                        // 下拉菜单
                        DropdownMenu(
                            modifier = Modifier,
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = {
                                    Text(
                                        text = stringResource(R.string.centimeters),
                                        color = Color.Black,
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    selected = "M"
                                },
                                colors = MenuDefaults.itemColors().copy(textColor = Color.Black)
                            )
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = { Text(stringResource(R.string.feet_inches)) },
                                onClick = {
                                    expanded = false
                                    selected = "I"
                                }
                            )
                        }
                    }
                    when (selected) {
                        "M" -> {
                            ListNumberPicker(
                                data = heightData,
                                selectIndex = heightData.indexOf(selectedHeight).takeIf { it != -1 }
                                    ?: 0,
                                visibleCount = 3,
                                modifier = Modifier
                                    .height(150.dp),
                                onSelect = { _, item ->
                                    selectedHeight = item
                                },
                            ) {
                                //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                if (it == selectedHeight) {
                                    val annotatedString = buildAnnotatedString {
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 30.sp,
                                                fontWeight = FontWeight(500),
                                                color = Color(0xFF333333)
                                            )
                                        ) {
                                            append("$it ")
                                        }
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 20.sp,
                                                fontWeight = FontWeight(500),
                                                color = Color(0xFF333333)
                                            )
                                        ) {
                                            append(stringResource(id = R.string.cm))
                                        }
                                    }
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally,
                                    ) {
                                        HorizontalDivider(
                                            modifier = Modifier
                                                .width(89.dp),
                                            color = Color.Gray
                                        )
                                        Text(
                                            text = annotatedString
                                        )
                                        HorizontalDivider(
                                            modifier = Modifier
                                                .width(89.dp),
                                            color = Color.Gray
                                        )
                                    }
                                } else {
                                    val annotatedString = buildAnnotatedString {
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 26.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFF999999)
                                            )
                                        ) {
                                            append("$it")
                                        }
                                        withStyle(
                                            style = SpanStyle(
                                                fontSize = 20.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFF999999)
                                            )
                                        ) {
                                            append(stringResource(id = R.string.cm))
                                        }
                                    }
                                    Text(
                                        text = annotatedString
                                    )
                                }
                            }
                        }

                        "I" -> {
                            Row(
                                modifier = Modifier,
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Row(
                                    modifier = Modifier.weight(1f),
                                ) {
                                    ListNumberPicker(
                                        data = heightDataFeet,
                                        selectIndex = heightDataFeet.indexOf(selectedHeightFeet)
                                            .takeIf { it != -1 } ?: 0,
                                        visibleCount = 3,
                                        modifier = Modifier
                                            .height(150.dp),
                                        onSelect = { _, item ->
                                            selectedHeightFeet = item
                                        },
                                        align = Alignment.CenterEnd
                                    ) {
                                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                        if (it == selectedHeightFeet) {
                                            val annotatedString = buildAnnotatedString {
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 30.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                ) {
                                                    append("$it")
                                                }
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 20.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                ) {
                                                    append(stringResource(id = R.string.feet))
                                                }
                                            }
                                            Column(
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp)
                                                        .padding(horizontal = 17.5.dp),
                                                    color = Color.Gray
                                                )
                                                Text(
                                                    text = annotatedString
                                                )
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp)
                                                        .padding(horizontal = 17.5.dp),
                                                    color = Color.Gray
                                                )
                                            }
                                        } else {
                                            val annotatedString = buildAnnotatedString {
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 26.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                ) {
                                                    append("$it")
                                                }
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 20.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                ) {
                                                    append(stringResource(id = R.string.feet))
                                                }
                                            }
                                            Column(
                                                modifier = Modifier
                                                    .width(89.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                Text(
                                                    text = annotatedString,
                                                )
                                            }
                                        }
                                    }
                                }
                                Spacer(modifier = Modifier.width(4.dp))
                                Row(
                                    modifier = Modifier.weight(1f)
                                ) {
                                    ListNumberPicker(
                                        data = heightDataInch,
                                        selectIndex = heightDataInch.indexOf(selectedHeightInch)
                                            .takeIf { it != -1 } ?: 0,
                                        visibleCount = 3,
                                        modifier = Modifier
                                            .height(150.dp),
                                        onSelect = { _, item ->
                                            selectedHeightInch = item
                                        },
                                        align = Alignment.CenterStart
                                    ) {
                                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                        if (it == selectedHeightInch) {
                                            val annotatedString = buildAnnotatedString {
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 30.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                ) {
                                                    append("$it ")
                                                }
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 20.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                ) {
                                                    append(stringResource(id = R.string.inch))
                                                }
                                            }
                                            Column(
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp)
                                                        .padding(horizontal = 17.5.dp),
                                                    color = Color.Gray
                                                )
                                                Text(
                                                    text = annotatedString
                                                )
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp)
                                                        .padding(horizontal = 17.5.dp),
                                                    color = Color.Gray
                                                )
                                            }
                                        } else {
                                            val annotatedString = buildAnnotatedString {
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 26.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                ) {
                                                    append("$it ")
                                                }
                                                withStyle(
                                                    style = SpanStyle(
                                                        fontSize = 20.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                ) {
                                                    append(stringResource(id = R.string.inch))
                                                }
                                            }
                                            Column(
                                                modifier = Modifier
                                                    .width(89.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                Text(
                                                    text = annotatedString
                                                )
                                            }
                                        }
                                    }
                                }

                            }
                        }

                        else -> {}
                    }
                }
            },
            confirmButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AIHTextButton(
                        text = stringResource(id = R.string.confirm),
                        onClick = {
                            when (selected) {
                                "M" -> {
                                    if (selectedHeight.toDouble() < HEIGHT_CM_MIN_LIMIT || selectedHeight.toDouble() > HEIGHT_CM_MAX_LIMIT) {
                                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_metric))
                                    } else {
                                        onHeightSelected(
                                            HeightUnit(
                                                value = selectedHeight.toDouble(),
                                                unit = "M"
                                            )
                                        )
                                    }
                                }

                                else -> {
                                    var value =
                                        selectedHeightFeet.toDouble().times(12)
                                    value += selectedHeightInch.toDouble()
                                    value *= 2.54

                                    LogUtil.i("height: ${value}, selectedHeightFeet:${selectedHeightFeet}, selectedHeightInch:${selectedHeightInch}")
                                    if (value < HEIGHT_CM_MIN_LIMIT || value > HEIGHT_CM_MAX_LIMIT) {
                                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                                    } else {
                                        onHeightSelected(
                                            HeightUnit(
                                                value = value,
                                                unit = "I"
                                            )
                                        )
                                    }

                                }
                            }
                        },
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color.White
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                    )
                }
            },
            dismissButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.cancel),
                        color = Color(0x70CECECE),
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .clickable { onDismiss() }
                    )
                }
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InputHeightPicker(
    modifier: Modifier = Modifier,
    initialHeight: Double = 170.00,
    selectedUnit: String = "M",
    onHeightSelected: (HeightUnit) -> Unit = { _ -> }
) {
    val context = LocalContext.current
    val heightData = (50..300).toList()
    val heightDataInch = (0..11).toList()
    val heightDataFeet = (1..8).toList()
    var selectedHeight by remember(initialHeight) { mutableIntStateOf(initialHeight.toInt()) }
    var selectedHeightInch by remember(initialHeight) {
        mutableIntStateOf(
            (initialHeight * 0.393701).toInt() % 12
        )
    }
    var selectedHeightFeet by remember(initialHeight) {
        mutableIntStateOf(
            (initialHeight * 0.393701).toInt() / 12
        )
    }
    var expanded by remember { mutableStateOf(false) }
    var selected by remember(selectedUnit) {
        mutableStateOf(
            when (selectedUnit) {
                "M" -> context.getString(R.string.centimeters)
                "I" -> context.getString(R.string.feet_inches)
                else -> ""
            }
        )
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.height),
            style = TextStyle(
                fontSize = 24.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
                textAlign = TextAlign.Center,
            )
        )

        ExposedDropdownMenuBox(
            modifier = Modifier
                .align(Alignment.End)
                .background(Color.White),
            expanded = expanded,
            onExpandedChange = { expanded = !it }
        ) {
            Card(
                modifier = Modifier
                    .height(32.dp)
                    .menuAnchor(),
                shape = RoundedCornerShape(size = 12.dp),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 1.dp,
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxHeight()
                        .clickable { expanded = !expanded }
                        .align(Alignment.CenterHorizontally),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.padding(start = 16.dp),
                        text = selected,
                        color = Color.Black,
                        fontWeight = FontWeight.Light,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        tint = Color.Black,
                        modifier = Modifier
                            .padding(end = 16.dp)
                            .size(16.dp)
                    )
                }
            }
            // 下拉菜单
            DropdownMenu(
                modifier = Modifier,
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                DropdownMenuItem(
                    modifier = Modifier,
                    text = {
                        Text(
                            text = stringResource(R.string.centimeters),
                            color = Color.Black,
                        )
                    },
                    onClick = {
                        expanded = false
                        selected = context.getString(R.string.centimeters)
                    },
                    colors = MenuDefaults.itemColors().copy(textColor = Color.Black)
                )
                DropdownMenuItem(
                    modifier = Modifier,
                    text = { Text(stringResource(R.string.feet_inches)) },
                    onClick = {
                        expanded = false
                        selected = context.getString(R.string.feet_inches)
                    }
                )
            }
        }

        if (selected == stringResource(R.string.centimeters)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                ListNumberPicker(
                    data = heightData,
                    selectIndex = heightData.indexOf(selectedHeight),
                    visibleCount = 3,
                    modifier = Modifier
                        .height(150.dp),
                    onSelect = { _, item ->
                        selectedHeight = item
                    },
                ) {
                    //判断是否是选中的状态，选中要展示的样式和非选中的样式
                    if (it == selectedHeight) {
                        val annotatedString = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 30.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF333333)
                                )
                            ) {
                                append("$it")
                            }
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF333333)
                                )
                            ) {
                                append(stringResource(id = R.string.cm))
                            }
                        }
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            HorizontalDivider(
                                modifier = Modifier
                                    .width(89.dp),
                                color = Color.Gray
                            )
                            Text(
                                text = annotatedString
                            )
                            HorizontalDivider(
                                modifier = Modifier
                                    .width(89.dp),
                                color = Color.Gray
                            )
                        }
                    } else {
                        val annotatedString = buildAnnotatedString {
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 26.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF999999)
                                )
                            ) {
                                append("$it")
                            }
                            withStyle(
                                style = SpanStyle(
                                    fontSize = 20.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF999999)
                                )
                            ) {
                                append(stringResource(id = R.string.cm))
                            }
                        }
                        Text(
                            text = annotatedString
                        )
                    }
                }
            }
            AIHTextButton(
                text = stringResource(id = R.string.next_step),
                onClick = {
                    if (selectedHeight.toDouble() < HEIGHT_CM_MIN_LIMIT || selectedHeight.toDouble() > HEIGHT_CM_MAX_LIMIT) {
                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                    } else {
                        onHeightSelected(
                            HeightUnit(
                                value = selectedHeight.toDouble(),
                                unit = "M"
                            )
                        )
                    }

                },
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 32.dp)
                    .fillMaxWidth(0.9f)
            )
        } else {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                ) {
                    ListNumberPicker(
                        data = heightDataFeet,
                        selectIndex = heightDataFeet.indexOf(selectedHeightFeet),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedHeightFeet = item
                        },
                        align = Alignment.CenterEnd
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedHeightFeet) {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append(stringResource(id = R.string.feet))
                                }
                            }
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = annotatedString
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append(stringResource(id = R.string.feet))
                                }
                            }
                            Column(
                                modifier = Modifier
                                    .width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = annotatedString,
                                )
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.width(4.dp))
                Row(
                    modifier = Modifier.weight(1f)
                ) {
                    ListNumberPicker(
                        data = heightDataInch,
                        selectIndex = heightDataInch.indexOf(selectedHeightInch),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedHeightInch = item
                        },
                        align = Alignment.CenterStart
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedHeightInch) {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                ) {
                                    append(stringResource(id = R.string.inch))
                                }
                            }
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = annotatedString
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp)
                                        .padding(horizontal = 17.5.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            val annotatedString = buildAnnotatedString {
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("$it")
                                }
                                withStyle(
                                    style = SpanStyle(
                                        fontSize = 20.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append(stringResource(id = R.string.inch))
                                }
                            }
                            Column(
                                modifier = Modifier
                                    .width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = annotatedString
                                )
                            }
                        }
                    }
                }

            }
            AIHTextButton(
                text = stringResource(id = R.string.next_step),
                onClick = {
                    val value =
                        (selectedHeightFeet.toDouble() * 12 + selectedHeightInch.toDouble()) * 2.54
                    LogUtil.i("height: ${value}, selectedHeightFeet:${selectedHeightFeet.toDouble()}, selectedHeightInch:${selectedHeightInch.toDouble()}")
                    if (value < HEIGHT_CM_MIN_LIMIT || value > HEIGHT_CM_MAX_LIMIT) {
                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                    } else {
                        onHeightSelected(
                            HeightUnit(
                                value = value,
                                unit = "I"
                            )
                        )
                    }

                },
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 32.dp)
                    .fillMaxWidth(0.9f)
            )
        }
    }
}

@Composable
fun HeightPickerWithInputDialog(
    visible: Boolean = true,
    initialHeight: Double = 0.0,
    selectedUnit: String = "M",
    onHeightSelected: (HeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val context = LocalContext.current
//    var selected by remember(selectedUnit) {
//        mutableStateOf(selectedUnit)
//    }
    var selectedOption by remember {
        mutableStateOf(
            when (selectedUnit) {
                "I" -> "inch"
                "M" -> "cm"
                else -> "inch"
            }
        )
    }
    val heightData = (50..300).toList()
    val heightDataInch = (20..118).toList()

    var heightInput by remember(initialHeight) {
        mutableDoubleStateOf(
            initialHeight
        )
    }
    var mTextFieldInput by remember(heightInput) { mutableStateOf(heightInput.toString()) }
    var iTextFieldInput by remember(heightInput) { mutableStateOf((heightInput * 0.3937007874).toString()) }

    if (visible) {
        Dialog(
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            val keyboardController = LocalSoftwareKeyboardController.current
            Column(
                modifier = Modifier
                    .background(Color.White, RoundedCornerShape(16.dp))
                    .fillMaxWidth(0.98f)
                    .padding(horizontal = 12.dp)
            ) {
                // close icon
                Row(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Icon(
                        modifier = Modifier
                            .size(24.dp)
                            .clickable {
                                onDismiss()
                            },
                        painter = painterResource(R.drawable.ic_close),
                        contentDescription = "close",
                        tint = Color.Black,
                    )
                }
                // title
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                        .padding(top = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.height),
                        style = TextStyle(
                            fontSize = 20.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(end = 24.dp)
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "inch",
                        selected = selectedOption == "inch",
                        onClick = {
                            selectedOption = "inch"
                        }
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "cm",
                        selected = selectedOption == "cm",
                        onClick = {
                            selectedOption = "cm"
                        }
                    )
                }
                // data picker
                Column(
                    modifier = Modifier,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    when (selectedOption) {
                        "cm" -> {
                            // data input
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 16.dp),
                                value = if (mTextFieldInput.toDoubleOrNull() == null
                                    || mTextFieldInput.last() == '.'
                                    || mTextFieldInput.toIntOrNull() != null
                                ) {
                                    mTextFieldInput
                                } else {
                                    String.format("%.1f", mTextFieldInput.toDouble())
                                },
                                onValueChange = { newText ->
//                                    LogUtil.i("newText$newText")
                                    if (newText.isEmpty()) {
                                        mTextFieldInput = ""
                                    } else if (newText.toDoubleOrNull() != null) {
                                        mTextFieldInput = newText
                                    }
                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        keyboardController?.hide()
                                        heightInput =
                                            if (mTextFieldInput.toDoubleOrNull() != null) mTextFieldInput.toDouble()
                                            else 0.0
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            HorizontalDataSelector(
                                modifier = Modifier.padding(top = 68.dp),
                                items = heightData,
                                initialData =
                                when (heightInput) {
                                    in (heightData.first() * 1.0..heightData.last() * 1.0) -> {
                                        heightInput
                                    }

                                    else -> {
                                        when {
                                            heightInput < heightData.first() * 1.0 -> heightData.first() * 1.0
                                            else -> heightData.last() * 1.0
                                        }
                                    }
                                },
                                unit = "cm"
                            ) { height ->
                                LogUtil.i("select item :${height}")
                                heightInput = height
                            }
                        }

                        "inch" -> {
                            // data input
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 16.dp),
                                value = if (iTextFieldInput.toDoubleOrNull() == null
                                    || iTextFieldInput.last() == '.'
                                    || iTextFieldInput.toIntOrNull() != null
                                ) {
                                    iTextFieldInput
                                } else {
                                    String.format("%.1f", iTextFieldInput.toDouble())
                                },
                                onValueChange = { newText ->
                                    if (newText.isEmpty()) {
                                        iTextFieldInput = ""
                                    } else if (newText.toDoubleOrNull() != null) {
                                        iTextFieldInput = newText
                                    }
                                    LogUtil.i("newText$newText, iTextFieldInput$iTextFieldInput")

                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        keyboardController?.hide()
                                        heightInput =
                                            if (iTextFieldInput.toDoubleOrNull() != null) iTextFieldInput.toDouble() * 2.54
                                            else 0.0
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            HorizontalDataSelector(
                                modifier = Modifier.padding(top = 68.dp),
                                items = heightDataInch,
                                initialData =
                                when (heightInput * 0.3937007874) {
                                    in (heightDataInch.first() * 1.0..heightDataInch.last() * 1.0) -> {
                                        heightInput * 0.3937007874
                                    }

                                    else -> {
                                        when {
                                            heightInput * 0.3937007874 < heightDataInch.first() * 1.0 -> heightDataInch.first() * 1.0
                                            else -> heightDataInch.last() * 1.0
                                        }
                                    }
                                },
                                unit = "inch"
                            ) { height ->
                                LogUtil.i("select item :${height}")
                                heightInput = (height * 2.54)
                            }
                        }

                        else -> {}
                    }
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 68.dp, bottom = 42.dp),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Box(
                        Modifier
                            .weight(1f)
                            .border(
                                width = 1.dp,
                                color = Color(0xFF3262FF),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .background(
                                shape = RoundedCornerShape(size = 16.dp),
                                color = Color.White
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable { onDismiss() }
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            modifier = Modifier
                                .align(Alignment.Center)
                                .clickable { onDismiss() },
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF3C72FF),
                            )
                        )
                    }
                    Spacer(modifier = Modifier.size(15.dp))
                    Box(
                        Modifier
                            .weight(1f)
                            .background(
                                Brush.linearGradient(
                                    colors = listOf(
                                        Color(0XFF73C5FF),
                                        Color(0XFF3161FF),
                                    )
                                ),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable {
                                when (selectedOption) {
                                    "cm" -> {
                                        if (heightInput < HEIGHT_CM_MIN_LIMIT || heightInput > HEIGHT_CM_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_metric))
                                        } else {
                                            onHeightSelected(
                                                HeightUnit(
                                                    value = heightInput,
                                                    unit = "M"
                                                )
                                            )
                                        }
                                    }

                                    else -> {
                                        if (heightInput < HEIGHT_CM_MIN_LIMIT || heightInput > HEIGHT_CM_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                                        } else {
                                            onHeightSelected(
                                                HeightUnit(
                                                    value = heightInput,
                                                    unit = "I"
                                                )
                                            )
                                        }

                                    }
                                }
                            }
                    ) {
                        Text(
                            modifier = Modifier.align(Alignment.Center),
                            text = stringResource(id = R.string.confirm),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color.White
                            )
                        )
                    }

                }
            }

        }
    }
}

@Composable
fun CustomItem(
    item: Int,
    isSelected: Boolean,
    unit: String
) {
    Box(
        modifier = Modifier
            .width(15.dp) // 父级宽度为 15.dp
            .wrapContentHeight(),
        contentAlignment = Alignment.TopCenter
    ) {
        Column(
            modifier = Modifier.wrapContentSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            if (item % 5 == 0 || item == 1) {
                val annotatedText = buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = if (isSelected) Color(0xFF333333) else Color(0xFFDEDEDE),
                            fontSize = if (isSelected) 24.sp else 20.sp,
                        )
                    ) {
                        append(item.toString())
                    }
                    if (isSelected) {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF333333),
                                fontSize = 16.sp
                            )
                        ) {
                            append(" $unit")
                        }
                    }
                }

                Box(
                    modifier = Modifier
                        .size(2.dp, 32.dp)
                        .background(
                            shape = RoundedCornerShape(size = 16.dp),
                            color = if (isSelected) Color(0xFF444444) else Color(0xFFDEDEDE)
                        )
                )
                Text(
                    text = annotatedText,
                    maxLines = 1,
                    overflow = TextOverflow.Visible, // 使用 Clip 以防止多行文本
                    softWrap = false,
                    modifier = Modifier
                        .padding(top = if (isSelected) 8.dp else 12.dp)
                        .wrapContentWidth()
                        .offset(
                            x = when (item) {
                                in 1..9 -> 0.dp
                                in 10..100 -> (-7.5).dp
                                else -> (-15).dp
                            }
                        )
                )
            } else {
                Box(
                    modifier = Modifier
                        .size(2.dp, 24.dp)
                        .background(
                            shape = RoundedCornerShape(size = 16.dp),
                            color = if (isSelected) Color(0xFF444444) else Color(0xFFF2F2F2)
                        )
                )
            }
        }
    }
}

@Composable
fun HorizontalDataSelector(
    modifier: Modifier,
    items: List<Int> = (50..300).toList(),
    initialData: Int = 50,
    unit: String = "",
    onItemSelected: (Int) -> Unit = { _ -> }
) {
    val listState = rememberLazyListState()

    val density = LocalDensity.current

    // Screen width in pixels
    val screenWidthPx = with(density) { LocalConfiguration.current.screenWidthDp.dp.toPx() }

    // Item width in pixels
    val itemWidthDp = 15.dp
    val itemWidthPx = with(density) { itemWidthDp.toPx() }

    // Flag to indicate programmatic scrolling
    var isProgrammaticScroll by remember { mutableStateOf(false) }

    // Calculate start and end padding to center items
    val startEndPaddingPx = (screenWidthPx - itemWidthPx) / 2
    val startEndPadding = with(density) { startEndPaddingPx.toDp() }

    // Calculate center item index
    val centerItemIndex by remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            if (layoutInfo.visibleItemsInfo.isNotEmpty()) {
                val viewportCenter =
                    layoutInfo.viewportStartOffset + layoutInfo.viewportSize.width / 2

                layoutInfo.visibleItemsInfo.minByOrNull { item ->
                    val itemCenter = item.offset + item.size / 2
                    abs(itemCenter - viewportCenter)
                }?.index ?: 0
            } else {
                0
            }
        }
    }

    // Scroll to initialData when it changes
    LaunchedEffect(initialData) {
        LogUtil.i("initialData: $initialData")
        val index = items.indexOf(initialData)
        if (index != -1) {
            // Calculate the offset to center the item
            val scrollOffset =
                if (itemWidthPx % 1 > 0) (itemWidthPx * 2).toInt() + 1 else (itemWidthPx * 2).toInt()
            listState.scrollToItem(index, scrollOffset)
        }
    }

    // Observe scroll state and call onItemSelected when scroll stops
    LaunchedEffect(listState) {
        snapshotFlow { listState.isScrollInProgress }
            .distinctUntilChanged()
            .collect { isScrolling ->
                if (!isScrolling) {
                    onItemSelected(items[centerItemIndex])
                }
            }
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White),
    ) {
        Icon(
            modifier = Modifier
                .padding(top = 16.dp)
                .width(16.dp),
            painter = painterResource(id = R.drawable.baseline_arrow_right_24),
            contentDescription = ""
        )
        LazyRow(
            state = listState,
            contentPadding = PaddingValues(horizontal = startEndPadding),
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .background(Color.White)
                .height(60.dp) // Set height to accommodate items
        ) {
            itemsIndexed(items) { index, item ->
                val isSelected = index == centerItemIndex
                CustomItem(
                    item = item,
                    isSelected = isSelected,
                    unit = unit
                )
            }
        }
        Icon(
            modifier = Modifier
                .padding(top = 16.dp)
                .width(16.dp),
            painter = painterResource(id = R.drawable.baseline_arrow_left_24),
            contentDescription = ""
        )
    }
}

@Composable
fun HorizontalDataSelector(
    modifier: Modifier,
    items: List<Int> = (50..300).toList(),
    initialData: Double = 50.0,
    unit: String = "",
    zoom: Int = 10,
    onItemSelected: (Double) -> Unit = { _ -> }
) {
    val listState = rememberLazyListState()

    val density = LocalDensity.current

    // Screen width in pixels
    val screenWidthPx = with(density) { LocalConfiguration.current.screenWidthDp.dp.toPx() }

    // Item width in pixels
    val itemWidthDp = 15.dp
    val itemWidthPx = with(density) { itemWidthDp.toPx() }

    // Calculate start and end padding to center items
    val startEndPaddingPx = (screenWidthPx - itemWidthPx) / 2
    val startEndPadding = with(density) { startEndPaddingPx.toDp() }

    val lists = (items.first() * zoom..items.last() * zoom).toList()

    // Calculate center item index
    val centerItemIndex by remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            if (layoutInfo.visibleItemsInfo.isNotEmpty()) {
                val viewportCenter =
                    layoutInfo.viewportStartOffset + layoutInfo.viewportSize.width / 2

                layoutInfo.visibleItemsInfo.minByOrNull { item ->
                    val itemCenter = item.offset + item.size / 2
                    abs(itemCenter - viewportCenter)
                }?.index ?: 0
            } else {
                0
            }
        }
    }

    // Scroll to initialData when it changes
    LaunchedEffect(initialData) {
        LogUtil.i("initialData: $initialData")
        val index = lists.indexOf((initialData * zoom).roundToInt())
        if (index != -1) {
            // Calculate the offset to center the item
            val scrollOffset =
                if (itemWidthPx % 1 > 0) (itemWidthPx * 2).toInt() + 1 else (itemWidthPx * 2).toInt()
            listState.scrollToItem(index, scrollOffset)
        }
    }

    // Observe scroll state and call onItemSelected when scroll stops
    LaunchedEffect(listState) {
        snapshotFlow { listState.isScrollInProgress }
            .distinctUntilChanged()
            .collect { isScrolling ->
                if (!isScrolling) {
                    onItemSelected(lists[centerItemIndex] / 10.0)
                }
            }
    }

    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White),
    ) {
        Icon(
            modifier = Modifier
                .padding(top = 16.dp)
                .width(16.dp),
            painter = painterResource(id = R.drawable.baseline_arrow_right_24),
            contentDescription = ""
        )
        LazyRow(
            state = listState,
            contentPadding = PaddingValues(horizontal = startEndPadding),
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
                .background(Color.White)
                .height(60.dp) // Set height to accommodate items
        ) {
            itemsIndexed(lists) { index, item ->
                val isSelected = index == centerItemIndex
                CustomItem(
                    item = item / 10.0,
                    isSelected = isSelected,
                    unit = unit
                )
            }
        }
        Icon(
            modifier = Modifier
                .padding(top = 16.dp)
                .width(16.dp),
            painter = painterResource(id = R.drawable.baseline_arrow_left_24),
            contentDescription = ""
        )
    }
}

@Composable
fun CustomItem(
    item: Double,
    isSelected: Boolean,
    unit: String
) {
    Box(
        modifier = Modifier
            .width(15.dp) // 父级宽度为 15.dp
            .wrapContentHeight(),
        contentAlignment = Alignment.TopCenter
    ) {
        Column(
            modifier = Modifier.wrapContentSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            if (item % 1 == 0.0) {
                val annotatedText = buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = if (isSelected) Color(0xFF333333) else Color(0xFFDEDEDE),
                            fontSize = if (isSelected) 24.sp else 20.sp,
                        )
                    ) {
                        append("$item".format(".1f"))
                    }
                    if (isSelected) {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF333333),
                                fontSize = 16.sp
                            )
                        ) {
                            append(" $unit")
                        }
                    }
                }

                Box(
                    modifier = Modifier
                        .size(2.dp, 32.dp)
                        .background(
                            shape = RoundedCornerShape(size = 16.dp),
                            color = if (isSelected) Color(0xFF444444) else Color(0xFFDEDEDE)
                        )
                )
                Text(
                    text = annotatedText,
                    maxLines = 1,
                    overflow = TextOverflow.Visible, // 使用 Clip 以防止多行文本
                    softWrap = false,
                    modifier = Modifier
                        .padding(top = if (isSelected) 8.dp else 12.dp)
                        .wrapContentWidth()
                        .offset(
                            x = when (item) {
                                in 1.0..9.0 -> 0.dp
                                in 10.0..100.0 -> (-7.5).dp
                                else -> (-15).dp
                            }
                        )
                )
            } else {
                Box(
                    modifier = Modifier
                        .size(2.dp, 24.dp)
                        .background(
                            shape = RoundedCornerShape(size = 16.dp),
                            color = if (isSelected) Color(0xFF444444) else Color(0xFFF2F2F2)
                        )
                )
            }
        }
    }
}

@Composable
fun CustomChip(
    modifier: Modifier,
    text: String,
    selected: Boolean,
    onClick: () -> Unit
) {
    Box(
        modifier = modifier
            .border(
                width = 1.dp,
                color = Color(0xFF3262FF),
                shape = RoundedCornerShape(size = 16.dp)
            )
            .background(
                shape = RoundedCornerShape(size = 16.dp),
                color = if (selected) {
                    Color(0xFF3262FF)
                } else {
                    Color.White
                }
            )
            .widthIn(min = 70.dp)
            .wrapContentHeight()
            .padding(horizontal = 12.dp, vertical = 4.dp)
            .clickable {
                onClick()
            }
    ) {
        Text(
            modifier = Modifier.align(Alignment.Center),
            text = text,
            fontSize = 16.sp,
            fontWeight = FontWeight(500),
            color = if (selected) {
                Color.White
            } else {
                Color(0xFF3262FF)
            },
            lineHeight = TextUnit.Unspecified
        )
    }
}
