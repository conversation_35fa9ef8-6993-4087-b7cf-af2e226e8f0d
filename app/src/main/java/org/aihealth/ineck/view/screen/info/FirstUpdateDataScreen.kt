package org.aihealth.ineck.view.screen.info

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.snapping.rememberSnapFlingBehavior
import androidx.compose.foundation.interaction.Interaction
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.SecondaryIndicator
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import org.aihealth.ineck.R
import org.aihealth.ineck.model.angles.Gender
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.CreateAccountViewModel
import org.aihealth.ineck.viewmodel.user


@Composable
fun SetUerInfoRoute() {
    val viewModel = viewModel<CreateAccountViewModel>()
    SetUserInfoScreen(
        modifier = Modifier.fillMaxSize(),
        uploadData = viewModel::uploadData
    )
}

@Composable
fun SetUserInfoScreen(
    modifier: Modifier = Modifier,
    uploadData: (User) -> Unit = {}
) {
    var name by remember {
        mutableStateOf(user.name)
    }

    var gender by remember {
        mutableStateOf(Gender.U)
    }

    var height by remember {
        mutableDoubleStateOf(0.0)
    }

    var heightUnit by remember {
        mutableStateOf("I")
    }

    var weight by remember {
        mutableDoubleStateOf(0.0)
    }

    var weightUnit by remember {
        mutableStateOf("I")
    }
    var birthdate by remember {
        mutableStateOf("")
    }
    var birthdateString by remember {
        mutableStateOf("")
    }
    val focusManager = LocalFocusManager.current

    BasePageView(
        modifier = modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures(onTap = {
                    focusManager.clearFocus()
                })
            }
            .windowInsetsPadding(WindowInsets.ime),
        background = {
            Image(
                painter = painterResource(id = R.drawable.bg_login_new),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .imePadding()
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 61.dp, start = 16.dp),
                text = stringResource(R.string.improve_information),

                style = TextStyle(
                    fontSize = 32.sp,
                    lineHeight = 42.sp,
                    fontWeight = FontWeight(700),
                    color = Color(0xFFFFFFFF),
                )
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp, start = 16.dp),
                text = stringResource(R.string.improve_information_hint),

                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 18.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0x8C464F6F),
                )
            )
            Card(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 22.dp)
                    .padding(horizontal = 8.dp),
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors().copy(
                    containerColor = Color.White,
                    contentColor = Color.White,
                    disabledContentColor = Color.White,
                    disabledContainerColor = Color.White,
                ),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 1.dp,
                )
            ) {
                val genders = listOf(Gender.M, Gender.F, Gender.U)
                var selectedTabIndex by remember { mutableIntStateOf(genders.indexOf(gender)) }
                TabRow(
                    selectedTabIndex = selectedTabIndex,
                    containerColor = Color(0xFFF0F4FF),
                    contentColor = Color.Black,
                    indicator = { tabPositions ->
                        SecondaryIndicator(
                            modifier = Modifier
                                .tabIndicatorOffset(tabPositions[selectedTabIndex]),
                            height = 3.dp,
                            color = Color(0xFF3161FF) // Blue underline color
                        )
                    },
                    divider = {}
                ) {
                    genders.forEachIndexed { index, g ->
                        Tab(
                            modifier = Modifier.fillMaxWidth(),
                            selected = selectedTabIndex == index,
                            onClick = { selectedTabIndex = index },
                            text = {
                                Text(
                                    stringResource(g.text)
                                )
                            },
                            interactionSource = NoRippleInteractionSource()
                        )
                    }
                }
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {

                    EditeName(
                        modifier = Modifier.padding(top = 16.dp),
                        value = name,
                        onValueChange = {
                            name = it.ifEmpty { "" }
                        }
                    )
                    EditHeight(
                        modifier = Modifier.padding(top = 16.dp),
                        height = height,
                        selectedUnit = heightUnit,
                        onHeightChange = {
                            height = it.value
                            heightUnit = it.unit
                        },
                    )
                    EditWeight(
                        modifier = Modifier.padding(top = 16.dp),
                        weight = weight,
                        selectedUnit = weightUnit,
                        onWeightChange = {
                            weight = it.value
                            weightUnit = it.unit
                        },
                    )
                    val context = LocalContext.current
                    EditBirthdate(
                        modifier = Modifier.padding(top = 16.dp),
                        birthdate = birthdate,
                        onBirthdateChange = {
                            if (it != 0L) {
                                birthdate = convertUtcMillisToDateString(it,context.getString(R.string.dateFormat))
                                birthdateString = TimeUtil.longToFormattedString(it?:System.currentTimeMillis())
                            }
                        },
                    )
                    AIHTextButton(
                        text = stringResource(id = R.string.next_step),
                        onClick = {
                            val user = User().apply {
                                this.gender = when (gender) {
                                    Gender.U -> "U"
                                    Gender.F -> "F"
                                    Gender.M -> "M"
                                }
                                this.name = name
                                this.birthdate = birthdateString
                                this.height = height
                                this.weight = weight
                                this.preferences.heightUnit = heightUnit
                                this.preferences.weightUnit = weightUnit
                            }
                            uploadData(user)
                        },
                        style = TextStyle(
                            fontWeight = FontWeight(400),
                            color = Color(0xFFF7F7F7),
                            fontSize = 20.sp,
                            textAlign = TextAlign.Center
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(top = 100.dp, bottom = 64.dp),
                    )
                }
            }
        }
    }
}

/**
 * 编辑姓名
 * @param modifier Modifier
 * @param value String
 * @param onValueChange Function1<String, Unit>
 */
@Composable
fun EditeName(
    modifier: Modifier = Modifier,
    value: String = "",
    onValueChange: (String) -> Unit = {}
) {
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    Column(
        modifier = modifier
            .fillMaxWidth()
    ) {
        Text(
            text = stringResource(id = R.string.name),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF444444)
            )
        )
        TextField(
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth()
                .onFocusChanged { focusState ->
                    if (focusState.isFocused) {
                        keyboardController?.show()
                    }
                },
            shape = RoundedCornerShape(24.dp),
            maxLines = 1,
            keyboardOptions = KeyboardOptions().copy(
                imeAction = ImeAction.Done
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    focusManager.clearFocus()
                }
            ),
            placeholder = {
                Text(
                    text = stringResource(id = R.string.write_real_name),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFDEDEDE)
                    )
                )
            },
            colors = TextFieldDefaults.colors().copy(
                focusedTextColor = Color.Black,
                disabledTextColor = Color.Transparent,
                focusedContainerColor = Color(0x1FC7CBD6),
                unfocusedContainerColor = Color(0x1FC7CBD6),
                disabledIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent
            ),
            value = value,
            onValueChange = {
                onValueChange(it)
            }
        )
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun <T> ListNumberPicker(
    data: List<T>,
    selectIndex: Int,
    visibleCount: Int,
    modifier: Modifier = Modifier,
    onSelect: (index: Int, item: T) -> Unit,
    align: Alignment = Alignment.Center,
    content: @Composable (item: T) -> Unit
) {
    BoxWithConstraints(modifier = modifier, propagateMinConstraints = true) {
        val pickerHeight = maxHeight
        val size = data.size
        val itemHeight = pickerHeight / visibleCount
        val listState = rememberLazyListState(
            initialFirstVisibleItemIndex = selectIndex
        )
        val firstVisibleItemIndex by remember { derivedStateOf { listState.firstVisibleItemIndex } }
        LazyColumn(
            modifier = Modifier,
            state = listState,
            flingBehavior = rememberSnapFlingBehavior(listState),
        ) {
            //占据相应的高度比如显示5个那么中间那个是选中的其他的就是非选中，但也要占据一定的空间。
            for (i in 1..visibleCount / 2) {
                item {
                    Surface(modifier = Modifier.height(itemHeight)) {}
                }
            }
            items(size) { index ->
                //预防滑动的时候出现数组越界
                if (firstVisibleItemIndex >= size) {
                    onSelect(size - 1, data[size - 1])
                } else {
                    onSelect(firstVisibleItemIndex, data[firstVisibleItemIndex])
                }
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(itemHeight),
                    contentAlignment = align,
                ) {
                    content(data[index])
                }
            }
            for (i in 1..visibleCount / 2) {
                item {
                    Surface(modifier = Modifier.height(itemHeight)) {}
                }
            }
        }
    }
}

@Preview()
@Composable
private fun Preview() {
    SetUserInfoScreen()
}

class NoRippleInteractionSource : MutableInteractionSource {
    override val interactions: Flow<Interaction> = emptyFlow()
    override suspend fun emit(interaction: Interaction) {}
    override fun tryEmit(interaction: Interaction) = true
}

