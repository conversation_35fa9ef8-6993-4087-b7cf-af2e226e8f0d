package org.aihealth.ineck.view.screen.article

import android.content.Intent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.net.toUri
import androidx.lifecycle.ViewModelProvider
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.screen.defaultscreen.NoMessageScreen

@Composable
fun ArticleRoute() {
    val viewModel = ViewModelProvider(activity)[ArticleViewModel::class.java]

    val advices = viewModel.articleList.collectAsState().value
    val isLoading = viewModel.isArticleLoading.collectAsState().value
    val hasMorePages = viewModel.hasMoreArticlePages.collectAsState().value

    // 触发加载更多的监听器
    val listState = rememberLazyListState()

    // 监听列表滚动到底部，触发加载更多
    val shouldLoadMore = remember {
        derivedStateOf {
            val lastVisibleItem = listState.layoutInfo.visibleItemsInfo.lastOrNull()
            val totalItems = listState.layoutInfo.totalItemsCount

            // 检查是否达到了列表底部
            lastVisibleItem != null &&
                    totalItems > 0 &&
                    lastVisibleItem.index == totalItems - 1 &&
                    !isLoading &&
                    hasMorePages
        }
    }

    /**
     * 初次数据请求
     */
    LaunchedEffect(Unit) {
        viewModel.fetchArticleList(refresh = true)
    }

    // 使用key参数为true，确保每次shouldLoadMore.value变化时都会执行
    LaunchedEffect(shouldLoadMore.value) {
        if (shouldLoadMore.value) {
            LogUtil.d("Reached end of list, loading more articles")
            viewModel.loadMoreArticle()
        }
    }

    ArticleScreen(
        listState = listState,
        articles = advices,
        isLoading = isLoading,
        hasMorePages = hasMorePages,
        onLoadMore = {
            // 提供额外的手动触发方式
            if (!isLoading && hasMorePages) {
                LogUtil.d("Manual trigger to load more articles")
                viewModel.loadMoreArticle()
            }
        }
    )
}

@Composable
fun ArticleScreen(
    modifier: Modifier = Modifier,
    listState: LazyListState = rememberLazyListState(),
    articles: List<Article>,
    isLoading: Boolean,
    hasMorePages: Boolean,
    onLoadMore: () -> Unit = {}
) {
    val context = LocalContext.current

    if (articles.isNotEmpty()) {
        LazyColumn(
            modifier = modifier
                .fillMaxSize()
                .background(Color.White)
                .padding(horizontal = 12.dp, vertical = 8.dp),
            verticalArrangement = Arrangement.spacedBy(4.dp),
            state = listState
        ) {
            itemsIndexed(articles) { index, article ->
                ArticleItem(
                    article = article,
                    onClick = {
                        val intent = Intent(Intent.ACTION_VIEW, article.link.toUri())
                        context.startActivity(intent)
                    }
                )
                if (index != articles.lastIndex) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 16.dp, bottom = 12.dp)
                            .height(1.dp)
                            .background(color = Color(0xFFFAFAFA))
                    )
                }
            }

            // 底部加载更多指示器
            item {
                if (isLoading) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator(color = Color(0xFF4285F4))
                    }
                } else if (!hasMorePages && articles.isNotEmpty()) {
                    // 已经加载完所有数据
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.at_bottom),
                            style = Typography.bodyMedium,
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                } else if (hasMorePages && articles.isNotEmpty()) {
                    // 添加一个点击加载更多的区域作为备选方案
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp)
                            .clickable { onLoadMore() },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(R.string.load_more),
                            style = Typography.bodyMedium,
                            color = Color(0xFF4285F4)
                        )
                    }
                }
            }
        }
    } else {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            NoMessageScreen()
        }
    }
}

@Composable
private fun ArticleItem(
    modifier: Modifier = Modifier,
    article: Article,
    onClick: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(120.dp)
            .clickable(onClick = onClick),
        horizontalArrangement = Arrangement.spacedBy(32.dp)
    ) {
        Column(
            modifier = Modifier
                .weight(1f)
                .fillMaxHeight(),
            verticalArrangement = Arrangement.SpaceAround
        ) {
            Text(
                text = article.title,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF333333),
                ),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis

            )

            if (article.describe.isNotEmpty()) {
                Text(
                    modifier = Modifier.padding(top = 3.dp),
                    text = article.describe,
                    style = TextStyle(
                        fontSize = 11.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Justify,
                    ),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis

                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 12.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                if (article.authorAvatar.isNotEmpty()) {
                    AsyncImage(
                        model = article.authorAvatar,
                        contentDescription = null,
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .border(1.dp, Color(0xFF4285F4), CircleShape),
                        contentScale = ContentScale.Crop,
                        error = painterResource(R.drawable.header_3)
                    )
                }
                Column(
                    modifier = Modifier.padding(start = 8.dp)
                ) {
                    if (article.author.isNotEmpty()) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Text(
                                text = article.author,
                                style = TextStyle(
                                    fontSize = 11.sp,
                                    fontWeight = FontWeight(500),
                                    color = Color(0xFF333333),
                                    textAlign = TextAlign.Justify,
                                )
                            )
                        }
                    }

                    if (article.date.isNotEmpty()) {
                        Text(
                            text = formatDate(article.date),
                            style = TextStyle(
                                fontSize = 8.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF999999),
                                textAlign = TextAlign.Justify,
                            )
                        )
                    }
                }
            }
        }
        Column(
            modifier = Modifier
                .fillMaxHeight(),
            verticalArrangement = Arrangement.Top
        ) {
            // 如果有网络图片URL，加载网络图片；否则使用本地图片
            if (article.image.isNotEmpty()) {
                // 使用Coil加载网络图片
                AsyncImage(
                    model = article.image,
                    contentDescription = null,
                    modifier = Modifier
                        .size(96.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .border(1.dp, Color(0xFFEEEEEE), RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Crop,
                    alignment = Alignment.Center,
                    error = painterResource(id = article.placeholderImage)
                )
            } else {
                // 使用本地图片（占位图）
                Image(
                    painter = painterResource(id = article.placeholderImage),
                    contentDescription = null,
                    modifier = Modifier
                        .size(96.dp)
                        .clip(RoundedCornerShape(8.dp)),
                    contentScale = ContentScale.Fit,
                    alignment = Alignment.Center
                )
            }
        }
    }
}

// 日期格式化函数，处理ISO格式的日期字符串
private fun formatDate(dateString: String): String {
    return try {
        // 假设日期格式为 "2024-12-25T00:00:00"
        val parts = dateString.split("T")[0].split("-")
        if (parts.size == 3) {
            "${parts[0]}-${parts[1]}-${parts[2]}"
        } else {
            dateString
        }
    } catch (e: Exception) {
        dateString
    }
}
