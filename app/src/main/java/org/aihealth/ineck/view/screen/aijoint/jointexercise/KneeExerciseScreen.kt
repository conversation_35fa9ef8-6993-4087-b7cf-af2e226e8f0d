package org.aihealth.ineck.view.screen.aijoint.jointexercise

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.Angle
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.AIHSelectButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.screen.home.AngleStatue
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.user

@Composable
fun KneeExerciseScreen(
    viewModel: MainViewModel
) {
//    LogUtil.i("KneeExerciseScreen Max:${viewModel.deviceScreen.kneeAngleMax},Min:${viewModel.deviceScreen.kneeAngleMin}")
    val scope = rememberCoroutineScope()
    // 当前设备是否连接
    var isConnected by remember {
        mutableStateOf(false)
    }
    var isExercising by remember {
        mutableStateOf(false)
    }
    var isActionOne by remember {
        mutableStateOf(true)
    }

    // 设备角度
    val deviceAngle = remember { Animatable(0f) }
    val imgRes = remember {
        mutableIntStateOf(R.drawable.knee_0)
    }
    var previousAngle by remember { mutableIntStateOf(0) }
    val finishedCount by viewModel.deviceScreen.kneeExercise.collectAsState()

    LaunchedEffect(deviceAngle.value) {
        if (isActionOne) {
            val currentAngle = deviceAngle.value.toInt()
            if (currentAngle >= viewModel.deviceScreen.kneeAngleMax) {
                previousAngle = currentAngle
            }
            if (previousAngle >= viewModel.deviceScreen.kneeAngleMax && currentAngle < 5) {
                previousAngle = 0
                viewModel.deviceScreen.setKneeExerciseOne()
            }
        } else {
            val currentAngle = deviceAngle.value.toInt()
            if (currentAngle <= (viewModel.deviceScreen.kneeAngleMin)) {
                previousAngle = currentAngle
            }
            if (previousAngle <= (viewModel.deviceScreen.kneeAngleMin) && currentAngle > -10 && currentAngle < 5) {
                previousAngle = 0
                viewModel.deviceScreen.setKneeExerciseTwo()
            }
        }
    }
    LaunchedEffect(Unit) {
        isExercising = false
    }
    LaunchedEffect(isConnected) {
        while (isConnected) {
            delay(250)
            val angle = deviceAngle.value.toInt()
            when (angle) {
                in -80 until -45 -> {
                    imgRes.intValue = R.drawable.knee_p50
                }

                in -45 until -35 -> {
                    imgRes.intValue = R.drawable.knee_p40
                }

                in -35 until -25 -> {
                    imgRes.intValue = R.drawable.knee_p30
                }

                in -25 until -20 -> {
                    imgRes.intValue = R.drawable.knee_p20
                }

                in -20 until -15 -> {
                    imgRes.intValue = R.drawable.knee_p15
                }

                in -15 until -10 -> {
                    imgRes.intValue = R.drawable.knee_p10
                }

                in -5 until 0 -> {
                    imgRes.intValue = R.drawable.knee_p5
                }

                in 0 until 5 -> {
                    imgRes.intValue = R.drawable.knee_0
                }

                in 5 until 15 -> {
                    imgRes.intValue = R.drawable.knee_10
                }

                in 15 until 25 -> {
                    imgRes.intValue = R.drawable.knee_20
                }

                in 25 until 35 -> {
                    imgRes.intValue = R.drawable.knee_30
                }

                in 35 until 45 -> {
                    imgRes.intValue = R.drawable.knee_40
                }

                in 45 until 65 -> {
                    imgRes.intValue = R.drawable.knee_60
                }

                in 65 until 75 -> {
                    imgRes.intValue = R.drawable.knee_70
                }

                in 75 until 85 -> {
                    imgRes.intValue = R.drawable.knee_80
                }

                in 85 until 180 -> {
                    imgRes.intValue = R.drawable.knee_90
                }

                else -> {
                    imgRes.intValue = R.drawable.knee_0
                }
            }
        }
    }

    BasePageView(
        showBackIcon = true,
        title = stringResource(id = R.string.knne_exercise),
    ) {
        Column(
            modifier = Modifier
                .padding(top = 5.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 28.dp)
                    .height(40.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AIHSelectButton(
                    selectedIndex = if (isActionOne) 0 else 1,
                    array = arrayOf(
                        stringResource(id = R.string.action_one),
                        stringResource(id = R.string.action_two),
                    ),
                    onClick = {
                        LogUtil.i("click:$it")
                        isActionOne = it == 0
                    },
                    modifier = Modifier
                        .width(200.dp)
                        .height(50.dp)
                        .height(44.dp),
                    backgroundColor = Color(0XFFEBEEF8),
                    padding = PaddingValues(5.dp)
                )
                if (isExercising) {
                    Text(
                        text = stringResource(id = R.string.exercising),
                        style = TextStyle(
                            fontSize = 24.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF000000),
                        )
                    )
                }
            }
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Image(
                        painter = painterResource(id = imgRes.intValue),
                        contentDescription = "",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .size(200.dp)
                    )
                    if (isExercising) {
                        AngleStatue(
                            modifier = Modifier
                                .align(Alignment.BottomEnd)
                                .padding(end = 16.dp, bottom = 16.dp),
                            angle = deviceAngle.value.toInt()
                        )
                    }
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 100.dp),
                    horizontalArrangement = Arrangement.Center

                ) {
                    Column(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .size(70.dp)
                                .background(
                                    color = Color.Transparent,
                                    shape = CircleShape
                                )
                                .border(
                                    width = 1.dp,
                                    color = Color(0xFF1E4BDF),
                                    shape = CircleShape
                                )
                        ) {
                            Text(
                                modifier = Modifier.align(Alignment.Center),
                                text = if (isActionOne) "${finishedCount.first}/10" else "${finishedCount.second}/10",
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF333333),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }
                    }
                    Column(
                        modifier = Modifier.weight(1f),
                        horizontalAlignment = Alignment.CenterHorizontally

                    ) {
                        if (isExercising) {
                            Box(
                                modifier = Modifier
                                    .size(70.dp)
                                    .background(
                                        color = Color.White,
                                        shape = CircleShape
                                    )
                                    .border(
                                        width = 1.dp,
                                        color = Color(0xFF1E4BDF),
                                        shape = CircleShape
                                    )
                                    .clickable(
                                        interactionSource = remember {
                                            MutableInteractionSource()
                                        },
                                        indication = null,
                                    ) {
                                        isExercising = false
                                    }
                            ) {

                                Text(
                                    modifier = Modifier.align(Alignment.Center),
                                    text = stringResource(id = R.string.stop),
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color.Red,
                                    )
                                )
                            }
                        } else {
                            Box(
                                modifier = Modifier
                                    .size(70.dp)
                                    .background(
                                        color = Color(0xFF1E4BDF),
                                        shape = CircleShape
                                    )
                                    .clickable(
                                        interactionSource = remember {
                                            MutableInteractionSource()
                                        },
                                        indication = null,
                                    ) {
                                        isExercising = true
                                    }
                            ) {
                                Text(
                                    modifier = Modifier.align(Alignment.Center),
                                    text = stringResource(id = R.string.start),
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFFFFFFFF),
                                    )
                                )
                            }
                        }
                    }
                }
            }
        }
    }
    LaunchedEffect(finishedCount.first) {
        if (finishedCount.first == 10) {
            DialogUtil.showToast(baseApplication.getString(R.string.action_one_finished))
        }
    }
    LaunchedEffect(finishedCount.second) {
        if (finishedCount.second == 10) {
            DialogUtil.showToast(baseApplication.getString(R.string.action_two_finished))
        }
    }
    // 获取设备状态
    LaunchedEffect(isExercising) {
        if (isExercising) {
            launch {
                viewModel.deviceScreen.kneeDeviceState.collect {
                    isConnected = it.isDeviceConnected ?: false
                    it.angle?.let { angle ->
                        deviceAngle.animateTo((angle).toFloat(), tween(250))
                        scope.launch(Dispatchers.IO) {
                            viewModel.angleDao.add(
                                Angle(
                                    uuid = user.uuid,
                                    angle = angle,
                                    type = DeviceType.KneeJoint.name,
                                    timestamp = System.currentTimeMillis() / 1000
                                )
                            )
                        }
                    } ?: 0
                }
            }
        }
    }
}
