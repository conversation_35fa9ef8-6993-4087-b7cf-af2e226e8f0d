package org.aihealth.ineck.view.screen.onboarding

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.splash.pages
import kotlin.math.absoluteValue

/**
 * 首次打开应用UI
 */

@Composable
fun OnBoardingScreen(
    onEvent: () -> Unit
) {
    val pagerState = rememberPagerState(initialPage = 0) {
        pages.size // Ensure the pager state knows the number of pages
    }
    Box(
        modifier = Modifier.fillMaxSize()
    ) {
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize()
        ) { page ->
            Box(modifier = Modifier.fillMaxSize()) {
                Image(
                    painter = painterResource(id = pages[page].image),
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth(0.9f)
                        .navigationBarsPadding()
                        .padding(bottom = 48.dp),
                ) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .matchParentSize(),
                        painter = painterResource(id = R.drawable.on_boarding_button),
                        contentScale = ContentScale.FillBounds,
                        contentDescription = null
                    )
                    Row(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .clickable(
                                interactionSource = remember { MutableInteractionSource() },
                                indication = null,
                                enabled = page == pages.size - 1
                            ) {
                                if (page == pages.size - 1) {
                                    onEvent()
                                }
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (page == pages.size - 1) {
                            Text(
                                modifier = Modifier.padding(start = 24.dp, end = 6.dp),
                                text = stringResource(R.string.experience_now),
                                fontSize = 24.sp,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                color = Color.White
                            )
                            Icon(
                                modifier = Modifier.padding(end = 24.dp),
                                painter = painterResource(id = R.drawable.icon_onboarding_button),
                                contentDescription = null,
                                tint = Color.White
                            )
                        } else {
                            Text(
                                modifier = Modifier,
                                text = stringResource(id = pages[page].text),
                                fontSize = 24.sp,
                                fontWeight = androidx.compose.ui.text.font.FontWeight.Medium,
                                color = Color.White,
                                maxLines = 1,
                            )
                        }
                    }
                }
            }
        }
        AnimatedPageIndicator(
            pageCount = pages.size,
            currentPage = pagerState.currentPage,
            currentPageOffset = pagerState.currentPageOffsetFraction,
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .navigationBarsPadding()
                .padding(bottom = 20.dp),

            )

    }
}

@Composable
fun AnimatedPageIndicator(
    pageCount: Int,
    currentPage: Int,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(pageCount) { page ->
            val isSelected = page == currentPage

            // 动画大小变化
            val size by animateDpAsState(
                targetValue = if (isSelected) 10.dp else 8.dp,
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                ),
                label = "size"
            )

            // 动画透明度变化
            val color by animateColorAsState(
                targetValue = if (isSelected)
                    Color.White
                else
                    Color.White.copy(alpha = 0.3f),
                animationSpec = tween(300),
                label = "color"
            )

            Box(
                modifier = Modifier
                    .size(size)
                    .background(color = color, shape = CircleShape)
                    // 可选：添加点击事件以直接跳到该页
                    .clickable { /* 如果需要点击跳转可以在这里处理 */ }
            )
        }
    }
}

@Composable
fun AnimatedPageIndicator(
    pageCount: Int,
    currentPage: Int,
    currentPageOffset: Float = 0f,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        repeat(pageCount) { page ->
            // 计算当前页面和下一页的激活度
            val isCurrentPage = page == currentPage
            val isNextPage = page == currentPage + 1 || (currentPage == pageCount - 1 && page == 0)

            // 计算激活程度 (0.0-1.0)
            val activeFraction = when {
                isCurrentPage -> 1f - currentPageOffset.absoluteValue
                isNextPage -> currentPageOffset.absoluteValue
                else -> 0f
            }

            // 大小动画
            val size by animateDpAsState(
                targetValue = 8.dp + (2.dp * activeFraction),
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessLow
                ),
                label = "size"
            )

            // 颜色动画
            val color by animateColorAsState(
                targetValue = Color.White.copy(alpha = 0.3f + (0.7f * activeFraction)),
                animationSpec = tween(150),
                label = "color"
            )

            Box(
                modifier = Modifier
                    .size(size)
                    .background(color = color, shape = CircleShape)
                    .clickable { /* 点击跳转处理 */ }
            )
        }
    }
}

@Preview
@Composable
private fun Preview() {
    OnBoardingScreen { }
}