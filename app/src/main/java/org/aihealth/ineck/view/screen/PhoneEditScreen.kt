package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.CountryCode
import org.aihealth.ineck.util.CountryCodeUtil
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.viewmodel.PersonalDataViewModel
import org.aihealth.ineck.viewmodel.user

@Composable
fun PhoneEditRoute(
    viewModel: PersonalDataViewModel = viewModel()
) {
    val currentPhone = user.phone

    PhoneEditScreen(
        initialPhone = currentPhone,
        onSave = { phone ->
            if (phone.isBlank()) {
                // 允许清空手机号码
                viewModel.setMobileNumber("")
                finish()
            } else if (isValidPhoneNumber(phone)) {
                viewModel.setMobileNumber(phone)
                finish()
            } else {
                DialogUtil.showToast(localeResources.getString(R.string.write_correct_phone))
            }
        },
        onCancel = { finish() }
    )
}

@Composable
fun PhoneEditScreen(
    initialPhone: String = "",
    onSave: (String) -> Unit = { },
    onCancel: () -> Unit = {}
) {
    // Parse initial phone number
    val (initialCountryCode, initialNationalNumber) = CountryCodeUtil.parsePhoneNumber(initialPhone)
    
    var selectedCountryCode by remember { mutableStateOf(initialCountryCode) }
    var nationalNumber by remember { mutableStateOf(initialNationalNumber) }
    var countryCodeDropdownExpanded by remember { mutableStateOf(false) }

    BaseEditScreen(
        onSave = {
            // Use universal format for storage (E.164 format)
            val formattedNumber = CountryCodeUtil.formatForStorage(selectedCountryCode, nationalNumber)
            onSave(formattedNumber)
        },
        onCancel = onCancel
    ) {
        // Phone Field Container - Two-part module as shown in the image
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp)
                )
        ) {
            // Upper section: Country Code Selector
            CountryCodeSelector(
                selectedCountryCode = selectedCountryCode,
                expanded = countryCodeDropdownExpanded,
                onExpandedChange = { countryCodeDropdownExpanded = it },
                onCountryCodeSelected = { selectedCountryCode = it }
            )


            AIHDivider(modifier = Modifier)
            
            // Lower section: Phone Number Input
            PhoneNumberInput(
                value = nationalNumber,
                onValueChange = { nationalNumber = it },
                countryCode = selectedCountryCode
            )
        }
    }
}

@Composable
private fun CountryCodeSelector(
    selectedCountryCode: CountryCode,
    expanded: Boolean,
    onExpandedChange: (Boolean) -> Unit,
    onCountryCodeSelected: (CountryCode) -> Unit
) {
    Box {
        // Country Code Selector Button
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable { onExpandedChange(true) }
                .padding(horizontal = 16.dp, vertical = 16.dp), // 添加与 OutlinedTextField 一致的 padding
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "${selectedCountryCode.code} ${selectedCountryCode.name}",
                fontSize = 16.sp,
                color = Color(0xFF007AFF), // Blue color as shown in image
                fontWeight = FontWeight.Medium
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            // Dropdown arrow
            Icon(
                painter = painterResource(id = R.drawable.baseline_arrow_drop_down_24),
                contentDescription = "Select Country Code",
                modifier = Modifier.size(20.dp),
                tint = Color(0xFF007AFF)
            )
        }
        
        // Dropdown Menu
        DropdownMenu(
            expanded = expanded,
            onDismissRequest = { onExpandedChange(false) },
            modifier = Modifier
                .background(Color.White)
                .width(200.dp)
        ) {
            CountryCodeUtil.countryCodes.forEach { countryCode ->
                DropdownMenuItem(
                    text = {
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = countryCode.flag,
                                fontSize = 16.sp
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "${countryCode.code} ${countryCode.name}",
                                fontSize = 14.sp,
                                color = Color(0xFF333333)
                            )
                        }
                    },
                    onClick = {
                        onCountryCodeSelected(countryCode)
                        onExpandedChange(false)
                    }
                )
            }
        }
    }
}

@Composable
private fun PhoneNumberInput(
    value: String,
    onValueChange: (String) -> Unit,
    countryCode: CountryCode
) {
    // 实时校验手机号码
    val isValid = remember(value, countryCode) {
        if (value.isBlank()) true else CountryCodeUtil.isValidPhoneNumber(countryCode, value)
    }
    
    // 获取校验提示信息
    val validationMessage = remember(value, countryCode) {
        when {
            value.isBlank() -> null
            !isValid -> getValidationMessage(countryCode)
            else -> null
        }
    }
    
    Column {
        OutlinedTextField(
            value = value,
            onValueChange = { newValue: String ->
                // Allow only digits for the national number
                val filteredValue = newValue.filter { it.isDigit() }
                if (filteredValue.length <= 15) { // Reasonable max length
                    onValueChange(filteredValue)
                }
            },
            modifier = Modifier.fillMaxWidth(),
            singleLine = true,
            textStyle = LocalTextStyle.current.copy(
                fontSize = 16.sp,
                textAlign = TextAlign.Start,
                fontWeight = FontWeight.Medium,
                color = if (isValid || value.isBlank()) Color(0xFF333333) else Color(0xFFE53E3E)
            ),
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Phone,
                imeAction = ImeAction.Done,
                autoCorrect = false
            ),
            keyboardActions = KeyboardActions(
                onDone = {
                    // Handle done action
                }
            ),
            placeholder = {
                Text(
                    text = getPlaceholderText(countryCode),
                    fontSize = 16.sp,
                    color = Color(0xFF999999),
                    fontWeight = FontWeight.Normal
                )
            },
            colors = OutlinedTextFieldDefaults.colors(
                focusedBorderColor = if (isValid || value.isBlank()) Color(0xFF007AFF) else Color(0xFFE53E3E),
                unfocusedBorderColor = if (isValid || value.isBlank()) Color.Transparent else Color(0xFFE53E3E),
                focusedLabelColor = Color(0xFF007AFF),
                unfocusedLabelColor = Color(0xFF999999),
                cursorColor = Color(0xFF007AFF),
                focusedContainerColor = Color.Transparent,
                unfocusedContainerColor = Color.Transparent
            )
        )
        
        // 显示校验提示信息
        if (validationMessage != null) {
            Text(
                text = validationMessage,
                fontSize = 12.sp,
                color = Color(0xFFE53E3E),
                modifier = Modifier.padding(start = 4.dp, top = 4.dp)
            )
        }
    }
}

/**
 * Get validation message for specific country
 */
private fun getValidationMessage(countryCode: CountryCode): String {
    return when (countryCode.isoCode) {
        "US", "CA" -> "Please enter a valid 10-digit phone number"
        "CN" -> "Please enter a valid 11-digit phone number starting with 1"
        "GB" -> "Please enter a valid 10-11 digit phone number"
        "JP" -> "Please enter a valid 10-11 digit phone number"
        "KR" -> "Please enter a valid 10-11 digit phone number"
        else -> "Please enter a valid phone number (7-15 digits)"
    }
}

/**
 * Get placeholder text for specific country
 */
private fun getPlaceholderText(countryCode: CountryCode): String {
    return when (countryCode.isoCode) {
        "US", "CA" -> "Enter 10-digit number"
        "CN" -> "Enter 11-digit number"
        "GB" -> "Enter 10-11 digit number"
        "JP" -> "Enter 10-11 digit number"
        "KR" -> "Enter 10-11 digit number"
        else -> "Enter phone number"
    }
}

private fun isValidPhoneNumber(phone: String): Boolean {
    if (phone.isBlank()) return true // Allow empty phone numbers
    
    try {
        val (countryCode, nationalNumber) = CountryCodeUtil.parsePhoneNumber(phone)
        return CountryCodeUtil.isValidPhoneNumber(countryCode, nationalNumber)
    } catch (e: Exception) {
        // Fallback to basic validation for unknown formats
        val cleanNumber = phone.filter { it.isDigit() }
        return cleanNumber.length >= 7 && cleanNumber.length <= 15
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun PreviewPhoneEditScreen() {
    AIH_UserTheme {
        PhoneEditScreen(
            initialPhone = "+86 182 4563 7968"
        )
    }
}
