package org.aihealth.ineck.view.screen.exercise

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import coil.compose.AsyncImage
import kotlinx.coroutines.delay
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.ImproveDetailViewModel
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.user

@Composable
fun PhotoTrainingScreen(
    mainViewModel: MainViewModel,
    viewModel: ImproveDetailViewModel
) {

    BasePageView(
        title = stringResource(id = R.string.improve_cervical_spine),
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true
    ) {
        if (viewModel.pageIndex == 0) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState())
                    .padding(16.dp),
                verticalArrangement = Arrangement.Top,
            ) {
                // 封面图
                Surface(
                    modifier = Modifier
                        .height(200.dp)
                        .fillMaxWidth(),
                ) {
                    AsyncImage(
                        model = viewModel.improveProgramDetail.cover,
                        contentDescription = viewModel.improveProgramDetail.title,
                        placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .clip(RoundedCornerShape(17.dp)),
                        alignment = Alignment.Center
                    )
                }
                // 课程标题
                Text(
                    text = viewModel.improveProgramDetail.title,
                    fontSize = MaterialTheme.typography.displayMedium.fontSize,
                    color = Color.Black,
                    textAlign = TextAlign.Start,
                    maxLines = 3,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = 20.dp
                        )
                )
                /* 改善项目方案关键属性， 时长、已练习人数等 */
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    val durationString = TimeUtil.convertSecondsToAnnotatedString14(
                        viewModel.improveProgramDetail.duration,
                        LocalContext.current
                    )

                    val numberOfPeopleString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF6181E9),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(viewModel.improveProgramDetail.frequency.toString())
                        }
                    }
                    val deviceString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF6181E9),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(stringResource(id = R.string.none))
                        }
                    }
                    AttributeCard(
                        modifier = Modifier.weight(1f),
                        icons = R.drawable.img_duration_time,
                        number = durationString,
                        unit = "",
                        description = stringResource(id = R.string.duration)
                    )
                    Spacer(modifier = Modifier.width(5.dp))

                    AttributeCard(
                        modifier = Modifier.weight(1f),
                        icons = R.drawable.participated,
                        number = numberOfPeopleString,
                        unit = stringResource(id = R.string.people_text),
                        description = stringResource(id = R.string.participated)
                    )
                    Spacer(modifier = Modifier.width(5.dp))

                    AttributeCard(
                        modifier = Modifier.weight(1f),
                        icons = R.drawable.img_device,
                        number = deviceString,
                        unit = "",
                        description = stringResource(id = R.string.need_device)
                    )
                }

                /* 课程介绍 */
                Text(
                    text = viewModel.improveProgramDetail.description,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),
                    ),
                    textAlign = TextAlign.Start,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp)
                )
                /* 分割线 */
                HorizontalDivider(
                    thickness = 1.dp,
                    color = Color(0xFFEFEFEF)
                )

                /* 开始练习按钮 */
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                        .padding(
                            top = 20.dp
                        ),
                    verticalArrangement = Arrangement.Bottom,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    // 会员相关
                    if (MainViewModel.onlineTime <= MainViewModel.trailTimeLimit || !viewModel.improveProgramDetail.isMembershipRequired || user.vipStatus) {
                        AIHButton(
                            text = stringResource(id = R.string.start),
                            onClick = {
                                viewModel.pageIndex += 1
                            },
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 12.dp)
                        )
                    } else if (!user.memberTrial) {
                        AIHButton(
                            text = stringResource(id = R.string.try_free),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 12.dp)
                        )
                    } else {
                        AIHButton(
                            text = stringResource(id = R.string.go_to_subscription_membership),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 12.dp)
                        )
                    }
                }

            }
        } else {
            PhotoTrainingContext(
                photoTrainingData = viewModel.dataList[viewModel.pageIndex - 1]
            ) { index ->
                val next = stringResource(id = R.string.next_step)
                val previous = stringResource(id = R.string.previous)
                val finish = stringResource(id = R.string.finish)

                if (index < viewModel.dataList.size - 1) {
                    val nextButtonCountdownTotalSeconds = 12
                    val nextButtonEnabledAtTimeLeft = 4

                    var timeLeft by remember(index) {
                        mutableIntStateOf(nextButtonCountdownTotalSeconds)
                    }
                    
                    LaunchedEffect(key1 = index, key2 = viewModel.pageIndex) {
                        if (viewModel.pageIndex - 1 == index) {
                            if (timeLeft <= 0 && nextButtonCountdownTotalSeconds > 0) {
                                timeLeft = nextButtonCountdownTotalSeconds
                            } else if (timeLeft > nextButtonCountdownTotalSeconds) {
                                timeLeft = nextButtonCountdownTotalSeconds
                            }

                            var currentLocalTimeLeft = timeLeft
                            while (currentLocalTimeLeft > 0) {
                                if (viewModel.pageIndex - 1 != index) break
                                delay(1000L)
                                currentLocalTimeLeft--
                                timeLeft = currentLocalTimeLeft
                            }
                            
                            if (viewModel.pageIndex - 1 == index && timeLeft == 0) {
                                viewModel.pageIndex += 1
                            }
                        }
                    }

                    CountdownButton(
                        modifier = Modifier.fillMaxWidth(),
                        timeLeft = timeLeft,
                        text = next,
                        onClick = {
                            if (viewModel.pageIndex - 1 == index && timeLeft <= nextButtonEnabledAtTimeLeft) {
                                viewModel.pageIndex += 1
                            }
                        },
                        enabled = timeLeft <= nextButtonEnabledAtTimeLeft && (viewModel.pageIndex - 1 == index)
                    )

                    if (index > 0) {
                         AIHOutlinedButton(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 4.dp),
                            text = previous,
                            backgroundColor = Color.White,
                            fontColor = Color(0xFF444444),
                            onClick = {
                                if (viewModel.pageIndex - 1 == index) {
                                   viewModel.pageIndex -= 1
                                }
                            }
                        )
                    }
                } else {
                    val finishButtonTotalCountdown = 3
                    
                    var timeLeft by remember(index) {
                        mutableIntStateOf(finishButtonTotalCountdown)
                    }

                    LaunchedEffect(key1 = index, key2 = viewModel.pageIndex) {
                         if (viewModel.pageIndex - 1 == index) {
                             if (timeLeft <= 0 && finishButtonTotalCountdown > 0) {
                                 timeLeft = finishButtonTotalCountdown
                             } else if (timeLeft > finishButtonTotalCountdown) {
                                 timeLeft = finishButtonTotalCountdown
                             }

                            var currentLocalTimeLeft = timeLeft
                            while (currentLocalTimeLeft > 0) {
                                if (viewModel.pageIndex - 1 != index) break
                                delay(1000L)
                                currentLocalTimeLeft--
                                timeLeft = currentLocalTimeLeft
                            }
                            if (viewModel.pageIndex - 1 == index && timeLeft == 0) {
                                viewModel.finishedPhotoDialogVisible = true
                            }
                        }
                    }

                    CountdownButton(
                        modifier = Modifier.fillMaxWidth(),
                        text = finish,
                        timeLeft = timeLeft,
                        onClick = {
                            if (viewModel.pageIndex - 1 == index) {
                                viewModel.finishedPhotoDialogVisible = true
                            }
                        },
                        enabled = timeLeft <= 0 && (viewModel.pageIndex - 1 == index)
                    )
                    AIHOutlinedButton(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 4.dp),
                        backgroundColor = Color.White,
                        fontColor = Color(0xFF444444),
                        text = previous,
                        onClick = {
                            if (viewModel.pageIndex - 1 == index) {
                               viewModel.pageIndex -= 1
                            }
                        }
                    )
                }
            }
        }
        FinishedHockDialog(mainViewModel, viewModel)
    }
    /**
    TailMembershipDialog(
        mainViewModel,
        onClick = {
            mainViewModel.isShowTailMembershipDialog = false
            userSP.edit().putBoolean("isShowTailMembershipDialog", false).apply()
        }
    )
    TrialMembershipFinishDialog(mainViewModel)

    if (!userSP.getBoolean(
            "showTrailMembershipFinishedDialog",
            false
        ) && MainViewModel.onlineTime > MainViewModel.trailTimeLimit
    ) {
        mainViewModel.isShowTailMembershipFinishedDialog = true
    }
     **/
    /* 处理生命周期 */
    DisposableEffect(Unit) {
        onDispose {
            LogUtil.i("PhotoScreen onDispose")
            viewModel.pageIndex = 0
            viewModel.finishedPhotoDialogVisible = false
        }
    }
}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun FinishedHockDialog(
    mainViewModel: MainViewModel,
    viewModel: ImproveDetailViewModel
) {
    if (viewModel.finishedPhotoDialogVisible) {
        Dialog(onDismissRequest = {
//            viewModel.finishedPhotoDialogVisible = false
        }) {
            Column(
                Modifier
                    .width(330.dp)
                    .background(Color.White, RoundedCornerShape(10.dp))
                    .padding(horizontal = 16.dp, vertical = 30.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    modifier = Modifier,
                    text = stringResource(id = R.string.please_it_up),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF2B56D7),
                        letterSpacing = 0.49.sp,
                    )
                )
                Spacer(modifier = Modifier.height(15.dp))
                Text(
                    text = stringResource(id = R.string.cervical_comprehensive_evaluation),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF464646),
                        textAlign = TextAlign.Center,
                        letterSpacing = 0.63.sp,
                    )
                )
                Text(
                    text = stringResource(id = R.string.your_score, viewModel.score.value),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF464646),
                        textAlign = TextAlign.Center,
                        letterSpacing = 0.63.sp,
                    )
                )
                Spacer(modifier = Modifier.height(15.dp))
                HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                Spacer(modifier = Modifier.height(15.dp))
                Text(
                    modifier = Modifier
                        .clickable {
                            viewModel.score.value = 0
                            viewModel.pageIndex = 0
                            viewModel.finishedPhotoDialogVisible = false
                        },
                    text = stringResource(id = R.string.training_again),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF2B56D7),
                        letterSpacing = 0.56.sp,
                    )
                )
                Spacer(modifier = Modifier.height(15.dp))
                HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                Spacer(modifier = Modifier.height(15.dp))
                Text(
                    modifier = Modifier
                        .clickable {
                            viewModel.finishedPhotoDialogVisible = false
                            LogUtil.i("MainViewModel:$mainViewModel")
                            popScreen(Screen.Main.route)
                        },
                    text = stringResource(id = R.string.next_practice),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF2B56D7),
                        letterSpacing = 0.56.sp,
                    )
                )
                Spacer(modifier = Modifier.height(15.dp))
                HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                Spacer(modifier = Modifier.height(15.dp))
                Text(
                    modifier = Modifier
                        .clickable {
                            viewModel.finishedDialogVisible = false
                            startScreen(Screen.TrainingEvaluation.route, true)
                        },
                    text = stringResource(id = R.string.evaluate),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF2B56D7),
                        letterSpacing = 0.56.sp,
                    )
                )
                Spacer(modifier = Modifier.height(15.dp))
                HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                Spacer(modifier = Modifier.height(15.dp))

            }
        }
    }
    LaunchedEffect(viewModel.finishedPhotoDialogVisible) {
        viewModel.postUserExerciseData()
    }

}


data class PhotoTrainingData(
    /**
     * 序号
     */
    val index: Int,
    /**
     * 标题 ex: 动作一：等长运动
     */
    val title: String,
    /**
     * 描述 ex: 坐立或站立缩颈
     */
    val description: String,
    /**
     * 动作 ex: 以 "中立 "的头部姿势坐立或站着缩颈，保持3秒。
     */
    val action: String,
    /**
     * 图片链接
     */
    val photo: String,
)

@Composable
fun PhotoTrainingContext(
    photoTrainingData: PhotoTrainingData,
    bottomBtn: @Composable (Int) -> Unit,
) {
    DisposableEffect(photoTrainingData.action) {
        TextToSpeech.ttsSpeaking(photoTrainingData.action)
        onDispose {
            TextToSpeech.ttsStop()
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 16.dp, vertical = 20.dp),
        verticalArrangement = Arrangement.Top,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = photoTrainingData.title,
            style = TextStyle(
                fontSize = 22.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF2B4DE3)
            )
        )
        Text(
            text = photoTrainingData.description,
            style = TextStyle(
                fontSize = 22.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF666666)
            )
        )
        AsyncImage(
            model = photoTrainingData.photo,
            contentDescription = photoTrainingData.action,
            placeholder = painterResource(id = R.drawable.img_cover_placeholder),
            contentScale = ContentScale.Fit,
            modifier = Modifier
                .clip(RoundedCornerShape(17.dp))
                .padding(top = 25.dp)
                .size(300.dp),
            alignment = Alignment.Center
        )
        Text(
            modifier = Modifier.padding(top = 40.dp),
            text = photoTrainingData.action,
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF666666),
            )
        )

        Column(modifier = Modifier.padding(top = 16.dp)) {
            bottomBtn(photoTrainingData.index)
        }


    }
}

@Composable
private fun CountdownButton(
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
    timeLeft: Int = 3,
    text: String = "",
    onClick: () -> Unit,
    backgroundColor: Color = Color(0XFF1E4BDF),
    shape: Shape = CircleShape,
    enabled: Boolean = true,
) {
    Box(
        modifier = modifier
            .fillMaxSize()
            .clip(shape),
    ) {
        Button(
            modifier = Modifier
                .fillMaxSize()
                .clip(shape),
            onClick = onClick,
            enabled = enabled,
            colors = ButtonDefaults.buttonColors(
                containerColor = backgroundColor,
                disabledContainerColor = Color(0xFFDADADA)
            )
        ) {
            // Display the time left on the button
            Text(
                text = if (timeLeft > 0) "$text $timeLeft" else text,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFF9F9F9),
                )
            )
        }
    }
}