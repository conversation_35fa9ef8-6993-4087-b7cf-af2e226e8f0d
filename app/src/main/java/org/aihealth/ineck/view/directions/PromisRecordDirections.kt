package org.aihealth.ineck.view.directions

import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.angles.Promis

class PromisRecordDirections {
    data class PromisRecordArgs(
        val model: PromisRecordModel
    )

    @Parcelize
    data class PromisRecordModel(
        val isPromis: Boolean = false,
        val showToggleButton: Boolean = true,
        val baseTime: Promis?
    ) : Parcelable

    companion object {
        val route = "${Screen.PromisHisRecord.route}?model={model}"
        val gson = Gson()

        val argumentsList:MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model"){
                   type = object : NavType<PromisRecordModel>(false){
                       override val name: String
                           get() = "PromisRecordModel"

                       override fun get(bundle: Bundle, key: String): PromisRecordModel? {
                            return bundle.getParcelable(key)
                       }

                       override fun parseValue(value: String): PromisRecordModel {
                           return gson.fromJson(value, object : TypeToken<PromisRecordModel>(){}.type)
                       }

                       override fun put(bundle: Bundle, key: String, value: PromisRecordModel) {
                           bundle.putParcelable(key,value)
                       }
                   }
                }
            )
        fun parseArguments(backStackEntry: androidx.navigation.NavBackStackEntry): PromisRecordArgs {
            return PromisRecordArgs(
                model = backStackEntry.arguments?.getParcelable<PromisRecordModel>("model")!!
            )
        }
        fun actionToOdiPromisRecord(model: PromisRecordModel):String{
            return Screen.PromisHisRecord.route+"?model=${android.net.Uri.encode(gson.toJson(model))}"
        }

    }
}