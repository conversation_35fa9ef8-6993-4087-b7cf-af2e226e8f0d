package org.aihealth.ineck.view.screen.meeting

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.res.Configuration
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.SystemClock
import android.view.WindowManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.ViewModelProvider
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.notification.ScreenRecordService
import org.aihealth.ineck.util.ErrorMsgUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.SharedViewModel
import us.zoom.sdk.IncomingLiveStreamStatus
import us.zoom.sdk.SubSessionKit
import us.zoom.sdk.SubSessionUserHelpRequestHandler
import us.zoom.sdk.UVCCameraStatus
import us.zoom.sdk.ZoomVideoSDK
import us.zoom.sdk.ZoomVideoSDKAnnotationHelper
import us.zoom.sdk.ZoomVideoSDKAudioHelper
import us.zoom.sdk.ZoomVideoSDKAudioRawData
import us.zoom.sdk.ZoomVideoSDKAudioStatus
import us.zoom.sdk.ZoomVideoSDKCRCCallStatus
import us.zoom.sdk.ZoomVideoSDKCameraControlRequestHandler
import us.zoom.sdk.ZoomVideoSDKCameraControlRequestType
import us.zoom.sdk.ZoomVideoSDKChatHelper
import us.zoom.sdk.ZoomVideoSDKChatMessage
import us.zoom.sdk.ZoomVideoSDKChatMessageDeleteType
import us.zoom.sdk.ZoomVideoSDKChatPrivilegeType
import us.zoom.sdk.ZoomVideoSDKDelegate
import us.zoom.sdk.ZoomVideoSDKFileTransferStatus
import us.zoom.sdk.ZoomVideoSDKLiveStreamHelper
import us.zoom.sdk.ZoomVideoSDKLiveStreamStatus
import us.zoom.sdk.ZoomVideoSDKLiveTranscriptionHelper
import us.zoom.sdk.ZoomVideoSDKMultiCameraStreamStatus
import us.zoom.sdk.ZoomVideoSDKNetworkStatus
import us.zoom.sdk.ZoomVideoSDKPasswordHandler
import us.zoom.sdk.ZoomVideoSDKPhoneFailedReason
import us.zoom.sdk.ZoomVideoSDKPhoneStatus
import us.zoom.sdk.ZoomVideoSDKProxySettingHandler
import us.zoom.sdk.ZoomVideoSDKRawDataPipe
import us.zoom.sdk.ZoomVideoSDKRawDataPipeDelegate
import us.zoom.sdk.ZoomVideoSDKReceiveFile
import us.zoom.sdk.ZoomVideoSDKRecordingConsentHandler
import us.zoom.sdk.ZoomVideoSDKRecordingStatus
import us.zoom.sdk.ZoomVideoSDKSSLCertificateInfo
import us.zoom.sdk.ZoomVideoSDKSendFile
import us.zoom.sdk.ZoomVideoSDKSessionLeaveReason
import us.zoom.sdk.ZoomVideoSDKShareAction
import us.zoom.sdk.ZoomVideoSDKShareHelper
import us.zoom.sdk.ZoomVideoSDKShareSetting
import us.zoom.sdk.ZoomVideoSDKShareStatus
import us.zoom.sdk.ZoomVideoSDKSubSessionManager
import us.zoom.sdk.ZoomVideoSDKSubSessionParticipant
import us.zoom.sdk.ZoomVideoSDKSubSessionStatus
import us.zoom.sdk.ZoomVideoSDKTestMicStatus
import us.zoom.sdk.ZoomVideoSDKUser
import us.zoom.sdk.ZoomVideoSDKUserHelpRequestResult
import us.zoom.sdk.ZoomVideoSDKUserHelper
import us.zoom.sdk.ZoomVideoSDKVideoAspect
import us.zoom.sdk.ZoomVideoSDKVideoCanvas
import us.zoom.sdk.ZoomVideoSDKVideoHelper
import us.zoom.sdk.ZoomVideoSDKVideoRawData
import us.zoom.sdk.ZoomVideoSDKVideoResolution
import us.zoom.sdk.ZoomVideoSDKVideoSubscribeFailReason
import us.zoom.sdk.ZoomVideoSDKVideoView
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.acos
import kotlin.math.atan2
import kotlin.math.sqrt

enum class LayoutMode {
    FULL_AND_SMALL,  // 全屏+小窗模式
    VERTICAL_SPLIT   // 垂直分屏
}

@Composable
fun MeetingDemo() {
    var currentLayout by remember { mutableStateOf(LayoutMode.VERTICAL_SPLIT) }
    val users = remember { mutableStateListOf<ZoomVideoSDKUser>() }
    val context = LocalContext.current
    val configuration = LocalConfiguration.current
    val displayRotation = when (configuration.orientation) {
        Configuration.ORIENTATION_LANDSCAPE -> 90
        Configuration.ORIENTATION_PORTRAIT -> 0
        else -> 0
    }
    var selectedUser = remember { mutableStateOf<ZoomVideoSDKUser?>(null) }
    var isJoin = remember { mutableStateOf(false) }
    val _rawDataFlow = MutableStateFlow<ZoomVideoSDKVideoRawData?>(null)
    val rawDataFlow: StateFlow<ZoomVideoSDKVideoRawData?> = _rawDataFlow
    val _rawDataFlowOther = MutableStateFlow<ZoomVideoSDKVideoRawData?>(null)
    val rawDataFlowOther: StateFlow<ZoomVideoSDKVideoRawData?> = _rawDataFlowOther
    // Screen recording
    var showRecordingDialog by remember { mutableStateOf(false) }
    val mediaProjectionManager = remember {
        baseApplication.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
    }
    var isRecording by remember { mutableStateOf(false) }
    var micOpened by remember { mutableStateOf(ZoomVideoSDK.getInstance().session.mySelf?.audioStatus?.isMuted == false) }
    var cameraOpened by remember { mutableStateOf(ZoomVideoSDK.getInstance().session.mySelf?.videoPipe?.videoStatus?.isOn == true) }
    val sharedViewModel: SharedViewModel = ViewModelProvider(activity).get(SharedViewModel::class.java)

    // Launcher for requesting screen capture permission
    val launcher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult(),
        onResult = { result ->
            LogUtil.i("Screen capture permission result: ${result.resultCode}")
            if (result.resultCode == Activity.RESULT_OK) {
                if (result.data == null) {
                    LogUtil.i("Screen capture permission no Data")
                } else {
                    result.data?.let { data ->
                        val serviceIntent = Intent(context, ScreenRecordService::class.java).apply {
                            putExtra(ScreenRecordService.EXTRA_RESULT_CODE, result.resultCode)
                            putExtra(ScreenRecordService.EXTRA_DATA, result.data)
                        }

                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            context.startForegroundService(serviceIntent)
                        } else {
                            context.startService(serviceIntent)
                        }

                    }

                }
            }
        }
    )

    LaunchedEffect(Unit) {
        selectedUser.value = ZoomVideoSDK.getInstance().session.mySelf
        ZoomVideoSDK.getInstance().videoHelper.rotateMyVideo(displayRotation)
    }
    DisposableEffect(Unit) {
        val window = MainActivity.getInstance().window
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        onDispose {
            window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }

    LaunchedEffect(ZoomVideoSDK.getInstance().isInSession) {
        if (ZoomVideoSDK.getInstance().isInSession) {
            isJoin.value = true
            users.clear()
            users.addAll(UserHelper.getAllUsers())
            val mySelf = ZoomVideoSDK.getInstance().session.mySelf
            if (mySelf?.audioStatus?.audioType == ZoomVideoSDKAudioStatus.ZoomVideoSDKAudioType.ZoomVideoSDKAudioType_None) {
                ZoomVideoSDK.getInstance().audioHelper.startAudio()
                micOpened = true
            } else {
                if (mySelf.audioStatus.isMuted) {
                    ZoomVideoSDK.getInstance().audioHelper.unMuteAudio(mySelf)
                    micOpened = false
                }
            }
            LogUtil.i("audioType${mySelf?.audioStatus?.audioType}")
        }
    }
    // Add broadcast receiver to listen for recording state changes and upload progress
    DisposableEffect(Unit) {
        val recordingStateReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                when (intent?.action) {
                    ScreenRecordService.ACTION_RECORDING_START -> {
                        LogUtil.i("Recording started")
                        isRecording = true
                    }
                    ScreenRecordService.ACTION_RECORDING_STOP -> {
                        LogUtil.i("Recording stopped")
                        isRecording = false
                        // Get the video path from ScreenRecordService
                        val videoPaths = ScreenRecordService.getVideoPaths()
                        if (videoPaths.isNotEmpty()) {
                            LogUtil.i("Recording ${sharedViewModel}")
                            sharedViewModel.videoUrl = videoPaths.last()
                            sharedViewModel.showUploadDialog = true
                            LogUtil.i("recording url:${videoPaths.last()}")
                        }
                    }
                }
            }
        }

        // Register receiver
        val intentFilter = IntentFilter().apply {
            addAction(ScreenRecordService.ACTION_RECORDING_START)
            addAction(ScreenRecordService.ACTION_RECORDING_STOP)
        }
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(
                recordingStateReceiver,
                intentFilter,
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            context.registerReceiver(recordingStateReceiver, intentFilter)
        }
        onDispose {
            context.unregisterReceiver(recordingStateReceiver)
            // When meeting ends, update video url in SharedViewModel
            val videoPaths = ScreenRecordService.getVideoPaths()
            if (videoPaths.isNotEmpty()) {
                sharedViewModel.videoUrl = videoPaths.last()
            }
        }
    }

    // Zoom SDK Listener Setup
    DisposableEffect(Unit) {
        val mySelf = ZoomVideoSDK.getInstance().session.mySelf
        val listener = object : ZoomVideoSDKDelegate {
            override fun onSessionJoin() {
                isJoin.value = true
                users.clear()
                users.addAll(UserHelper.getAllUsers())
                LogUtil.i("Join the Session")
            }

            override fun onSessionLeave() {
                isJoin.value = false
                LogUtil.i("lease Session")
            }

            override fun onSessionLeave(reason: ZoomVideoSDKSessionLeaveReason?) {

            }

            override fun onError(errorCode: Int) {
                LogUtil.i("errorCode:$errorCode,${ErrorMsgUtil.getMsgByErrorCode(errorCode)}")
            }

            override fun onUserJoin(
                userHelper: ZoomVideoSDKUserHelper,
                userList: MutableList<ZoomVideoSDKUser>,
            ) {
                LogUtil.i("user Join")
                users.clear()
                users.addAll(UserHelper.getAllUsers())
            }

            override fun onUserLeave(
                userHelper: ZoomVideoSDKUserHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {
                users.clear()
                users.addAll(UserHelper.getAllUsers())
                if (selectedUser.value !in users) {
                    selectedUser.value = ZoomVideoSDK.getInstance().session.mySelf
                }
            }

            override fun onUserVideoStatusChanged(
                videoHelper: ZoomVideoSDKVideoHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onShareNetworkStatusChanged(
                shareNetworkStatus: ZoomVideoSDKNetworkStatus?,
                isSendingShare: Boolean
            ) {
                
            }

            override fun onUserAudioStatusChanged(
                audioHelper: ZoomVideoSDKAudioHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onUserShareStatusChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                userInfo: ZoomVideoSDKUser?,
                status: ZoomVideoSDKShareStatus?,
            ) {

            }

            override fun onUserShareStatusChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                userInfo: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onShareContentChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                userInfo: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onLiveStreamStatusChanged(
                liveStreamHelper: ZoomVideoSDKLiveStreamHelper?,
                status: ZoomVideoSDKLiveStreamStatus?,
            ) {

            }

            override fun onChatNewMessageNotify(
                chatHelper: ZoomVideoSDKChatHelper?,
                messageItem: ZoomVideoSDKChatMessage?,
            ) {

            }

            override fun onChatDeleteMessageNotify(
                chatHelper: ZoomVideoSDKChatHelper?,
                msgID: String?,
                deleteBy: ZoomVideoSDKChatMessageDeleteType?,
            ) {

            }

            override fun onChatPrivilegeChanged(
                chatHelper: ZoomVideoSDKChatHelper?,
                currentPrivilege: ZoomVideoSDKChatPrivilegeType?,
            ) {

            }

            override fun onUserHostChanged(
                userHelper: ZoomVideoSDKUserHelper?,
                userInfo: ZoomVideoSDKUser?,
            ) {

            }

            override fun onUserManagerChanged(user: ZoomVideoSDKUser?) {

            }

            override fun onUserNameChanged(user: ZoomVideoSDKUser?) {

            }

            override fun onUserActiveAudioChanged(
                audioHelper: ZoomVideoSDKAudioHelper?,
                list: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onSessionNeedPassword(handler: ZoomVideoSDKPasswordHandler?) {

            }

            override fun onSessionPasswordWrong(handler: ZoomVideoSDKPasswordHandler?) {

            }

            override fun onMixedAudioRawDataReceived(rawData: ZoomVideoSDKAudioRawData?) {

            }

            override fun onOneWayAudioRawDataReceived(
                rawData: ZoomVideoSDKAudioRawData?,
                user: ZoomVideoSDKUser?,
            ) {

            }

            override fun onShareAudioRawDataReceived(rawData: ZoomVideoSDKAudioRawData?) {

            }

            override fun onCommandReceived(
                sender: ZoomVideoSDKUser?,
                strCmd: String?,
            ) {

            }

            override fun onCommandChannelConnectResult(isSuccess: Boolean) {

            }

            override fun onCloudRecordingStatus(
                status: ZoomVideoSDKRecordingStatus?,
                handler: ZoomVideoSDKRecordingConsentHandler?,
            ) {

            }

            override fun onHostAskUnmute() {

            }

            override fun onInviteByPhoneStatus(
                status: ZoomVideoSDKPhoneStatus?,
                reason: ZoomVideoSDKPhoneFailedReason?,
            ) {

            }

            override fun onMultiCameraStreamStatusChanged(
                status: ZoomVideoSDKMultiCameraStreamStatus?,
                user: ZoomVideoSDKUser?,
                videoPipe: ZoomVideoSDKRawDataPipe?,
            ) {

            }

            override fun onMultiCameraStreamStatusChanged(
                status: ZoomVideoSDKMultiCameraStreamStatus?,
                user: ZoomVideoSDKUser?,
                canvas: ZoomVideoSDKVideoCanvas?,
            ) {

            }

            override fun onLiveTranscriptionStatus(status: ZoomVideoSDKLiveTranscriptionHelper.ZoomVideoSDKLiveTranscriptionStatus?) {

            }

            override fun onOriginalLanguageMsgReceived(messageInfo: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionMessageInfo?) {

            }

            override fun onLiveTranscriptionMsgInfoReceived(messageInfo: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionMessageInfo?) {

            }

            override fun onLiveTranscriptionMsgError(
                spokenLanguage: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionLanguage?,
                transcriptLanguage: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionLanguage?,
            ) {

            }

            override fun onSpokenLanguageChanged(spokenLanguage: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionLanguage?) {
                
            }

            override fun onProxySettingNotification(handler: ZoomVideoSDKProxySettingHandler?) {

            }

            override fun onSSLCertVerifiedFailNotification(info: ZoomVideoSDKSSLCertificateInfo?) {

            }

            override fun onCameraControlRequestResult(
                user: ZoomVideoSDKUser?,
                isApproved: Boolean,
            ) {

            }

            override fun onCameraControlRequestReceived(
                user: ZoomVideoSDKUser?,
                requestType: ZoomVideoSDKCameraControlRequestType?,
                requestHandler: ZoomVideoSDKCameraControlRequestHandler?,
            ) {

            }

            override fun onUserVideoNetworkStatusChanged(
                status: ZoomVideoSDKNetworkStatus?,
                user: ZoomVideoSDKUser?,
            ) {

            }

            override fun onUserRecordingConsent(user: ZoomVideoSDKUser?) {

            }

            override fun onCallCRCDeviceStatusChanged(status: ZoomVideoSDKCRCCallStatus?) {

            }

            override fun onVideoCanvasSubscribeFail(
                fail_reason: ZoomVideoSDKVideoSubscribeFailReason?,
                pUser: ZoomVideoSDKUser?,
                view: ZoomVideoSDKVideoView?,
            ) {

            }

            override fun onShareCanvasSubscribeFail(
                fail_reason: ZoomVideoSDKVideoSubscribeFailReason?,
                pUser: ZoomVideoSDKUser?,
                view: ZoomVideoSDKVideoView?,
            ) {

            }

            override fun onShareCanvasSubscribeFail(
                pUser: ZoomVideoSDKUser?,
                view: ZoomVideoSDKVideoView?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onAnnotationHelperCleanUp(helper: ZoomVideoSDKAnnotationHelper?) {

            }

            override fun onAnnotationPrivilegeChange(
                shareOwner: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }
            override fun onTestMicStatusChanged(status: ZoomVideoSDKTestMicStatus?) {

            }

            override fun onMicSpeakerVolumeChanged(
                micVolume: Int,
                speakerVolume: Int,
            ) {

            }

            override fun onCalloutJoinSuccess(
                user: ZoomVideoSDKUser?,
                phoneNumber: String?,
            ) {

            }

            override fun onSendFileStatus(
                file: ZoomVideoSDKSendFile?,
                status: ZoomVideoSDKFileTransferStatus?,
            ) {

            }

            override fun onReceiveFileStatus(
                file: ZoomVideoSDKReceiveFile?,
                status: ZoomVideoSDKFileTransferStatus?,
            ) {

            }

            override fun onUVCCameraStatusChange(
                cameraId: String?,
                status: UVCCameraStatus?,
            ) {

            }

            override fun onVideoAlphaChannelStatusChanged(isAlphaModeOn: Boolean) {

            }

            override fun onSpotlightVideoChanged(
                videoHelper: ZoomVideoSDKVideoHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onFailedToStartShare(
                shareHelper: ZoomVideoSDKShareHelper?,
                user: ZoomVideoSDKUser?
            ) {
                
            }

            override fun onBindIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onUnbindIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onIncomingLiveStreamStatusResponse(
                bSuccess: Boolean,
                streamsStatusList: List<IncomingLiveStreamStatus?>?
            ) {
                
            }

            override fun onStartIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onStopIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onShareContentSizeChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                user: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onSubSessionStatusChanged(
                status: ZoomVideoSDKSubSessionStatus?,
                subSessionKitList: List<SubSessionKit?>?
            ) {
                
            }

            override fun onSubSessionManagerHandle(manager: ZoomVideoSDKSubSessionManager?) {
                
            }

            override fun onSubSessionParticipantHandle(participant: ZoomVideoSDKSubSessionParticipant?) {
                
            }

            override fun onSubSessionUsersUpdate(subSessionKit: SubSessionKit?) {
                
            }

            override fun onBroadcastMessageFromMainSession(
                message: String?,
                userName: String?
            ) {
                
            }

            override fun onSubSessionUserHelpRequest(handler: SubSessionUserHelpRequestHandler?) {
                
            }

            override fun onSubSessionUserHelpRequestResult(eResult: ZoomVideoSDKUserHelpRequestResult?) {
                
            }

            override fun onShareSettingChanged(setting: ZoomVideoSDKShareSetting?) {
                
            }
        }
        val dataDelegate = object : ZoomVideoSDKRawDataPipeDelegate {
            override fun onRawDataStatusChanged(status: ZoomVideoSDKRawDataPipeDelegate.RawDataStatus?) {
            }

            override fun onRawDataFrameReceived(rawData: ZoomVideoSDKVideoRawData?) {
                rawData?.let {
                    _rawDataFlow.value = it
                }
            }
        }
        ZoomVideoSDK.getInstance().addListener(listener)
        val pipe = mySelf.videoPipe
        pipe.subscribe(ZoomVideoSDKVideoResolution.VideoResolution_360P, dataDelegate)
        cameraOpened = true
        onDispose {
            LogUtil.e("onDispose clear zoom and record sources")
            isJoin.value = false
            pipe.unSubscribe(dataDelegate)
            ZoomVideoSDK.getInstance().leaveSession(false)
            ZoomVideoSDK.getInstance().removeListener(listener)
        }
    }

    BasePageView(
        modifier = Modifier.fillMaxSize(),
        title = stringResource(R.string.meeting),
        showBackIcon = true
    ) {
        Box(modifier = Modifier.fillMaxSize()) {
            when (currentLayout) {
                LayoutMode.FULL_AND_SMALL -> {
                    selectedUser.value?.let {
                        ZoomVideoView(
                            modifier = Modifier.fillMaxSize(),
                            user = it
                        )
                    }
                    LazyColumn(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .background(Color.Transparent)
                            .padding(8.dp),
                        horizontalAlignment = Alignment.End
                    ) {
                        item {
                            if (isJoin.value) {
                                Column(
                                    modifier = Modifier
                                        .border(
                                            width = 1.dp,
                                            color = Color.White,
                                            shape = CircleShape
                                        )
                                        .padding(1.dp)
                                        .width(48.dp)
                                        .height(48.dp)
                                        .background(Color(0x73000000), shape = CircleShape)
                                        .clickable() {
                                            currentLayout = LayoutMode.VERTICAL_SPLIT
                                        },
                                    verticalArrangement = Arrangement.Center,
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_split_vertical),
                                        contentDescription = null,
                                        tint = Color.White,
                                    )
                                }
                            }
                        }

                        items(users) { user ->
                            if (user != selectedUser.value) {
                                ParticipantThumbnail(
                                    modifier = Modifier.padding(top = 5.dp),
                                    user = user
                                ) {
                                    selectedUser.value = user // Update full-screen view on click
                                }
                            }
                        }
                    }

                }

                LayoutMode.VERTICAL_SPLIT -> {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                    ) {
                        selectedUser.value?.let {
                            ZoomVideoView(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .weight(1f),
                                user = it
                            )
                        }
                        if (isJoin.value && users.filterNot { ZoomVideoSDK.getInstance().session.mySelf == it }
                                .isNotEmpty()) {
                            DisposableEffect(Unit) {
                                val dataDelegate = object : ZoomVideoSDKRawDataPipeDelegate {
                                    override fun onRawDataStatusChanged(status: ZoomVideoSDKRawDataPipeDelegate.RawDataStatus?) {
                                    }

                                    override fun onRawDataFrameReceived(rawData: ZoomVideoSDKVideoRawData?) {
                                        rawData?.let {
                                            _rawDataFlowOther.value = it
                                        }
                                    }
                                }
                                val pipe = users
                                    .filterNot { ZoomVideoSDK.getInstance().session.mySelf == it }
                                    .last().videoPipe
                                pipe.subscribe(ZoomVideoSDKVideoResolution.VideoResolution_360P, dataDelegate)
                                onDispose {
                                    LogUtil.e("onDispose unsubscribe other user video pipe")
                                    pipe.unSubscribe(dataDelegate)
                                }
                            }
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .weight(1f)
                            ) {
                                ZoomVideoView(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .clickable {
                                            selectedUser.value = users
                                                .filterNot { ZoomVideoSDK.getInstance().session.mySelf == it }
                                                .last()
                                        },
                                    user = users
                                        .filterNot { ZoomVideoSDK.getInstance().session.mySelf == it }
                                        .last()
                                )

                                Column(
                                    modifier = Modifier
                                        .align(Alignment.TopStart)
                                        .padding(top = 16.dp, start = 16.dp)
                                        .border(
                                            width = 1.dp,
                                            color = Color(0xFF3E7FF9),
                                            shape = RoundedCornerShape(8.dp)
                                        )
                                        .widthIn(min = 120.dp)
                                        .background(Color(0x73000000), shape = RoundedCornerShape(8.dp)),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    AngleText(
                                        context = context,
                                        rawDataFlow = rawDataFlowOther
                                    )
                                }

                            }

                        }
                    }

                    LazyColumn(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .background(Color.Transparent)
                            .padding(8.dp),
                    ) {
                        item {
                            if (isJoin.value) {
                                Column(
                                    modifier = Modifier
                                        .padding(8.dp)
                                        .border(
                                            width = 1.dp,
                                            color = Color.White,
                                            shape = CircleShape
                                        )
                                        .padding(1.dp)
                                        .width(48.dp)
                                        .height(48.dp)
                                        .background(Color(0x73000000), shape = CircleShape)
                                        .clickable {
                                            currentLayout = LayoutMode.FULL_AND_SMALL
                                        }
                                        .align(Alignment.TopEnd),
                                    verticalArrangement = Arrangement.Center,
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Icon(
                                        painter = painterResource(id = R.drawable.ic_split_small_large),
                                        contentDescription = null,
                                        tint = Color.White
                                    )
                                }
                            }
                        }
                    }
                }
            }
            // Full-screen video view


            // Bottom bar for participant thumbnails
            Column(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(top = 16.dp, start = 16.dp)
                    .border(
                        width = 1.dp,
                        color = Color(0xFF3E7FF9),
                        shape = RoundedCornerShape(8.dp)
                    )
                    .widthIn(min = 120.dp)
                    .background(Color(0x73000000), shape = RoundedCornerShape(8.dp)),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    modifier = Modifier
                        .padding(4.dp),
                    text = if (isJoin.value) stringResource(R.string.connected) else stringResource(
                        R.string.connecting
                    ),
                    color = Color.White,
                    fontSize = 16.sp
                )

                AngleText(context = context, rawDataFlow = rawDataFlow)
            }
            if (isJoin.value) {
                MeetingButton(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 32.dp),
                    micOpened = micOpened,
                    cameraOpened = cameraOpened,
                    recordOpened = isRecording,
                    mic = {
                        ZoomVideoSDK.getInstance().session.mySelf?.let {
                            if (it.audioStatus?.isMuted == true) {
                                ZoomVideoSDK.getInstance().audioHelper.unMuteAudio(it)
                                micOpened = true
                            } else {
                                ZoomVideoSDK.getInstance().audioHelper.muteAudio(it)
                                micOpened = false
                            }
                        }
                    },
                    camera = {
                        ZoomVideoSDK.getInstance().session.mySelf?.let {
                            if (it.videoPipe?.videoStatus?.isOn == true) {
                                ZoomVideoSDK.getInstance().videoHelper.stopVideo()
                                cameraOpened = false
                            } else {
                                ZoomVideoSDK.getInstance().videoHelper.startVideo()
                                cameraOpened = true
                            }
                        }
                    },
                    record = {
                        if (!isRecording) {
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) { // Android 15
                                showRecordingDialog = true
                            } else {
                                val captureIntent =
                                    mediaProjectionManager.createScreenCaptureIntent()
                                launcher.launch(captureIntent)
                            }
                        } else {
                            val serviceIntent = Intent(context, ScreenRecordService::class.java)
                            context.stopService(serviceIntent)
                        }
                    },
                    leave = {
                        finish()
                    }
                )
                if (showRecordingDialog) {
                    AlertDialog(
                        onDismissRequest = { showRecordingDialog = false },
                        title = { Text(stringResource(R.string.recording_guide_title)) },
                        text = { Text(stringResource(R.string.recording_guide_message)) },
                        confirmButton = {
                            androidx.compose.material3.TextButton(
                                onClick = {
                                    showRecordingDialog = false
                                    val captureIntent =
                                        mediaProjectionManager.createScreenCaptureIntent()
                                    launcher.launch(captureIntent)
                                }
                            ) {
                                Text(stringResource(R.string.ok))
                            }
                        },
                        dismissButton = {
                            androidx.compose.material3.TextButton(
                                onClick = { showRecordingDialog = false }
                            ) {
                                Text(stringResource(R.string.cancel))
                            }
                        }
                    )
                }
            }

        }
    }

}

@Preview(showBackground = true)
@Composable
private fun MeetingButton(
    modifier: Modifier = Modifier,
    micOpened: Boolean = true,
    cameraOpened: Boolean = true,
    recordOpened: Boolean = false,
    mic: () -> Unit = {},
    camera: () -> Unit = {},
    record: () -> Unit = {},
    leave: () -> Unit = {},
) {
    Row(
        modifier = modifier
            .background(color = Color(0x3D000000), shape = RoundedCornerShape(size = 35.dp))
            .padding(start = 12.dp, end = 12.dp, top = 8.dp, bottom = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .width(48.dp)
                .height(48.dp)
                .background(color = Color(0x4D000000), shape = RoundedCornerShape(size = 24.dp))
                .clickable {
                    mic()
                },
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .size(32.dp),
                painter = if (micOpened) painterResource(id = R.drawable.icon_un_mircophone)
                else painterResource(id = R.drawable.icon_mircophone),
                contentDescription = null

            )
        }
        Spacer(Modifier.width(24.dp))
        Box(
            modifier = Modifier
                .width(48.dp)
                .height(48.dp)
                .background(color = Color(0x4D000000), shape = RoundedCornerShape(size = 24.dp))
                .clickable {
                    camera()
                },
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .size(32.dp),
                painter = if (cameraOpened) painterResource(id = R.drawable.icon_camera)
                else painterResource(id = R.drawable.icon_un_camera),
                contentDescription = null

            )
        }
        Spacer(Modifier.width(24.dp))
        Box(
            modifier = Modifier
                .width(48.dp)
                .height(48.dp)
                .background(color = Color(0x4D000000), shape = RoundedCornerShape(size = 24.dp))
                .clickable {
                    record()
                },
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .size(32.dp),
                painter = if (recordOpened) painterResource(id = R.drawable.icon_recorded)
                else painterResource(id = R.drawable.icon_un_record),
                contentDescription = null

            )
        }
        Spacer(Modifier.width(24.dp))
        Box(
            modifier = Modifier
                .width(48.dp)
                .height(48.dp)
                .background(color = Color(0xFFFF4D40), shape = RoundedCornerShape(size = 24.dp))
                .padding(start = 14.dp, top = 12.dp, end = 10.dp, bottom = 12.dp)
                .clickable {
                    leave()
                },
            contentAlignment = Alignment.Center
        ) {
            Image(
                modifier = Modifier
                    .size(32.dp),
                painter = painterResource(id = R.drawable.icon_meeting_leave),
                contentDescription = null

            )
        }

    }
}

@Composable
fun ParticipantThumbnail(
    modifier: Modifier,
    user: ZoomVideoSDKUser,
    onClick: () -> Unit
) {
    // Obtain screen width and height using LocalConfiguration
    val context = LocalContext.current
    val videoView = remember { ZoomVideoSDKVideoView(context) }
    DisposableEffect(Unit) {
        onDispose {
            user.videoCanvas.unSubscribe(videoView)
        }
    }
    Card(
        modifier = modifier
            .height(160.dp)
            .width(90.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(1.dp, Color(0xFF3E7FF9)),
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Transparent, shape = RoundedCornerShape(8.dp))
                .clip(RoundedCornerShape(8.dp))
        ) {
            // Video View
            AndroidView(
                modifier = Modifier
                    .height(160.dp)
                    .width(90.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(Color.Transparent, shape = RoundedCornerShape(8.dp)),
                factory = { context ->
                    videoView
                },
                update = {
                    user.videoCanvas.subscribe(
                        videoView,
                        ZoomVideoSDKVideoAspect.ZoomVideoSDKVideoAspect_PanAndScan
                    )
                }
            )
            // Overlay for name and speaker icon
            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 8.dp)
                    .background(
                        color = Color.Gray.copy(alpha = 0.5f),
                        shape = RoundedCornerShape(6.dp)
                    )
                    .clip(RoundedCornerShape(6.dp))
            ) {
                Text(
                    text = user.userName,
                    color = Color.White,
                )
            }

        }
    }
}

@Composable
fun AngleText(
    context: Context,
    rawDataFlow: StateFlow<ZoomVideoSDKVideoRawData?>,
) {

    var neckAngleCV by remember { mutableStateOf("") }
    val backAngleCV = remember { mutableStateOf("") }

    val queueSize = 10
    // 添加额外的阈值过滤
    val threshold = 2.5
    // 添加一个用于存储历史角度值的队列
    val neckAngleQueue = remember { ArrayDeque<Float>(queueSize) } // 存储最近10个值

    // 添加一个用于存储历史角度值的队列
    val backAngleQueue = remember { ArrayDeque<Int>(queueSize) } // 存储最近10个值
    // 协程错误捕捉
    val exceptionHandler = CoroutineExceptionHandler { _, exception ->
    }
    val scope = CoroutineScope(Dispatchers.Default + exceptionHandler)

    // mediapipe
    val poseLandmarker = remember {
        initializePoseLandmarker(context) { result, mpImage, _ ->
            result?.let { poseLandmarkerResult ->
                for (landmark in poseLandmarkerResult.worldLandmarks()) {
                    // Get midpoint of ears (7, 8)
                    val noseX = landmark[0].x()
                    val noseY = landmark[0].y()
                    val noseZ = landmark[0].z()

                    val earMidX = (landmark[7].x() + landmark[8].x()) / 2
                    val earMidY = (landmark[7].y() + landmark[8].y()) / 2
                    val earMidZ = (landmark[7].z() + landmark[8].z()) / 2

                    // Get midpoint of shoulders (11, 12)
                    val shoulderMidX = (landmark[11].x() + landmark[12].x()) / 2
                    val shoulderMidY = (landmark[11].y() + landmark[12].y()) / 2
                    val shoulderMidZ = (landmark[11].z() + landmark[12].z()) / 2

                    val x2 =
                        landmark[23].x() / 2 + landmark[24].x() / 2
                    val y2 =
                        landmark[23].y() / 2 + landmark[24].y() / 2
                    val z2 =
                        landmark[23].z() / 2 + landmark[24].z() / 2

                    scope.launch {
                        // neck
                        withContext(Dispatchers.IO) {
                            // Calculate neck vector (from shoulders to ears)
                            val neckAngle =
                                calculateNeckAngle(noseX, noseY, noseZ, earMidX, earMidY, earMidZ)
                            neckAngleQueue.addLast(neckAngle)
                            // Apply moving average filter for neck angle
                            if (neckAngleQueue.size >= queueSize) {
                                // Calculate average neck angle
                                val averageNeckAngle = neckAngleQueue.average()

                                // Apply threshold filtering for neck angle
                                val currentNeckAngle = neckAngleQueue.last()
                                LogUtil.i("neck: ave:${averageNeckAngle},curr:${currentNeckAngle},now:${neckAngle}")

                                val filteredNeckAngle =
                                    if (abs(averageNeckAngle - currentNeckAngle) > threshold) {
                                        currentNeckAngle.toInt()
                                    } else {
                                        averageNeckAngle.toInt()
                                    }

                                neckAngleCV = filteredNeckAngle.toString()
//                        String.format("%d", filteredNeckAngle.toInt())
                                neckAngleQueue.removeFirstOrNull()
                            }
                        }

                        // back
                        withContext(Dispatchers.IO) {
                            val abX = x2 - shoulderMidX
                            val abY = y2 - shoulderMidY
                            val abZ = z2 - shoulderMidZ
                            val cX = 0.0
                            val cY = 1.0
                            val cZ = 0.0
                            // 计算向量AB和向量C的点积
                            val dotProduct = abX * cX + abY * cY + abZ * cZ

                            // 计算向量AB和向量C的模长
                            val abMagnitude =
                                sqrt(abX * abX + abY * abY + abZ * abZ)
                            val cMagnitude = sqrt(cX * cX + cY * cY + cZ * cZ)

                            // 计算夹角的余弦值
                            val cosTheta = dotProduct / (abMagnitude * cMagnitude)
                            val angle = acos(cosTheta) * (180.0 / PI)
                            backAngleQueue.addLast(angle.toInt())
                            // 应用移动平均滤波
                            if (backAngleQueue.size >= queueSize) {
                                // 计算平均值
                                val averageBackAngle = backAngleQueue.average()

                                val currentAngle = backAngleQueue.last()
                                LogUtil.i("back: ave:${averageBackAngle},curr:${currentAngle},now:${angle}")
                                val filteredAngle =
                                    if (abs(averageBackAngle - currentAngle) > threshold) {
                                        currentAngle.toInt()
                                    } else {
                                        averageBackAngle.toInt()
                                    }
                                backAngleCV.value = filteredAngle.toString()
                                backAngleQueue.removeFirstOrNull()
                            }
                        }
                    }
                }
            }
        }
    }

    // Process frames from the raw data flow
    LaunchedEffect(rawDataFlow) {
        rawDataFlow.collect { rawData ->
            if (rawData != null) {
                withContext(Dispatchers.IO) {
                    processFrameAsync(rawData, poseLandmarker, SystemClock.uptimeMillis())
                }
            }
        }
    }

    AngleData(modifier = Modifier, angle = neckAngleCV, description = "neck")
    AngleData(modifier = Modifier, angle = backAngleCV.value, description = "back")
    Row(
        Modifier
            .width(28.dp)
            .height(16.dp)
            .background(color = Color(0x73FFFFFF), shape = RoundedCornerShape(size = 12.dp)),
        horizontalArrangement = Arrangement.SpaceAround,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            Modifier
                .padding(1.dp)
                .width(4.dp)
                .height(4.dp)
                .background(color = Color(0xFFFFFFFF))
        ) {}
        Column(
            Modifier
                .padding(1.dp)
                .width(4.dp)
                .height(4.dp)
                .background(color = Color(0xFFFFFFFF))
        ) {}
        Column(
            Modifier
                .padding(1.dp)
                .width(4.dp)
                .height(4.dp)
                .background(color = Color(0xFFFFFFFF))
        ) {}
    }
}
@Composable
fun AngleTextOther(
    context: Context,
    rawDataFlow: StateFlow<ZoomVideoSDKVideoRawData?>,
) {

    var neckAngleCV by remember { mutableStateOf("") }
    val backAngleCV = remember { mutableStateOf("") }

    val queueSize = 10
    // 添加额外的阈值过滤
    val threshold = 2.5
    // 添加一个用于存储历史角度值的队列
    val neckAngleQueue = remember { ArrayDeque<Float>(queueSize) } // 存储最近10个值

    // 添加一个用于存储历史角度值的队列
    val backAngleQueue = remember { ArrayDeque<Int>(queueSize) } // 存储最近10个值
    // 协程错误捕捉
    val exceptionHandler = CoroutineExceptionHandler { _, exception ->
    }
    val scope = CoroutineScope(Dispatchers.Default + exceptionHandler)

    // mediapipe
    val poseLandmarker = remember {
        initializePoseLandmarker(context) { result, mpImage, _ ->
            result?.let { poseLandmarkerResult ->
                for (landmark in poseLandmarkerResult.worldLandmarks()) {
                    // Get midpoint of ears (7, 8)
                    val noseX = landmark[0].x()
                    val noseY = landmark[0].y()
                    val noseZ = landmark[0].z()

                    val earMidX = (landmark[7].x() + landmark[8].x()) / 2
                    val earMidY = (landmark[7].y() + landmark[8].y()) / 2
                    val earMidZ = (landmark[7].z() + landmark[8].z()) / 2

                    // Get midpoint of shoulders (11, 12)
                    val shoulderMidX = (landmark[11].x() + landmark[12].x()) / 2
                    val shoulderMidY = (landmark[11].y() + landmark[12].y()) / 2
                    val shoulderMidZ = (landmark[11].z() + landmark[12].z()) / 2

                    val x2 =
                        landmark[23].x() / 2 + landmark[24].x() / 2
                    val y2 =
                        landmark[23].y() / 2 + landmark[24].y() / 2
                    val z2 =
                        landmark[23].z() / 2 + landmark[24].z() / 2

                    scope.launch {
                        // neck
                        withContext(Dispatchers.IO) {
                            // Calculate neck vector (from shoulders to ears)
                            val neckAngle =
                                calculateNeckAngle(noseX, noseY, noseZ, earMidX, earMidY, earMidZ)
                            neckAngleQueue.addLast(neckAngle)
                            // Apply moving average filter for neck angle
                            if (neckAngleQueue.size >= queueSize) {
                                // Calculate average neck angle
                                val averageNeckAngle = neckAngleQueue.average()

                                // Apply threshold filtering for neck angle
                                val currentNeckAngle = neckAngleQueue.last()
                                LogUtil.i("neck: other ave:${averageNeckAngle},curr:${currentNeckAngle},now:${neckAngle}")

                                val filteredNeckAngle =
                                    if (abs(averageNeckAngle - currentNeckAngle) > threshold) {
                                        currentNeckAngle.toInt()
                                    } else {
                                        averageNeckAngle.toInt()
                                    }

                                neckAngleCV = filteredNeckAngle.toString()
//                        String.format("%d", filteredNeckAngle.toInt())
                                neckAngleQueue.removeFirstOrNull()
                            }
                        }

                        // back
                        withContext(Dispatchers.IO) {
                            val abX = x2 - shoulderMidX
                            val abY = y2 - shoulderMidY
                            val abZ = z2 - shoulderMidZ
                            val cX = 0.0
                            val cY = 1.0
                            val cZ = 0.0
                            // 计算向量AB和向量C的点积
                            val dotProduct = abX * cX + abY * cY + abZ * cZ

                            // 计算向量AB和向量C的模长
                            val abMagnitude =
                                sqrt(abX * abX + abY * abY + abZ * abZ)
                            val cMagnitude = sqrt(cX * cX + cY * cY + cZ * cZ)

                            // 计算夹角的余弦值
                            val cosTheta = dotProduct / (abMagnitude * cMagnitude)
                            val angle = acos(cosTheta) * (180.0 / PI)
                            backAngleQueue.addLast(angle.toInt())
                            // 应用移动平均滤波
                            if (backAngleQueue.size >= queueSize) {
                                // 计算平均值
                                val averageBackAngle = backAngleQueue.average()

                                val currentAngle = backAngleQueue.last()
                                LogUtil.i("back: ave:${averageBackAngle},curr:${currentAngle},now:${angle}")
                                val filteredAngle =
                                    if (abs(averageBackAngle - currentAngle) > threshold) {
                                        currentAngle.toInt()
                                    } else {
                                        averageBackAngle.toInt()
                                    }
                                backAngleCV.value = filteredAngle.toString()
                                backAngleQueue.removeFirstOrNull()
                            }
                        }
                    }
                }
            }
        }
    }

    // Process frames from the raw data flow
    LaunchedEffect(rawDataFlow) {
        rawDataFlow.collect { rawData ->
            if (rawData != null) {
                withContext(Dispatchers.IO) {
                    processFrameAsync(rawData, poseLandmarker, SystemClock.uptimeMillis())
                }
            }
        }
    }

    AngleData(modifier = Modifier, angle = neckAngleCV, description = "neck")
    AngleData(modifier = Modifier, angle = backAngleCV.value, description = "back")
    Row(
        Modifier
            .width(28.dp)
            .height(16.dp)
            .background(color = Color(0x73FFFFFF), shape = RoundedCornerShape(size = 12.dp)),
        horizontalArrangement = Arrangement.SpaceAround,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            Modifier
                .padding(1.dp)
                .width(4.dp)
                .height(4.dp)
                .background(color = Color(0xFFFFFFFF))
        ) {}
        Column(
            Modifier
                .padding(1.dp)
                .width(4.dp)
                .height(4.dp)
                .background(color = Color(0xFFFFFFFF))
        ) {}
        Column(
            Modifier
                .padding(1.dp)
                .width(4.dp)
                .height(4.dp)
                .background(color = Color(0xFFFFFFFF))
        ) {}
    }
}

@Composable
fun AngleData(
    modifier: Modifier = Modifier,
    angle: String = "",
    description: String = "neck",
) {
    Column(
        modifier = Modifier.wrapContentWidth(),
        horizontalAlignment = Alignment.Start
    ) {
        Text(
            modifier = modifier,
            text = "${description}: ${angle}°",
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFFFFFFFF)
            )
        )
    }

}

@Composable
fun AngleText(
    modifier: Modifier = Modifier,
    angle: String = "",
    description: String = "neck",
) {
    Column(
        modifier = modifier
            .border(width = 1.dp, color = Color(0xFF3E7FF9), shape = CircleShape)
            .padding(1.dp)
            .width(71.dp)
            .height(71.dp)
            .background(Color(0x73000000), shape = CircleShape),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = "${angle}°",
            style = TextStyle(
                fontSize = 24.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFFFFFFFF),
            )
        )
        Text(
            text = description,
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFFFFFFFF),
            )
        )
    }
}

@Composable
fun ZoomVideoView(
    modifier: Modifier = Modifier,
    user: ZoomVideoSDKUser,
) {
    val videoView = remember { ZoomVideoSDKVideoView(baseApplication) }

    DisposableEffect(Unit) {
        onDispose {
            LogUtil.i("onDispose")
            user.videoCanvas?.unSubscribe(
                videoView
            )
        }
    }
    AndroidView(
        modifier = modifier,
        factory = {
            videoView
        },
        update = { view ->
            // 只在surface创建后更新
            LogUtil.i("update subscribe")
            // 你的更新逻
            user.videoCanvas.subscribe(
                view,
                ZoomVideoSDKVideoAspect.ZoomVideoSDKVideoAspect_PanAndScan
            )
            ZoomVideoSDK.getInstance().videoHelper.startVideo()
        }
    )
}

fun calculateNeckAngle(
    noseX: Float,
    noseY: Float,
    noseZ: Float,
    earMidX: Float,
    earMidY: Float,
    earMidZ: Float
): Float {
    // 计算鼻子到耳朵中点的向量
    val vectorX = earMidX - noseX
    val vectorY = earMidY - noseY
    val vectorZ = earMidZ - noseZ

    // 计算向量的模长（即向量的长度）
    val vectorLength = sqrt(vectorX * vectorX + vectorY * vectorY + vectorZ * vectorZ)

    // 计算鼻子到耳朵中点的方向角（此处使用Y轴与向量的夹角来表示弯曲程度）
    // 计算向量在XY平面上的投影
    val xyLength = sqrt(vectorX * vectorX + vectorY * vectorY)

    // 使用反正切（atan2）计算夹角，返回角度
    val angle = atan2(xyLength, vectorZ) * (180 / PI)

    return angle.toFloat()
}
