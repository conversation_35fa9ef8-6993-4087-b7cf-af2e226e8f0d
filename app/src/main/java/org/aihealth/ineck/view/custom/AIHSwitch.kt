package org.aihealth.ineck.view.custom

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp


@Composable
fun AIHSwitch(
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit,
    modifier: Modifier = Modifier,
    checkedText: String = "",
    unCheckedText: String = "",
    trackUnCheckedColor: Color = Color(0XFF999999),
    trackCheckedColor: Color = Color(0xFF7893EC),
    thumbColor: Color = Color.White,
    switchWidth: Dp = 60.dp,
    switchHeight: Dp = 28.dp,
    padding: PaddingValues = PaddingValues(1.dp)
) {
    val weight by animateFloatAsState(targetValue = if (checked) 1F else 0F)
    val backgroundColor by animateColorAsState(targetValue = if (checked) trackCheckedColor else trackUnCheckedColor)
    var _checked by remember {
        mutableStateOf(checked)
    }
    _checked = checked
    var height by remember {
        mutableStateOf(0)
    }
    Row(modifier = Modifier
        .then(modifier)
        .size(switchWidth, switchHeight)

        .pointerInput(Unit) {
            detectTapGestures {
                onCheckedChange(!_checked)
            }
        }
        .background(backgroundColor, CircleShape)
        .padding(padding)
        .onSizeChanged {
            height = it.height
        }) {
        if (weight > 0) {
            Box(modifier = Modifier
                .fillMaxHeight()
                .weight(weight), contentAlignment = Alignment.Center){
                if (checkedText.isNotBlank() && weight == 1F) {
                    Text(text = checkedText, fontSize = 11.sp, color = Color.White, textAlign = TextAlign.Center)
                }
            }
        }
        Box(modifier = Modifier
            .fillMaxHeight()
            .width(with(LocalDensity.current) { height.toDp() })
            .background(thumbColor, CircleShape))
        if (weight < 1) {
            Box(modifier = Modifier
                .fillMaxHeight()
                .weight(1F - weight), contentAlignment = Alignment.Center){
                if (unCheckedText.isNotBlank() && weight == 0F) {
                    Text(text = unCheckedText, fontSize = 11.sp, color = Color.White, textAlign = TextAlign.Center)
                }
            }
        }
    }

}
