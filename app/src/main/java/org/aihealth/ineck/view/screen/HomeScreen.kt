package org.aihealth.ineck.view.screen

import android.view.WindowManager
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.curCaloriesBurnedRecord
import org.aihealth.ineck.curStep
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.Constants
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.serverCaloriesBurnedRecord
import org.aihealth.ineck.serverStep
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.dialog.QuestionnaireReminderDialog
import org.aihealth.ineck.view.dialog.RecalibrationDialog
import org.aihealth.ineck.view.directions.OdiRecordDirections
import org.aihealth.ineck.view.screen.home.AiBackCVHomeModule
import org.aihealth.ineck.view.screen.home.AiBackHomeModule
import org.aihealth.ineck.view.screen.home.AiFits
import org.aihealth.ineck.view.screen.home.AiNeckCVHomeModule
import org.aihealth.ineck.view.screen.home.AiNeckHomeModule
import org.aihealth.ineck.view.screen.home.HomeHeader
import org.aihealth.ineck.view.screen.home.NeuralScale
import org.aihealth.ineck.view.screen.home.ODIAndPromise
import org.aihealth.ineck.view.screen.home.PainScale
import org.aihealth.ineck.view.screen.home.Tips
import org.aihealth.ineck.view.screen.home.VitalSignsCard
import org.aihealth.ineck.view.screen.home.VitalSignsModuleState
import org.aihealth.ineck.view.screen.home.aijoint.AiJoinShoulderHomeModule
import org.aihealth.ineck.view.screen.home.aijoint.AiJointElbowHomeModule
import org.aihealth.ineck.view.screen.home.aijoint.AiJointHipHomeModule
import org.aihealth.ineck.view.screen.home.aijoint.AiJointKneeHomeModule
import org.aihealth.ineck.view.screen.home.aijoint.KneeExercise
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.device.DeviceViewModel
import org.aihealth.ineck.viewmodel.user
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone

/**
 * 主页页面UI
 */
@OptIn(ExperimentalFoundationApi::class, FlowPreview::class)
@Composable
fun HomeScreen(
    viewModel: MainViewModel,
    deviceViewModel: DeviceViewModel,
) {
    // Home 页面选中的 DeviceType
    val deviceType by remember {
        derivedStateOf { viewModel.homeScreen.currentDeviceType }
    }

    val vitalSignsModuleState = viewModel.homeScreen.vitalSignsModuleVisibleState.collectAsState()

    DisposableEffect(Unit) {
        val window = MainActivity.getInstance().window
        window?.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        onDispose {
            window?.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }
    
    // 获取服务器步数数据
    LaunchedEffect(Unit) {
        // 生成当前时间的ISO格式字符串
        val currentDateTime = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault()).apply {
            timeZone = TimeZone.getTimeZone("UTC")
        }.format(Date())
        
        apiService.getBodyFitness(
            dateTime = currentDateTime
        ).enqueueBody { response ->
            response?.data?.let { data ->
                // 解析返回的数据，假设数据结构中包含步数信息
                try {
                    // 数据是一个数组，需要遍历找到最新的包含number和calorie的记录
                    if (data is List<*>) {
                        // 找到第一条包含number和calorie字段的记录（数组已按时间倒序排列）
                        val latestRecord = data.find { record ->
                            record is Map<*, *> && 
                            record.containsKey("number") && 
                            record.containsKey("calorie") &&
                            record["number"] != null &&
                            record["calorie"] != null
                        } as? Map<*, *>
                        
                        latestRecord?.let { record ->
                            // 提取步数 (number字段)
                            val steps = when (val numberValue = record["number"]) {
                                is Number -> numberValue.toInt()
                                is String -> numberValue.toIntOrNull() ?: 0
                                else -> 0
                            }
                            
                            // 提取卡路里 (calorie字段)
                            val calories = when (val calorieValue = record["calorie"]) {
                                is Number -> calorieValue.toDouble()
                                is String -> calorieValue.toDoubleOrNull() ?: 0.0
                                else -> 0.0
                            }
                            
                            if (steps > 0) {
                                serverStep = steps
                                serverCaloriesBurnedRecord = calories
                                LogUtil.i("获取服务器数据成功: 步数=$steps, 卡路里=$calories")
                            }
                        }
                    }
                } catch (e: Exception) {
                    LogUtil.e("解析body_fitness数据失败: ${e.message}")
                }
            }
        }
    }

    BasePageView(
        headerContent = {
            HomeHeader(
                deviceType = deviceType,
                currentDeviceType = viewModel.homeScreen.currentDeviceType,
                toggleDeviceType = {
                    viewModel.homeScreen.toggleDeviceType(it)
                },
                toCamera = {
                    if (XXPermissions.isGranted(activity, Permission.CAMERA)) {
                        viewModel.homeScreen.onScanClick()
                    } else {
                        viewModel.homeScreen.showPowerDialogVisibleState = true
                        viewModel.homeScreen.theClickType = 1
                    }
                }
            )
        },
        background = {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.linearGradient(
                            listOf(
                                Color(0XFF3654CD), Color(0XFF3F6CE3), Color(0XFF789AF7)
                            ),
                            start = Offset(0f, 0f),
                            end = Offset(0f, Float.POSITIVE_INFINITY)
                        )
                    )
            )
        },
        statusBarDarkContentEnabled = false
    ) {
        LazyColumn (
            modifier = Modifier
                .fillMaxSize(),
            contentPadding = PaddingValues(bottom = 16.dp),
        ){
            when (viewModel.homeScreen.currentDeviceType) {
                DeviceType.aiNeck -> {
                    item (key = "aiNeck") {
                        AiNeckHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel,
                            deviceViewModel = deviceViewModel
                        )
                    }

                }

                DeviceType.aiNeckCV -> {
                    item (key = "aiNeckCV"){
                        AiNeckCVHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel
                        )
                    }

                }

                DeviceType.aiBack -> {
                    item (key = "aiBack"){
                        AiBackHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel,
                            deviceViewModel = deviceViewModel
                        )
                    }

                }

                DeviceType.aiBackCV -> {
                    item (key = "aiBackCV"){
                        AiBackCVHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel
                        )
                    }

                }

                DeviceType.KneeJoint -> {
                    item (key = "KneeJoint") {
                        val finishedCount by viewModel.deviceScreen.kneeExercise.collectAsState()

                        AiJointKneeHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel
                        )
                        KneeExercise(
                            Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            toStart = {
                                startScreen(Screen.KneeExerciseScreen.route)
                            },
                            toReport = {
                                startScreen(Screen.KneeExerciseReportScreen.route)
                            },
                            valueOne = finishedCount.first,
                            valueTwo = finishedCount.second
                        )
                    }
                }

                DeviceType.ElbowJoint -> {
                    item (key = "ElbowJoint"){
                        AiJointElbowHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel
                        )
                    }
                }

                DeviceType.ShoulderJoint -> {
                    item {
                        AiJoinShoulderHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel
                        )
                    }

                }

                DeviceType.HipJoint -> {
                    item (key = "HipJoint"){
                        AiJointHipHomeModule(
                            modifier = Modifier
                                .padding(top = 8.dp)
                                .fillMaxWidth()
                                .padding(horizontal = 16.dp),
                            viewModel = viewModel
                        )
                    }
                }
                else -> {}
            }

            item (key = "Tips"){
                Tips(
                    modifier = Modifier
                        .animateItem()
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    stringRes = if (viewModel.homeScreen.currentDeviceType == DeviceType.aiNeckCV || viewModel.homeScreen.currentDeviceType == DeviceType.aiNeck) R.string.tips_1 else R.string.tips_2,
                    onClick = {
                        MainViewModel.pageIndex = 2
                    }
                )
            }

            item (key = "PainScale") {
                PainScale(
                    modifier = Modifier
                        .animateItem()
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    onPainClick = {
                        startScreen(Screen.PainRecord.route)
                    }
                )
            }

            item (key = "NeuralScale"){
                NeuralScale(
                    modifier = Modifier
                        .animateItem()
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    onNeuralClick = {
                        startScreen(Screen.NeuralScaleScreen.route)
                    }
                )
            }

            item (key = "ODIAndPromise"){
                ODIAndPromise(
                    modifier = Modifier
                        .animateItem()
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    onMeasurementClick = {
//                    默认跳转到ODI
                        val model = OdiRecordDirections.OdiRecordModel(baseTime = null)
                        startScreen(OdiRecordDirections.actionToOdiPromisRecord(model = model))
                    }
                )
            }

            item(key = "aiFits") {
                AiFits(
                    modifier = Modifier
                        .animateItem()
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    stepData = if (serverStep > 0) serverStep else curStep,
                    calorieData = if (serverCaloriesBurnedRecord > 0) serverCaloriesBurnedRecord else curCaloriesBurnedRecord,
                    hasData = if (serverStep > 0) true else (viewModel.useLocalData == 0)
                )

            }

            item(key = "vitalSigns") {
                VitalSignsCard(
                    modifier = Modifier
                        .animateItem()
                        .padding(top = 8.dp)
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    state = vitalSignsModuleState.value,
                    onClickBtn = {

                        viewModel.homeScreen.changeVitalSignsModuleStateVisibleState(
                            if (vitalSignsModuleState.value == VitalSignsModuleState.VISIBLE) {
                                VitalSignsModuleState.HIDDEN
                            } else {
                                VitalSignsModuleState.VISIBLE
                            }
                        )
                    },
                    viewModel = viewModel.homeScreen,
                    isCelsiusState = user.preferences.temperatureUnit == "M",
                    isMmolL = user.preferences.bloodSugarUnit == "I",
                )
            }
        }
    }

    /** 问卷 */
    when (deviceType) {
        DeviceType.aiNeck, DeviceType.aiNeckCV -> {
            QuestionnaireReminderDialog(
                modifier = Modifier.fillMaxWidth(0.95f),
                visible = viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value,
                onConfirm = {
                    viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = false
                    userSP
                        .edit()
                        .putLong(
                            Constants.LAST_FINISHED_NECK_QUESTIONNAIRE,
                            System.currentTimeMillis()
                        )
                        .apply()
                    // 更新用户问卷字段
                    user = user.copy().apply { this.neckQuestionnaire = 1 }
                    apiService.updateInfo(
                        body = hashMapOf(Pair("neck_questionnaire", 1))
                    ).enqueueBody {}
                    startScreen(Screen.QuestionnaireWelcome.route, false)
                },
                onDismiss = {
                    viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = false
                    userSP
                        .edit()
                        .putLong(
                            Constants.LAST_FINISHED_NECK_QUESTIONNAIRE,
                            System.currentTimeMillis()
                        )
                        .apply()
                    // 更新用户问卷字段
                    user = user.copy().apply { this.neckQuestionnaire = 1 }
                    apiService.updateInfo(
                        body = hashMapOf(Pair("neck_questionnaire", 1))
                    ).enqueueBody {}
                }
            )

            LaunchedEffect(Unit) {
                delay(1000)
                // 如果从未填写过问卷，直接显示
                if (user.neckQuestionnaire == 0) {
                    viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = true
                } else {
                    // 如果填写过问卷，检查周期
                    val now = System.currentTimeMillis()
                    val lastTime = userSP.getLong(Constants.LAST_FINISHED_NECK_QUESTIONNAIRE, now)
                    if (now - lastTime >= Constants.DAY_7) {
                        viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = true
                    }
                }
            }
        }

        DeviceType.aiBack, DeviceType.aiBackCV -> {
            QuestionnaireReminderDialog(
                modifier = Modifier.fillMaxWidth(0.95f),
//                visible = true,
                visible = viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value,
                onConfirm = {
                    viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = false
                    userSP
                        .edit()
                        .putLong(
                            Constants.LAST_FINISHED_BACK_QUESTIONNAIRE,
                            System.currentTimeMillis()
                        )
                        .apply()
                    // 更新用户问卷字段
                    user = user.copy().apply { this.backQuestionnaire = 1 }
                    apiService.updateInfo(
                        body = hashMapOf(Pair("back_questionnaire", 1))
                    ).enqueueBody {}
                    startScreen(Screen.AiBackQuestionnaireWelcome.route, false)
                },
                onDismiss = {
                    viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = false
                    userSP
                        .edit()
                        .putLong(
                            Constants.LAST_FINISHED_BACK_QUESTIONNAIRE,
                            System.currentTimeMillis()
                        )
                        .apply()
                    // 更新用户问卷字段
                    user = user.copy().apply { this.backQuestionnaire = 1 }
                    apiService.updateInfo(
                        body = hashMapOf(Pair("back_questionnaire", 1))
                    ).enqueueBody {}
                }
            )

            LaunchedEffect(Unit) {
                delay(1000)
                // 如果从未填写过问卷，直接显示
                if (user.backQuestionnaire == 0) {
                    viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = true
                } else {
                    // 如果填写过问卷，检查周期
                    val now = System.currentTimeMillis()
                    val lastTime = userSP.getLong(Constants.LAST_FINISHED_BACK_QUESTIONNAIRE, now)
                    if (now - lastTime >= Constants.DAY_7) {
                        viewModel.homeScreen.isShowQuestionnaireVCGuideDetectResultDialog.value = true
                    }
                }
            }

        }

        else -> {}
    }

    RecalibrationDialog(
        isShowRecalibrationDialog = viewModel.homeScreen.recalibrationDialogVisible,
        onDismissRequest = {
            viewModel.homeScreen.recalibrationDialogVisible = false
        },
        onConfirm = {
            viewModel.homeScreen.recalibrationDialogVisible = false
            startScreen(Screen.CalibrationDevice.route)
        }
    )

    /** 权限使用说明 */
    PermissionGrantDialog(viewModel)
}

/**
 * 下拉菜单CV
 */
@Composable
internal fun AiNeckCVAndAiBackCVHeaderDropdownMenu(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    onClick: (DeviceType) -> Unit = {}
) {
    DropdownMenu(
        modifier = Modifier.background(Color.White),
        expanded = visible,
        onDismissRequest = onDismissRequest
    ) {
        DropdownMenuItem(
            onClick = { onClick(DeviceType.aiNeckCV) },
            text = { Text(text = stringResource(id = R.string.ai_neck_cv)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
        AIHDivider()
        DropdownMenuItem(
            onClick = { onClick(DeviceType.aiBackCV) },
            text = { Text(text = stringResource(id = R.string.ai_back_cv)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
    }
}

/**
 * 下拉菜单
 */
@Composable
internal fun AiNeckAndAiBackHeaderDropdownMenu(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    onClick: (DeviceType) -> Unit = {}
) {
    DropdownMenu(
        modifier = Modifier.background(Color.White),
        expanded = visible,
        onDismissRequest = onDismissRequest
    ) {
        DropdownMenuItem(
            onClick = { onClick(DeviceType.aiNeck) },
            text = { Text(text = stringResource(id = R.string.ai_neck)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
        AIHDivider()
        DropdownMenuItem(
            onClick = { onClick(DeviceType.aiBack) },
            text = { Text(text = stringResource(id = R.string.ai_back)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
    }
}

@Composable
internal fun AiJointHeaderDropdownMenu(
    visible: Boolean,
    onDismissRequest: () -> Unit,
    onClick: (DeviceType) -> Unit = {}
) {
    DropdownMenu(
        modifier = Modifier.background(Color.White),
        expanded = visible,
        onDismissRequest = onDismissRequest
    ) {
        // 肘关节
        DropdownMenuItem(
            onClick = { onClick(DeviceType.ElbowJoint) },
            text = { Text(text = stringResource(id = R.string.joint_elbow)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
        AIHDivider()
        //膝关节
        DropdownMenuItem(
            onClick = { onClick(DeviceType.KneeJoint) },
            text = { Text(text = stringResource(id = R.string.joint_knee)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
        AIHDivider()
        //肩关节
        DropdownMenuItem(
            onClick = { onClick(DeviceType.ShoulderJoint) },
            text = { Text(text = stringResource(id = R.string.joint_shoulder)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
        AIHDivider()
        // 髋关节
        DropdownMenuItem(
            onClick = { onClick(DeviceType.HipJoint) },
            text = { Text(text = stringResource(id = R.string.joint_hip)) },
            colors = MenuDefaults.itemColors(
                textColor = Color.Black,
            ),
        )
    }
}

/**
 *  连接状态卡片中欢迎提示框
 */
@Composable
fun TopTipsBlockOfConnectionStatusCard(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(vertical = 10.dp),
        elevation = CardDefaults.cardElevation(1.dp),
        colors = CardDefaults.cardColors(Color(0xFFF2F2F2)),
        shape = RoundedCornerShape(16.dp),
    ) {
        content()
    }
}


/**
 * 权限说明弹窗
 */
@Composable
private fun PermissionGrantDialog(
    viewModel: MainViewModel
) {
    if (viewModel.homeScreen.showPowerDialogVisibleState) {
        Dialog(onDismissRequest = { viewModel.homeScreen.showPowerDialogVisibleState = false }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White)
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.camera_permission),
                    modifier = Modifier.padding(horizontal = 8.dp),
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.homeScreen.showPowerDialogVisibleState = false
                            }, contentAlignment = Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.homeScreen.showPowerDialogVisibleState = false
                                XXPermissions
                                    .with(activity)
                                    .permission(Permission.CAMERA)
                                    .request(object : OnPermissionCallback {
                                        override fun onGranted(
                                            p0: MutableList<String>,
                                            allGranted: Boolean
                                        ) {
                                            if (allGranted) {
                                                /* 触发视频检测前对话框 */
                                                if (viewModel.homeScreen.theClickType == 1) {
                                                    viewModel.homeScreen.onScanClick()
                                                } else {
                                                    viewModel.homeScreen.isBootVCGuideScreen.value =
                                                        true
                                                }
                                            } else {
                                                DialogUtil.showToast(activity.getString(R.string.please_grant_permission))
                                            }
                                        }

                                        override fun onDenied(
                                            permissions: MutableList<String>,
                                            doNotAskAgain: Boolean
                                        ) {
                                            super.onDenied(permissions, doNotAskAgain)
                                            if (doNotAskAgain) {
                                                DialogUtil.showToast(activity.getString(R.string.please_grant_permission_manual))
                                                XXPermissions.startPermissionActivity(
                                                    activity,
                                                    permissions
                                                )
                                            } else {
                                                DialogUtil.showToast(activity.getString(R.string.please_grant_permission))
                                            }
                                        }

                                    })
                            }, contentAlignment = Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TailMembershipDialog(
    viewModel: MainViewModel,
    onClick: () -> Unit
) {
    if (viewModel.isShowTailMembershipDialog) {
        Dialog(onDismissRequest = {
            onClick()
        }) {
            if (currentLocale == Locale.CHINESE) {
                Column(
                    modifier = Modifier
                        .widthIn(min = 300.dp)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(horizontal = 16.dp)
                        .padding(top = 24.dp, bottom = 12.dp),
                    horizontalAlignment = CenterHorizontally
                ) {
                    Text(
                        modifier = Modifier,
                        text = stringResource(id = R.string.trail_membership_100_hours),
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF000000),
                    )
                    Spacer(modifier = Modifier.height(20.dp))
                    Row(
                        horizontalArrangement = Arrangement.Center
                    ) {
                        AIHButton(
                            text = "我收下了",
                            onClick = {
                                onClick()
                            },
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                    }
                }
            } else {
                Column(
                    modifier = Modifier
                        .widthIn(min = 300.dp)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(top = 24.dp, bottom = 12.dp),
                    horizontalAlignment = CenterHorizontally
                ) {
                    Text(
                        modifier = Modifier
                            .padding(horizontal = 16.dp),
                        text = stringResource(id = R.string.trail_membership_100_hours),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF000000),
                        )
                    )
                    Spacer(modifier = Modifier.height(20.dp))
                    Row(
                        horizontalArrangement = Arrangement.Center
                    ) {
                        AIHButton(
                            text = "OK",
                            onClick = {
                                onClick()
                            },
                            fontSize = 14.sp,
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TrialMembershipFinishDialog(
    viewModel: MainViewModel
) {
    if (viewModel.isShowTailMembershipFinishedDialog) {
        Dialog(
            onDismissRequest = {
                viewModel.isShowTailMembershipFinishedDialog = false
                userSP.edit().putBoolean("showTrailMembershipFinishedDialog", true).apply()
            },
            properties = DialogProperties(
                dismissOnBackPress = false,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .background(Color.White, RoundedCornerShape(10.dp))
                    .padding(top = 24.dp, bottom = 12.dp),
                horizontalAlignment = CenterHorizontally
            ) {
                if (currentLocale == Locale.CHINESE) {
                    Text(
                        modifier = Modifier
                            .padding(horizontal = 16.dp),
                        text = stringResource(id = R.string.trail_membership_100_hours_finished),
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF000000),
                    )
                    Spacer(modifier = Modifier.height(20.dp))
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        AIHOutlinedButton(
                            text = stringResource(id = R.string.cancel),
                            onClick = {
                                viewModel.isShowTailMembershipFinishedDialog = false
                                userSP.edit().putBoolean("showTrailMembershipFinishedDialog", true)
                                    .apply()
                            },
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        AIHButton(
                            text = stringResource(id = R.string.confirm),
                            onClick = {
                                viewModel.isShowTailMembershipFinishedDialog = false
                                userSP.edit().putBoolean("showTrailMembershipFinishedDialog", true)
                                    .apply()
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                    }
                } else {
                    Text(
                        modifier = Modifier,
                        text = stringResource(id = R.string.trail_membership_100_hours_finished),
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF000000),
                    )

                    Spacer(modifier = Modifier.height(20.dp))
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        AIHOutlinedButton(
                            text = stringResource(id = R.string.cancel),
                            onClick = {
                                viewModel.isShowTailMembershipFinishedDialog = false
                                userSP.edit().putBoolean("showTrailMembershipFinishedDialog", true)
                                    .apply()
                            },
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        AIHButton(
                            text = stringResource(id = R.string.confirm),
                            onClick = {
                                viewModel.isShowTailMembershipFinishedDialog = false
                                userSP.edit().putBoolean("showTrailMembershipFinishedDialog", true)
                                    .apply()
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                            fontWeight = FontWeight(400),
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                    }
                }

            }
        }
    }
}