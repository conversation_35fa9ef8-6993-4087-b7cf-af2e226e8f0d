package org.aihealth.ineck.view.screen.info

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import org.aihealth.ineck.R
import org.aihealth.ineck.model.Constants.WEIGHT_KG_MAX_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_KG_MIN_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_LB_MAX_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_LB_MIN_LIMIT
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil

@Composable
fun WeightVerticalPickerWithInputDialog(
    visible: Boolean = true,
    initialWeight: Double = 10.0,
    selectedUnit: String = "I",
    onWeightSelected: (WeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    AnimatedVisibility(
        visible = visible
    ) {
        Dialog(
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            val context = LocalContext.current

            var selectedOption by remember {
                mutableStateOf(
                    when (selectedUnit) {
                        "I" -> "lb"
                        "M" -> "kg"
                        else -> "lb"
                    }
                )
            }
            LogUtil.i("selectedOption:$selectedOption")
            val weightKg = (0..500).toList()
            val weightLb = (0..1100).toList()

            var weightInput by remember(initialWeight) {
                mutableDoubleStateOf(initialWeight)
            }
            var mTextFieldInput by remember(weightInput) { mutableStateOf(weightInput.toString()) }
            var iTextFieldInput by remember(weightInput) { mutableStateOf((weightInput * 2.2046226218).toString()) }
            val keyboardController = LocalSoftwareKeyboardController.current
            val focusManager = LocalFocusManager.current

            Column(
                modifier = Modifier
                    .background(Color.White, RoundedCornerShape(16.dp))
                    .fillMaxWidth(0.98f)
                    .padding(horizontal = 12.dp)
            ) {
                // close icon
                Row(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Icon(
                        modifier = Modifier
                            .size(24.dp)
                            .clickable {
                                onDismiss()
                            },
                        painter = painterResource(R.drawable.ic_close),
                        contentDescription = "close",
                        tint = Color.Black,
                    )
                }
                // title
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                        .padding(top = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.weight),
                        style = TextStyle(
                            fontSize = 20.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(end = 24.dp)
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "lb",
                        selected = selectedOption == "lb",
                        onClick = {
                            selectedOption = "lb"
                        }
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "kg",
                        selected = selectedOption == "kg",
                        onClick = {
                            selectedOption = "kg"
                        }
                    )
                }
                // data picker
                Column(
                    modifier = Modifier
                        .padding(top = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    when (selectedOption) {
                        "kg" -> {
                            var textFieldValue by remember(mTextFieldInput) {
                                mutableStateOf(
                                    TextFieldValue(
                                        text =
                                        if (mTextFieldInput.toDoubleOrNull() == null
                                            || mTextFieldInput.last() == '.'
                                            || mTextFieldInput.toIntOrNull() != null
                                        ) {
                                            mTextFieldInput
                                        } else {
                                            String.format("%.1f", mTextFieldInput.toDouble())
                                        },
                                        selection = TextRange(
                                            if (mTextFieldInput.toDoubleOrNull() == null
                                                || mTextFieldInput.last() == '.'
                                                || mTextFieldInput.toIntOrNull() != null
                                            ) {
                                                mTextFieldInput.length
                                            } else {
                                                String.format("%.1f", mTextFieldInput.toDouble()).length
                                            }
                                        )
                                    )
                                )
                            }
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .onFocusChanged { focusState ->
                                        if (!focusState.isFocused) {
                                            // Hide the keyboard when the TextField loses focus
                                            keyboardController?.hide()
                                        }
                                    },
                                value = textFieldValue,
                                onValueChange = { newText ->
                                    if (newText.text.isEmpty()) {
                                        mTextFieldInput = ""
                                    } else if (newText.text.toDoubleOrNull() != null) {
                                        mTextFieldInput = newText.text
                                    }

                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        weightInput =
                                            if (mTextFieldInput.toDoubleOrNull() != null) mTextFieldInput.toDouble()
                                            else 0.0
                                        focusManager.clearFocus()
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            VerticalDataSelector(
                                modifier = Modifier
                                    .padding(top = 10.dp)
                                    .height(250.dp),
                                items = weightKg,
                                initialData = when (weightInput) {
                                    in (weightKg.first() * 1.0..weightKg.last() * 1.0) -> {
                                        weightInput
                                    }

                                    else -> {
                                        when {
                                            weightInput < weightKg.first() * 1.0 -> weightKg.first() * 1.0
                                            else -> weightKg.last() * 1.0
                                        }
                                    }
                                },
                                unit = "kg"
                            ) { weight ->
                                LogUtil.i("select item :${weight}")
                                weightInput = weight
                            }
                        }

                        "lb" -> {
                            // data input
                            var textFieldValue by remember(iTextFieldInput) {
                                mutableStateOf(
                                    TextFieldValue(
                                        text =
                                        if (iTextFieldInput.toDoubleOrNull() == null
                                            || iTextFieldInput.last() == '.'
                                            || iTextFieldInput.toIntOrNull() != null
                                        ) {
                                            iTextFieldInput
                                        } else {
                                            String.format("%.1f", iTextFieldInput.toDouble())
                                        },
                                        selection = TextRange(
                                            if (iTextFieldInput.toDoubleOrNull() == null
                                                || iTextFieldInput.last() == '.'
                                                || iTextFieldInput.toIntOrNull() != null
                                            ) {
                                                iTextFieldInput.length
                                            } else {
                                                String.format(
                                                    "%.1f",
                                                    iTextFieldInput.toDouble()
                                                ).length
                                            }
                                        )
                                    )
                                )
                            }
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .onFocusChanged { focusState ->
                                        if (!focusState.isFocused) {
                                            // Hide the keyboard when the TextField loses focus
                                            keyboardController?.hide()
                                        }
                                    },
                                value = textFieldValue,
                                onValueChange = { newText ->
                                    if (newText.text.isEmpty()) {
                                        iTextFieldInput = ""
                                    } else if (newText.text.toDoubleOrNull() != null) {
                                        iTextFieldInput = newText.text
                                    }
                                    LogUtil.i("newText$newText, iTextFieldInput$iTextFieldInput")
                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        weightInput =
                                            if (iTextFieldInput.toDoubleOrNull() != null) iTextFieldInput.toDouble() / 2.2046226218
                                            else 0.0
                                        focusManager.clearFocus()
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            VerticalDataSelector(
                                modifier = Modifier
                                    .padding(top = 10.dp)
                                    .height(250.dp),
                                items = weightLb,
                                initialData = when (weightInput * 2.2046226218) {
                                    in (weightLb.first() * 1.0..weightLb.last() * 1.0) -> {
                                        weightInput * 2.2046226218
                                    }

                                    else -> {
                                        when {
                                            weightInput * 2.2046226218 < weightLb.first() * 1.0 -> weightLb.first() * 1.0
                                            else -> weightLb.last() * 1.0
                                        }
                                    }
                                },
                                unit = "lb"
                            ) { weight ->
                                LogUtil.i("select item :${weight}")
                                weightInput = (weight / 2.2046226218)
                            }

                        }

                        else -> {}
                    }
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 30.dp),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Box(
                        Modifier
                            .weight(1f)
                            .border(
                                width = 1.dp,
                                color = Color(0xFF3262FF),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .background(
                                shape = RoundedCornerShape(size = 16.dp),
                                color = Color.White
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable { onDismiss() }
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            modifier = Modifier
                                .align(Alignment.Center)
                                .clickable { onDismiss() },
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF3C72FF),
                            )
                        )
                    }
                    Spacer(modifier = Modifier.size(15.dp))
                    Box(
                        Modifier
                            .weight(1f)
                            .background(
                                Brush.linearGradient(
                                    colors = listOf(
                                        Color(0XFF73C5FF),
                                        Color(0XFF3161FF),
                                    )
                                ),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable {
                                when (selectedOption) {
                                    "kg" -> {
                                        weightInput =
                                            if (mTextFieldInput.toDoubleOrNull() != null) mTextFieldInput.toDouble()
                                            else 0.0

                                        if (weightInput < WEIGHT_KG_MIN_LIMIT || weightInput > WEIGHT_KG_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_metric))
                                        } else {
                                            onWeightSelected(
                                                WeightUnit(
                                                    value = weightInput,
                                                    unit = "M"
                                                )
                                            )
                                        }
                                    }

                                    else -> {
                                        weightInput =
                                            if (iTextFieldInput.toDoubleOrNull() != null) iTextFieldInput.toDouble() / 2.2046226218
                                            else 0.0

                                        if (weightInput * 2.2046226218 < WEIGHT_LB_MIN_LIMIT || weightInput * 2.2046226218 > WEIGHT_LB_MAX_LIMIT) {
                                            LogUtil.i("weightInput:$weightInput,${weightInput * 2.2046226218}")
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_imperial))
                                        } else {
                                            onWeightSelected(
                                                WeightUnit(
                                                    value = weightInput,
                                                    unit = "I"
                                                )
                                            )
                                        }
                                    }
                                }
                            }
                    ) {
                        Text(
                            modifier = Modifier.align(Alignment.Center),
                            text = stringResource(id = R.string.confirm),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color.White
                            )
                        )
                    }

                }
            }
        }
    }
}
