package org.aihealth.ineck.view.custom

import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun AIHDivider(
    modifier: Modifier = Modifier,
    color: Color = Color(0XFFDCDCDC),
    thickness: Dp = 0.4.dp,
    startIndent: Dp = 0.dp,
    isVertical: Boolean = false
) {
    if (isVertical) {
        HorizontalDivider(
            modifier
                .fillMaxHeight()
                .width(thickness), thickness, color)
    } else {
        HorizontalDivider(modifier, thickness, color)
    }
}