package org.aihealth.ineck.view.screen.vcguide

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.LifecycleOwner
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import kotlinx.coroutines.launch
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectedError
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideProcessState
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel

@Composable
fun AiBackVCGuideScreen(
    modifier: Modifier = Modifier,
    visible: Boolean,
    viewModel: VCGuideViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onDismissEvent: (VCDetectingResult) -> Unit
) {
    AnimatedVisibility(
        visible = visible,
    ) {
        // 总流程分页状态
        val pagerState = rememberPagerState(
            initialPage = VCGuideProcessState.PreviousGuidePage.toPageNumber(),
        )

        // scope
        val dialogScope = rememberCoroutineScope()
        val isSpeechEnable = remember {
            TextToSpeech.isSpeakingEnable()
        }
        /* 当前静音状态 */
        val isMuteState = viewModel.isMuteState.collectAsState()

        Dialog(
            onDismissRequest = {
                onDismissEvent(VCDetectingResult.DetectingError(VCDetectedError.Cancel()))
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false,
            )
        ) {
            Column(
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Card(
                    modifier = modifier
                        .fillMaxWidth(0.9f)
                        .fillMaxHeight(0.9f),
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFFF4F4F4),
                    ),
                    shape = RoundedCornerShape(10.dp),
                ) {
                    HorizontalPager(
                        state = pagerState,
                        count = 3,
                        modifier = Modifier.fillMaxWidth(),
                        userScrollEnabled = false
                    ) { page ->
                        when (page) {
                            VCGuideProcessState.PreviousGuidePage.toPageNumber() -> {
                                AiBackPreviousGuideScreen(
                                    modifier = Modifier.fillMaxWidth(),
                                    onIgnoreEvent = {
                                        onDismissEvent(
                                            VCDetectingResult.DetectingError(
                                                VCDetectedError.Cancel()
                                            )
                                        )
                                    },
                                    onNextEvent = {
                                        viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                                        dialogScope.launch {
                                            pagerState.scrollToPage(VCGuideProcessState.DetectingPage.toPageNumber())
                                        }
                                    }
                                )
                            }

                            VCGuideProcessState.DetectingPage.toPageNumber() -> {
// 校准过程中的分页
                                val detectingPagerState = rememberPagerState(0)
                                AiBackVCDetectingScreen(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .wrapContentHeight(),
                                    pagerState = detectingPagerState,
                                    pagerCount = 2,
                                ) { currentDetectingPager ->
                                    when (currentDetectingPager) {
                                        0 -> {
                                            VCBackDetectingScreen(
                                                modifier = Modifier.fillMaxWidth(),
                                                isMuteState = isMuteState.value,
                                                isVoiceEnable = isSpeechEnable,
                                                changeMuteState = {
                                                    viewModel.changeCurrentMuteState(!isMuteState.value)
                                                },
                                                nextPage = {
                                                    LogUtil.d("VCHorizontalDetectingScreen next Pager")
                                                    dialogScope.launch {
                                                        detectingPagerState.scrollToPage(1)
                                                    }
                                                },
                                                timeOut = {
                                                    LogUtil.d("VCHorizontalDetectingScreen time Out")

                                                    viewModel.changeDetectedResult(
                                                        VCDetectingResult.DetectingError(
                                                            VCDetectedError.Timeout()
                                                        )
                                                    )
                                                    viewModel.changeGuideDetectProcessState(
                                                        VCGuideProcessState.DetectedPage
                                                    )
                                                    dialogScope.launch {
                                                        pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                    }
                                                }
                                            )
                                        }

                                        1 -> {
                                            AiBackVCFaceDetectingScreen(
                                                modifier = Modifier.fillMaxWidth(),
                                                isMuteState = isMuteState.value,
                                                isVoiceEnable = isSpeechEnable,
                                                changeMuteState = {
                                                    viewModel.changeCurrentMuteState(!isMuteState.value)
                                                },
                                                nextPage = { it ->
                                                    viewModel.changeDetectedResult(it)
                                                    viewModel.changeGuideDetectProcessState(
                                                        VCGuideProcessState.DetectedPage
                                                    )
                                                    dialogScope.launch {
                                                        pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                        detectingPagerState.scrollToPage(0)
                                                    }
                                                },
                                                lifecycleOwner = lifecycleOwner,
                                                timeOut = {
                                                    viewModel.changeDetectedResult(
                                                        VCDetectingResult.DetectingError(
                                                            VCDetectedError.Timeout()
                                                        )
                                                    )
                                                    viewModel.changeGuideDetectProcessState(
                                                        VCGuideProcessState.DetectedPage
                                                    )
                                                    dialogScope.launch {
                                                        pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                        detectingPagerState.scrollToPage(0)
                                                    }
                                                }
                                            )
                                        }
                                    }

                                }
                            }

                            VCGuideProcessState.DetectedPage.toPageNumber() -> {
                                LogUtil.i(
                                    "VCGuideScreen",
                                    "${viewModel.detectedResult.collectAsState().value}"
                                )
                                VCDetectedScreen(
                                    modifier = Modifier.fillMaxWidth(),
                                    viewModel = viewModel,
                                    onDismissEvent = {
                                        dialogScope.launch {
                                            pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
                                        }
                                        onDismissEvent(it)
                                    },
                                    tryAgainEvent = {
                                        viewModel.changeGuideDetectProcessState(VCGuideProcessState.PreviousGuidePage)

                                        dialogScope.launch {
                                            pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
                                        }
                                    }
                                )
                            }
                        }
                    }
                }
                if (pagerState.currentPage != 0 && pagerState.currentPage != 2) {
                    CloseDetectingDialog(
                        modifier = Modifier
                            .padding(top = 10.dp),
                        onDismissEvent = {
                            dialogScope.launch {
//                                pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
//                                detectingPagerState.scrollToPage(0)
                            }
                            onDismissEvent(VCDetectingResult.DetectingError(VCDetectedError.Cancel()))
                        }
                    )
                }
            }
        }

    }
}
