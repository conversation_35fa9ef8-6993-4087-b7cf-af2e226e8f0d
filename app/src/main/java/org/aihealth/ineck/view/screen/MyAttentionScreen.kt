package org.aihealth.ineck.view.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.gestures.rememberDraggableState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.angles.Follow
import org.aihealth.ineck.model.angles.Gender
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.ui.theme.Sizes
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHCard
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.ChatViewModel
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.MyAttentionViewModel
import kotlin.math.roundToInt

@Composable
fun MyAttentionScreen(mainViewModel: MainViewModel) {
    val viewModel = viewModel<MyAttentionViewModel>()
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }

    LaunchedEffect(Unit) {
        apiService.getFollows().enqueueBody {
            it?.data?.let { list ->
                viewModel.list.clear()
                viewModel.list.addAll(list.following_list)
            }
        }
    }
    BasePageView(
        title = stringResource(id = R.string.my_provider),
        showBackIcon = true,
        headerColor = Color.White,
        headerHeight = 60.dp,
        headerContent = {
            Image(
                painter = painterResource(id = R.drawable.img_add_attention),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = Sizes.actionHorizontalPadding)
                    .size(20.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            startScreen(Screen.AddAttention.route)
                        }
                    }
            )
        },
        headerBackgroundColor = Color(0XFF3C59CF),
        statusBarDarkContentEnabled = false
    ) {
        var searchValue by remember {
            mutableStateOf("")
        }
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0XFFF5F6F8))
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .offset(0.dp, (-24).dp)
        ) {
            AIHCard() {
                Card {
                    Surface(
                        color = Color.White,
                        shape = RoundedCornerShape(2.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp)
                        ) {
                            OutlinedTextField(
                                value = searchValue,
                                onValueChange = { searchValue = it },
                                singleLine = true,
                                placeholder = {
                                    Text(
                                        text = stringResource(id = R.string.myattention_search_tip),
                                        style = TextStyle(
                                            color = Color(0XFF999999),
                                            fontSize = fontSize14,
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis
                                    )
                                },
                                modifier = Modifier
                                    .weight(1f)  // 使输入框占据剩余空间
                                    .height(56.dp),  // 与 Material3
                                shape = RoundedCornerShape(8.dp)
                            )
                        }
                    }
                }
            }
            val list by remember {
                derivedStateOf {
                    viewModel.list.filter {
                        it.uuid.isNotEmpty()
                                && (it.uuid.contains(searchValue)
                                || it.email.contains(searchValue)
                                || it.name.contains(searchValue))
                    }
                }
            }
            Spacer(modifier = Modifier.height(4.dp))
            if (list.isNotEmpty()) {
                LazyColumn(modifier = Modifier.fillMaxSize()) {
                    items(list) {
                        Item(
                            follow = it,
                            onClick = {
                                val chatViewModel =
                                    ViewModelProvider(activity).get(ChatViewModel::class.java)
                                chatViewModel.setChatUser(
                                    uuid = it.uuid,
                                    name = it.name,
                                    avatar = it.photo
                                )
                                startScreen(Screen.Chat.route)
                            },
                            onSendClick = {
                                viewModel.selectFollow = it
                                viewModel.sendReportDialogVisible = true
                            },
                            onDeleteClick = {
                                viewModel.selectFollow = it
                                viewModel.deleteFollow()
                            }
                        )
                    }
                }
            } else {
                Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                    Text(
                        text = stringResource(id = R.string.no_attention),
                        color = Color(0XFF666666),
                        fontSize = fontSize16,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
    SendReportDialog(viewModel, mainViewModel)
}

@Preview()
@Composable
private fun Item(
    follow: Follow = Follow(name = "asdasdasd", gender = "M"),
    onClick: () -> Unit = {},
    onSendClick: () -> Unit = {},
    onDeleteClick: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize11 = with(density) { 11.sp / fontScale }
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }

    val max = 0.dp
    val min = (-50).dp
    val (minPx, maxPx) = with(LocalDensity.current) { min.toPx() to max.toPx() }
    // this is the  state we will update while dragging
    val offsetPosition = remember { mutableStateOf(0f) }

    AIHCard(
        modifier = Modifier
            .height(70.dp)
            .draggable(
                state = rememberDraggableState { delta ->
                    LogUtil.i("offset delta: $delta")
                    val newValue = offsetPosition.value + delta
                    offsetPosition.value = newValue.coerceIn(minPx, maxPx)
                },
                orientation = Orientation.Horizontal,
            ),
        paddingValues = PaddingValues(start = 18.dp, end = 18.dp, bottom = 4.dp)
    ) {

        BoxWithConstraints {
            val boxWithConstraintsScope = this
            Box(
                modifier = Modifier
                    .width(50.dp)
                    .fillMaxHeight()
                    .offset {
                        IntOffset(
                            boxWithConstraintsScope.constraints.maxWidth + offsetPosition.value.roundToInt(),
                            0
                        )
                    }
                    .background(Color(0XFFF5795E))
                    .clickable {
                        onDeleteClick()
                    }, contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(id = R.string.delete),
                    fontSize = fontSize11,
                    color = Color.White
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 12.dp)
                    .offset { IntOffset(offsetPosition.value.roundToInt(), 0) }
                    .pointerInput(Unit) {
                        detectTapGestures {
                            onClick()
                        }
                    }, verticalAlignment = Alignment.CenterVertically
            ) {
                if (follow.photo == "") {
                    Image(
                        painter = painterResource(id = R.drawable.img_myattention_photo),
                        contentDescription = null,
                        modifier = Modifier
                            .size(54.dp)
                            .clip(CircleShape)
                    )
                } else {
                    AsyncImage(
                        model = follow.photo,
                        contentDescription = null,
                        modifier = Modifier
                            .size(54.dp)
                            .clip(CircleShape),
                        contentScale = ContentScale.Crop,
                        error = painterResource(id = R.drawable.img_myattention_photo),
                    )
                }
                Column(
                    modifier = Modifier
                        .weight(1F)
                        .padding(horizontal = 8.dp)
                ) {
                    Row {
                        Text(
                            text = follow.name,
                            color = Color(0XFF666666),
                            fontSize = fontSize16,
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.width(5.dp))
                        Text(
                            text = follow.doctor_title,
                            color = Color(0XFF666666),
                            fontSize = fontSize16,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            modifier = Modifier,
                            text = stringResource(id = if (follow.gender == "F") Gender.F.text else Gender.M.text),
                            color = Color(0XFF666666),
                            fontSize = fontSize16,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    Spacer(modifier = Modifier.height(4.dp))
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(
                                Color(0XFFF4F4F4),
                                RoundedCornerShape(3.dp)
                            )
                    ) {
                        Text(
                            text = follow.userId,
                            color = Color(0XFF999999),
                            fontSize = fontSize10,
                            overflow = TextOverflow.Ellipsis,
                            modifier = Modifier
                                .padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
                AIHButton(
                    text = stringResource(id = R.string.send_report),
                    onClick = {
                        onSendClick()
                    },
                    modifier = Modifier
                        .width(80.dp)
                        .height(30.dp),
                    shape = RoundedCornerShape(3.dp),
                    fontSize = fontSize10
                )
            }
        }
    }
}

@Composable
private fun SendReportDialog(
    viewModel: MyAttentionViewModel,
    mainViewModel: MainViewModel
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize13 = with(density) { 13.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }
    val fontSize18 = with(density) { 18.sp / fontScale }

    if (viewModel.sendReportDialogVisible) {
        Dialog(onDismissRequest = {
            viewModel.sendReportDialogVisible = false
        }) {
            Column(
                modifier = Modifier
                    .width(290.dp)
                    .background(
                        Color.White,
                        RoundedCornerShape(24.dp)
                    )
                    .padding(horizontal = 35.dp, vertical = 25.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(
                        id = R.string.myattention_sendreport,
                        viewModel.selectFollow?.name ?: ""
                    ),
                    fontSize = fontSize18,
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(30.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 8.dp)
                ) {
                    AIHButton(
                        text = stringResource(id = R.string.cancel),
                        onClick = {
                            viewModel.sendReportDialogVisible = false
                        },
                        modifier = Modifier.size(72.dp, 24.dp),
                        backgroundColor = Color(0XFFA0A0A0),
                        shape = RoundedCornerShape(3.dp),
                        fontSize = fontSize10
                    )
                    Spacer(modifier = Modifier.weight(1F))
                    AIHButton(
                        text = stringResource(id = R.string.send_report),
                        onClick = {
                            viewModel.sendReport(mainViewModel)
                            viewModel.sendReportDialogVisible = false
                        },
                        modifier = Modifier.size(72.dp, 24.dp),
                        shape = RoundedCornerShape(3.dp),
                        fontSize = fontSize10
                    )
                }
            }
        }
    }
}