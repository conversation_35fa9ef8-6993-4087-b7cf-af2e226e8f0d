package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun FeedbackScreen(

) {
    BasePageView(
        title = stringResource(id = R.string.feedback),
        showBackIcon = true,
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0XFFCDCDCD))
    ) {
       Column(Modifier.fillMaxSize()) {
           Box(modifier = Modifier
               .padding(top = 35.dp, bottom = 8.dp)
               .fillMaxWidth()
               .background(Color.White)
               .height(48.dp)
               .clickable {

               }
               .padding(horizontal = 20.dp)
           ) {
               Text(
                   text = stringResource(id = R.string.problem_type),
                   fontSize = 14.sp,
                   color = Color(0XFF444444),
                   fontWeight = FontWeight.Bold,
                   modifier = Modifier.align(Alignment.CenterStart)
               )
               Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.align(Alignment.CenterEnd)) {
                   Text(
                       text = stringResource(id = R.string.choose_problem_type),
                       fontSize = 12.sp,
                       color = Color(0XFF666666)
                   )
                   Spacer(modifier = Modifier.width(1.dp))
                   Icon(
                       painter = painterResource(id = R.drawable.img_next),
                       contentDescription = null,
                       modifier = Modifier.size(14.dp),
                       tint = Color(0XFFA8A8A7)
                   )
               }
           }
           var feedback by remember {
               mutableStateOf("")
           }
           BasicTextField(
               value = feedback,
               onValueChange = { feedback = it },
               modifier = Modifier
                   .fillMaxWidth()
                   .height(178.dp)
                   .background(Color.White)
                   .padding(horizontal = 20.dp, vertical = 12.dp)
           )
           Spacer(modifier = Modifier.height(8.dp))
           var contactInfo by remember {
               mutableStateOf("")
           }
           BasicTextField(
               value = contactInfo,
               onValueChange = { contactInfo = it },
               decorationBox = {
                   if (contactInfo.isBlank()) {
                       Text(text = stringResource(id = R.string.feedback_contact_info), fontSize = 12.sp, color = Color(0XFF999999))
                   }
                   it()
               },
               modifier = Modifier.fillMaxWidth().height(40.dp).background(Color.White).padding(vertical = 12.dp, horizontal = 20.dp),
               singleLine = true
           )
           AIHButton(
               text = stringResource(id = R.string.submit),
               onClick = {
                   startScreen(Screen.Feedback.route)
               },
               modifier = Modifier.padding(42.dp).fillMaxWidth().height(42.dp),
               fontSize = 20.sp
           )
       }

    }
}