package org.aihealth.ineck.view.screen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.google.accompanist.web.WebView
import com.google.accompanist.web.rememberWebViewState
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.view.custom.BasePageView
import java.util.Locale

@Composable
fun UserAgreementScreen() {
    BasePageView(
        title = stringResource(id = R.string.user_agreement),
        showBackIcon = true
    ) {
        if (currentLocale == Locale.CHINESE) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp)
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = stringResource(id = R.string.mztk))
            }
        } else {
            val state = rememberWebViewState(
                url = "https://medical.aihnet.com/terms-of-us/"
            )
            WebView(state = state, modifier = Modifier
                .imePadding()
                .fillMaxSize(),
                onCreated = {
                    it.settings.javaScriptEnabled = true
                }
            )
        }

    }
}