package org.aihealth.ineck.view.screen.membershipcenter

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.SuggestionChip
import androidx.compose.material3.SuggestionChipDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.model.improvement.ImproveProgram
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.util.TimeUtil

/**
 * zh
 *  改善推荐页数据请求成功 卡片显示VIP内容
 *  @param  programsData    改善方案项目数据
 */
@Composable
fun ImprovementProgramCardSuccessContentWithVipEnOnly(
    programsData: ImprovementProgramsData
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }

    var data:List<ImproveProgram> = mutableListOf()
    programsData.programs.forEach {
        if (it.isMembershipRequired){
            data = data.plus(it)
        }
    }
    val context = LocalContext.current
    data = data.toList()
    data.forEachIndexed { index, program ->
        if (index != 0) {
            HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
        }
        Card(
            elevation = CardDefaults.cardElevation(0.dp),
            colors = CardDefaults.cardColors(Color.White),
            shape = RoundedCornerShape(16.dp),
            // 点击进入具体跟练页
            modifier = Modifier
                .clickable {
//                val model = program.toImprovementDetailModel()
//                startScreen(
//                    route = ImprovementDetailDirections.actionToImprovementDetailCompose(
//                        model = model
//                    ),
//                    finish = false
//                )
            }
        ) {
            // 相对布局
            ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
                val (programCover, programTitle, showDetail, enableDevice) = createRefs()
                /* 改善项目方案封面 */
                Surface(
                    modifier = Modifier
                        .size(96.dp)
                        .constrainAs(programCover) {
                            start.linkTo(parent.start, 10.dp)
                            top.linkTo(parent.top, 10.dp)
                            bottom.linkTo(parent.bottom, 10.dp)
                        },
                    shape = RoundedCornerShape(6.dp)
                ) {
                    AsyncImage(
                        model = program.cover,
                        contentDescription = program.title,
                        placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                        modifier = Modifier.fillMaxSize()
                    )
                }
                /* 改善项目方案标题 */
                Text(
                    text = program.title,
                    fontSize = fontSize14,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF666666),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.constrainAs(programTitle) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(parent.top, 10.dp)
                        end.linkTo(parent.end, 0.dp)
                        width = Dimension.fillToConstraints
                    }
                )
                /* 改善项目方案关键属性， 时长、已练习人数等 */
                Row(
                    modifier = Modifier.constrainAs(showDetail) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(programTitle.bottom, 8.dp)
                        end.linkTo(parent.end, 10.dp)
                        width = Dimension.fillToConstraints
                    },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Vip 文字
                    val vipString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFFF8C847),
                                fontSize = fontSize10,
                                fontWeight = FontWeight.Normal,
                            )
                        ) {
                            append(context.getString(R.string.vip_exclusive))
                        }
                    }

                    // 时长
                    val durationString = TimeUtil.convertSecondsToAnnotatedString(
                        program.duration,
                        LocalContext.current
                    )

                    // 锻炼人数
                    val numberOfPeopleString = buildAnnotatedString {
                        // 数字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = fontSize14,
                                fontWeight = FontWeight.Normal,

                                )
                        ) {
                            append(program.frequency.toString())
                            append(" ")
                        }
                        // 人锻炼
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF999999),
                                fontSize = fontSize12,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.unit_participated))
                        }
                    }
//                    LogUtil.i("lessonLevel: ${program.lessonLevel}")
                    if(program.isMembershipRequired){
                        Row(
                            Modifier.weight(1.7f),
                            horizontalArrangement = Arrangement.Start,
                            verticalAlignment = Alignment.CenterVertically
                        )
                        {
                            Image(
                                painter = painterResource(id = R.drawable.ic_vip),
                                contentDescription = "持续时间",
                                modifier = Modifier
                                    .size(14.dp)
                            )
                        }
                    }

                    /* 请求体响应结构中提供的“持续时间”参数是以秒为单位，这里需要再显示上做转换计算 */
                    Text(
                        text = durationString,
                        maxLines = 2,
                        modifier = Modifier
                            .padding(horizontal = 4.dp)
                            .weight(3f),
                    )
                    Text(
                        text = numberOfPeopleString,
                        maxLines = 2,
                        modifier = Modifier
                            .padding(horizontal = 4.dp)
                            .weight(4f)
                    )
                }
                /* 是否需要配对设备 Chip */
                if (program.isDevicesRequired) {
                    SuggestionChip(
                        enabled = false,
                        onClick = { /* empty */ },
                        modifier = Modifier
                            .height(36.dp)
                            .padding(8.dp)
                            .constrainAs(enableDevice) {
                                start.linkTo(programCover.end, 6.dp)
                                top.linkTo(showDetail.bottom, 4.dp)
                            },
                        border = SuggestionChipDefaults.suggestionChipBorder(
                            enabled = true,
                            borderWidth = 1.dp,
                            borderColor = Color(0xFF5777D4)
                        ),
                        colors = SuggestionChipDefaults.suggestionChipColors(
                            containerColor = Color(0xFFE1E7FB),
                            disabledContainerColor = Color(0xFFE1E7FB)
                        ),
                        shape = RoundedCornerShape(17.dp),
                        label = {
                            Text(
                                text = stringResource(id = R.string.is_need_of_devices),
                                color = Color(0xFF5777D4),
                                fontSize = fontSize12,
                            )
                        }
                    )
                }
            }
        }
    }
}