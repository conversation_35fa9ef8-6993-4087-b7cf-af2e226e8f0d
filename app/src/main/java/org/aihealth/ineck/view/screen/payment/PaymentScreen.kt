package org.aihealth.ineck.view.screen.payment

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.model.PayWayItem
import org.aihealth.ineck.ui.theme.Sizes
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.ActionBar
import org.aihealth.ineck.viewmodel.PaymentViewModel

@Composable
fun PaymentScreen(
    index: Int
) {
    val paymentViewModel: PaymentViewModel = viewModel()
    Scaffold(
        topBar = {
            ActionBar(
                stringResource(id = R.string.pending_payment),
                true,
                Sizes.actionBarHeight,
                Color(0XFF333333),
                Color.Transparent,
                true,
                content = {},
            )
        },
        bottomBar = {
            BottomAppBar(
                containerColor = Color.White,
                contentColor = MaterialTheme.colorScheme.primary,
            ) {
                Spacer(modifier = Modifier.weight(0.1f))
                Row(
                    modifier = Modifier
                        .weight(0.3f)
                        .padding(end = 10.dp),
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.End
                ) {
                    Text(
                        text = stringResource(id = R.string.total_one),
                        style = TextStyle(
                            fontSize = 12.sp,
                            lineHeight = 25.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF999999),
                            letterSpacing = 0.38.sp,
                        )
                    )
                    Spacer(modifier = Modifier.width(10.dp))
                    Text(
                        text = stringResource(id = R.string.summary_count)+""+if(index== 3) paymentViewModel.goodList[2].goodPrice else paymentViewModel.goodList[index-1].goodSalePrice,
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 25.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF333333),
                            letterSpacing = 0.38.sp,
                        ),
                        maxLines = 1,
                    )
                }

                Button(
                    modifier = Modifier
                        .weight(0.3f)
                        .border(
                            width = 1.dp,
                            color = Color(0xFFF45F1F),
                            shape = RoundedCornerShape(size = 24.dp)
                        )
                        .background(
                            color = Color(0xFFF45F1F),
                            shape = RoundedCornerShape(size = 24.dp)
                        ),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFFF45F1F),
                        contentColor = Color(0xFFF45F1F)
                    ),
                    onClick = {
                        LogUtil.i("onclick button")
                        paymentViewModel.payWayClick(index)

                    },
                ) {
                    Text(
//                        modifier = Modifier
//                            .clickable(
//                                onClick = {},
//                                indication = null,
//                                interactionSource = remember { MutableInteractionSource() }
//                            ),
                        text = stringResource(id = R.string.submit),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                            letterSpacing = 0.49.sp,
                        ),
                    )
                }
                Spacer(modifier = Modifier.width(10.dp))
            }
        }
    ) { innerPadding ->
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(innerPadding)
                .background(Color(0xFFF5F6F8)),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            // 填写地址
//            Row(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .height(61.dp)
//                    .background(color = Color(0xFFFFFFFF)),
//                verticalAlignment = Alignment.CenterVertically,
//                horizontalArrangement = Arrangement.Center,
//            ) {
//                Column(
//                    modifier = Modifier
//                        .fillMaxHeight()
//                        .padding(end = 30.dp)
//                        .clickable(
//                            onClick = { },
//                            indication = null,
//                            interactionSource = remember { MutableInteractionSource() }
//                        ),
//                    verticalArrangement = Arrangement.Center,
//                ) {
//                    Text(
//                        text = stringResource(id = R.string.input_address_text),
//                        style = TextStyle(
//                            fontSize = 16.sp,
//                            lineHeight = 25.sp,
//                            fontWeight = FontWeight(400),
//                            color = Color(0xFF000000),
//                            letterSpacing = 0.38.sp,
//                        ),
//                    )
//                }
//
//                Button(
//                    modifier = Modifier
//                        .border(
//                            width = 1.dp, color = Color(0xFF2B56D7),
//                            shape = RoundedCornerShape(size = 24.dp)
//                        )
//                        .background(
//                            color = Color(0xFFFFFFFF),
//                            shape = RoundedCornerShape(size = 24.dp)
//                        ),
//                    colors = ButtonDefaults.buttonColors(
//                        containerColor = Color.White,
//                        contentColor = Color.White,
//                    ),
//                    onClick = { /*TODO*/ }
//                ) {
//                    Text(
//                        modifier = Modifier
//                            .clickable(
//                                onClick = { },
//                                indication = null,
//                                interactionSource = remember { MutableInteractionSource() }
//                            ),
//                        text = stringResource(id = R.string.input_address),
//                        style = TextStyle(
//                            fontSize = 14.sp,
//                            fontWeight = FontWeight(400),
//                            color = Color(0xFF666666),
//                            letterSpacing = 0.49.sp,
//                        ),
//                    )
//                }
//
//            }

            // 商品
            if (index == 3) {
                ChosenGoods(
                    paymentViewModel.goodList[2].goodIcon,
                    paymentViewModel.goodList[2].goodName,
                    paymentViewModel.goodList[2].goodPrice,
                    paymentViewModel.goodList[2].goodSalePrice
                )
                ChosenGoodsT(
                    paymentViewModel.goodList[3].goodIcon,
                    paymentViewModel.goodList[3].goodName,
                    paymentViewModel.goodList[3].goodSalePrice,
                    paymentViewModel.goodList[3].goodPrice,
                    )

            } else {
                ChosenGoodsT(
                    paymentViewModel.goodList[index-1].goodIcon,
                    paymentViewModel.goodList[index-1].goodName,
                    paymentViewModel.goodList[index-1].goodSalePrice,
                    paymentViewModel.goodList[index-1].goodPrice,
                )
            }

            // 金额
            SumMoney(
                if(index== 3) paymentViewModel.goodList[2].goodSalePrice else paymentViewModel.goodList[index-1].goodSalePrice,
                stringResource(id = R.string.postal_rates),
                if(index== 3) paymentViewModel.goodList[2].goodSalePrice else paymentViewModel.goodList[index-1].goodSalePrice,
                )

            // 付款方式
            PayWay(
                paymentViewModel.payWayList
            )
        }
    }
}

@Composable
fun PayWay(
    payWayItemList: List<PayWayItem>
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 6.dp)
            .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 6.dp))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 4.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = stringResource(id = R.string.pay_way),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.38.sp,
                )
            )
        }
        payWayItemList.forEach { item ->
            PayItem(
                item.isCheck,
                {
                    LogUtil.i("click item ${item.payName}")
                    payWayItemList.forEach { it.isCheck = false }
                    item.isCheck = true
                },
                item.payName,
                item.payIcon,
            )
        }

    }
}

@Composable
private fun PayItem(
    isCheck: Boolean = true,
    onClick: () -> Unit = {},
    payName: String,
    @DrawableRes payIcon: Int,

    ) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp)
            .clickable(
                onClick = { onClick() },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier.padding(end = 5.dp),
                painter = painterResource(id = payIcon),
                contentDescription = "wechat pay"
            )
            Text(
                text = payName,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    letterSpacing = 0.38.sp,
                )
            )
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = if (isCheck) painterResource(id = R.drawable.ic_payment_check) else painterResource(
                    id = R.drawable.ic_payment_no_check
                ),
                contentDescription = "is check?"
            )
        }
    }
}


@Composable
private fun SumMoney(
    goodMoney: String,
    shippingService: String,
    totalMoney: String,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 6.dp)
            .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 6.dp))
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = stringResource(id = R.string.payment_total_money),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.38.sp,
                )
            )
            Text(
                text = goodMoney,
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    letterSpacing = 0.38.sp,
                )
            )

        }
        HorizontalDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .border(width = 0.5.dp, color = Color(0xFFE4E4E4))
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = stringResource(id = R.string.send_service),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.38.sp,
                )
            )
            Text(
                text = shippingService,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    letterSpacing = 0.38.sp,
                )
            )
        }

        HorizontalDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .border(width = 0.5.dp, color = Color(0xFFE4E4E4))
        )

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = stringResource(id = R.string.goods_total),
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.38.sp,
                )
            )
            Text(
                text = totalMoney,
                style = TextStyle(
                    fontSize = 16.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    letterSpacing = 0.38.sp,
                )
            )
        }
    }
}

@Composable
fun ChosenGoods(
    @DrawableRes goodId: Int,
    goodName: String,
    goodPrice: String,
    goodNumber: String,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(78.dp)
            .background(color = Color(0xFFFFFFFF)),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Row(
            modifier = Modifier
                .weight(0.7f)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .width(56.dp)
                    .height(56.dp)
                    .padding(horizontal = 10.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(size = 3.dp)
                    ),
                painter = painterResource(id = goodId),
                contentDescription = "goods"
            )
            Text(
                text = goodName,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    letterSpacing = 0.38.sp,
                )
            )
        }
        Column(
            modifier = Modifier
                .weight(0.3f)
                .padding(horizontal = 22.dp),
            horizontalAlignment = Alignment.End,
        ) {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = goodPrice,
                    style = TextStyle(
                        fontSize = 24.sp,
                        lineHeight = 25.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                        letterSpacing = 0.38.sp,
                    )
                )
            }
            Row(
                verticalAlignment = Alignment.Bottom
            ) {
                Image(
                    modifier = Modifier
                        .width(14.dp)
                        .height(14.dp),
                    painter = painterResource(id = R.drawable.ic_payment_x),
                    contentDescription = "img"
                )
                Text(
                    text = goodNumber,
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 25.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                        letterSpacing = 0.38.sp,
                    )
                )
            }
        }
    }
}


@Composable
fun ChosenGoodsT(
    @DrawableRes goodId: Int,
    goodName: String,
    goodSalePrice: String,
    goodPrice: String,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(78.dp)
            .background(color = Color(0xFFFFFFFF)),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Row(
            modifier = Modifier
                .weight(0.7f)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .width(56.dp)
                    .height(56.dp)
                    .padding(horizontal = 10.dp)
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(size = 3.dp)
                    ),
                painter = painterResource(id = goodId),
                contentDescription = "goods"
            )
            Text(
                text = goodName,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 25.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    letterSpacing = 0.38.sp,
                )
            )
        }
        Column(
            modifier = Modifier
                .weight(0.3f)
                .padding(horizontal = 22.dp),
            horizontalAlignment = Alignment.End,
        ) {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.Bottom
            ) {
                Text(
                    text = goodSalePrice,
                    style = TextStyle(
                        fontSize = 24.sp,
                        lineHeight = 25.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                        letterSpacing = 0.38.sp,
                    )
                )
            }
            Row(
                verticalAlignment = Alignment.Bottom
            ) {

                Text(
                    text = goodPrice,
                    style = TextStyle(
                        fontSize = 16.sp,
                        lineHeight = 25.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                        letterSpacing = 0.38.sp,
                    ),
                    textDecoration = TextDecoration.LineThrough
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewPaymentScreen() {
    PaymentScreen(4)
}
