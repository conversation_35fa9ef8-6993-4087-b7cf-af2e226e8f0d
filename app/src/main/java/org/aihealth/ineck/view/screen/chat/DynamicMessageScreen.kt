package org.aihealth.ineck.view.screen.chat

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModelProvider
import coil.compose.AsyncImage
import com.google.accompanist.swiperefresh.SwipeRefresh
import com.google.accompanist.swiperefresh.rememberSwipeRefreshState
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.improvement.ChatItem
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.screen.defaultscreen.NoMessageScreen
import org.aihealth.ineck.viewmodel.ChatViewModel


@Composable
fun DynamicMessagePage(
    viewmodel: ChatViewModel  // 接收viewmodel参数
) {
    // 从ViewModel中收集chatItems数据
    val chatItems by viewmodel.chatItems.collectAsState()

    // 添加下拉刷新功能
    val refreshing by viewmodel.isChatListLoading.collectAsState()
    // 使用Accompanist的SwipeRefresh
    val swipeRefreshState = rememberSwipeRefreshState(isRefreshing = refreshing)
    
    // 定义协程作用域
    val coroutineScope = rememberCoroutineScope()

    // 添加初始加载功能
    LaunchedEffect(Unit) {
        // 确保页面显示时数据是最新的
        viewmodel.fetchChatList()
    }
    
    // 添加定时轮询功能
    DisposableEffect(Unit) {
        // 创建轮询任务
        val pollingJob = coroutineScope.launch {
            while(true) {
                // 延迟30秒后执行
                delay(10000) // 30秒轮询一次
                viewmodel.fetchChatList()
            }
        }
        
        // 清理工作：当组件从组合中移除时，取消轮询任务
        onDispose {
            pollingJob.cancel()
        }
    }

    SwipeRefresh(
        state = swipeRefreshState,
        onRefresh = { viewmodel.fetchChatList() },
        modifier = Modifier.fillMaxSize()
    ) {
        if (chatItems.isNotEmpty()) {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 6.dp)
            ) {
                items(chatItems) { item ->
                    ChatListItem(item = item)
                    HorizontalDivider(
                        modifier = Modifier.padding(vertical = 8.dp),
                        color = Color(0xFFEEEEEE)
                    )
                }
            }
        } else {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                NoMessageScreen()
            }
        }
    }
}

@Composable
private fun ChatListItem(item: ChatItem) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable {
                val chatViewModel =
                    ViewModelProvider(activity).get(ChatViewModel::class.java)
                chatViewModel.setChatUser(
                    uuid = item.uuid,
                    name = item.displayName,
                    avatar = item.displayAvatar
                )
                startScreen(Screen.Chat.route)
            }
            .padding(vertical = 12.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        AsyncImage(
            model = item.displayAvatar,
            contentDescription = null,
            modifier = Modifier
                .size(48.dp)
                .clip(RoundedCornerShape(8.dp)),
            contentScale = ContentScale.Crop,
            error = painterResource(id = R.drawable.header_3),
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 12.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = item.displayName,
                    style = Typography.titleMedium,
                    color = Color(0xFF333333)
                )

                Image(
                    painter = painterResource(R.drawable.png_doctor),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .size(16.dp)
                )
            }

            Text(
                text = item.displayMessage,
                style = Typography.bodyMedium,
                color = Color(0xFF666666),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.padding(top = 4.dp)
            )
        }
        Column(
            modifier = Modifier,
            horizontalAlignment = Alignment.End,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = TimeUtil.timestampToDate(item.time.toLong()),
                style = Typography.bodySmall,
                color = Color(0xFF999999)
            )

            if (item.unread > 0) {
                Box(
                    modifier = Modifier
                        .padding(top = 8.dp)
                        .size(20.dp)
                        .clip(CircleShape)
                        .background(Color.Red),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = item.unread.toString(),
                        color = Color.White,
                        fontSize = 12.sp
                    )
                }
            }
        }
    }
}
