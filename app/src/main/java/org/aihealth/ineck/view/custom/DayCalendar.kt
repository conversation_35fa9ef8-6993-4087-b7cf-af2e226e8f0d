package org.aihealth.ineck.view.custom

import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.util.copy
import org.aihealth.ineck.util.dayOfWeek
import org.aihealth.ineck.util.toPx
import java.text.SimpleDateFormat
import java.util.Calendar
import kotlin.math.absoluteValue


private val minDragWidth by lazy {
    20.dp.toPx()
}

class DayCalendarState(
    val today: Calendar
) {
    var selectedDay by mutableStateOf(today)
    val offset = Animatable(0F)
    var isHasConnected = false
    var pageIndex: Int = 0
    var containerWidth by mutableFloatStateOf(0f)
    
    val itemWidth: Float
        get() = containerWidth / 7
}

/**
 * 在 DrawScope 中绘制日历
 */
private fun DrawScope.drawCalendarPage(
    pageIndex: Int,
    selectedDay: Calendar,
    today: Calendar,
    isHasConnected: Boolean,
    offsetX: Float = 0f
) {
    val calendar = today.copy()
    calendar.add(Calendar.DATE, pageIndex * 7 - calendar.dayOfWeek)
    val week = baseApplication.resources.getStringArray(R.array.week3)
    
    val paint = android.graphics.Paint().apply {
        textSize = 10.dp.toPx()
        textAlign = android.graphics.Paint.Align.CENTER
        isAntiAlias = true
    }
    
    val itemWidth = size.width / 7
    var x = itemWidth / 2f + offsetX
    
    repeat(7) { dayIndex ->
        // 绘制选中状态背景
        if (selectedDay.timeInMillis == calendar.timeInMillis) {
            val bgColor = if (isHasConnected) {
                android.graphics.Color.parseColor("#FFBDCDE6")
            } else {
                android.graphics.Color.parseColor("#FFE7F0FE")
            }
            drawRoundRect(
                color = Color(bgColor),
                topLeft = Offset(x - 13.dp.toPx(), 0f),
                size = androidx.compose.ui.geometry.Size(26.dp.toPx(), size.height),
                cornerRadius = androidx.compose.ui.geometry.CornerRadius(13.dp.toPx())
            )
            paint.isFakeBoldText = true
            paint.color = if (isHasConnected) {
                android.graphics.Color.parseColor("#FF3A57CE")
            } else {
                android.graphics.Color.parseColor("#FF425FD1")
            }
        } else if (calendar.timeInMillis == today.timeInMillis) {
            paint.color = if (isHasConnected) {
                android.graphics.Color.parseColor("#FF3A57CE")
            } else {
                // 绘制今天的边框
                drawRoundRect(
                    color = Color(android.graphics.Color.parseColor("#FFBDCDE6")),
                    topLeft = Offset(x - 13.dp.toPx(), 0f),
                    size = androidx.compose.ui.geometry.Size(26.dp.toPx(), size.height),
                    cornerRadius = androidx.compose.ui.geometry.CornerRadius(13.dp.toPx()),
                    style = androidx.compose.ui.graphics.drawscope.Stroke(width = 1.dp.toPx())
                )
                android.graphics.Color.WHITE
            }
            paint.isFakeBoldText = false
        } else {
            paint.color = if (isHasConnected) {
                android.graphics.Color.parseColor("#FF999999")
            } else {
                android.graphics.Color.WHITE
            }
            paint.isFakeBoldText = false
        }
        
        // 绘制星期文字
        drawContext.canvas.nativeCanvas.drawText(
            week[dayIndex % 7], 
            x, 
            18.dp.toPx(), 
            paint
        )
        
        // 绘制日期数字
        drawContext.canvas.nativeCanvas.drawText(
            calendar.get(Calendar.DATE).toString(),
            x,
            36.dp.toPx(),
            paint
        )
        
        x += itemWidth
        calendar.add(Calendar.DATE, 1)
    }
}

/**
 * 简化版的日报告周历
 */
@Composable
fun DayCalendar(
    calendarState: DayCalendarState,
    isHasConnected: Boolean,
    onClick: (Calendar) -> Unit
) {
    val coroutineScope = rememberCoroutineScope()
    calendarState.isHasConnected = isHasConnected
    
    Column(Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        Text(
            text = SimpleDateFormat(stringResource(id = R.string.dateFormat)).format(calendarState.selectedDay.time),
            fontSize = 20.sp,
            fontWeight = FontWeight.Medium,
            color = if (isHasConnected) Color(0XFF444444) else Color.White
        )
        Row(
            Modifier
                .fillMaxWidth()
                .padding(start = 16.dp, end = 16.dp, top = 10.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.img_report_calender_pre),
                contentDescription = null,
                tint = if (isHasConnected) Color(0XFF999999) else Color(0XFFF7F7F7),
                modifier = Modifier
                    .size(10.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            coroutineScope.launch {
                                if (calendarState.pageIndex > 0) {
                                    calendarState.pageIndex--
                                    calendarState.offset.snapTo(calendarState.offset.value - calendarState.containerWidth)
                                    calendarState.offset.animateTo(0f)
                                }
                            }
                        }
                    }
            )
            
            Box(
                modifier = Modifier
                    .weight(1F)
                    .height(60.dp)
                    .clip(RectangleShape)
                    .onSizeChanged { size ->
                        calendarState.containerWidth = size.width.toFloat()
                    }
                    .pointerInput(Unit) {
                        detectTapGestures { offset ->
                            val calendar = calendarState.today.copy()
                            val dayIndex = (offset.x / calendarState.itemWidth).toInt()
                            val num = calendarState.pageIndex * 7 - calendar.dayOfWeek + dayIndex
                            if (num <= 0) {
                                calendar.add(Calendar.DATE, num)
                                onClick(calendar)
                            }
                        }
                    }
            ) {
                Canvas(
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(Unit) {
                            detectHorizontalDragGestures(
                                onHorizontalDrag = { _: PointerInputChange, dragAmount: Float ->
                                    coroutineScope.launch {
                                        calendarState.offset.snapTo(calendarState.offset.value + dragAmount)
                                    }
                                },
                                onDragEnd = {
                                    coroutineScope.launch {
                                        if (calendarState.offset.value.absoluteValue < minDragWidth) {
                                            calendarState.offset.animateTo(0F)
                                        } else {
                                            if (calendarState.offset.value < 0) {
                                                // 下一页
                                                if (calendarState.pageIndex == 0) {
                                                    calendarState.offset.animateTo(0F)
                                                } else {
                                                    calendarState.pageIndex++
                                                    calendarState.offset.snapTo(calendarState.offset.value + size.width)
                                                    calendarState.offset.animateTo(0F)
                                                }
                                            } else {
                                                // 上一页
                                                calendarState.pageIndex--
                                                calendarState.offset.snapTo(calendarState.offset.value - size.width)
                                                calendarState.offset.animateTo(0F)
                                            }
                                        }
                                    }
                                }
                            )
                        }
                ) {
                    // 绘制前一页（如果存在）
                    if (calendarState.pageIndex > 0) {
                        drawCalendarPage(
                            pageIndex = calendarState.pageIndex - 1,
                            selectedDay = calendarState.selectedDay,
                            today = calendarState.today,
                            isHasConnected = isHasConnected,
                            offsetX = calendarState.offset.value - size.width
                        )
                    }
                    
                    // 绘制当前页
                    drawCalendarPage(
                        pageIndex = calendarState.pageIndex,
                        selectedDay = calendarState.selectedDay,
                        today = calendarState.today,
                        isHasConnected = isHasConnected,
                        offsetX = calendarState.offset.value
                    )
                    
                    // 绘制下一页（如果不是当前周）
                    if (calendarState.pageIndex > 0) {
                        drawCalendarPage(
                            pageIndex = calendarState.pageIndex + 1,
                            selectedDay = calendarState.selectedDay,
                            today = calendarState.today,
                            isHasConnected = isHasConnected,
                            offsetX = calendarState.offset.value + size.width
                        )
                    }
                }
            }
            
            Icon(
                painter = painterResource(id = R.drawable.img_report_calender_next),
                tint = if (isHasConnected) Color(0XFF999999) else Color(0XFFF7F7F7),
                contentDescription = null,
                modifier = Modifier
                    .size(10.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            coroutineScope.launch {
                                if (calendarState.pageIndex == 0) return@launch
                                calendarState.pageIndex++
                                calendarState.offset.snapTo(calendarState.offset.value + calendarState.containerWidth)
                                calendarState.offset.animateTo(0f)
                            }
                        }
                    }
            )
        }
    }
}