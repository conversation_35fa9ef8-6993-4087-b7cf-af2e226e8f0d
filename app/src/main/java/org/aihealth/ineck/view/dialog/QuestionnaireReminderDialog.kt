package org.aihealth.ineck.view.dialog

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.view.button.QuestionnaireEnterButton
import java.util.Locale

@Composable
fun QuestionnaireReminderDialog(
    modifier: Modifier = Modifier,
    visible: Boolean = true,
    onDismiss: () -> Unit = {},
    onConfirm: () -> Unit = {}
) {
    AnimatedVisibility(
        visible = visible
    ) {
        Dialog(
            onDismissRequest = {
                onDismiss()
            },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false,
            )
        ) {
            Column(
                modifier = modifier,
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top
            ) {
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    shape = RoundedCornerShape(size = 12.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White,
                    )
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 4.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(182.dp)
                                .clip(
                                    shape = RoundedCornerShape(
                                        topStart = 12.dp,
                                        topEnd = 12.dp
                                    )
                                ),
                            painter = painterResource(id = R.drawable.img_dialog_bg),
                            contentDescription = "Image",
                            contentScale = ContentScale.Crop
                        )
                        Text(
                            modifier = Modifier.padding(top = 12.dp),
                            text = stringResource(id = R.string.health_questionnaire),
                            style = TextStyle(
                                fontSize = 18.sp,
                                fontWeight = FontWeight(800),
                                color = Color(0xFF333333),
                            )
                        )
                        Text(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 8.dp),
                            text = stringResource(id = R.string.enter_questionnaire_tip),
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF333333),
                            )
                        )
                        QuestionnaireEnterButton(
                            modifier = Modifier
                                .padding(top = 16.dp)
                                .fillMaxWidth(0.8f),
                            onClick = { onConfirm() }
                        )
                        val countDown = remember {
                            mutableIntStateOf(15)
                        }
                        LaunchedEffect(visible) {
                            if (visible) {
                                while (countDown.intValue > 0) {
                                    delay(1000)
                                    countDown.intValue--
                                    if (countDown.intValue == 0) {
                                        onConfirm()
                                    }
                                }
                            }
                        }

                        val annotatedString = if (currentLocale == Locale.CHINESE) {
                            buildAnnotatedString {
                                withStyle(
                                    SpanStyle(
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF4B84F5)
                                    )
                                ) {
                                    append(countDown.intValue.toString())
                                }
                                withStyle(
                                    SpanStyle(
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("s 后自动进入问卷")
                                }
                            }
                        } else {
                            buildAnnotatedString {
                                withStyle(
                                    SpanStyle(
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("Automatically enter the questionnaire in ")
                                }
                                withStyle(
                                    SpanStyle(
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF4B84F5)
                                    )
                                ) {
                                    append(countDown.intValue.toString())
                                }
                                withStyle(
                                    SpanStyle(
                                        fontSize = 10.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                ) {
                                    append("s")
                                }
                            }

                        }
                        Text(
                            modifier = Modifier.padding(vertical = 12.dp),
                            text = annotatedString,
                        )
                    }
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 32.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Image(
                        modifier = Modifier.clickable { onDismiss() },
                        painter = painterResource(id = R.drawable.ic_closed_2),
                        contentDescription = "closed"
                    )
                }

            }
        }
    }
}

@Preview(locale = "en")
@Composable
private fun PreviewDialog() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
    ) {
        QuestionnaireReminderDialog(
            modifier = Modifier.fillMaxWidth(0.7f)
        )
    }
}