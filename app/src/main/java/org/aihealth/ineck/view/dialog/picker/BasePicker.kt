package org.aihealth.ineck.view.dialog.picker

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Composable
fun BasePicker(
    title: String = "",
    modifier: Modifier = Modifier,
    onConfirmClick: () -> Unit,
    onCancelClick: () -> Unit = {},
    content: @Composable () -> Unit
) {
    Column(
        modifier = Modifier.then(modifier)
            .fillMaxWidth()
            .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .pointerInput(Unit) {
                detectTapGestures {  }
            }
    ) {
        Box(
            modifier = Modifier
                .height(50.dp)
                .fillMaxWidth()
                .padding(horizontal = 20.dp)
        ) {
            Text(
                text = stringResource(id = R.string.cancel),
                fontSize = 16.sp,
                color = Color(0XFF333333),
                modifier = Modifier.align(
                    Alignment.CenterStart
                ).pointerInput(Unit) {
                    detectTapGestures { onCancelClick() }
                }
            )
            Text(
                text = stringResource(id = R.string.confirm),
                fontSize = 16.sp,
                color = Color(0XFF333333),
                modifier = Modifier.align(
                    Alignment.CenterEnd
                ).pointerInput(Unit) {
                    detectTapGestures { onConfirmClick() }
                }
            )
            Text(
                text = title,
                fontSize = 16.sp,
                color = Color.Black,
                modifier = Modifier.align(
                    Alignment.Center
                )
            )
        }
        content()
    }
}