package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.Sizes.actionBarHeight
import org.aihealth.ineck.util.finish

/**
 * 通用的编辑页面框架
 * @param title 页面标题（可选，如果为空则不显示标题）
 * @param onSave 保存按钮点击事件
 * @param onCancel 取消/返回按钮点击事件，默认调用 finish()
 * @param saveButtonText 保存按钮文本，默认为 "Done"
 * @param canSave 是否可以保存，控制保存按钮的可点击状态
 * @param content 页面内容
 */
@Composable
fun BaseEditScreen(
    title: String? = null,
    onSave: () -> Unit,
    onCancel: () -> Unit = { finish() },
    saveButtonText: String = stringResource(R.string.done),
    canSave: Boolean = true,
    content: @Composable () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF4F4F4))
            .statusBarsPadding()
    ) {
        // Custom Toolbar
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(actionBarHeight)
                .padding(horizontal = 16.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Back Button
            Icon(
                painter = painterResource(id = R.drawable.img_back),
                contentDescription = null,
                tint = Color(0xff666666),
                modifier = Modifier
                    .size(16.dp)
                    .clickable { onCancel() }
            )
            
            // Title (if provided)
            title?.let {
                Text(
                    text = it,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
            }
            
            // Save/Done Button
            Text(
                text = saveButtonText,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                color = if (canSave) MaterialTheme.colorScheme.primary else Color(0xFF999999),
                modifier = Modifier
                    .clickable(enabled = canSave) {
                        if (canSave) {
                            onSave()
                        }
                    }
            )
        }
        
        // Content
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            content()
        }
    }
}