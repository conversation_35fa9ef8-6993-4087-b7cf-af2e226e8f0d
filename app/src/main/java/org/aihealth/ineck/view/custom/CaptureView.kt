package org.aihealth.ineck.view.custom

import android.graphics.Bitmap
import android.graphics.Canvas
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.runtime.Composable
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.viewinterop.AndroidView

class CaptureController {
    internal var _view: View ?= null
    internal val view: View
        get() = _view!!
    fun getBitmap(): Bitmap{
        val bitmap: Bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        view.draw(canvas)
        return bitmap
    }
}

@Composable
fun CaptureView(
    captureController: CaptureController,
    content: @Composable () -> Unit
){
    AndroidView(factory = {
        FrameLayout(it).apply {
            val composeView = ComposeView(it).apply {
                setContent {
                    content()
                }
            }
            addView(
                composeView, ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
            )
            captureController._view = this
        }
    })
}