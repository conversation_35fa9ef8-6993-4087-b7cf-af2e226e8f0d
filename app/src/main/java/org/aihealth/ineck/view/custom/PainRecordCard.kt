package org.aihealth.ineck.view.custom

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.accompanist.flowlayout.FlowRow
import org.aihealth.ineck.R
import org.aihealth.ineck.model.angles.BackNeuralHistoryItem
import org.aihealth.ineck.model.angles.BackPainHistoryItem
import org.aihealth.ineck.model.angles.NeckNeuralHistoryItem
import org.aihealth.ineck.model.angles.NeckPainHistoryItem
import org.aihealth.ineck.util.TimeUtil

/**
 *  颈部疼痛数据历史记录条目单项组件
 *  @param  modifier    修饰符参数
 *  @param  neckPain    颈部疼痛数据单元
 *  @param  onClickEvent    颈部疼痛历史记录条目
 */
@Composable
fun NeckPainRecordItemCard(
    modifier: Modifier = Modifier
        .fillMaxWidth()
        .defaultMinSize(minHeight = 80.dp),
    neckPain: NeckPainHistoryItem,
    onClickEvent: () -> Unit,
) {
    Column(
        modifier = modifier
            .clickable { onClickEvent() }
            .background(color = Color.White),
        verticalArrangement = Arrangement.Center
    ) {
        Row(modifier = Modifier.padding(vertical = 4.dp)) {
            Text(
                text = stringResource(id = R.string.pain_record),
                fontSize = 16.sp,
                color = Color.Black,
                textAlign = TextAlign.Start,
                modifier = Modifier
                    .padding(start = 12.dp)
                    .weight(1f)
            )
            Surface(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(end = 8.dp)
                    .clip(RoundedCornerShape(10.dp)),
                color = Color.Black.copy(alpha = .05f)
            ) {
                Text(
                    text = TimeUtil.timestampToDate(neckPain.datetime),
                    fontSize = 14.sp,
                    color = Color.Black.copy(alpha = .8f),
                    textAlign = TextAlign.Start,
                    modifier = Modifier
                        .padding(horizontal = 8.dp, vertical = 1.dp)
                )
            }
        }
        /* 各个颈部疼痛数据Chip */
        FlowRow(modifier = Modifier.padding(horizontal = 12.dp)) {
            /* 颈部疼痛数据概览 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.pain_record_neck_pain) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = neckPain.neck.toString(),
                    color = when(neckPain.neck) {
                        in 0 until 2 -> { Color(0xFF43B749) }
                        in 2 until 4 -> { Color(0xFFf3AE28) }
                        in 4 until 6 -> { Color(0xFFF28A29) }
                        in 6 until 8 -> { Color(0xFFEE652A) }
                        in 8 until 10 -> { Color(0xFFE13C32) }
                        else -> { Color.Black.copy(alpha = .8f) }
                    },
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = when(neckPain.neck) {
                        in 0 until 2 -> { painterResource(id = R.drawable.img_pain_record_pain1) }
                        in 2 until 4 -> { painterResource(id = R.drawable.img_pain_record_pain2) }
                        in 4 until 6 -> { painterResource(id = R.drawable.img_pain_record_pain3) }
                        in 6 until 8 -> { painterResource(id = R.drawable.img_pain_record_pain4) }
                        in 8 until 10 -> { painterResource(id = R.drawable.img_pain_record_pain5) }
                        else -> { painterResource(id = R.drawable.icon_face_without_emotion_normal) }
                     },
                    contentDescription = when(neckPain.neck) {
                        in 0..2 -> { stringResource(id = R.string.pain_record_no_pain) }
                        in 3..7 -> { stringResource(id = R.string.pain_record_moderate) }
                        in 8..10 -> { stringResource(id = R.string.pain_record_unbearable_pain) }
                        else -> { "N/A" }
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
            /* 手部疼痛概览 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.pain_record_hand_pain) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = neckPain.hand.toString(),
                    color = when(neckPain.hand) {
                        in 0 until 2 -> { Color(0xFF43B749) }
                        in 2 until 4 -> { Color(0xFFf3AE28) }
                        in 4 until 6 -> { Color(0xFFF28A29) }
                        in 6 until 8 -> { Color(0xFFEE652A) }
                        in 8 until 10 -> { Color(0xFFE13C32) }
                        else -> { Color.Black.copy(alpha = .8f) }
                    },
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = when(neckPain.hand) {
                        in 0 until 2 -> { painterResource(id = R.drawable.img_pain_record_pain1) }
                        in 2 until 4 -> { painterResource(id = R.drawable.img_pain_record_pain2) }
                        in 4 until 6 -> { painterResource(id = R.drawable.img_pain_record_pain3) }
                        in 6 until 8 -> { painterResource(id = R.drawable.img_pain_record_pain4) }
                        in 8 until 10 -> { painterResource(id = R.drawable.img_pain_record_pain5) }
                        else -> { painterResource(id = R.drawable.icon_face_without_emotion_normal) }
                    },
                    contentDescription = when(neckPain.hand) {
                        in 0..2 -> { stringResource(id = R.string.pain_record_no_pain) }
                        in 3..7 -> { stringResource(id = R.string.pain_record_moderate) }
                        in 8..10 -> { stringResource(id = R.string.pain_record_unbearable_pain) }
                        else -> { "N/A" }
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
        }
        Spacer(modifier = Modifier.height(4.dp))
        HorizontalDivider(modifier = Modifier.fillMaxWidth())
    }
}


/**
 *  颈部神经数据历史记录条目单项组件
 *  @param  modifier    修饰符参数
 *  @param  neckNeural    颈部疼痛数据单元
 *  @param  onClickEvent    颈部疼痛历史记录条目
 */
@Composable
fun NeckNeuralRecordItemCard(
    modifier: Modifier = Modifier
        .fillMaxWidth()
        .defaultMinSize(minHeight = 80.dp),
    neckNeural: NeckNeuralHistoryItem,
    onClickEvent: () -> Unit,
) {
    Column(
        modifier = modifier
            .clickable { onClickEvent() }
            .background(color = Color.White),
        verticalArrangement = Arrangement.Center
    ) {
        Row(modifier = Modifier.padding(vertical = 4.dp)) {
            Text(
                text = stringResource(id = R.string.pain_record),
                fontSize = 16.sp,
                color = Color.Black,
                textAlign = TextAlign.Start,
                modifier = Modifier
                    .padding(start = 12.dp)
                    .weight(1f)
            )
            Surface(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(end = 8.dp)
                    .clip(RoundedCornerShape(10.dp)),
                color = Color.Black.copy(alpha = .05f)
            ) {
                Text(
                    text = TimeUtil.timestampToDate(neckNeural.datetime),
                    fontSize = 14.sp,
                    color = Color.Black.copy(alpha = .8f),
                    textAlign = TextAlign.Start,
                    modifier = Modifier
                        .padding(horizontal = 8.dp, vertical = 1.dp)
                )
            }
        }
        /* 各个颈部疼痛数据Chip */
        FlowRow(modifier = Modifier.padding(horizontal = 12.dp)) {
            /* 肌肉力量 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.muscle_strength) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = neckNeural.muscle.toString(),
                    color = when (neckNeural.muscle) {
                        5 -> {
                            Color(0xFF43B749)
                        }

                        4 -> {
                            Color(0xFFf3AE28)
                        }

                        3 -> {
                            Color(0xFFF28A29)
                        }

                        2 -> {
                            Color(0xFFEE652A)
                        }

                        in (1 downTo 0) -> {
                            Color(0xFFE13C32)
                        }

                        else -> {
                            Color.Black.copy(alpha = .8f)
                        }
                    },
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = when (neckNeural.muscle) {
                        5 -> {
                            painterResource(id = R.drawable.img_pain_record_pain1)
                        }

                        4 -> {
                            painterResource(id = R.drawable.img_pain_record_pain2)
                        }

                        3 -> {
                            painterResource(id = R.drawable.img_pain_record_pain3)
                        }

                        2 -> {
                            painterResource(id = R.drawable.img_pain_record_pain4)
                        }

                        in 0..1 -> {
                            painterResource(id = R.drawable.img_pain_record_pain5)
                        }

                        else -> {
                            painterResource(id = R.drawable.icon_face_without_emotion_normal)
                        }
                    },
                    contentDescription = when (neckNeural.muscle) {
                        5 -> {
                            stringResource(id = R.string.muscle_strength_description_5)
                        }

                        4 -> {
                            stringResource(id = R.string.muscle_strength_description_4)
                        }

                        3 -> {
                            stringResource(id = R.string.muscle_strength_description_3)
                        }

                        2 -> {
                            stringResource(id = R.string.muscle_strength_description_2)
                        }

                        1 -> {
                            stringResource(id = R.string.muscle_strength_description_1)
                        }

                        0 -> {
                            stringResource(id = R.string.muscle_strength_description_0)
                        }

                        else -> {
                            "N/A"
                        }
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
            /* 是否麻木 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.numbness_tingling) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(end = 2.dp, start = 4.dp)
                )
                /* 具体数值 */
                Text(
                    text = stringArrayResource(id = R.array.pain_record_numbness_status).let {
                        if (neckNeural.numb == 1) it[0] else it[1]
                    },
                    color = if (neckNeural.numb == 0) Color(0xFF43B749) else Color(0xFFE13C32),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = painterResource(id = if (neckNeural.numb == 0) R.drawable.img_pain_record_pain1 else R.drawable.img_pain_record_pain5),
                    contentDescription = stringArrayResource(id = R.array.pain_record_numbness_status).let {
                        if (neckNeural.numb == 1) it[0] else it[1]
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
            /* 是否能保持平衡 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.balance) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = stringArrayResource(id = R.array.pain_record_balance_status).let {
                        if (neckNeural.balance == 1) it[0] else it[1]
                    },
                    color = if (neckNeural.balance == 1) Color(0xFF43B749) else Color(0xFFE13C32),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = painterResource(id = if (neckNeural.balance == 1) R.drawable.img_pain_record_pain1 else R.drawable.img_pain_record_pain5),
                    contentDescription = stringArrayResource(id = R.array.pain_record_balance_status).let {
                        if (neckNeural.balance == 1) it[0] else it[1]
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
        }
        Spacer(modifier = Modifier.height(4.dp))
        HorizontalDivider(modifier = Modifier.fillMaxWidth())
    }
}

/**
 *  背部疼痛数据历史记录条目单项组件
 *  @param  modifier    修饰符参数
 *  @param  backPain    颈部疼痛数据单元
 *  @param  onClickEvent    颈部疼痛历史记录条目
 */
@Composable
fun BackPainRecordItemCard(
    modifier: Modifier = Modifier
        .fillMaxWidth()
        .defaultMinSize(minHeight = 80.dp),
    backPain: BackPainHistoryItem,
    onClickEvent: () -> Unit,
) {
    Column(
        modifier = modifier
            .clickable { onClickEvent() }
            .background(color = Color.White),
        verticalArrangement = Arrangement.Center
    ) {
        Row(modifier = Modifier.padding(vertical = 4.dp)) {
            Text(
                text = stringResource(id = R.string.pain_record),
                fontSize = 16.sp,
                color = Color.Black,
                textAlign = TextAlign.Start,
                modifier = Modifier
                    .padding(start = 12.dp)
                    .weight(1f)
            )
            Surface(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(end = 8.dp)
                    .clip(RoundedCornerShape(10.dp)),
                color = Color.Black.copy(alpha = .05f)
            ) {
                Text(
                    text = TimeUtil.timestampToDate(backPain.datetime),
                    fontSize = 14.sp,
                    color = Color.Black.copy(alpha = .8f),
                    textAlign = TextAlign.Start,
                    modifier = Modifier
                        .padding(horizontal = 8.dp, vertical = 1.dp)
                )
            }
        }
        /* 各个颈部疼痛数据Chip */
        FlowRow(modifier = Modifier.padding(horizontal = 12.dp)) {
            /* 背部疼痛数据概览 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.pain_record_back_pain) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = backPain.back.toString(),
                    color = when (backPain.back) {
                        in 0 until 2 -> {
                            Color(0xFF43B749)
                        }

                        in 2 until 4 -> {
                            Color(0xFFf3AE28)
                        }

                        in 4 until 6 -> {
                            Color(0xFFF28A29)
                        }

                        in 6 until 8 -> {
                            Color(0xFFEE652A)
                        }

                        in 8 until 10 -> {
                            Color(0xFFE13C32)
                        }

                        else -> {
                            Color.Black.copy(alpha = .8f)
                        }
                    },
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = when (backPain.back) {
                        in 0 until 2 -> {
                            painterResource(id = R.drawable.img_pain_record_pain1)
                        }

                        in 2 until 4 -> {
                            painterResource(id = R.drawable.img_pain_record_pain2)
                        }

                        in 4 until 6 -> {
                            painterResource(id = R.drawable.img_pain_record_pain3)
                        }

                        in 6 until 8 -> {
                            painterResource(id = R.drawable.img_pain_record_pain4)
                        }

                        in 8 until 10 -> {
                            painterResource(id = R.drawable.img_pain_record_pain5)
                        }

                        else -> {
                            painterResource(id = R.drawable.icon_face_without_emotion_normal)
                        }
                    },
                    contentDescription = when (backPain.back) {
                        in 0..2 -> {
                            stringResource(id = R.string.pain_record_no_pain)
                        }

                        in 3..7 -> {
                            stringResource(id = R.string.pain_record_moderate)
                        }

                        in 8..10 -> {
                            stringResource(id = R.string.pain_record_unbearable_pain)
                        }

                        else -> {
                            "N/A"
                        }
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
            /* 腿部疼痛概览 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.pain_record_leg_pain) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = backPain.leg.toString(),
                    color = when (backPain.leg) {
                        in 0 until 2 -> {
                            Color(0xFF43B749)
                        }

                        in 2 until 4 -> {
                            Color(0xFFf3AE28)
                        }

                        in 4 until 6 -> {
                            Color(0xFFF28A29)
                        }

                        in 6 until 8 -> {
                            Color(0xFFEE652A)
                        }

                        in 8 until 10 -> {
                            Color(0xFFE13C32)
                        }

                        else -> {
                            Color.Black.copy(alpha = .8f)
                        }
                    },
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = when (backPain.leg) {
                        in 0 until 2 -> {
                            painterResource(id = R.drawable.img_pain_record_pain1)
                        }

                        in 2 until 4 -> {
                            painterResource(id = R.drawable.img_pain_record_pain2)
                        }

                        in 4 until 6 -> {
                            painterResource(id = R.drawable.img_pain_record_pain3)
                        }

                        in 6 until 8 -> {
                            painterResource(id = R.drawable.img_pain_record_pain4)
                        }

                        in 8 until 10 -> {
                            painterResource(id = R.drawable.img_pain_record_pain5)
                        }

                        else -> {
                            painterResource(id = R.drawable.icon_face_without_emotion_normal)
                        }
                    },
                    contentDescription = when (backPain.leg) {
                        in 0..2 -> {
                            stringResource(id = R.string.pain_record_no_pain)
                        }

                        in 3..7 -> {
                            stringResource(id = R.string.pain_record_moderate)
                        }

                        in 8..10 -> {
                            stringResource(id = R.string.pain_record_unbearable_pain)
                        }

                        else -> {
                            "N/A"
                        }
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
        }
        Spacer(modifier = Modifier.height(4.dp))
        HorizontalDivider(modifier = Modifier.fillMaxWidth())
    }
}


/**
 *  颈部神经数据历史记录条目单项组件
 *  @param  modifier    修饰符参数
 *  @param  backNeural    颈部疼痛数据单元
 *  @param  onClickEvent    颈部疼痛历史记录条目
 */
@Composable
fun BackNeuralRecordItemCard(
    modifier: Modifier = Modifier
        .fillMaxWidth()
        .defaultMinSize(minHeight = 80.dp),
    backNeural: BackNeuralHistoryItem,
    onClickEvent: () -> Unit,
) {
    Column(
        modifier = modifier
            .clickable { onClickEvent() }
            .background(color = Color.White),
        verticalArrangement = Arrangement.Center
    ) {
        Row(modifier = Modifier.padding(vertical = 4.dp)) {
            Text(
                text = stringResource(id = R.string.pain_record),
                fontSize = 16.sp,
                color = Color.Black,
                textAlign = TextAlign.Start,
                modifier = Modifier
                    .padding(start = 12.dp)
                    .weight(1f)
            )
            Surface(
                modifier = Modifier
                    .wrapContentSize()
                    .padding(end = 8.dp)
                    .clip(RoundedCornerShape(10.dp)),
                color = Color.Black.copy(alpha = .05f)
            ) {
                Text(
                    text = TimeUtil.timestampToDate(backNeural.datetime),
                    fontSize = 14.sp,
                    color = Color.Black.copy(alpha = .8f),
                    textAlign = TextAlign.Start,
                    modifier = Modifier
                        .padding(horizontal = 8.dp, vertical = 1.dp)
                )
            }
        }
        /* 各个颈部疼痛数据Chip */
        FlowRow(modifier = Modifier.padding(horizontal = 12.dp)) {
            /* 肌肉力量 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.muscle_strength) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = backNeural.muscle.toString(),
                    color = when (backNeural.muscle) {
                        5 -> { Color(0xFF43B749) }
                        4 -> { Color(0xFFf3AE28) }
                        3 -> { Color(0xFFF28A29) }
                        2 -> { Color(0xFFEE652A) }
                        in (1 downTo 0) -> { Color(0xFFE13C32) }
                        else -> { Color.Black.copy(alpha = .8f) }
                    },
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = when (backNeural.muscle) {
                        5 -> { painterResource(id = R.drawable.img_pain_record_pain1) }
                        4 -> { painterResource(id = R.drawable.img_pain_record_pain2) }
                        3 -> { painterResource(id = R.drawable.img_pain_record_pain3) }
                        2 -> { painterResource(id = R.drawable.img_pain_record_pain4) }
                        in 0..1 -> { painterResource(id = R.drawable.img_pain_record_pain5) }
                        else -> { painterResource(id = R.drawable.icon_face_without_emotion_normal) }
                    },
                    contentDescription = when (backNeural.muscle) {
                        5 -> { stringResource(id = R.string.muscle_strength_description_5) }
                        4 -> { stringResource(id = R.string.muscle_strength_description_4) }
                        3 -> { stringResource(id = R.string.muscle_strength_description_3) }
                        2 -> { stringResource(id = R.string.muscle_strength_description_2) }
                        1 -> { stringResource(id = R.string.muscle_strength_description_1) }
                        0 -> { stringResource(id = R.string.muscle_strength_description_0) }
                        else -> { "N/A" }
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
            /* 是否麻木 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.numbness_tingling) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(end = 2.dp, start = 4.dp)
                )
                /* 具体数值 */
                Text(
                    text = stringArrayResource(id = R.array.pain_record_numbness_status).let {
                        if (backNeural.numb == 1) it[0] else it[1]
                    },
                    color = if (backNeural.numb == 0) Color(0xFF43B749) else Color(0xFFE13C32),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = painterResource(id = if (backNeural.numb == 0) R.drawable.img_pain_record_pain1 else R.drawable.img_pain_record_pain5),
                    contentDescription = stringArrayResource(id = R.array.pain_record_numbness_status).let {
                        if (backNeural.numb == 1) it[0] else it[1]
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
            /* 是否能保持平衡 */
            Row(
                modifier = Modifier
                    .padding(top = 2.dp, end = 10.dp, bottom = 2.dp)
                    .border(
                        width = 1.dp,
                        color = Color.Black.copy(alpha = .3f),
                        shape = RoundedCornerShape(6.dp)
                    ),
                verticalAlignment = Alignment.CenterVertically
            ) {
                /* 项目名 */
                Text(
                    text = stringResource(id = R.string.balance) + ": ",
                    color = Color.Black.copy(alpha = .8f),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(start = 4.dp, end = 2.dp)
                )
                /* 具体数值 */
                Text(
                    text = stringArrayResource(id = R.array.pain_record_balance_status).let {
                        if (backNeural.balance == 1) it[0] else it[1]
                    },
                    color = if (backNeural.balance == 1) Color(0xFF43B749) else Color(0xFFE13C32),
                    fontSize = 12.sp,
                    modifier = Modifier.padding(horizontal = 2.dp)
                )
                Image(
                    painter = painterResource(id = if (backNeural.balance == 1) R.drawable.img_pain_record_pain1 else R.drawable.img_pain_record_pain5),
                    contentDescription = stringArrayResource(id = R.array.pain_record_balance_status).let {
                        if (backNeural.balance == 1) it[0] else it[1]
                    },
                    modifier = Modifier
                        .size(16.dp)
                        .padding(start = 2.dp, end = 4.dp)
                        .background(Color.Transparent),
                )
            }
        }
        Spacer(modifier = Modifier.height(4.dp))
        HorizontalDivider(modifier = Modifier.fillMaxWidth())
    }
}

/**
 *  疼痛类型
 */
/**
 *  疼痛类型
 */
enum class PainTypeState {
    NECK_PAIN,
    NECK_NEURAL,
    BACK_PAIN,
    BACK_NEURAL
}
