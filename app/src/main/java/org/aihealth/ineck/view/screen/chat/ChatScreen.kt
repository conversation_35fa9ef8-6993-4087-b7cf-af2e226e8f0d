package org.aihealth.ineck.view.screen.chat

import android.widget.Toast
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.SoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.ViewModelProvider
import coil.compose.AsyncImage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.chat.ChatMessageEntity
import org.aihealth.ineck.model.chat.ChatSession
import org.aihealth.ineck.model.chat.ChatSmsEntity
import org.aihealth.ineck.model.chat.FunctionButton
import org.aihealth.ineck.model.chat.MediaType
import org.aihealth.ineck.model.chat.MessageType
import org.aihealth.ineck.model.chat.ReportContent
import org.aihealth.ineck.model.chat.getFollowContent
import org.aihealth.ineck.model.chat.getMeetContent
import org.aihealth.ineck.model.chat.getPhoneRequestContent
import org.aihealth.ineck.model.chat.getReportContent
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.ChatViewModel
import org.aihealth.ineck.viewmodel.user

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChatScreen() {

    val viewModel =
        ViewModelProvider(activity)[ChatViewModel::class.java]
    val scrollState = rememberLazyListState()
    val context = LocalContext.current

    val textState = remember { mutableStateOf(TextFieldValue()) }
    val scope = rememberCoroutineScope()
    val keyboardController = LocalSoftwareKeyboardController.current
    val focusRequester = remember { FocusRequester() }

    // 是否显示功能按钮区域
    val showFunctionButtons = remember { mutableStateOf(false) }

    // 从ViewModel获取数据
    val currentUserName = viewModel.currentUserName.collectAsState().value
    val currentUserId = viewModel.currentUserId.collectAsState().value
    val chatMessageList = viewModel.chatMessagesList.collectAsState()
    // 获取Socket连接状态
    val isSocketConnected = viewModel.isConnected.collectAsState()

    // 消息是否正在加载中
    val isMessagesLoading = remember { mutableStateOf(true) }

    // 当消息列表变化时，如果不再为空，则认为消息已加载完成
    LaunchedEffect(chatMessageList.value.size) {
        // 如果消息列表已经加载完成（无论是否有数据），都应该隐藏加载状态
        if (viewModel.isMessagesLoaded.value) {
            isMessagesLoading.value = false
        }
    }

    // 添加一个新的LaunchedEffect，专门用于监听isMessagesLoaded状态
    LaunchedEffect(viewModel.isMessagesLoaded.collectAsState().value) {
        if (viewModel.isMessagesLoaded.value) {
            isMessagesLoading.value = false
        }
    }

    // 检测是否在列表底部
    val isAtBottom = remember {
        derivedStateOf {
            scrollState.firstVisibleItemIndex == 0 ||
                    (scrollState.firstVisibleItemIndex <= 1 && scrollState.firstVisibleItemScrollOffset < 50)
        }
    }

    // 当有新消息且用户在底部时，自动滚动到底部
    LaunchedEffect(Unit) {
        snapshotFlow { chatMessageList.value.size }.collect {
            LogUtil.d("socket get new message")
            if (isAtBottom.value && chatMessageList.value.isNotEmpty()) {
                LogUtil.d("socket get new message - scroll to bottom")
                scrollState.scrollToItem(0)
            }
        }

    }

    val messagesByDateList = remember {
        derivedStateOf {
            chatMessageList.value.sortedByDescending { it.dateTime }.groupBy { message ->
                TimeUtil.milliTimestampToDate(message.dateTime, "yyyy/MM/dd")
            }
        }
    }

    DisposableEffect(Unit) {
        onDispose {
            if (currentUserId != null) {
                viewModel.markMessagesAsRead()
                // 页面销毁时，调用clearCurrentChat标记消息为已读并断开Socket连接
                viewModel.clearCurrentChat()
                LogUtil.d("Chat screen: Socket connection cleared on dispose")
            }
        }
    }

    // 如果没有设置当前聊天用户，什么都不显示
    if (currentUserId == null || currentUserName == null) {
        return
    }
    // 检测键盘状态
    val ime = WindowInsets.Companion.ime
    val isKeyboardVisible = ime.getBottom(LocalDensity.current) > 0

    // 当键盘状态改变时处理
    LaunchedEffect(isKeyboardVisible) {
        if (isKeyboardVisible) {
            // 键盘显示时，确保输入框获得焦点，并隐藏功能按钮区（避免同时显示键盘和功能区）
            focusRequester.requestFocus()
            showFunctionButtons.value = false
        }
    }

    // 获取当前会话
    val currentSession = viewModel.getCurrentSession() ?: return

    // 当屏幕进入时，确保初始化Socket连接
    LaunchedEffect(key1 = Unit) {
        viewModel.initSocketIfNeeded()
        // 强制重新加入聊天室
        viewModel.joinRoomIfConnected()
    }

    // 在ChatScreen函数内部，现有状态变量区域添加这些状态变量
    var showPhoneNumberDialog by remember { mutableStateOf(false) }
    var currentMessage by remember { mutableStateOf<ChatMessageEntity?>(null) }
    var phoneNumber by remember { mutableStateOf("") }
    


    BasePageView(
        title = currentUserName,
        showBackIcon = true
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clickable (
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ){ 
                    keyboardController?.hide() 
                    // 点击背景区域时同时关闭功能区
                    if (showFunctionButtons.value) {
                        showFunctionButtons.value = false
                    }
                }
            // 确保内容能够在IME弹出时调整
        ) {
            // 聊天消息列表
            LazyColumn(
                state = scrollState,
                modifier = Modifier
                    .padding(
                        bottom = if (showFunctionButtons.value) 225.dp else 65.dp
                    )
                    .matchParentSize()
                    .imePadding(),
                contentPadding = PaddingValues(horizontal = 8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                reverseLayout = true
            ) {
                messagesByDateList.value.forEach { (date, messages) ->
                    items(
                        items = messages,
                        key = { message ->
                            "${message.dateTime}_${message.messageId}"
                        },
                    ) { message ->
                        val isPhoneRequest = message.getPhoneRequestContent()!=null

                        MessageRow(
                            modifier = Modifier.animateItem(),
                            message = if (isPhoneRequest)message.copy(messageType = MessageType.None) else message,
                            providerAvatar = currentSession.avatar,
                            patientAvatar = user.photo
                        ) {
                            when (message.mediaType) {
                                MediaType.TEXT -> {
                                    TextRequestCard(
                                        message = message,
                                    )
                                }
                                MediaType.FOLLOW -> {
                                    FollowRequestCard(
                                        message = message,
                                        onAccept = {
                                            LogUtil.d("socket accept follow")
                                            viewModel.acceptFollow(message)
                                        },
                                        onReject = {
                                            viewModel.rejectFollow(message)
                                        }
                                    )
                                }
                                MediaType.MEETING -> {
                                    MeetRequestCard(
                                        message = message,
                                        onAccept = {
                                            viewModel.acceptMeet(message)
                                        },
                                        onReject = {
                                            viewModel.rejectMeet(message)
                                        }
                                    )
                                }
                                MediaType.REPORT -> {
                                    ReportRequestCard(
                                        message = message,
                                    )
                                }
                                MediaType.PHONE_REQUEST -> {
                                    PhoneRequestContent(
                                        message = message,
                                        sendPhoneNumber = {
                                            currentMessage = message
                                            phoneNumber = "" // 重置phoneNumber
                                            showPhoneNumberDialog = true
                                        }
                                    )
                                }
                                MediaType.CALL_DURATION -> {
                                    CallDurationCard(
                                        message = message,
                                        callbackOnClick = {
                                            // Handle "Call Again" button click
                                            currentSession?.phone?.let { phone ->
                                                if (phone.isNotEmpty()) {
                                                    viewModel.makePhoneCall(phone)
                                                } else {
                                                    DialogUtil.showToast(context.getString(R.string.no_phone))
                                                }
                                            }
                                        }
                                    )
                                }
                                else ->{}

                            }
                        }

                    }
                    // 然后添加日期标题 - 由于reverseLayout=true，这会显示在消息的上方
                    item(key = date) {
                        DateHeader(date)
                    }
                }
            }

            // 消息加载指示器 - 只在Socket已连接但消息为空时显示
            AnimatedVisibility(
                visible = isSocketConnected.value && isMessagesLoading.value,
                enter = fadeIn(),
                exit = fadeOut(),
                modifier = Modifier
                    .align(Alignment.Center)
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                    modifier = Modifier.padding(16.dp)
                ) {
                    // 使用一个简单的循环旋转动画作为加载指示器
                    val rotation = remember { androidx.compose.animation.core.Animatable(0f) }
                    LaunchedEffect(Unit) {
                        rotation.animateTo(
                            targetValue = 360f,
                            animationSpec = infiniteRepeatable(
                                animation = tween(1000, easing = LinearEasing),
                                repeatMode = RepeatMode.Restart
                            )
                        )
                    }

                    Icon(
                        painter = painterResource(id = R.drawable.img_loading),
                        contentDescription = "Loading",
                        tint = Color(0xFF1B6BFF),
                        modifier = Modifier
                            .size(40.dp)
                            .rotate(rotation.value)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = stringResource(R.string.load_message),
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal
                    )
                }
            }

            // Socket连接状态指示器（当断开连接时显示）
            AnimatedVisibility(
                visible = !isSocketConnected.value,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically(),
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 4.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFFFEEEE))
                        .padding(vertical = 4.dp, horizontal = 16.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(R.string.socket_disconnected),
                        color = Color(0xFFE53935),
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            // 输入区域容器，确保它始终显示在底部且不被键盘遮挡
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    // 使输入框始终保持在键盘上方
                    .navigationBarsPadding()
                    .imePadding(),
            ) {
                ChatInputArea(
                    textState = textState,
                    showFunctionButtons = showFunctionButtons,
                    keyboardController = keyboardController,
                    focusRequester = focusRequester,
                    scope = scope,
                    viewModel = viewModel,
                    scrollState = scrollState,
                )
            }
        }

        // 手机号码请求对话框
        PhoneNumberRequestDialog(
            visible = showPhoneNumberDialog,
            phoneNumber = phoneNumber,
            onPhoneNumberChange = { phoneNumber = it },
            onDismiss = { showPhoneNumberDialog = false },
            onRefuse = {
                showPhoneNumberDialog = false
                currentMessage?.let { message ->
                    viewModel.rejectPhoneRequest(message)
                }
            },
            onSend = {
//                viewModel.setMobileNumber(phoneNumber)
                showPhoneNumberDialog = false
                currentMessage?.let { message ->
                    if (phoneNumber.isNotEmpty()) {
                        viewModel.acceptPhoneRequest(message,phoneNumber)
                    }
                }
            }
        )


    }
}

/**
 * 聊天输入区域组件
 */
@Composable
fun ChatInputArea(
    textState: MutableState<TextFieldValue>,
    showFunctionButtons: MutableState<Boolean>,
    keyboardController: SoftwareKeyboardController?,
    focusRequester: FocusRequester,
    scope: CoroutineScope,
    viewModel: ChatViewModel,
    scrollState: LazyListState,
) {
    val context = LocalContext.current
    val chatSession = viewModel.chatSession.collectAsState().value

    // 根据功能按钮状态计算功能区域高度
    val functionAreaHeight = if (showFunctionButtons.value) 160.dp else 0.dp

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(Color.White)
            .animateContentSize(
                animationSpec = spring(
                    dampingRatio = Spring.DampingRatioMediumBouncy,
                    stiffness = Spring.StiffnessMedium
                )
            )
    ) {
        AIHDivider()

        // 主输入行
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(
                onClick = {
                    // 点击More按钮时隐藏键盘并切换功能区显示状态
                    keyboardController?.hide()
                    showFunctionButtons.value = !showFunctionButtons.value
                    LogUtil.d("ChatScreen - More button clicked, function buttons: ${showFunctionButtons.value}")
                },
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
            ) {
                Icon(
                    painterResource(R.drawable.ic_chat_more),
                    contentDescription = "More functions",
                    tint = Color(0xff1B6BFF),
                    modifier = Modifier.size(20.dp)
                )
            }
            Spacer(modifier = Modifier.width(4.dp))

            // 输入框
            Box(
                modifier = Modifier
                    .weight(1f)
                    .clickable(
                        interactionSource = remember { MutableInteractionSource() },
                        indication = null
                    ) {
                        // 点击输入框时请求焦点并隐藏功能按钮
                        focusRequester.requestFocus()
                        keyboardController?.show()
                        showFunctionButtons.value = false
                    }
                    .clip(RoundedCornerShape(20.dp))
                    .background(Color(0xFFF5F5F5))
                    .padding(horizontal = 16.dp, vertical = 8.dp)
            ) {
                BasicTextField(
                    value = textState.value,
                    onValueChange = { newValue ->
                        textState.value = newValue
                    },
                    textStyle = TextStyle(
                        fontSize = 14.sp,
                        color = Color.Black
                    ),
                    cursorBrush = SolidColor(Color(0xFF1B6BFF)),
                    modifier = Modifier
                        .fillMaxWidth()
                        .wrapContentHeight()
                        .focusRequester(focusRequester)
                )

                if (textState.value.text.isEmpty()) {
                    Text(
                        text = stringResource(R.string.input_message),
                        fontSize = 14.sp,
                        color = Color.Gray
                    )
                }
            }
            Spacer(modifier = Modifier.width(4.dp))
            // 发送按钮
            Box(
                modifier = Modifier
                    .size(40.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF1B6BFF))
                    .clickable {
                        // 处理发送消息
                        val message = textState.value.text.trim()
                        if (message.isNotEmpty()) {
                            // 发送消息
                            viewModel.sendMessage(message) {
                                // 成功发送后清空输入框
                                textState.value = TextFieldValue("")
                                // 滚动到顶部 (最新消息处)
                                scope.launch {
                                    scrollState.animateScrollToItem(0)
                                }
                            }
                        }else {
                            DialogUtil.showToast(context.getString(R.string.input_message))
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_send),
                    contentDescription = "Send",
                    tint = Color.White,
                    modifier = Modifier.size(20.dp)
                )
            }

        }

        // 功能按钮区域 - 仅在showFunctionButtons为true时显示
        AnimatedVisibility(
            visible = showFunctionButtons.value,
            enter = expandVertically() + fadeIn(),
            exit = shrinkVertically() + fadeOut()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(functionAreaHeight) // 使用父组件定义的functionAreaHeight
            ) {
                FunctionsButtonsGrid(
                    onFunctionClick = { functionButton ->
                        when (functionButton.name) {
                            R.string.phone -> {
                                // 处理电话功能
                                LogUtil.d("触发电话功能")
                                // 使用新的makePhoneCall方法直接拨打电话并跟踪通话时长
                                if (chatSession?.phone?.isNotEmpty() == true) {
                                    viewModel.makePhoneCall(chatSession.phone)
                                } else {
                                    // 如果没有电话号码，可以给用户一个提示
                                    Toast.makeText(
                                        context,
                                        context.getString(R.string.no_phone),
                                        Toast.LENGTH_SHORT
                                    ).show()
                                }
                            }

                            R.string.report -> {
                                // 处理报告功能
                                LogUtil.d("触发报告功能")
                                // 这里可以跳转到报告页面，例如：
                                // navController.navigate("report_screen")
                                viewModel.sendReport(
                                    ReportContent(
                                        userId = user.uuid,
                                        avatar = user.photo,
                                        name = user.name,
                                    ),
                                    onComplete = {}
                                )
                            }

                            else -> {
                                // 处理其他功能
                                LogUtil.d("触发其他功能: ${functionButton.name}")
                            }
                        }
                        // 点击功能按钮后隐藏功能区域
                        showFunctionButtons.value = false
                    },
                    viewModel = viewModel
                )
            }
        }
    }
}

/**
 * 功能按钮网格
 */
@Composable
fun FunctionsButtonsGrid(
    onFunctionClick: (FunctionButton) -> Unit = {},
    viewModel: ChatViewModel
) {
    val functions = listOf(
        FunctionButton(R.string.phone, R.drawable.ic_chat_phone),
        FunctionButton(R.string.report, R.drawable.ic_chat_report),
    )
    
    // 检查上次通话状态
    val context = LocalContext.current
    val lastCallAnswered = remember { mutableStateOf(false) }
    val showCallStatus = remember { mutableStateOf(false) }
    
    // 显示通话状态的效果
    LaunchedEffect(showCallStatus.value) {
        if (showCallStatus.value) {
            delay(3000) // 显示3秒
            showCallStatus.value = false
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 12.dp, horizontal = 16.dp)
    ) {
        // 按钮分两行显示
        for (rowIndex in 0..1) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 12.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                for (colIndex in 0..3) {
                    val index = rowIndex * 4 + colIndex
                    if (index < functions.size) {
                        Box {
                            FunctionItem(
                                functionButton = functions[index],
                                onFunctionClick = { 
                                    if (it.name == R.string.phone) {
                                        // 点击电话按钮，先检查上次通话状态
                                        lastCallAnswered.value = viewModel.wasLastCallAnswered()
                                        showCallStatus.value = lastCallAnswered.value
                                    }
                                    onFunctionClick(it) 
                                }
                            )
                        }
                    } else {
                        // 添加空占位符保持布局均匀
                        Spacer(modifier = Modifier.width(70.dp))
                    }
                }
            }
        }
    }
}

/**
 * 单个功能按钮项
 */
@Composable
fun FunctionItem(
    functionButton: FunctionButton,
    onFunctionClick: (FunctionButton) -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .width(70.dp)
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ) {
                LogUtil.d("功能按钮点击: ${functionButton.name}")
                onFunctionClick(functionButton)
            }
    ) {
        Box(
            modifier = Modifier,
            contentAlignment = Alignment.Center
        ) {
            Image(
                painterResource(functionButton.icon),
                contentDescription = stringResource(functionButton.name),
                modifier = Modifier.size(60.dp),
            )
        }

        Text(
            text = stringResource(functionButton.name),
            fontSize = 13.sp,
            fontWeight = FontWeight.Medium,
            color = Color(0xFF555555),
            modifier = Modifier.padding(top = 6.dp)
        )
    }
}

@Composable
fun MessageItemView(it: ChatSmsEntity, session: ChatSession, viewModel: ChatViewModel) {
    // 获取显示文本（非关注请求时使用）
    val displayText = remember(it.mediaType, it.message) {
        when (it.mediaType) {
            else -> it.message  // 普通文本直接显示
        }
    }

    // 格式化消息时间
    val messageTime = remember(it.createBy) {
        // 将时间戳转换为可读格式 (仅显示小时:分钟)
        TimeUtil.milliTimestampToDate(it.createBy, "HH:mm")
    }

    Box(
        contentAlignment = if (it.messageType == MessageType.RECEIVE.value) Alignment.Companion.CenterStart else Alignment.Companion.CenterEnd,
        modifier = Modifier.Companion
            .fillMaxWidth()
            .wrapContentHeight()
            .padding(
                start = if (it.messageType == MessageType.RECEIVE.value) 0.dp else 40.dp,
                end = if (it.messageType == MessageType.SEND.value) 0.dp else 40.dp
            ),
    ) {
        Column(
            modifier = Modifier.Companion
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            /*** 对话信息*/
            Row(
                modifier = Modifier.Companion
                    .wrapContentWidth()
                    .wrapContentHeight()
            ) {
                /*** 他人头像（左边）*/
                if (it.messageType == MessageType.RECEIVE.value) {
                    Box(
                        modifier = Modifier.Companion
                            .size(45.dp)
                            .clip(CircleShape)
                            .background(Color.Companion.White)
                            .weight(1f)
                    ) {
                        AsyncImage(
                            model = session.avatar,
                            contentDescription = null,
                            contentScale = ContentScale.Companion.Crop,
                            modifier = Modifier.Companion
                                .fillMaxSize(),
                            error = painterResource(id = R.drawable.header_3)

                        )
                    }
                }
                Box(
                    modifier = Modifier.Companion
                        .weight(6f)
                        .wrapContentHeight(),
                    contentAlignment =
                        if (it.messageType == MessageType.RECEIVE.value) Alignment.Companion.TopStart
                        else Alignment.Companion.TopEnd
                ) {
                    Column(
                        horizontalAlignment = if (it.messageType == MessageType.RECEIVE.value) Alignment.Start else Alignment.End
                    ) {
                        /*** 内容 - 根据类型显示不同内容*/
                        Box(
                            modifier = Modifier.Companion
                                .padding(
                                    start = if (it.messageType == MessageType.RECEIVE.value) 12.dp else 0.dp,
                                    end = if (it.messageType == MessageType.RECEIVE.value) 0.dp else 12.dp,
                                )
                                .clip(androidx.compose.foundation.shape.RoundedCornerShape(8.dp))
                                .wrapContentWidth()
                                .wrapContentHeight(Alignment.Companion.CenterVertically),
                        ) {
                            when (it.mediaType) {
                                // 普通文本消息
                                MediaType.TEXT.value -> {
                                    TextRequestCard(
                                        message = displayText,
                                        messageType = if (it.messageType == MessageType.RECEIVE.value) MessageType.RECEIVE else MessageType.SEND
                                    )
                                }

                                MediaType.MEETING.value -> {
                                    LogUtil.d("the meetingdata ${it.message}")
                                    it.getMeetContent()?.let { meetContent ->
                                        LogUtil.d("the meetingdata not null")
                                        MeetRequestCard(meetContent, it, viewModel)
                                    }
                                }
                                // 关注请求卡片
                                MediaType.FOLLOW.value -> {
                                    it.getFollowContent()?.let { followContent ->
                                        FollowRequestCard(followContent, it, viewModel)
                                    }
                                }

                                MediaType.REPORT.value -> {
                                    it.getReportContent()?.let { reportContent ->
                                        ReportRequestCard(reportContent)
                                    }
                                }
                            }
                        }

                        // 显示消息时间
                        Text(
                            text = messageTime,
                            color = Color(0xFF999999),
                            fontSize = 10.sp,
                            modifier = Modifier.padding(
                                start = if (it.messageType == MessageType.RECEIVE.value) 12.dp else 0.dp,
                                end = if (it.messageType == MessageType.RECEIVE.value) 0.dp else 12.dp,
                                top = 4.dp
                            )
                        )
                    }
                }
                /*** 本人头像（右边）*/
                if (it.messageType == MessageType.SEND.value) {
                    Box(
                        modifier = Modifier.Companion
                            .size(45.dp)
                            .clip(CircleShape)
                            .background(Color.Companion.White)
                            .weight(1f)
                    ) {
                        AsyncImage(
                            model = user.photo,
                            contentDescription = null,
                            contentScale = ContentScale.Companion.Crop,
                            modifier = Modifier.Companion
                                .fillMaxSize(),
                            error = painterResource(id = R.drawable.header_3)

                        )
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun MessageItemViewPreview() {
    val chatSession = ChatSession(
        providerUUID = "1L",
        avatar = "test",
        name = "tt",
        message = "asdasdasd",
        messageType = MessageType.RECEIVE,
        createBy = 123456789L
    )
    val chatSmsEntity = ChatSmsEntity(
        message = "asdasdasd",
        createBy = 123456789L
    )
    MessageItemView(chatSmsEntity, chatSession, viewModel = ChatViewModel())
}

/**
 * 日期标题组件，显示yyyy/MM/dd格式的日期
 */
@Composable
fun DateHeader(date: String) {
    Box(
        modifier = Modifier.Companion
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        contentAlignment = Alignment.Companion.Center
    ) {
        Text(
            text = date,
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF999999)
            )
        )
    }
}

@Preview
@Composable
private fun DateHeaderPreview() {
    DateHeader("2023/05/15")
}

/**
 * 手机号码请求对话框
 */
@Preview()
@Composable
fun PhoneNumberRequestDialog(
    visible: Boolean = true,
    phoneNumber: String = "",
    onPhoneNumberChange: (String) -> Unit = {_ ->},
    onDismiss: () -> Unit = {},
    onRefuse: () -> Unit = {},
    onSend: () -> Unit = {}
) {
    if (visible) {
        Dialog(
            onDismissRequest = onDismiss,
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            Surface(
                shape = RoundedCornerShape(16.dp),
                tonalElevation = 8.dp,
                modifier = Modifier.fillMaxWidth(0.9f),
                color = Color.White
            ) {
                Column(
                    modifier = Modifier
                        .padding(8.dp)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Get Number Request",
                        style = TextStyle(
                            fontSize = 20.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                            ),
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    
                    Text(
                        text = "The other party did not receive your number and sent you a request to obtain it.",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF999999),
                            textAlign = TextAlign.Justify,
                        ),
                        modifier = Modifier.padding(bottom = 24.dp),
                    )
                    
                    Text(
                        text = "Fill in the number",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                            textAlign = TextAlign.Justify,
                        ),
                        modifier = Modifier.align(Alignment.Start).padding(bottom = 8.dp)
                    )
                    
                    TextField(
                        value = phoneNumber,
                        onValueChange = onPhoneNumberChange,
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                        placeholder = { Text("Please enter the number") },
                        colors = TextFieldDefaults.colors(
                            focusedContainerColor = Color(0xFFF5F5F5),
                            unfocusedContainerColor = Color(0xFFF5F5F5),
                            disabledContainerColor = Color(0xFFF5F5F5),
                            focusedIndicatorColor = Color.Transparent,
                            unfocusedIndicatorColor = Color.Transparent
                        ),
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(color = Color(0xFFF8F8F8), shape = RoundedCornerShape(size = 24.dp))
                            .padding(bottom = 24.dp)
                    )
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Button(
                            onClick = onRefuse,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color.White
                            ),
                            border = BorderStroke(1.dp, Color(0xFF1B6BFF)),
                            shape = RoundedCornerShape(24.dp),
                            modifier = Modifier
                                .weight(1f)
                                .padding(end = 8.dp)
                        ) {
                            Text(
                                text = "Refuse",
                                color = Color(0xFF1B6BFF)
                            )
                        }
                        
                        Button(
                            onClick = onSend,
                            colors = ButtonDefaults.buttonColors(
                                containerColor = Color(0xFF4F97F3)
                            ),
                            shape = RoundedCornerShape(24.dp),
                            modifier = Modifier
                                .weight(1f)
                                .padding(start = 8.dp)
                        ) {
                            Text(
                                text = "Send",
                                color = Color.White
                            )
                        }
                    }
                }
            }
        }
    }
}

