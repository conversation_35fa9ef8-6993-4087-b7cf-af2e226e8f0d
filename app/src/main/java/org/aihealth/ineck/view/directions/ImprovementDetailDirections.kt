package org.aihealth.ineck.view.directions

import android.net.Uri
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen

class ImprovementDetailDirections {

    data class ImprovementDetailArgs(
        val model: ImprovementDetailModel
    )
    @Parcelize
    data class ImprovementDetailModel(
        val materialId: Int,
        val type: String,
        val needVip: Boolean,
    ) : Parcelable

    companion object {
        val route = Screen.ImprovementDetail.route + "?model={model}"
        val gson = Gson()
        val argumentsList: MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model") {
                    type = object : NavType<ImprovementDetailModel>(false) {
                        override val name: String
                            get() = "ImprovementDetailModel"

                        override fun get(
                            bundle: android.os.Bundle,
                            key: String
                        ): ImprovementDetailModel? {
                            return bundle.getParcelable(key)
                        }

                        override fun parseValue(value: String): ImprovementDetailModel {
                            return gson.fromJson(
                                value,
                                object : TypeToken<ImprovementDetailModel>() {}.type
                            )
                        }

                        override fun put(
                            bundle: android.os.Bundle,
                            key: String,
                            value: ImprovementDetailModel
                        ) {
                            bundle.putParcelable(key, value)
                        }

                    }
                }
            )

        fun parseArguments(backStackEntry: NavBackStackEntry): ImprovementDetailArgs {
            return ImprovementDetailArgs(
                model = backStackEntry.arguments?.getParcelable<ImprovementDetailModel>("model")!!
            )
        }

        fun actionToImprovementDetailCompose(model: ImprovementDetailModel): String {
            return Screen.ImprovementDetail.route + "?model=${Uri.encode(gson.toJson(model))}"
        }
    }

}