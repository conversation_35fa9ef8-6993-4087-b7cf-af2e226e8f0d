package org.aihealth.ineck.view.directions

import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen

class MatchingDeviceDirections {

    data class MatchingDeviceArgs(
        val model: MatchingDeviceModel
    )
    @Parcelize
    data class MatchingDeviceModel(
        val deviceType: String,
    ): Parcelable

    companion object {
        val route = "${Screen.MatchingDevice.route}?model={model}"
        val gson = Gson()

        val argumentsList:MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model"){
                   type = object : NavType<MatchingDeviceModel>(false){
                       override val name: String
                           get() = "MatchingDeviceModel"
                       override fun get(bundle: Bundle, key: String): MatchingDeviceModel? {
                           return bundle.getParcelable(key)
                       }

                       override fun parseValue(value: String): MatchingDeviceModel {
                           return HistoryVitalSignsDirections.gson.fromJson(value, object :
                               TypeToken<MatchingDeviceModel>(){}.type)
                       }

                       override fun put(bundle: Bundle, key: String, value: MatchingDeviceModel) {
                           bundle.putParcelable(key,value)
                       }

                   }
                }
            )
        fun parseArguments(backStackEntry: androidx.navigation.NavBackStackEntry): MatchingDeviceArgs {
            return MatchingDeviceArgs(
                model = backStackEntry.arguments?.getParcelable<MatchingDeviceModel>("model")!!
            )
        }
        fun actionToMatchingDevice(model: MatchingDeviceModel):String{
            return "${Screen.MatchingDevice.route}?model=${Uri.encode(gson.toJson(model))}"
        }
    }
}