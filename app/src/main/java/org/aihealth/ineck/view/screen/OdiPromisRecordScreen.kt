package org.aihealth.ineck.view.screen

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupProperties
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.angles.BaseTime
import org.aihealth.ineck.model.angles.Odi
import org.aihealth.ineck.model.angles.Promis
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.roundToPx
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHSelectButton
import org.aihealth.ineck.view.custom.AIHSlider
import org.aihealth.ineck.view.custom.AIHSliderState
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.custom.Triangle
import java.util.Locale

@Composable
fun OdiPromisRecordScreen(
    isPromis: Boolean = false,
    showToggleButton: Boolean = true,
    baseTime: BaseTime? = null
) {
    var pageIndex by remember {
        mutableIntStateOf(if (isPromis) 1 else 0)
    }

    var explainVisible by remember {
        mutableStateOf(false)
    }
    LogUtil.i("data: isPromis:${isPromis},showToggleButton:${showToggleButton},baseTime:${baseTime}")

    BasePageView(
        showBackIcon = true,
        headerContent = {
            if (showToggleButton) {
                AIHSelectButton(
                    selectedIndex = pageIndex,
                    array = arrayOf("ODI", "PROMIS"),
                    onClick = { pageIndex = it },
                    padding = PaddingValues(3.dp),
                    modifier = Modifier
                        .fillMaxWidth(0.8f)
                        .align(
                            Alignment.Center
                        )
                )
            }

            Box(modifier = Modifier
                .align(Alignment.CenterEnd)
                .pointerInput(Unit) {
                    detectTapGestures {
                        explainVisible = true
                    }
                }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.img_explain),
                    contentDescription = null,
                    tint = Color(0XFF3B58CE),
                    modifier = Modifier
                        .padding(8.dp)
                        .size(18.dp)
                )
                if (explainVisible) {
                    Popup(
                        onDismissRequest = {
                            explainVisible = false
                        },
                        alignment = Alignment.TopEnd,
                        offset = IntOffset(12.dp.roundToPx(), 20.dp.roundToPx()),
                        properties = PopupProperties(focusable = true)
                    ) {
                        Column {
                            Triangle(
                                color = Color(0XFFF1F1F1), modifier = Modifier
                                    .align(Alignment.End)
                                    .padding(end = 14.dp)
                            )
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth(0.8f)
                                    .background(Color(0XFFF1F1F1), RoundedCornerShape(12.dp))
                                    .padding(horizontal = 14.dp, vertical = 24.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = stringResource(id = if (pageIndex == 0) R.string.odi_record_explain else R.string.promis_record_explain),
                                    fontSize = 12.sp,
                                    color = Color(0XFF666666)
                                )
                            }
                        }
                    }
                }
            }

        }
    ) {
        AIHDivider()

        val odiStateList = remember {
            mutableStateListOf<AIHSliderState>()
        }
        if (!isPromis && baseTime != null) {
            val odi = baseTime as Odi
            odiStateList.add(AIHSliderState(odi.pain))
            odiStateList.add(AIHSliderState(odi.care))
            odiStateList.add(AIHSliderState(odi.lifting))
            odiStateList.add(AIHSliderState(odi.walking))
            odiStateList.add(AIHSliderState(odi.sitting))
            odiStateList.add(AIHSliderState(odi.standing))
            odiStateList.add(AIHSliderState(odi.sleeping))
            odiStateList.add(AIHSliderState(odi.social))
            odiStateList.add(AIHSliderState(odi.travelling))
        } else {
            repeat(10) {
                odiStateList.add(AIHSliderState(0))
            }
        }

        val promisValueList = remember {
            mutableStateListOf<AIHSliderState>()
        }

        if (isPromis && baseTime != null) {
            val promis = baseTime as Promis
            promisValueList.add(AIHSliderState(promis.average_pain))
            promisValueList.add(AIHSliderState(promis.health))
            promisValueList.add(AIHSliderState(promis.life))
            promisValueList.add(AIHSliderState(promis.physical))
            promisValueList.add(AIHSliderState(promis.mental))
            promisValueList.add(AIHSliderState(promis.social))
            promisValueList.add(AIHSliderState(promis.behave))
            promisValueList.add(AIHSliderState(promis.sport))
            promisValueList.add(AIHSliderState(promis.sentiment))
            promisValueList.add(AIHSliderState(promis.fatigue))
        } else {
            repeat(10) {
                promisValueList.add(AIHSliderState(1))
            }
        }

        val odiScrollState = rememberScrollState()
        val promisScrollState = rememberScrollState()
        when (pageIndex) {
            0 -> ODIView(odiScrollState, odiStateList, baseTime == null) {
                pageIndex = 1
            }

            1 -> PromisView(promisScrollState, promisValueList, baseTime == null)
        }

    }
}

@Composable
private fun ODIView(
    scrollState: ScrollState,
    valueList: SnapshotStateList<AIHSliderState>,
    enabled: Boolean = true,
    onSuccess: () -> Unit = {}
) {
    Column(
        Modifier
            .fillMaxSize()
            .padding(horizontal = 8.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.odi_record_title),
            fontSize = 16.sp,
            color = Color(0XFF3B58CE),
        )
        Section(id = R.string.odi_record_section1)
        ODISelectedView(
            state = valueList[0],
            array = stringArrayResource(R.array.odi_record_option1),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section2)
        ODISelectedView(
            state = valueList[1],
            array = stringArrayResource(R.array.odi_record_option2),
            isBeyondLine = currentLocale != Locale.CHINESE,
            enabled = enabled
        )

        Section(id = R.string.odi_record_section3)
        ODISelectedView(
            state = valueList[2],
            array = stringArrayResource(R.array.odi_record_option3),
            isBeyondLine = true,
            enabled = enabled
        )

        Section(id = R.string.odi_record_section4)
        ODISelectedView(
            state = valueList[3],
            array = stringArrayResource(R.array.odi_record_option4),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section5)
        ODISelectedView(
            state = valueList[4],
            array = stringArrayResource(R.array.odi_record_option5),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section6)
        ODISelectedView(
            state = valueList[5],
            array = stringArrayResource(R.array.odi_record_option6),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section7)
        ODISelectedView(
            state = valueList[6],
            array = stringArrayResource(R.array.odi_record_option7),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section8)
        ODISelectedView(
            state = valueList[7],
            isBeyondLine = currentLocale != Locale.CHINESE,
            array = stringArrayResource(R.array.odi_record_option8),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section9)
        ODISelectedView(
            state = valueList[8],
            array = stringArrayResource(R.array.odi_record_option9),
            enabled = enabled
        )
        Spacer(modifier = Modifier.height(36.dp))
        if (enabled) {
            AIHButton(
                text = stringResource(id = R.string.submit),
                onClick = {
                    apiService.postOdi(
                        odi = Odi(
                            pain = valueList[0].value,
                            care = valueList[1].value,
                            lifting = valueList[2].value,
                            walking = valueList[3].value,
                            sitting = valueList[4].value,
                            standing = valueList[5].value,
                            sleeping = valueList[6].value,
                            social = valueList[7].value,
                            travelling = valueList[8].value
                        )
                    ).enqueueBody {
                        onSuccess()
                        DialogUtil.showToast(
                            localeResources.getString(
                                R.string.record_added_successfully,
                                "ODI"
                            )
                        )
                    }
                },
                fontSize = 20.sp,
                fontColor = Color.White,
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
        }
    }
}

@Composable
private fun PromisView(
    scrollState: ScrollState,
    valueList: SnapshotStateList<AIHSliderState>,
    enabled: Boolean = true
) {
    Column(
        Modifier
            .fillMaxSize()
            .padding(horizontal = 8.dp)
            .verticalScroll(scrollState),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.promis_record_title),
            fontSize = 16.sp,
            color = Color(0XFF3B58CE),
        )
        val statusArray = stringArrayResource(R.array.promis_record_option2)
        Section(id = R.string.promis_record_section1)
        PromisSelectedView(
            state = valueList[0],
            array = stringArrayResource(R.array.promis_record_option1),
            enabled = enabled
        )

        Section(id = R.string.promis_record_section2)
        PromisSelectedView(
            state = valueList[1],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section3)
        PromisSelectedView(
            state = valueList[2],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section4)
        PromisSelectedView(
            state = valueList[3],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section5)
        PromisSelectedView(
            state = valueList[4],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section6)
        PromisSelectedView(
            state = valueList[5],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section7)
        PromisSelectedView(
            state = valueList[6],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section8)
        PromisSelectedView(
            state = valueList[7],
            array = stringArrayResource(R.array.promis_record_option3),
            enabled = enabled
        )

        Section(id = R.string.promis_record_section9)
        PromisSelectedView(
            state = valueList[8],
            array = stringArrayResource(R.array.promis_record_option4),
            enabled = enabled
        )

        Section(id = R.string.promis_record_section10)
        PromisSelectedView(
            state = valueList[9],
            array = stringArrayResource(R.array.promis_record_option5),
            enabled = enabled
        )
        Spacer(modifier = Modifier.height(36.dp))
        if (enabled) {
            AIHButton(
                text = stringResource(id = R.string.submit),
                onClick = {
                    apiService.postPromis(
                        promis = Promis(
                            average_pain = valueList[0].value,
                            health = valueList[1].value,
                            life = valueList[2].value,
                            physical = valueList[3].value,
                            mental = valueList[4].value,
                            social = valueList[5].value,
                            behave = valueList[6].value,
                            sport = valueList[7].value,
                            sentiment = valueList[8].value,
                            fatigue = valueList[9].value
                        )
                    ).enqueueBody {
                        DialogUtil.showToast(
                            localeResources.getString(
                                R.string.record_added_successfully,
                                "PROMIS"
                            )
                        )
                        popScreen(Screen.Main.route)
                    }
                },
                fontSize = 20.sp,
                fontColor = Color.White,
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
        }
    }
}

@Composable
fun Section(id: Int) {
    Text(
        text = stringResource(id = id),
        fontSize = 18.sp,
        color = Color(0XFF333333),
        fontWeight = FontWeight.Medium,
        modifier = Modifier
            .padding(vertical = 16.dp)
            .fillMaxWidth()
    )
}

@Composable
fun ODISelectedView(
    state: AIHSliderState,
    array: Array<String> = emptyArray(),
    isBeyondLine: Boolean = false,
    enabled: Boolean = true
) {
    if (array.size > 2) {
        Text(
            text = array.getOrNull(state.value) ?: "",
            fontSize = 12.sp,
            color = Color.Black,
        )
        Spacer(modifier = Modifier.height(8.dp))
    } else if (array.size == 2) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = array.getOrNull(0) ?: "",
                fontSize = 12.sp,
                color = Color.Black,
            )
            if (!isBeyondLine) {
                Text(
                    text = array.getOrNull(1) ?: "",
                    fontSize = 12.sp,
                    color = Color.Black,
                    modifier = Modifier.align(Alignment.CenterEnd)
                )
            }
        }
        Spacer(modifier = Modifier.height(8.dp))
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 7.dp, end = 16.dp)
    ) {
        Text(text = "0", fontSize = 14.sp, color = Color.Black)
        Spacer(modifier = Modifier.width(4.dp))
        Column(
            modifier = Modifier
                .padding(horizontal = 3.dp)
                .weight(1F)
        ) {

            AIHSlider(
                state = state,
                progression = 0..5,
                enabled = enabled,
                trackColor = Color(0XFF85817C),
                thumbColor = Color(0XFF64CECD)
            )
            Column(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 5.dp)
            ) {

                BoxWithConstraints(
                    Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    val mWidth = maxWidth / 5
                    repeat(4) {
                        Text(
                            text = "${it + 1}",
                            fontSize = 12.sp,
                            color = Color.Black,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .padding(start = mWidth * it)
                                .width(mWidth * 2)
                        )
                    }
                }


            }
        }
        Spacer(modifier = Modifier.width(4.dp))
        Text(text = "5", fontSize = 14.sp, color = Color.Black)
    }
    if (isBeyondLine && array.size > 1) {
        Text(
            text = array[1],
            fontSize = 12.sp,
            color = Color.Black,
            textAlign = TextAlign.End,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Composable
fun ColumnScope.PromisSelectedView(
    state: AIHSliderState,
    array: Array<String> = emptyArray(),
    enabled: Boolean = true
) {
    if (array.size > 2) {
        Text(
            text = array.getOrNull(state.value - 1) ?: "",
            fontSize = 12.sp,
            color = Color.Black,
        )
        Spacer(modifier = Modifier.height(8.dp))
    } else if (array.size == 2) {
        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = array.getOrNull(0) ?: "",
                fontSize = 12.sp,
                color = Color.Black,
                textAlign = TextAlign.Start,
                modifier = Modifier.fillMaxWidth()
            )
            Text(
                text = array.getOrNull(1) ?: "",
                fontSize = 12.sp,
                color = Color.Black,
                textAlign = TextAlign.End,
                modifier = Modifier.fillMaxWidth()
            )
        }
        Spacer(modifier = Modifier.height(8.dp))
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(start = 7.dp, end = 16.dp)
    ) {
        Text(text = "1", fontSize = 14.sp, color = Color.Black)
        Spacer(modifier = Modifier.width(4.dp))
        Column(
            modifier = Modifier
                .padding(horizontal = 3.dp)
                .weight(1F)
        ) {

            AIHSlider(
                state = state,
                progression = 1..5,
                enabled = enabled,
                trackColor = Color(0XFF85817C),
                thumbColor = Color(0XFF64CECD)
            )
            Column(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 5.dp)
            ) {

                BoxWithConstraints(
                    Modifier
                        .padding(vertical = 4.dp)
                        .fillMaxWidth()
                ) {
                    val mWidth = maxWidth / 4
                    repeat(3) {
                        Text(
                            text = "${it + 2}",
                            fontSize = 12.sp,
                            color = Color.Black,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .padding(start = mWidth * it)
                                .width(mWidth * 2)
                        )
                    }
                }


            }
        }
        Spacer(modifier = Modifier.width(4.dp))
        Text(text = "5", fontSize = 14.sp, color = Color.Black)
    }
}