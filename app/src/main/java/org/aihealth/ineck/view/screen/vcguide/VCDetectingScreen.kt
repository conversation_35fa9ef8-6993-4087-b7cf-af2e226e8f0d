package org.aihealth.ineck.view.screen.vcguide

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.PagerState
import com.google.mlkit.vision.face.FaceDetectorOptions
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.analyzer.FaceImageAnalyzer
import org.aihealth.ineck.model.Queue
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.viewmodel.dao.TripleAngleWithTimestamp
import org.aihealth.ineck.viewmodel.dao.compareEnqueue
import org.aihealth.ineck.viewmodel.dao.getTimestampNow
import org.aihealth.ineck.viewmodel.dao.verifyTripleDiff
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import kotlin.math.abs
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun VCDetectingScreen(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    pagerCount: Int,
    content: @Composable (Int) -> Unit

) {
    HorizontalPager(
        state = pagerState,
        count = pagerCount,
        modifier = modifier.fillMaxWidth(),
        userScrollEnabled = false
    ) { page ->
        content(page)
    }
}
@Composable
fun VCHorizontalDetectingScreen(
    modifier: Modifier = Modifier,
    isMuteState: Boolean = false,
    isVoiceEnable: Boolean = true,
    changeMuteState: () -> Unit = {},
    nextPage: () -> Unit = {},
    timeOut: () -> Unit = {},
) {
    var isNextPage by remember {
        mutableStateOf(false)
    }
    var isTimeOut by remember {
        mutableStateOf(false)
    }
    var validPitchCount by remember { mutableIntStateOf(0) }
    val (oPitch, oRoll) = rememberOrientation()
    val pitch by animateIntAsState(
        targetValue = oPitch,
        animationSpec = tween(durationMillis = 300),
        label = "pitch"
    )
    val roll by animateIntAsState(
        targetValue = oRoll,
        animationSpec = tween(durationMillis = 300),
        label = "roll",
    )
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 静音按钮
        Box(
            modifier = modifier.fillMaxWidth()
        ) {
            MuteButton(
                isMuteState = isMuteState,
                isVoiceEnable = isVoiceEnable,
                changeMuteState = { changeMuteState() },
                modifier = Modifier
                    .padding(16.dp)
                    .size(30.dp)
                    .align(Alignment.CenterEnd)
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 10.dp),
            horizontalArrangement = Arrangement.Center,
        ) {
            if (isNextPage) {
                /** 垂直校准完成动画 */
                val finishComposition by rememberLottieComposition(
                    spec = LottieCompositionSpec.RawRes(
                        R.raw.vertical_calibration_finish_animation
                    )
                )
                LottieAnimation(
                    composition = finishComposition,
                    iterations = 1,
                    modifier = Modifier.size(50.dp)
                )

            } else {
                val speechText = stringResource(id = R.string.detect_guide_text)
                Text(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    text = speechText,
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFD9352E),
                    )
                )
                LaunchedEffect(isMuteState) {
                    if (isMuteState) {
                        TextToSpeech.ttsStop()
                    } else {
                        TextToSpeech.ttsSpeaking(speechText)
                    }
                }
            }
        }
        // 水平仪
        CustomComponent(
            pitch,
            roll,
        )
        LaunchedEffect(isNextPage) {
            if (isNextPage) {
                delay(2000)
                nextPage()
            }
        }
    }
    LaunchedEffect(Unit) {
        isTimeOut = true
        delay(15000L) // 10 seconds
        if (isTimeOut) {
            timeOut()
        }
    }
    LaunchedEffect(Unit) {
        while (true) {
            delay(300)
            if (pitch == 90 && roll == 0) {
                validPitchCount++
                if (validPitchCount >= 3) { // Check for 3 seconds (6 * 500ms)
                    isTimeOut = false
                    isNextPage = true
                    return@LaunchedEffect
                }
            } else {
                validPitchCount = 0
            }
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            TextToSpeech.ttsStop()
        }
    }
}

@Composable
fun VCFaceDetectingScreen(
    modifier: Modifier = Modifier,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    isMuteState: Boolean = false,
    isVoiceEnable: Boolean = true,
    changeMuteState: () -> Unit = {},
    nextPage: (VCDetectingResult.DetectingSuccess) -> Unit = {},
    timeOut: () -> Unit = {},
) {
    val context = LocalContext.current

    /** 组合的协程域 */
    val localScope = rememberCoroutineScope()

    /** 获取相机实例 */
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }

    /** 是否捕捉到面部 */
    var isCapturedFace by remember {
        mutableStateOf(false)
    }

    /** 是否处于平视状态（不晃动） */
    var isSightLineHorizontal by remember {
        mutableStateOf(false)
    }

    /** 点头的仰角 */
    var headEulerAngleX by remember {
        mutableFloatStateOf(0f)
    }

    /** 左右的摆角 */
    var headEulerAngleY by remember {
        mutableFloatStateOf(0f)
    }

    /** 顺势帧的旋转角度 */
    var headEulerAngleZ by remember {
        mutableFloatStateOf(0f)
    }


    /** 开始监测时间戳 - 从有效数据开始计算 */
    var startTimestampForEffectDetecting by remember {
        mutableLongStateOf(getTimestampNow())
    }

    /** 角度队列 */
    val angleQueue = remember {
        mutableStateOf(Queue<TripleAngleWithTimestamp>())
    }
    var isTimeOut by remember {
        mutableStateOf(false)
    }

    var isNextPager by remember {
        mutableStateOf(false)
    }

    /** 进度条动画是否应该开始 */
    var shouldStartProgress by remember {
        mutableStateOf(false)
    }

    /** 上次状态变化的时间戳 */
    var lastStateChangeTimestamp by remember {
        mutableLongStateOf(getTimestampNow())
    }

    // 添加日志监听状态变化
    LaunchedEffect(Unit) {
        LogUtil.i("VCDetecting - VCFaceDetectingScreen started")
    }
    
    LaunchedEffect(shouldStartProgress) {
        LogUtil.i("VCDetecting - shouldStartProgress changed to: $shouldStartProgress")
    }

    LaunchedEffect(isCapturedFace) {
        LogUtil.i("VCDetecting - isCapturedFace changed to: $isCapturedFace")
    }

    LaunchedEffect(isSightLineHorizontal) {
        LogUtil.i("VCDetecting - isSightLineHorizontal changed to: $isSightLineHorizontal")
    }

    val imageAnalyzer = FaceImageAnalyzer(
        opts = FaceDetectorOptions.Builder()
            .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
            .setContourMode(FaceDetectorOptions.CONTOUR_MODE_ALL)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
//            .setMinFaceSize(0.15f) // 设置最小检测人脸大小
//            .enableTracking() // 启用跟踪
            .build(),
        onUpdateAngle = { angleX, angleY, angleZ, isEffect ->
            if (isEffect) {
                /* 面部存在，更新头部各欧拉角参数 */
                if (!isCapturedFace) {
                    LogUtil.i("VCDetecting - face detected, angles: X=${angleX}, Y=${angleY}, Z=${angleZ}")
                    /* 重置状态变化时间戳 */
                    lastStateChangeTimestamp = getTimestampNow()
                }
                isCapturedFace = true
                headEulerAngleX = angleX
                headEulerAngleY = angleY
                headEulerAngleZ = angleZ

                /* 检测中 */
                angleQueue.value.compareEnqueue(
                    TripleAngleWithTimestamp(angleX, angleY, angleZ, getTimestampNow())
                )
                LogUtil.i("has face")

                val currentTime = getTimestampNow()
                val timeElapsed = currentTime - startTimestampForEffectDetecting
                LogUtil.i("VCDetecting - timeElapsed: ${timeElapsed}ms, isSightLineHorizontal: $isSightLineHorizontal")

                /* 若校准时间超过 5sec 以上，在当前拥有足够样本容量的情况下，判断队列差是否小于10度 */
                if (timeElapsed > 5000L &&
                    angleQueue.value.verifyTripleDiff(10f) && isSightLineHorizontal
                ) {
                    /* 检测成功 */
                    isTimeOut = false
                    isNextPager = true
                    LogUtil.i("VCDetecting - detecting success, timeElapsed: ${timeElapsed}ms")
                    localScope.launch {
                        delay(2000)
                        val last = angleQueue.value.last()
                        nextPage(
                            VCDetectingResult.DetectingSuccess(
                                result = Triple(last.angleX, last.angleY, last.angleZ)
                            )
                        )
                    }

                } else if (timeElapsed > 2000L && isSightLineHorizontal) {
                    /* 2秒后开始进度条动画 */
                    if (!shouldStartProgress) {
                        LogUtil.i("VCDetecting - starting progress animation, timeElapsed: ${timeElapsed}ms")
                        shouldStartProgress = true
                    }
                }
                /* 平视状态判断 */
                if (abs(headEulerAngleX) < 35f && abs(headEulerAngleY) < 45f && abs(headEulerAngleZ) < 35f) {
                    /* 在范围内，确认为 平视状态 */
                    if (!isSightLineHorizontal) {
                        LogUtil.i("VCDetecting - sight line became horizontal, angles: X=${headEulerAngleX}, Y=${headEulerAngleY}, Z=${headEulerAngleZ}")
                        isSightLineHorizontal = true
                        /* 重置状态变化时间戳 */
                        lastStateChangeTimestamp = getTimestampNow()
                    }

                } else {
                    if (isSightLineHorizontal) {
                        val now = getTimestampNow()
                        if (now - lastStateChangeTimestamp > 500L) {
                            /* 状态变化持续500ms以上才真正重置 */
                            LogUtil.i("VCDetecting - sight line no longer horizontal after 500ms buffer, angles: X=${headEulerAngleX}, Y=${headEulerAngleY}, Z=${headEulerAngleZ}")
                            isSightLineHorizontal = false
                            /* 检测开始时间戳被初始化 */
                            startTimestampForEffectDetecting = getTimestampNow()
                            LogUtil.i("VCDetecting - reset detection timestamp")
                            /* 重置进度条状态 */
                            if (shouldStartProgress) {
                                LogUtil.i("VCDetecting - resetting progress due to sight line not horizontal")
                                shouldStartProgress = false
                            }
                        } else {
                            /* 更新状态变化时间戳 */
                            lastStateChangeTimestamp = now
                        }
                    }
                }

            } else {
                /* 面部不存在 */
                if (isCapturedFace) {
                    val now = getTimestampNow()
                    if (now - lastStateChangeTimestamp > 1000L) {
                        /* 面部丢失持续1秒以上才真正重置 */
                        LogUtil.i("VCDetecting - face lost after 1s buffer")
                        isCapturedFace = false
                        isSightLineHorizontal = false
                        headEulerAngleX = 0f
                        headEulerAngleY = 0f
                        headEulerAngleZ = 0f
                        /* 重置面部开始检测时间  */
                        startTimestampForEffectDetecting = getTimestampNow()
                        /* 重置进度条状态 */
                        if (shouldStartProgress) {
                            LogUtil.i("VCDetecting - resetting progress due to no face detected")
                            shouldStartProgress = false
                        }
                    } else {
                        /* 更新状态变化时间戳 */
                        lastStateChangeTimestamp = now
                    }
                } else {
                    /* 面部一直不存在，重置相关状态 */
                    isSightLineHorizontal = false
                    headEulerAngleX = 0f
                    headEulerAngleY = 0f
                    headEulerAngleZ = 0f
                }
            }
        }
    )
    Column(
        modifier = modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Box(
            modifier = modifier.fillMaxWidth(),
        ) {
            MuteButton(
                isMuteState = isMuteState,
                isVoiceEnable = isVoiceEnable,
                changeMuteState = { changeMuteState() },
                modifier = Modifier
                    .padding(16.dp)
                    .size(30.dp)
                    .align(Alignment.CenterEnd)
            )
        }
        /** 检测引导完成动画 */
        val finishComposition by rememberLottieComposition(spec = LottieCompositionSpec.RawRes(R.raw.vertical_calibration_finish_animation))
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = Color.Transparent),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (isNextPager) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    /* 校准完成 */
                    LottieAnimation(
                        composition = finishComposition,
                        iterations = 1,
                        modifier = Modifier.size(60.dp)
                    )
                    Spacer(modifier = Modifier.height(1.dp))
                    Text(
                        text = stringResource(id = R.string.passed),
                        style = TextStyle(
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color(0xFF333333),
                        ),
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

            } else {
                val speechText =
                    if (!isCapturedFace) {
                        stringResource(id = R.string.detect_guide_please_show_your_face)
                    } else if (!isSightLineHorizontal) {
                        stringResource(id = R.string.detect_guide_dont_shake_body)
                    } else {
                        stringResource(id = R.string.precautions_of_detect_guide_line_3)
                    }
                LaunchedEffect(isMuteState, speechText) {
                    if (isMuteState) {
                        TextToSpeech.ttsStop()
                    } else {
                        TextToSpeech.ttsSpeaking(speechText)
                    }
                }
                Text(
                    text = speechText,
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight.W400,
                        color = if (isCapturedFace && isSightLineHorizontal)
                            Color(0xFF333333)
                        else
                            Color(0xFFFC7349),
                    ),
                    modifier = Modifier
                        .animateContentSize()
                )

            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 10.dp, bottom = 20.dp)
                .height(220.dp)
                .background(color = Color.Transparent)
        ) {
            val frameBlockColor: Color by animateColorAsState(
                targetValue = if (isCapturedFace && isSightLineHorizontal)
                    Color(0xFFDDE4f1)
                else Color(0xFFFC7349), label = ""
            )
            val circleProgressColor: Color by animateColorAsState(
                targetValue = if (isCapturedFace && isSightLineHorizontal)
                    Color(0xFF4CAF50)  // 明显的绿色
                else Color(0xFFFC7349), // 橙色
                label = "circleProgressColor"
            )
            val circleProgress = remember { Animatable(0f) }
            /* 检测框周边圆圈进度条 */
            CircularProgressIndicator(
                progress = circleProgress.value,
                modifier = Modifier
                    .size(196.dp)
                    .align(Alignment.Center),
                color = circleProgressColor,
                strokeWidth = 8.dp,
            )
            /* 外围检测框 */
            Icon(
                painter = painterResource(id = R.drawable.img_vc_detect_block),
                contentDescription = "Detection frame",
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
                tint = frameBlockColor
            )
            Surface(
                modifier = Modifier
                    .size(180.dp)
                    .align(Alignment.Center),
                shape = CircleShape
            ) {
                AndroidView(
                    factory = { context ->
                        val previewView = PreviewView(context)
                        val preview = Preview.Builder().build()
                        /* 摄像头选择 */
                        val selector = CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                            .build()

                        preview.setSurfaceProvider(previewView.surfaceProvider)
                        /* 自定义图像分析对象 */
                        val faceDetectAnalysis = ImageAnalysis.Builder()
                            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                            .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888)
                            .build()

                        faceDetectAnalysis.setAnalyzer(
                            ContextCompat.getMainExecutor(context),
                            imageAnalyzer
                        )
                        cameraProviderFuture.addListener({
                            val cameraProvider = cameraProviderFuture.get()
                            try {
                                cameraProvider.unbindAll()
                                cameraProvider.bindToLifecycle(
                                    lifecycleOwner, selector, preview, faceDetectAnalysis
                                )
                                preview.setSurfaceProvider(previewView.surfaceProvider)
                            } catch (e: Exception) {
                                e.printStackTrace()
                            }
                        }, ContextCompat.getMainExecutor(context))

                        previewView
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
            LaunchedEffect(shouldStartProgress) {
                if (shouldStartProgress) {
                    // 开始进度动画
                    LogUtil.i("VCDetecting - starting progress animation with 1s delay")
                    delay(1000)
                    (1..4).forEach { i ->
                        LogUtil.i("VCDetecting - animating to progress: ${i * (1f / 4f)}")
                        circleProgress.animateTo(
                            i * (1f / 4f),
                            animationSpec = tween(500, 0, LinearEasing)
                        )
                    }
                    LogUtil.i("VCDetecting - progress animation completed")
                } else {
                    // 重置进度条
                    LogUtil.i("VCDetecting - resetting progress to 0")
                    circleProgress.snapTo(0f)
                }
            }
        }

    }
    LaunchedEffect(Unit) {
        isTimeOut = true
        delay(20000)
        if (isTimeOut) {
            timeOut()
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            TextToSpeech.ttsStop()
        }
    }
}


@Composable
fun rememberOrientation(): Pair<Int, Int> {
    val context = LocalContext.current
    var angles by remember { mutableStateOf(0 to 0) }
    
    // 检测是否在预览模式
    val isInPreview = LocalInspectionMode.current
    
    if (isInPreview) {
        // 预览模式下返回模拟数据
        return 90 to 0
    }

    DisposableEffect(context) {
        val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
        val accelerometer = sensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER)
        val magnetometer = sensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD)

        val rotationMatrix = FloatArray(9)
        val orientationAngles = FloatArray(3)
        val accelerometerReading = FloatArray(3)
        val magnetometerReading = FloatArray(3)

        val listener = object : SensorEventListener {
            override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {}
            override fun onSensorChanged(event: SensorEvent) {
                when (event.sensor.type) {
                    Sensor.TYPE_ACCELEROMETER -> {
                        System.arraycopy(
                            event.values,
                            0,
                            accelerometerReading,
                            0,
                            accelerometerReading.size
                        )
                    }

                    Sensor.TYPE_MAGNETIC_FIELD -> {
                        System.arraycopy(
                            event.values,
                            0,
                            magnetometerReading,
                            0,
                            magnetometerReading.size
                        )
                    }
                }

                SensorManager.getRotationMatrix(
                    rotationMatrix,
                    null,
                    accelerometerReading,
                    magnetometerReading
                )
                SensorManager.getOrientation(rotationMatrix, orientationAngles)

                // Convert the angles from radians to degrees
                val pitch =
                    -Math.toDegrees(orientationAngles[1].toDouble()).toInt() // vertical angle
                val roll =
                    Math.toDegrees(orientationAngles[2].toDouble()).toInt() // horizontal angle
                angles = (if (90 - pitch < 10) 90 else pitch) to (if (abs(roll) < 7) 0 else roll)
            }
        }

        sensorManager.registerListener(listener, accelerometer, SensorManager.SENSOR_DELAY_UI)
        sensorManager.registerListener(listener, magnetometer, SensorManager.SENSOR_DELAY_UI)

        onDispose {
            sensorManager.unregisterListener(listener)
        }
    }

    return angles
}

@Composable
fun CustomComponent(
    pitch: Int = 0,
    roll: Int = 0,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Canvas(
            modifier = Modifier
                .size(200.dp)
        ) {
            val circleRed = Color(0XFFEA3F24)
            val circleBlue = Color(0XFF4561D2)
            val centerOffset = Offset(size.width / 2, size.height / 2)
            val circleRadius = size.minDimension / 3
            val lineThickness = 3.dp.toPx()
            val hollowCircleRadius = 10.dp.toPx()
            // Calculate the end point of the angled line based on the angle
            val angleRad = Math.toRadians(roll.toDouble()).toFloat()
            val rightLineStartOffset = Offset(
                x = centerOffset.x + hollowCircleRadius * cos(angleRad),
                y = centerOffset.y + hollowCircleRadius * sin(angleRad)
            )
            val rightLineEndOffset = Offset(
                x = centerOffset.x + circleRadius * cos(angleRad),
                y = centerOffset.y + circleRadius * sin(angleRad)
            )
            val leftLineStartOffset = Offset(
                x = centerOffset.x - hollowCircleRadius * cos(angleRad),
                y = centerOffset.y - hollowCircleRadius * sin(angleRad)
            )
            val leftLineEndOffset = Offset(
                x = centerOffset.x - circleRadius * cos(angleRad),
                y = centerOffset.y - circleRadius * sin(angleRad)
            )

            // Draw the dashed circle
            drawCircle(
                color = if (roll == 0) circleBlue else circleRed,
                radius = circleRadius,
                style = Stroke(
                    width = lineThickness,
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
                )
            )

            // Draw the hollow circle in the middle of the line
            drawCircle(
                color = if (roll == 0) circleBlue else circleRed,
                radius = hollowCircleRadius,
                center = centerOffset,
                style = Stroke(width = lineThickness)
            )
            if (roll == 0) {
                drawLine(
                    color = circleBlue,
                    start = Offset(0f, centerOffset.y),
                    end = Offset(centerOffset.x - hollowCircleRadius, centerOffset.y),
                    strokeWidth = lineThickness
                )
                drawLine(
                    color = circleBlue,
                    start = Offset(centerOffset.x + hollowCircleRadius, centerOffset.y),
                    end = Offset(size.width, centerOffset.y),
                    strokeWidth = lineThickness
                )
            } else {
                drawLine(
                    color = circleRed,
                    start = Offset(centerOffset.x - circleRadius - lineThickness, centerOffset.y),
                    end = Offset(0f, centerOffset.y),
                    strokeWidth = lineThickness
                )
                drawLine(
                    color = circleRed,
                    start = Offset(centerOffset.x + circleRadius + lineThickness, centerOffset.y),
                    end = Offset(size.width, centerOffset.y),
                    strokeWidth = lineThickness
                )

                // Draw the angled line
                drawLine(
                    color = circleRed,
                    start = leftLineStartOffset,
                    end = leftLineEndOffset,
                    strokeWidth = lineThickness
                )
                drawLine(
                    color = circleRed,
                    start = rightLineStartOffset,
                    end = rightLineEndOffset,
                    strokeWidth = lineThickness
                )
            }
        }
        Spacer(modifier = Modifier.height(16.dp))

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${pitch}º",
                    fontSize = 20.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF333333),
                    textAlign = TextAlign.Center,
                )

                Text(
                    modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                    text = stringResource(id = R.string.vertical),
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF333333),
                    textAlign = TextAlign.Center,
                )
            }

            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${roll}º",
                    fontSize = 20.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF333333),
                    textAlign = TextAlign.Center,
                )

                Text(
                    modifier = Modifier.padding(top = 12.dp, bottom = 12.dp),
                    text = stringResource(id = R.string.horizontal),
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF333333),
                    textAlign = TextAlign.Center,
                )
            }
        }
    }
}

@Composable
fun SpiritCircle(
    modifier: Modifier = Modifier,
    borderColor: Color = Color.Red,
    borderWidth: Dp = 1.dp,
    content: @Composable() BoxScope.() -> Unit,
) {
    Box(modifier) {
        content()
        Canvas(modifier = Modifier.fillMaxSize()) {
            drawCircle(
                color = borderColor,
                radius = (size.minDimension - 2 * borderWidth.toPx()) / 2,
                center = Offset((size.width) / 2, (size.height) / 2),
                style = Stroke(
                    width = borderWidth.toPx(),
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(1f, 4f), 0f)
                )
            )
        }
    }

}

@androidx.compose.ui.tooling.preview.Preview(showBackground = true)
@Composable
fun CustomComponentTest(
    modifier: Modifier = Modifier,
    pitch: Int = 0,
    roll: Int = 0,
    tendingColor: Color = Color.Red,
    achieveColor: Color = Color.Blue
) {
    Box(modifier = modifier) {
        SpiritCircle(
            modifier = Modifier
                .align(Alignment.Center)
                .size(200.dp),
            borderColor = tendingColor,
            borderWidth = 3.dp
        ) {
            Box(
                Modifier
                    .align(Alignment.Center)
                    .rotate(roll.toFloat())
            ) {

                Column(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .width(198.dp)
                        .height(1.dp)
                        .background(Color.Red),
                ) {}
                Box(
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(10.dp)
                        .background(Color.White)
                        .border(1.dp, Color.Red, CircleShape)
                ) {}
            }
        }


    }

}