package org.aihealth.ineck.view.screen.login

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.PasswordVisualTransformation
import androidx.compose.ui.text.input.VisualTransformation
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.AIHPasswordTextField
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.AIHTextField
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.LoginUSAViewModel

@Composable
fun LoginENSignUpScreen(
    viewModel: LoginUSAViewModel
) {
    val isReadChecked by viewModel.isReadChecked.collectAsState()
    val dialogVisible by viewModel.dialogVisible.collectAsState()

    BasePageView(
        title = stringResource(id = R.string.sign_up),
        showBackIcon = true
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
                .padding(bottom = 32.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(48.dp))
            AIHTextField(
                modifier = Modifier
                    .height(IntrinsicSize.Min)
                    .fillMaxWidth(),
                value = viewModel.emailSignUp,
                onValueChange = { viewModel.emailSignUp = it },
                keyboardType = KeyboardType.Email,
                placeholder = stringResource(id = R.string.please_enter_email_account)
            )
            Spacer(modifier = Modifier.height(16.dp))
            AIHPasswordTextField(
                value = viewModel.passwordSignUp,
                onValueChange = {
                    viewModel.passwordSignUp = it
                    viewModel.confirmPassword()
                },
                keyboardType = KeyboardType.Password,
                placeholder = stringResource(id = R.string.please_enter_password)
            )
            if (viewModel.passwordSignUp.isNotBlank()) {
                Spacer(modifier = Modifier.height(16.dp))
                AIHTextField(
                    value = viewModel.passwordConfirm,
                    onValueChange = { viewModel.passwordConfirm = it },
                    keyboardType = KeyboardType.Password,
                    placeholder = stringResource(id = R.string.please_enter_password_again)
                )
                Spacer(modifier = Modifier.height(16.dp))
                PasswordCheckList(
                    modifier = Modifier.fillMaxWidth(),
                    validLength = viewModel.passwordSignUp.length >= 8,
                    validLowercase = viewModel.passwordSignUp.any { it.isDigit() },
                    validUppercase = viewModel.passwordSignUp.any { it.isUpperCase() },
                    validNumber = viewModel.passwordSignUp.any { it.isLowerCase() },
                    validSpecial = viewModel.passwordSignUp.any { it in "!@#$%" },
                )
            }
//            Box {
//                AIHTextField(
//                    value = viewModel.code_sign_up,
//                    onValueChange = { viewModel.code_sign_up = it },
//                    keyboardType = KeyboardType.Number,
//                    placeholder = stringResource(id = R.string.enter_verification_code)
//                )
//                Text(text = if (viewModel.time_sign_up > 0) stringResource(
//                    id = R.string.get_code_after_time,
//                    viewModel.time_sign_up
//                ) else stringResource(
//                    id = R.string.get_code
//                ),
//                    color = Color(0XFF1E4BDF),
//                    fontSize = 14.sp,
//                    modifier = Modifier
//                        .align(Alignment.CenterEnd)
//                        .padding(end = 16.dp)
//                        .pointerInput(Unit) {
//                            detectTapGestures {
//                                viewModel.sendCodeClick()
//                            }
//                        }
//                )
//            }
            Spacer(modifier = Modifier.height(16.dp))
            LoginAgreementView(
                checked = isReadChecked,
                onCheckedChanged = {
                    viewModel.setIsReadChecked(!isReadChecked)
                }
            )
            AIHTextButton(
                text = stringResource(id = R.string.sign_up),
                onClick = {
                    viewModel.signUpClick()
                },
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(top = 30.dp),
                style = TextStyle(
                    fontWeight = FontWeight(400),
                    color = Color(0xFFF7F7F7),
                    fontSize = 20.sp,
                    textAlign = TextAlign.Center
                ),
            )
        }

    }
    AgreementDialog(
        visible = dialogVisible,
        onConfirm = {
            viewModel.setIsReadChecked(true)
            viewModel.setDialogVisible(false)
        },
        onCancel = {
            viewModel.setDialogVisible(false)
        }
    )
}


@Composable
fun PasswordTextField(
    modifier: Modifier = Modifier,
    password: String,
    placeholder: String,
    onValueChange: (String) -> Unit,
) {
    var showPassword by remember { mutableStateOf(false) }
    val showErrorText by remember { mutableStateOf(false) }
    var isDropdownVisible by remember { mutableStateOf(false) }

    Column(modifier = modifier) {
        OutlinedTextField(
            colors = OutlinedTextFieldDefaults.colors(
                focusedTextColor = Color.Black,
                disabledTextColor = Color.Transparent,
                focusedContainerColor = Color.Transparent,
                focusedBorderColor = Color.Transparent,
                unfocusedBorderColor = Color.Transparent,
                disabledBorderColor = Color.Transparent,
                errorBorderColor = Color.Transparent,
                focusedLabelColor = Color.Transparent,
                unfocusedLabelColor = Color.Transparent,
                disabledLabelColor = Color.Transparent,
                errorLabelColor = Color.Transparent
            ),
            value = password,
            onValueChange = {
                onValueChange(it)
            },
            placeholder = { Text(text = placeholder, fontSize = 14.sp, color = Color(0XFF838383)) },
            label = {},
            visualTransformation = if (showPassword) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                if (password.isNotEmpty()) {
                    IconButton(onClick = { showPassword = !showPassword }) {
                        Icon(
                            painter = painterResource(id = if (showPassword) R.drawable.img_password_show else R.drawable.img_password_hide),
                            contentDescription = if (showPassword) "Hide password" else "Show password"
                        )
                    }

                } else {
                    IconButton(onClick = { isDropdownVisible = true }) {
                        Icon(
                            painter = painterResource(R.drawable.ic_msg),
                            contentDescription = "Password rules"
                        )

                    }
                }
            },
            isError = showErrorText,
            singleLine = true,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password),
            modifier = Modifier
                .height(IntrinsicSize.Min)
                .fillMaxWidth()
                .background(color = Color(0XFFF0F0F0), shape = RoundedCornerShape(10.dp))
        )


    }
}

@Composable
fun ConfirmPasswordTextField(
    modifier: Modifier = Modifier,
    confirmPassword: String,
    onConfirmValueChange: (String) -> Unit,
) {
    var showConfirmPassword by remember { mutableStateOf(false) }
    Column(modifier = modifier) {
        OutlinedTextField(
            colors = OutlinedTextFieldDefaults.colors(
                focusedTextColor = Color.Black,
                disabledTextColor = Color.Transparent,
                focusedContainerColor = Color.Transparent,
                focusedBorderColor = Color.Transparent,
                unfocusedBorderColor = Color.Transparent,
                disabledBorderColor = Color.Transparent,
                errorBorderColor = Color.Transparent,
                focusedLabelColor = Color.Transparent,
                unfocusedLabelColor = Color.Transparent,
                disabledLabelColor = Color.Transparent,
                errorLabelColor = Color.Transparent
            ),
            modifier = Modifier
                .height(IntrinsicSize.Min)
                .fillMaxWidth()
                .background(color = Color(0XFFF0F0F0), shape = RoundedCornerShape(10.dp)),
            value = confirmPassword,
            onValueChange = { onConfirmValueChange(it) },
//                label = { Text("请再次输入密码") },
            visualTransformation = if (showConfirmPassword) VisualTransformation.None else PasswordVisualTransformation(),
            trailingIcon = {
                IconButton(onClick = { showConfirmPassword = !showConfirmPassword }) {
                    Icon(
                        painter = painterResource(id = if (showConfirmPassword) R.drawable.img_password_show else R.drawable.img_password_hide),
                        contentDescription = "Toggle password visibility"
                    )
                }

            }
        )
    }
}

@Composable
fun ShowInputPassword() {
    var password by remember { mutableStateOf("") }
    var confirmPassword by remember { mutableStateOf("") }
    var level by remember { mutableIntStateOf(0) }
    var isRule by remember { mutableStateOf(false) }
    Column {
        PasswordTextField(
            modifier = Modifier,
            password = password,
            placeholder = "Please enter password",
            onValueChange = {
                password = it
                level = 0
                if (password.trim().length >= 8) {
                    if (password.contains(Regex("[0-9]"))) {
                        level++
                    }
                    if (password.contains(Regex("[a-z]"))) {
                        level++
                    }
                    if (password.contains(Regex("[A-Z]"))) {
                        level++
                    }
                    if (password.contains("!") || password.contains("@")
                        || password.contains("#") || password.contains("$")
                        || password.contains("%") || password.contains("^")
                        || password.contains("&") || password.contains("*")
                    ) {
                        level++
                    }
                    isRule = level >= 3
                    level = 1
                    if (password.contains("!") || password.contains("@")) {
                        level++
                    }
                    if (password.contains("#") || password.contains("$")) {
                        level++
                    }
                    if (password.contains("%") || password.contains("^")) {
                        level++
                    }
                    if (password.contains("&") || password.contains("*")) {
                        level++
                    }
                    if (password.trim().length >= 12) {
                        level++
                    }
                    LogUtil.i("passwordLevel: $level")
                } else {
                    isRule = false
                    level = 0
                }
//            showErrorText = password.isNotEmpty() && password.length < 8
            },
        )

        ConfirmPasswordTextField(
            confirmPassword = confirmPassword,
            onConfirmValueChange = {
                confirmPassword = it
            })
    }
}