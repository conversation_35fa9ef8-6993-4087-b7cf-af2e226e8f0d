package org.aihealth.ineck.view.screen.my

import android.content.Intent
import android.net.Uri
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import coil.request.ImageRequest
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.VersionUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDialog
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.PromisRecordDirections
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.user
import java.util.Locale


/**
 * 我的页面UI
 */
@Composable
fun MyScreen(
    viewModel: MainViewModel
) {
    /* 注销登录提示对话框显示状态 */
    val isVisibleLogoutTipDialogState = viewModel.myScreen.isVisibleLogoutTipDialog.collectAsState()
//    LogUtil.i("userInMyScreen: ${user}")
    val showDeviceSetting = when (viewModel.homeScreen.currentDeviceType) {
        DeviceType.aiNeck, DeviceType.aiBack -> true
        else -> false
    }
    BasePageView(
        headerContent = {
            MyHeader(modifier = Modifier.fillMaxWidth())
        }
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .background(color = Color(0xFFFAFAFA))
                .verticalScroll(state = rememberScrollState()),
        ) {
            // 头像 和 昵称
            Header(Modifier, user)

            /**
            Spacer(modifier = Modifier.height(6.dp))
            Column(
                Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 18.dp),
            ) {
                // 当用户是会员，并且有效时间大于3天
                if (user.vipStatus && user.vipActiveTime > Limit_Time) {
                    if (currentLocale == Locale.CHINESE) {
                        VipMessageZhGold(
                            vipHelloString = stringResource(id = R.string.hi_a_member_of_aiSpine),
                            vipDescriptionString = "~",
                            nextDescriptionString = stringResource(id = R.string.my_member),
                            onclick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                        )
                    } else {
                        VipMessageEnGold(
                            vipHelloString = stringResource(id = R.string.hi_a_member_of_aiSpine),
                            vipDescriptionString = "~",
                            nextDescriptionString = stringResource(id = R.string.my_member),
                            onclick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                        )
                    }

                }
                // 当用户是会员，并且有效时间不足3天
                else if (user.vipStatus && user.vipActiveTime <= Limit_Time) {
                    if (currentLocale == Locale.CHINESE) {
                        VipMessageZhGold(
                            vipHelloString = stringResource(id = R.string.hi_a_member_of_aiSpine),
                            vipDescriptionString = "~",
                            nextDescriptionString = stringResource(id = R.string.renew_now),
                            onclick = {
                                startScreen(Screen.MemberShipCenter.route + "?renewal=true", false)
                            }
                        )
                    } else {
                        VipMessageEnGold(
                            vipHelloString = stringResource(id = R.string.hi_a_member_of_aiSpine),
                            vipDescriptionString = "~",
                            nextDescriptionString = stringResource(id = R.string.renew_now),
                            onclick = {
                                startScreen(Screen.MemberShipCenter.route + "?renewal=true", false)
                            }
                        )
                    }

                }
                // 当用户不是会员，可以试用
                else if (!user.vipStatus && !user.memberTrial) {
                    if (currentLocale == Locale.CHINESE) {
                        VipMessageNoVipZhSliver(
                            vipHelloString = stringResource(id = R.string.hi_become_a_member_of_aiSpine),
                            vipDescriptionString = stringResource(id = R.string.open_a_healthy_life),
                            nextDescriptionString = stringResource(id = R.string.free_trial),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            }
                        )
                    } else {
                        VipMessageNoVipEnSliver(

                            vipHelloString = stringResource(id = R.string.hi_become_a_member_of_aiSpine),
                            vipDescriptionString = stringResource(id = R.string.open_a_healthy_life),
                            nextDescriptionString = stringResource(id = R.string.free_trial),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            }
                        )
                    }

                } else {
                    if (currentLocale == Locale.CHINESE) {
                        VipMessageNoVipZhSliver(
                            vipHelloString = stringResource(id = R.string.hi_become_a_member_of_aiSpine),
                            vipDescriptionString = stringResource(id = R.string.open_a_healthy_life),
                            nextDescriptionString = stringResource(id = R.string.open_immediately),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            }
                        )
                    } else {
                        VipMessageNoVipEnSliver(
                            vipHelloString = stringResource(id = R.string.hi_become_a_member_of_aiSpine),
                            vipDescriptionString = stringResource(id = R.string.open_a_healthy_life),
                            nextDescriptionString = stringResource(id = R.string.open_immediately),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            }
                        )
                    }

                }

            }
            Spacer(modifier = Modifier.height(6.dp))

             */
            // 在线时长 和 练习时长
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(horizontal = 16.dp)
                    .background(Color.White),
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                TimeItem(
                    modifier = Modifier.weight(0.44f),
                    tag = if (MainViewModel.onlineTime < MainViewModel.trailTimeLimit)
                        R.string.count_member_time
                    else R.string.online_time,
                    unit = R.string.time_day,
                    time = MainViewModel.onlineTime,
                )
                Spacer(modifier = Modifier.size(10.dp))
                TimeItem(
                    modifier = Modifier.weight(0.44f),
                    tag = R.string.practice_time,
                    unit = R.string.time_day,
                    time = MainViewModel.exerciseTime,
                )
            }
            Spacer(modifier = Modifier.height(6.dp))

            // 功能列表
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .wrapContentHeight()
                    .padding(horizontal = 18.dp)
            ) {
                Item(
                    drawableId = R.drawable.img_my_attention,
                    title = stringResource(id = R.string.my_provider),
                    shape = RoundedCornerShape(6.dp, 6.dp, 0.dp, 0.dp),
                    onClick = {
                        startScreen(Screen.MyAttention.route)
                    }
                )
                /**
                Item(
                    drawableId = R.drawable.ic_reminder_set,
                    title = stringResource(id = R.string.reminder_set),
                    onClick = {
                        // TODO: 添加提醒设置功能
//                        startScreen(Screen.Test.route)
                    }
                )
                */
                Item(
                    drawableId = R.drawable.img_health_questionnaire,
                    title = stringResource(id = R.string.my_health_questionnaire),
                    onClick = {
                        startScreen(Screen.QuestionnaireResultScreen.route)
                    }
                )
                IconItem(
                    drawableId = R.drawable.img_my_record,
                    title = stringResource(id = R.string.pain_record),
                    onClick = {
                        startScreen(Screen.PainRecord.route)
                    }
                )
                IconItem(
                    drawableId = R.drawable.ic_neural_record,
                    title = stringResource(id = R.string.neural_record),
                    onClick = {
                        startScreen(Screen.NeuralScaleScreen.route)
                    }
                )
                Item(
                    drawableId = R.drawable.img_my_odi,
                    title = stringResource(id = R.string.health_scale),
                    onClick = {
                        val model = PromisRecordDirections.PromisRecordModel(baseTime = null)
                        startScreen(PromisRecordDirections.actionToOdiPromisRecord(model = model))
                    }
                )
                /**
                Item(
                    drawableId = R.drawable.img_my_odi,
                    title = stringResource(id = R.string.odi_scale),
                    onClick = {
                        val model = PromisRecordDirections.PromisRecordModel(baseTime = null)
                        startScreen(PromisRecordDirections.actionToOdiPromisRecord(model = model))
                    }
                )

                Item(
                    drawableId = R.drawable.ic_promis_scale,
                    title = stringResource(id = R.string.promis_scale),
                    onClick = {
                        // TODO: 添加PROMIS量表功能 - 暂时指向ODI记录界面
                        val model = PromisRecordDirections.PromisRecordModel(baseTime = null)
                        startScreen(PromisRecordDirections.actionToOdiPromisRecord(model = model))
                    }
                )
                */
                Item(
                    drawableId = R.drawable.ic_unit_switching,
                    title = stringResource(id = R.string.unit_switching),
                    onClick = {
                        startScreen(Screen.UnitSettings.route)
                    }
                )
                if (showDeviceSetting) {
                    Item(
                        drawableId = R.drawable.img_my_setting,
                        title = stringResource(id = R.string.set_up_the_device),
                        onClick = {
                            startScreen(Screen.DeviceSettings.route +"?deviceType=${viewModel.homeScreen.currentDeviceType.name}")
                        },
                        subContent = {
                            Text(
                                text = stringResource(id = R.string.my_go_set_up),
                                fontSize = 14.sp,
                                color = Color(0XFF1E4BDF)
                            )
                        }
                    )
                }
                Item(
                    drawableId = R.drawable.my_setting_devices,
                    title = stringResource(id = R.string.third_party_data_sources),
                    onClick = {
                        startScreen(Screen.ThirdPartyDataSources.route)
                    }
                )
                val context = LocalContext.current
                Item(
                    drawableId = R.drawable.img_my_website,
                    title = stringResource(id = R.string.aih_net),
                    onClick = {
                        val uri: Uri = Uri.parse(
                            if (currentLocale == Locale.CHINESE) {
                                "https://health.aihnet.cn/"
                            } else {
                                "https://medical.aihnet.com/"
                            }
                        )
                        val intent = Intent(Intent.ACTION_VIEW, uri)
                        context.startActivity(intent)

                    }
                )
                Item(
                    drawableId = R.drawable.img_my_edition,
                    title = stringResource(id = R.string.my_about),
                    shape = RoundedCornerShape(0.dp, 0.dp, 6.dp, 6.dp),
                    onClick = {
                        startScreen(Screen.MyAbout.route)
                    },
                    subContent = {
                        Text(
                            text = VersionUtil.getVersionName(LocalContext.current),
                            fontSize = 14.sp,
                            color = Color(0XFF666666),
                            fontWeight = FontWeight.Light
                        )
                    }
                )
                Item(
                    drawableId = R.drawable.language_setting,
                    title = stringResource(if (currentLocale == Locale.CHINESE) R.string.chinese else R.string.english),
                    shape = RoundedCornerShape(0.dp, 0.dp, 6.dp, 6.dp),
                    onClick = {
                        LogUtil.i("onClick")
                        startScreen(Screen.LanguageSettings.route, false)
                    }
                )


                AIHOutlinedButton(
                    text = stringResource(id = R.string.logout),
                    onClick = {
                        viewModel.myScreen.changeIsVisibleLogoutTipDialog(true)
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp)
                        .padding(horizontal = 16.dp),
                    fontSize = 18.sp,
                    fontColor = Color(0XFF333333)
                )
            }


        }
    }
    LogoutTipsDialog(
        isVisible = isVisibleLogoutTipDialogState.value,
        onConfirmEvent = {
            viewModel.myScreen.onLogoutClick()
            viewModel.myScreen.changeIsVisibleLogoutTipDialog(false)
        },
        onDismissEvent = {
            viewModel.myScreen.changeIsVisibleLogoutTipDialog(false)
        }
    )
}


@Composable
private fun Header(
    modifier: Modifier = Modifier,
    user: User = User(),
) {
    Row(
        modifier = Modifier
            .height(90.dp)
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectTapGestures {
                    startScreen(Screen.PersonalData.route)
                }
            }
            .background(color = Color.White)
            .padding(start = 18.dp, end = 23.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(60.dp)
        ) {
            AsyncImage(
                model = ImageRequest.Builder(LocalContext.current)
                    .data(user.photo)
                    .crossfade(true) // Optional fade animation
                    .build(),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(64.dp)
                    .border(
                        border = BorderStroke(
                            width = 2.dp,
                            color = Color(0x70CECECE),
                        ),
                        shape = CircleShape,
                    )
                    .clip(CircleShape),
                // 圆形头像，设置图片的缩放属性
                contentScale = ContentScale.Crop,
                placeholder = painterResource(R.drawable.header_2),
                error = painterResource(R.drawable.header_2)
            )
            // 会员图标
            if (user.vipStatus) {
                Image(
                    painter = painterResource(R.drawable.vip_status_header),
                    contentDescription = "会员图标",
                    modifier = Modifier
                        .align(Alignment.TopStart)
                        .size(20.dp)
                )
            }
        }

        Spacer(modifier = Modifier.width(10.dp))
        Text(
            text = user.name,
            fontSize = 24.sp,
            color = Color(0XFF444444),
            fontWeight = FontWeight.Medium
        )
        if (user.vipStatus) {
            Image(
                modifier = Modifier
                    .padding(start = 6.dp)
                    .size(24.dp),
                painter = painterResource(id = R.drawable.ic_vip), contentDescription = "vip"
            )
        }
        Spacer(modifier = Modifier.weight(1F))
        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = stringResource(id = R.string.personal_data),
                fontSize = 14.sp,
                color = Color(0XFF1E4BDF)
            )
            Spacer(modifier = Modifier.width(3.dp))
            Icon(
                painter = painterResource(id = R.drawable.img_next),
                contentDescription = null,
                modifier = Modifier.size(22.dp),
                tint = Color(0XFF999999)
            )
        }
    }

}

/**
 *  注销登录对话框
 *  @param  isVisible   是否显示状态
 *  @param  onConfirmEvent  确认点击事件
 *  @param  onDismissEvent  取消点击事件
 */
@Preview
@Composable
private fun LogoutTipsDialog(
    isVisible: Boolean = true,
    onConfirmEvent: () -> Unit = {},
    onDismissEvent: () -> Unit = {},
) {
    AnimatedVisibility(visible = isVisible) {
        AIHDialog(
            onDismissRequest = onDismissEvent
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(color = Color.Transparent),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                        .wrapContentHeight(),
                    elevation = CardDefaults.cardElevation(0.dp),
                    colors = CardDefaults.cardColors(Color.White),
                    shape = RoundedCornerShape(17.dp),
                ) {
                    Column(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.logout_tips),
                            style = TextStyle(
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Normal,
                                color = Color(0xFF464646),
                            ),
                            modifier = Modifier
                                .fillMaxWidth(.9f)
                                .padding(top = 16.dp)
                        )
                        AIHButton(
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 28.dp, bottom = 12.dp),
                            text = localeResources.getString(R.string.cancel),
                            fontSize = 16.sp,
                            fontColor = Color.White,
                            onClick = { onDismissEvent() }
                        )
                        AIHOutlinedButton(
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(bottom = 28.dp),
                            text = localeResources.getString(R.string.confirm),
                            fontSize = 16.sp,
                            fontColor = Color(0xFF444444),
                            onClick = { onConfirmEvent() }
                        )
                    }
                }
            }
        }
    }
}

@Preview()
@Composable
fun Item(
    @DrawableRes drawableId: Int = R.drawable.img_my_attention,
    title: String = "my attention",
    subContent: @Composable () -> Unit = {},
    shape: Shape = RectangleShape,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = 50.dp)
            .clickable { onClick() }
            .background(Color.White, shape),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(10.dp))
        Image(
            painter = painterResource(drawableId),
            contentDescription = null,
            modifier = Modifier.size(22.dp)
        )
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        subContent()
        Spacer(modifier = Modifier.width(9.dp))
        Icon(
            painter = painterResource(id = R.drawable.img_next),
            contentDescription = null,
            modifier = Modifier.size(22.dp),
            tint = Color(0XFF999999)
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
    AIHDivider(modifier = Modifier.padding(horizontal = 10.dp))
}

@Composable
fun IconItem(
    @DrawableRes drawableId: Int,
    title: String,
    subContent: @Composable () -> Unit = {},
    shape: Shape = RectangleShape,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .height(50.dp)
            .fillMaxWidth()
            .clickable { onClick() }
            .background(Color.White, shape),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(10.dp))
        Icon(
            painter = painterResource(drawableId),
            contentDescription = null,
            modifier = Modifier.size(22.dp),
            tint = Color(0xff7893EC)
        )
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        subContent()
        Spacer(modifier = Modifier.width(9.dp))
        Icon(
            painter = painterResource(id = R.drawable.img_next),
            contentDescription = null,
            modifier = Modifier.size(22.dp),
            tint = Color(0XFF999999)
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
    AIHDivider(modifier = Modifier.padding(horizontal = 10.dp))
}


@Composable
fun TimeItem(
    modifier: Modifier = Modifier,
    @StringRes tag: Int,
    @StringRes unit: Int,
    time: Long,
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize13 = with(density) { 13.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }

    val tagString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                color = Color(0xFF666666),
                fontSize = fontSize14,
                fontWeight = FontWeight.Medium
            )
        ) {
            append(stringResource(id = tag))
        }
    }
    val dayTime = TimeUtil.getDay(time)
    val detailTime =
        TimeUtil.convertSecondsToStandardString((time % (24 * 60 * 60)).toInt() )
    val timeString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                color = Color(0xFF2B56D7),
                fontSize = fontSize16,
                fontWeight = FontWeight.Medium
            )
        ) {
            append(dayTime)
        }
    }
    val unitString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                color = Color(0xFF2B56D7),
                fontSize = fontSize12,
                fontWeight = FontWeight.Medium
            )
        ) {
            append(" ${stringResource(id = unit)}")
        }
    }
    val detailTimeString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                color = Color(0xFF2B56D7),
                fontSize = fontSize14,
                fontWeight = FontWeight.Medium
            )
        ) {
            append(" $detailTime")
        }
    }
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clip(shape = RoundedCornerShape(6.dp))
            .background(color = Color.White),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(color = Color.White),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = timeString + unitString + detailTimeString
            )
            Text(
                modifier = Modifier.padding(top = 5.dp),
                text = tagString
            )
        }
    }
}

@Composable
fun MyHeader(
    modifier: Modifier = Modifier,
) {
    Column(modifier = modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier
                    .align(Alignment.Center)
            ) {
                Text(
                    text = stringResource(id = R.string.my_profile),
                    style = Typography.displayLarge
                )
            }
            Row(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_message),
                    contentDescription = null,
                    tint = Color(0xFF666666),
                    modifier = Modifier
                        .size(16.dp)
                        .pointerInput(Unit) {
                            detectTapGestures {
                                startScreen(Screen.MessageCenter.route, false)
                            }
                        },
                )
            }
        }
    }
}