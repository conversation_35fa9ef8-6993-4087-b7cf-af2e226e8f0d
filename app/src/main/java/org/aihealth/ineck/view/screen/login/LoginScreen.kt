package org.aihealth.ineck.view.screen.login

import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.delay
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.AIHTextButton

/**
 * 用于判断登入页的选择
 * isInChina为true时，显示国内服务器登入界面，该值来自于 application 的 isInChina
 */
@Composable
fun LoginScreen() {
    val isChina by rememberUpdatedState(newValue = isInChina)

    if (isChina) {
        LoginChinaRoute()
    } else {
        LoginUSARoute()
    }

}

@Composable
fun LoginAgreementView(
    checked: Boolean,
    onCheckedChanged: () -> Unit
) {
    LogUtil.i("LoginUSARoute in LoginAgreementView checked = $checked")
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Image(
            painter = painterResource(id = if (checked) R.drawable.img_check else R.drawable.img_uncheck),
            contentDescription = null,
            modifier = Modifier
                .size(16.dp)
                .pointerInput(Unit) {
                    detectTapGestures {
                        onCheckedChanged()
                    }
                },
        )
        val annotatedText = buildAnnotatedString {
            withStyle(style = SpanStyle(fontSize = 12.sp, color = Color(0XFF838383))) {
                append(stringResource(id = R.string.I_have_read_and_agreed_to_the_aih))
            }
            pushStringAnnotation("yhxy", stringResource(id = R.string.user_agreement))
            withStyle(style = SpanStyle(fontSize = 12.sp, color = Color(0XFF1E4BDF))) {
                append(stringResource(id = R.string.user_agreement))
            }
            pop()
            withStyle(style = SpanStyle(fontSize = 12.sp, color = Color(0XFF838383))) {
                append(stringResource(id = R.string.and))
            }
            pushStringAnnotation("yscy", stringResource(id = R.string.privacy_statement))
            withStyle(style = SpanStyle(fontSize = 12.sp, color = Color(0XFF1E4BDF))) {
                append(stringResource(id = R.string.privacy_statement))
            }
            pop()
        }
        ClickableText(
            modifier = Modifier
                .padding(start = 10.dp)
                .fillMaxWidth(),
            text = annotatedText,
            onClick = { offset ->
                annotatedText.getStringAnnotations(
                    tag = "yhxy", start = offset,
                    end = offset
                ).firstOrNull()?.let {
                    startScreen(Screen.UserAgreement.route)
                }
                annotatedText.getStringAnnotations(
                    tag = "yscy", start = offset,
                    end = offset
                ).firstOrNull()?.let {
                    startScreen(Screen.PrivateTerm.route)
                }
            }
        )

    }
}

/**
 * 同意隐私政策和用户协议对话框
 */
@Composable
fun AgreementDialog(
    visible: Boolean,
    onConfirm: () -> Unit,
    onCancel: () -> Unit
) {
    if (visible) {
        Dialog(
            onDismissRequest = { onCancel() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            androidx.compose.animation.AnimatedVisibility(
                visible = visible,
                enter = fadeIn(animationSpec = tween(300)) + scaleIn(
                    initialScale = 0.8f,
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(200)) + scaleOut(
                    targetScale = 0.8f,
                    animationSpec = tween(200)
                )
            ) {
                Column(
                    Modifier
                        .fillMaxWidth(0.9f)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(horizontal = 8.dp, vertical = 24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                Text(
                    text = stringResource(id = R.string.user_agreement_and_privacy_statement),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(16.dp))
                val annotatedText = buildAnnotatedString {
                    withStyle(style = SpanStyle()) {
                        append(stringResource(id = R.string.in_order_to_better_protect_your_legitimate))
                    }
                    pushStringAnnotation(
                        "ystk",
                        "《${stringResource(id = R.string.privacy_statement)}》"
                    )
                    withStyle(style = SpanStyle(color = Color(0XFF1E4BDF))) {
                        append("《${stringResource(id = R.string.privacy_statement)}》")
                    }
                    pop()
                    withStyle(style = SpanStyle()) {
                        append("、")
                    }
                    pushStringAnnotation(
                        "mzsm",
                        "《${stringResource(id = R.string.user_agreement)}》"
                    )
                    withStyle(style = SpanStyle(color = Color(0XFF1E4BDF))) {
                        append("《${stringResource(id = R.string.user_agreement)}》")
                    }
                    pop()

                }
                ClickableText(
                    text = annotatedText,
                    onClick = { offset ->
                        annotatedText.getStringAnnotations(
                            tag = "ystk", start = offset,
                            end = offset
                        ).firstOrNull()?.let {
                            onCancel()
                            startScreen(Screen.PrivateTerm.route)

                        }
                        annotatedText.getStringAnnotations(
                            tag = "mzsm", start = offset,
                            end = offset
                        ).firstOrNull()?.let {
                            onCancel()
                            startScreen(Screen.UserAgreement.route)
                        }
                    }
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHTextButton(
                    text = stringResource(id = R.string.agree),
                    onClick = {
                        onConfirm()
                    },
                    style = TextStyle(
                        fontWeight = FontWeight(400),
                        color = Color(0xFFF7F7F7),
                        fontSize = 20.sp,
                        textAlign = TextAlign.Center
                    ),
                    modifier = Modifier.fillMaxWidth(0.9f)
                )
                Spacer(modifier = Modifier.height(10.dp))
                AIHOutlinedButton(
                    text = stringResource(id = R.string.disagree),
                    onClick = {
                        onCancel()
                    },
                    fontSize = 18.sp,
                    modifier = Modifier.fillMaxWidth(0.9f)
                )
                }
            }

        }
    }
}

