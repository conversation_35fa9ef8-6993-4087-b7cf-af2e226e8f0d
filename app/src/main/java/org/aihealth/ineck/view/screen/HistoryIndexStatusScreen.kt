package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.angles.BaseTime
import org.aihealth.ineck.model.angles.Odi
import org.aihealth.ineck.model.angles.Promis
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SetOnLifeCycleListener
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.OdiRecordDirections
import org.aihealth.ineck.view.directions.PromisRecordDirections
import java.text.SimpleDateFormat
import java.util.Date

@Composable
fun HistoryIndexStatusScreen(
    startTime: String,
    endTime: String,
    isPromis: Boolean
) {
    val list = remember {
        mutableStateListOf<BaseTime>()
    }
    SetOnLifeCycleListener(
        onStart = {
            if (isPromis) {
                apiService.getPromisHistory(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    response?.data?.let {
                        list.addAll(it.promis)
                    }
                }
            } else {
                apiService.getOdiHistory(
                    from_date = startTime,
                    to_date = endTime
                ).enqueueBody { response ->
                    response?.data?.let {
                        list.addAll(it.odi)
                    }
                }
            }
        }
    )
    BasePageView(
        title = stringResource(id = R.string.history_index_status_record),
        showBackIcon = true
    ) {
        if (list.isEmpty()) {
            NullHistoryDataTemplate()
        } else {
            LazyColumn {
                items(list) {
                    Item(baseTime = it)
                }
            }
        }
    }
}

@Preview
@SuppressLint("SimpleDateFormat")
@Composable
private fun Item(
    baseTime: BaseTime = Odi()
) {
    val isPromis = baseTime is Promis
    LogUtil.i("isPromis:$isPromis baseTime:$baseTime")
    Column(
        modifier = Modifier
            .height(70.dp)
            .fillMaxWidth()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1F)
                .padding(horizontal = 8.dp)
        ) {
            Column(modifier = Modifier.align(Alignment.CenterStart)) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = stringResource(
                            id = R.string.history_index_status_title,
                            if (!isPromis) "ODI" else "PROMIS"
                        ),
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0XFF666666)
                    )
                    AIHButton(
                        text = stringResource(id = R.string.view_details),
                        onClick = {
                            if (isPromis) {
                                val model = PromisRecordDirections.PromisRecordModel(
                                    isPromis = isPromis,
                                    showToggleButton = false,
                                    baseTime = baseTime as Promis
                                )
                                startScreen(
                                    PromisRecordDirections.actionToOdiPromisRecord(model = model),
                                    false
                                )
                            } else {
                                val model = OdiRecordDirections.OdiRecordModel(
                                    isPromis = isPromis,
                                    showToggleButton = false,
                                    baseTime = baseTime as Odi
                                )
                                startScreen(
                                    OdiRecordDirections.actionToOdiPromisRecord(model = model),
                                    false
                                )
                            }

                        },
                        modifier = Modifier,
                        fontSize = 10.sp
                    )
                }


                Text(
                    text = stringResource(
                        id = R.string.history_index_status_time,
                        SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(
                            Date(baseTime.datetime * 1000)
                        )
                    ), fontSize = 12.sp, fontWeight = FontWeight.Light, color = Color(0XFF999999)
                )
            }

        }
        AIHDivider()
    }
}

/**
 *  ODI & PROMIS 空数据显示模板
 */
@Preview
@Composable
internal fun NullHistoryDataTemplate() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFFF5F6F8)),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Icon(
            painter = painterResource(id = R.drawable.icon_empty_record_here),
            contentDescription = stringResource(id = R.string.empty_records),
            tint = Color.Black.copy(alpha = .4f),
            modifier = Modifier
                .size(80.dp)
                .padding(12.dp)
        )
        Text(
            text = stringResource(id = R.string.empty_records),
            color = Color.Black.copy(alpha = .4f),
            fontSize = 14.sp,
            fontWeight = FontWeight.Light,
        )
    }
}

/**
 *  加载历史数据状态模板
 */
@Composable
internal fun LoadingHistoryDataTemplate() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFFF5F6F8)),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        CircularProgressIndicator(
            color = Color(0xFF389BFF),
            modifier = Modifier
                .size(80.dp)
                .padding(12.dp)
        )
        Text(
            text = stringResource(id = R.string.loading_records),
            color = Color.Black.copy(alpha = .4f),
            fontSize = 14.sp,
            fontWeight = FontWeight.Light,
            textAlign = TextAlign.Center,
            maxLines = 2,
        )
    }
}

/**
 *  历史数据获取失败状态模板
 *  @param  onClickEvent    点击事件回调
 */
@Composable
internal fun FailureHistoryDataTemplate(
    onClickEvent: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color(0xFFF5F6F8))
            .clickable { onClickEvent() },
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Image(
            painter = painterResource(id = R.drawable.icon_query_result_failure),
            contentDescription = "Load Failure",
            modifier = Modifier
                .size(80.dp)
                .padding(12.dp)
        )
        Text(
            text = stringResource(id = R.string.failure_records),
            color = Color.Black.copy(alpha = .4f),
            fontSize = 14.sp,
            fontWeight = FontWeight.Light,
            textAlign = TextAlign.Center,
            maxLines = 2,
        )
    }
}

@Preview(showBackground = true)
@Composable
fun PreviewFailureHistoryDataTemplate() {
    FailureHistoryDataTemplate { }
}