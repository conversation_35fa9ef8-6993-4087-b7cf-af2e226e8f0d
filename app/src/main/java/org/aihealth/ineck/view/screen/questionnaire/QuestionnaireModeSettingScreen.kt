package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHSelectButton
import org.aihealth.ineck.view.custom.BasePageView
import java.util.Locale

@Preview
@Composable
fun QuestionnaireModeSettingScreen(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
//    LogUtil.i("FormatDeviceScreen MainViewModel:${viewModel}")

    BasePageView(
        modifier = modifier,
        showBackIcon = true,
        headerContent = {
            Text(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(0.8f),
                text = stringResource(id = R.string.questionnaire_auto),
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF141D26),
                    textAlign = TextAlign.Center
                )
            )
        }
    ) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .verticalScroll(
                    rememberScrollState()
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            var selectedIndex by remember {
                mutableIntStateOf(0)
            }
            AIHSelectButton(
                selectedIndex = selectedIndex,
                array = stringArrayResource(id = R.array.device_mode),
                onClick = {
                    selectedIndex = it
                },
                modifier = Modifier
                    .padding(top = 32.dp, bottom = 12.dp)
                    .fillMaxWidth(0.9f)
                    .size(if (currentLocale == Locale.CHINESE) 122.dp else 200.dp, 38.dp)
                    .align(Alignment.CenterHorizontally)
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .height(330.dp)
            ) {
                if (selectedIndex == 0) {
                    Image(
                        painter = painterResource(id = R.drawable.img_devicemode_intervention),
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(187.dp, 243.dp)
                    )
                    Text(
                        text = stringResource(id = R.string.intervention_mode),
                        fontSize = 20.sp,
                        color = Color.Black,
                        modifier = Modifier
                            .align(
                                Alignment.TopCenter
                            )
                    )
                } else {
                    Image(
                        painter = painterResource(id = R.drawable.img_devicemode_passive),
                        contentDescription = null,
                        modifier = Modifier
                            .align(Alignment.Center)
                            .size(187.dp, 243.dp)
                    )
                    Text(
                        text = stringResource(id = R.string.passive_mode),
                        fontSize = 20.sp,
                        color = Color.Black,
                        modifier = Modifier
                            .align(
                                Alignment.TopCenter
                            )
                    )
                }

            }
            Text(
                text = stringResource(id = if (selectedIndex == 0) R.string.intervention_mode_tip else R.string.passive_mode_tip),
                fontSize = 18.sp,
                color = Color(0XFF444444),
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(bottom = 32.dp)
            )
            AIHButton(
                text = stringResource(id = R.string.welcome_start),
                onClick = {
                    onClick()
                },
                modifier = Modifier
                    .padding(bottom = 72.dp)
                    .fillMaxWidth(0.9f),
                fontSize = 20.sp,
                fontColor = Color(0XFFF7F7F7)
            )

        }
    }


}