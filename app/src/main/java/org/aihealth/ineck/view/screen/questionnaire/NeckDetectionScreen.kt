package org.aihealth.ineck.view.screen.questionnaire

import android.Manifest
import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.LifecycleOwner
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.notification.FloatingCameraService
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.view.custom.AIHDialog
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.dialog.AIHTipButtonDialog
import org.aihealth.ineck.view.screen.vcguide.CloseDetectingDialog
import org.aihealth.ineck.view.screen.vcguide.PreviousGuideScreen
import org.aihealth.ineck.view.screen.vcguide.VCDetectedScreen
import org.aihealth.ineck.view.screen.vcguide.VCDetectingScreen
import org.aihealth.ineck.view.screen.vcguide.VCFaceDetectingScreen
import org.aihealth.ineck.view.screen.vcguide.VCNeckDetectingScreen
import org.aihealth.ineck.viewmodel.DetectedResult
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectedError
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideProcessState

@Composable
fun NeckDetectionScreen(
    modifier: Modifier = Modifier,
    viewModel: org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onDismissEvent: (VCDetectingResult) -> Unit,
) {
    val pagerState = rememberPagerState(
        initialPage = VCGuideProcessState.PreviousGuidePage.toPageNumber(),
    )

    // scope
    val dialogScope = rememberCoroutineScope()
    val isSpeechEnable = remember {
        TextToSpeech.isSpeakingEnable()
    }
    /* 当前静音状态 */
    val isMuteState = viewModel.isMuteState.collectAsState()

    LaunchedEffect(Unit) {
        // 当设备类型改变时，停止悬浮窗服务
        if (FloatingCameraService.isServiceRunning.value == true) {
            activity.stopService(Intent(activity, FloatingCameraService::class.java))
        }
    }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF4F4F4),
            ),
            shape = RoundedCornerShape(10.dp),
        ) {
            HorizontalPager(
                state = pagerState,
                count = 3,
                modifier = Modifier,
                userScrollEnabled = false
            ) { page ->
                when (page) {
                    VCGuideProcessState.PreviousGuidePage.toPageNumber() -> {
                        PreviousGuideScreen(
                            modifier = Modifier.fillMaxWidth(),
                            onIgnoreEvent = {
                                onDismissEvent(
                                    VCDetectingResult.DetectingError(
                                        VCDetectedError.Cancel()
                                    )
                                )
                            },
                            onNextEvent = {
                                if (ActivityResultUtils.checkPermissions(arrayOf(Manifest.permission.CAMERA))) {
                                    viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                                    dialogScope.launch {
                                        pagerState.scrollToPage(VCGuideProcessState.DetectingPage.toPageNumber())
                                    }
                                } else {
                                    viewModel.showPowerDialogVisibleState = true
                                }
                            }
                        )
                    }

                    VCGuideProcessState.DetectingPage.toPageNumber() -> {
                        // 校准过程中的分页
                        val detectingPagerState = rememberPagerState(0)
                        VCDetectingScreen(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                            pagerState = detectingPagerState,
                            pagerCount = 2,
                        ) { currentDetectingPager ->
                            when (currentDetectingPager) {
                                0 -> {
                                    VCNeckDetectingScreen(
                                        modifier = Modifier.fillMaxWidth(),
                                        isMuteState = isMuteState.value,
                                        isVoiceEnable = isSpeechEnable,
                                        changeMuteState = {
                                            viewModel.changeCurrentMuteState(!isMuteState.value)
                                        },
                                        nextPage = {
                                            LogUtil.d("VCHorizontalDetectingScreen next Pager")
                                            dialogScope.launch {
                                                detectingPagerState.scrollToPage(1)
                                            }
                                        },
                                        timeOut = {
                                            LogUtil.d("VCHorizontalDetectingScreen time Out")

                                            viewModel.changeDetectedResult(
                                                VCDetectingResult.DetectingError(
                                                    VCDetectedError.Timeout()
                                                )
                                            )
                                            viewModel.changeGuideDetectProcessState(
                                                VCGuideProcessState.DetectedPage
                                            )
                                            dialogScope.launch {
                                                pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                            }
                                        }
                                    )
                                }

                                1 -> {
                                    VCFaceDetectingScreen(
                                        modifier = Modifier.fillMaxWidth(),
                                        isMuteState = isMuteState.value,
                                        isVoiceEnable = isSpeechEnable,
                                        changeMuteState = {
                                            viewModel.changeCurrentMuteState(!isMuteState.value)
                                        },
                                        nextPage = { it ->
                                            viewModel.changeDetectedResult(it)
                                            viewModel.changeGuideDetectProcessState(
                                                VCGuideProcessState.DetectedPage
                                            )
                                            dialogScope.launch {
                                                pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                detectingPagerState.scrollToPage(0)
                                            }
                                        },
                                        lifecycleOwner = lifecycleOwner,
                                        timeOut = {
                                            viewModel.changeDetectedResult(
                                                VCDetectingResult.DetectingError(
                                                    VCDetectedError.Timeout()
                                                )
                                            )
                                            viewModel.changeGuideDetectProcessState(
                                                VCGuideProcessState.DetectedPage
                                            )
                                            dialogScope.launch {
                                                pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                detectingPagerState.scrollToPage(0)
                                            }
                                        }
                                    )
                                }
                            }

                        }
                    }

                    VCGuideProcessState.DetectedPage.toPageNumber() -> {
                        LogUtil.i(
                            "VCGuideScreen",
                            "${viewModel.detectedResult.collectAsState().value}"
                        )
                        VCDetectedScreen(
                            modifier = Modifier.fillMaxWidth(),
                            viewModel = viewModel,
                            onDismissEvent = {
                                dialogScope.launch {
                                    pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
                                }
                                onDismissEvent(it)
                            },
                            tryAgainEvent = {
                                viewModel.changeGuideDetectProcessState(VCGuideProcessState.PreviousGuidePage)

                                dialogScope.launch {
                                    pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
                                }
                            }
                        )
                    }
                }
            }


        }
        if (pagerState.currentPage != 0 && pagerState.currentPage != 2) {
            CloseDetectingDialog(
                modifier = Modifier
                    .padding(top = 10.dp),
                onDismissEvent = {
                    dialogScope.launch {
//                                pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
//                                detectingPagerState.scrollToPage(0)
                    }
                    onDismissEvent(VCDetectingResult.DetectingError(VCDetectedError.Cancel()))
                }
            )
        }
    }


    ShowPowerDialog(viewModel)

}


/**
 * 颈椎检测使用说明
 */
@Composable
private fun NeckDetectPostDialog(
    viewModel: MainViewModel,
    onStart: () -> Unit = {},
    onSkip: () -> Unit = {}
) {
    if (viewModel.homeScreen.showEnterAiNeckDetect) {
        Dialog(
            onDismissRequest = {
                viewModel.homeScreen.showEnterAiNeckDetect = false
            }
        ) {
            Column(
                modifier = Modifier
                    .width(300.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.detec_success),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF464646),
                        letterSpacing = 0.63.sp,
                    )
                )
                Spacer(modifier = Modifier.height(5.dp))
                Text(
                    text = stringResource(id = R.string.detect_guide_neck_text),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF999999),
                        textAlign = TextAlign.Center,
                        letterSpacing = 0.49.sp,
                    )
                )
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.homeScreen.showEnterAiNeckDetect = false
//                                startScreen(Screen.QuestionnaireNeckDetection.route, true)
                                onStart()
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.start_now),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.homeScreen.showEnterAiNeckDetect = false
                                onSkip()
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.skip),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}

/**
 *  检测引导结果对话框
 */
@Composable
private fun DetectGuideResultDialog(
    isVisible: Boolean,
    detectedResult: DetectedResult,
    onConfirmEvent: () -> Unit,
    onDismissEvent: () -> Unit,
    onRetryEvent: () -> Unit
) {
    AnimatedVisibility(visible = isVisible && detectedResult != DetectedResult.None) {
        AIHDialog(onDismissRequest = onDismissEvent) {
            Card(
                modifier = Modifier
                    .fillMaxWidth(.8f)
                    .height(166.dp),
                elevation = CardDefaults.cardElevation(0.dp),
                colors = CardDefaults.cardColors(Color.White),
                shape = RoundedCornerShape(17.dp),
            ) {
                ConstraintLayout(modifier = Modifier.fillMaxSize()) {
                    val (title, text, buttonGroup) = createRefs()
                    val titleGuideline = createGuidelineFromTop(.25f)
                    val centerGuideline = createGuidelineFromTop(.5f)
                    val bottomGuideline = createGuidelineFromBottom(.3f)
                    when (detectedResult) {
                        DetectedResult.DetectedFailure -> {
                            Text(
                                text = stringResource(id = R.string.detect_guide_failure),
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF464646),
                                    letterSpacing = 0.63.sp,
                                ),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.constrainAs(title) {
                                    start.linkTo(parent.start, 20.dp)
                                    top.linkTo(titleGuideline)
                                    end.linkTo(parent.end, 20.dp)
                                    bottom.linkTo(titleGuideline)
                                }
                            )
                            Text(
                                text = stringResource(id = R.string.detect_guide_no_face),
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.W400,
                                    color = Color(0xFF999999),
                                    letterSpacing = 0.49.sp,
                                ),
                                maxLines = 2,
                                textAlign = TextAlign.Center,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.constrainAs(text) {
                                    start.linkTo(parent.start, 36.dp)
                                    top.linkTo(centerGuideline)
                                    end.linkTo(parent.end, 36.dp)
                                    bottom.linkTo(centerGuideline)
                                }
                            )
                            Row(
                                modifier = Modifier
                                    .border(width = 1.dp, color = Color(0xFFD9D9D9))
                                    .constrainAs(buttonGroup) {
                                        start.linkTo(parent.start, 36.dp)
                                        top.linkTo(bottomGuideline)
                                        end.linkTo(parent.end, 36.dp)
                                        bottom.linkTo(parent.bottom)
                                        height = Dimension.fillToConstraints
                                    },
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                TextButton(
                                    onClick = onDismissEvent,
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .background(color = Color.Transparent)
                                        .weight(1f),
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.detect_guide_next_time),
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.W400,
                                            color = Color(0xFF999999),
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Center
                                    )
                                }
                                Box(
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .width(1.dp)
                                        .background(color = Color(0xFFD9D9D9))
                                )
                                TextButton(
                                    onClick = onRetryEvent,
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .background(color = Color.Transparent)
                                        .weight(1f),
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.detect_guide_try_again),
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.W400,
                                            color = Color(0xFF6181E9),
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }

                        is DetectedResult.DetectedSuccess -> {
                            Text(
                                text = stringResource(id = R.string.detect_guide_success),
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF464646),
                                    letterSpacing = 0.63.sp,
                                ),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.constrainAs(title) {
                                    start.linkTo(parent.start)
                                    top.linkTo(titleGuideline)
                                    end.linkTo(parent.end)
                                    bottom.linkTo(titleGuideline)
                                }
                            )
                            Text(
                                text = stringResource(id = R.string.detect_guide_success_complete),
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.W400,
                                    color = Color(0xFF999999),
                                    letterSpacing = 0.49.sp,
                                ),
                                maxLines = 2,
                                textAlign = TextAlign.Center,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.constrainAs(text) {
                                    start.linkTo(parent.start, 36.dp)
                                    top.linkTo(centerGuideline)
                                    end.linkTo(parent.end, 36.dp)
                                    bottom.linkTo(centerGuideline)
                                }
                            )
                            Row(
                                modifier = Modifier
                                    .border(width = 1.dp, color = Color(0xFFD9D9D9))
                                    .constrainAs(buttonGroup) {
                                        start.linkTo(parent.start, 36.dp)
                                        top.linkTo(bottomGuideline)
                                        end.linkTo(parent.end, 36.dp)
                                        bottom.linkTo(parent.bottom)
                                        height = Dimension.fillToConstraints
                                    },
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                TextButton(
                                    onClick = onConfirmEvent,
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .background(color = Color.Transparent)
                                        .weight(1f),
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.detect_guide_i_known),
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.W400,
                                            color = Color(0xFF999999),
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Center,
                                        modifier = Modifier.fillMaxSize()
                                    )
                                }
                            }
                        }

                        DetectedResult.DetectedTimeout -> {
                            Text(
                                text = stringResource(id = R.string.detect_guide_failure),
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    color = Color(0xFF464646),
                                    letterSpacing = 0.63.sp,
                                ),
                                textAlign = TextAlign.Center,
                                modifier = Modifier.constrainAs(title) {
                                    start.linkTo(parent.start)
                                    top.linkTo(titleGuideline)
                                    end.linkTo(parent.end)
                                    bottom.linkTo(titleGuideline)
                                }
                            )
                            Text(
                                text = stringResource(id = R.string.detect_guide_time_out),
                                style = TextStyle(
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.W400,
                                    color = Color(0xFF999999),
                                    letterSpacing = 0.49.sp,
                                ),
                                maxLines = 2,
                                textAlign = TextAlign.Center,
                                overflow = TextOverflow.Ellipsis,
                                modifier = Modifier.constrainAs(text) {
                                    start.linkTo(parent.start, 36.dp)
                                    top.linkTo(centerGuideline)
                                    end.linkTo(parent.end, 36.dp)
                                    bottom.linkTo(centerGuideline)
                                }
                            )
                            Row(
                                modifier = Modifier
                                    .border(width = 1.dp, color = Color(0xFFD9D9D9))
                                    .constrainAs(buttonGroup) {
                                        start.linkTo(parent.start, 36.dp)
                                        top.linkTo(bottomGuideline)
                                        end.linkTo(parent.end, 36.dp)
                                        bottom.linkTo(parent.bottom)
                                        height = Dimension.fillToConstraints
                                    },
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                TextButton(
                                    onClick = onDismissEvent,
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .background(color = Color.Transparent)
                                        .weight(1f),
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.detect_guide_next_time),
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.W400,
                                            color = Color(0xFF999999),
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Center
                                    )
                                }
                                Box(
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .width(1.dp)
                                        .background(color = Color(0xFFD9D9D9))
                                )
                                TextButton(
                                    onClick = onRetryEvent,
                                    modifier = Modifier
                                        .fillMaxHeight()
                                        .background(color = Color.Transparent)
                                        .weight(1f),
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.detect_guide_try_again),
                                        style = TextStyle(
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.W400,
                                            color = Color(0xFF6181E9),
                                        ),
                                        maxLines = 1,
                                        overflow = TextOverflow.Ellipsis,
                                        textAlign = TextAlign.Center
                                    )
                                }
                            }
                        }

                        DetectedResult.None -> { /* empty */
                            CircularProgressIndicator(
                                modifier = Modifier
                                    .size(58.dp)
                                    .constrainAs(text) {
                                        start.linkTo(parent.start)
                                        top.linkTo(parent.top)
                                        end.linkTo(parent.end)
                                        bottom.linkTo(parent.bottom)
                                    },
                                color = Color(0xFF6181E9)
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 * 权限说明弹窗
 */
@Composable
private fun ShowPowerDialog(
    viewModel: org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel
) {
    if (viewModel.showPowerDialogVisibleState) {
        Dialog(onDismissRequest = { viewModel.showPowerDialogVisibleState = false }) {
            Column(
                modifier = Modifier
                    .width(300.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White)
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.camera_permission),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 40.dp),
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                                ActivityResultUtils.requestPermissions(
                                    permissions = arrayOf(Manifest.permission.CAMERA),
                                    onAllGranted = {
                                    },
                                    onProhibited = {
                                        if (SPUtil.getBoolean(
                                                SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION,
                                                true
                                            )
                                        ) {
                                            SPUtil.putBoolean(
                                                SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION,
                                                false
                                            )
                                        } else {
                                            viewModel.showPowerDialogVisibleState =
                                                false
                                            DialogUtil.showToast {
                                                AIHTipButtonDialog(
                                                    text = localeResources.getString(R.string.permission_denied),
                                                    buttonText = localeResources.getString(
                                                        R.string.to_authorization
                                                    ),
                                                    onClick = {
                                                        APPUtil.goAPPDetail()
                                                    }
                                                ) {
                                                    DialogUtil.hideToast()
                                                }
                                            }
                                        }
                                    }
                                )
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}
