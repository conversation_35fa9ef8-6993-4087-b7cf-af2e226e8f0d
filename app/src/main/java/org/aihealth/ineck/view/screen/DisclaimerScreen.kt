package org.aihealth.ineck.view.screen

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.BasePageView

/**
 * 免责条款UI
 */
@Composable
fun DisclaimerScreen(
) {
    BasePageView(
        title = stringResource(id = R.string.disclaimer),
        showBackIcon = true
    ) {
        Column(modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState()), horizontalAlignment = Alignment.CenterHorizontally){
            Text(text = stringResource(id = R.string.mztk))
        }
    }
}