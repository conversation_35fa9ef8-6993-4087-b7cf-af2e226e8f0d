package org.aihealth.ineck.view.screen.healquestionnairehistory

import android.annotation.SuppressLint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.AiNeckQuestionnaireHistoryModel
import org.aihealth.ineck.model.angles.BaseResponse
import org.aihealth.ineck.network.NetWork.enqueue
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toDp
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshIndicator
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshLayout
import org.aihealth.ineck.view.screen.defaultscreen.NoDataScreen
import org.aihealth.ineck.view.screen.defaultscreen.ServerErrorScreen
import org.aihealth.ineck.view.screen.exercise.UserExerciseCardLoadingContent
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.QuestionnaireReportResultViewModel
import org.aihealth.ineck.viewmodel.QuestionnaireResultLoadState
import org.aihealth.ineck.viewmodel.QuestionnaireResultViewModel
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Composable
fun AiNeckQuestionnaireHistoryScreen(
    modifier: Modifier = Modifier,
    viewModel: QuestionnaireResultViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val coroutineScope = rememberCoroutineScope()

    LogUtil.i("viewmodel: $viewModel")
    val list = viewModel.aiNeckDataModeState.collectAsState()
    /* 处理生命周期 */
    DisposableEffect(key1 = lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_START) {
                coroutineScope.launch {
                    apiService.getAiNeckHistory().enqueue(
                        onResponse = { _, response ->
                            val gson = Gson()
                            try {
                                val data = gson.fromJson(response, BaseResponse::class.java)
                                if (data.code == 0) {
                                    viewModel.aiNeckDataModeState.update {
                                        QuestionnaireResultLoadState.Success(emptyList())
                                    }
                                }
                            } catch (e: Exception) {
                                try {
                                    val listType = object :
                                        TypeToken<List<AiNeckQuestionnaireHistoryModel>>() {}.type
                                    val reList: List<AiNeckQuestionnaireHistoryModel> =
                                        gson.fromJson(
                                            response?.asJsonArray,
                                            listType
                                        )
                                    viewModel.aiNeckDataModeState.update {
                                        QuestionnaireResultLoadState.Success(reList)
                                    }
                                    LogUtil.i("getAiNeckHistory success")
                                } catch (e: Exception) {
                                    viewModel.aiNeckDataModeState.update {
                                        QuestionnaireResultLoadState.Error(e)
                                    }
                                }
                            }
                        },
                        onFailure = { _, throwable ->
                            QuestionnaireResultLoadState.Error(throwable)
                        }
                    )
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    AIHRefreshLayout(
        state = viewModel.refreshState,
        onRefresh = {
            coroutineScope.launch {
                apiService.getAiNeckHistory().enqueue(
                    onResponse = { _, response ->
                        val gson = Gson()
                        try {
                            val data = gson.fromJson(response, BaseResponse::class.java)
                            if (data.code == 0) {
                                viewModel.aiNeckDataModeState.update {
                                    QuestionnaireResultLoadState.Success(emptyList())
                                }
                            }
                        } catch (e: Exception) {
                            try {
                                val listType = object :
                                    TypeToken<List<AiNeckQuestionnaireHistoryModel>>() {}.type
                                val reList: List<AiNeckQuestionnaireHistoryModel> = gson.fromJson(
                                    response?.asJsonArray,
                                    listType
                                )
                                viewModel.aiNeckDataModeState.update {
                                    QuestionnaireResultLoadState.Success(reList)
                                }
                                LogUtil.i("getAiNeckHistory success")
                            } catch (e: Exception) {
                                viewModel.aiNeckDataModeState.update {
                                    QuestionnaireResultLoadState.Error(e)
                                }
                            }
                        }
                    },
                    onFailure = { _, throwable ->
                        QuestionnaireResultLoadState.Error(throwable)
                    }
                )
                viewModel.refreshState.refreshing = false
            }
        },
        indicator = {
            AIHRefreshIndicator(
                state = viewModel.refreshState,
                fontColor = Color.White,
                iconColor = Color(0XFFF1F1F1)
            )
        }
    ) {
        when (list.value) {
            is QuestionnaireResultLoadState.Loading -> {
                repeat(3) { count ->
                    UserExerciseCardLoadingContent(count = count)
                }
            }

            is QuestionnaireResultLoadState.Success -> {
                val historyList = (list.value as QuestionnaireResultLoadState.Success).data
                if (historyList.isNotEmpty()) {
                    Column(
                        modifier = modifier.fillMaxSize()
                    ) {
                        historyList
                            .sortedByDescending {
                                it.dateTime
                            }
                            .forEach {
                                AiNeckQuestionnaireHistoryItem(
                                    model = it,
                                    modifier = Modifier
                                        .padding(top = 12.dp)
                                        .padding(horizontal = 12.dp),
                                    onClickQuestionnaire = { info ->
                                        val objId = info.let { url ->
                                            val regex = Regex("obj_id=([^&]*)")
                                            val matchResult = regex.find(url)
                                            val objId = matchResult?.groups?.get(1)?.value
                                            objId ?: ""
                                        }
                                        apiService.getAiNeckDetailHistory(
                                            objId = objId
                                        ).enqueue(
                                            onResponse = { _, response ->
                                                response?.questions?.let { list ->
                                                    if (list.isNotEmpty()) {

                                                        list.forEach { item ->
                                                            QuestionnaireReportResultViewModel.questionList.forEach { it ->
                                                                it.answer.forEach { answer ->
                                                                    answer.check = false
                                                                }
                                                                if (item.questionId == it.id) {
                                                                    it.answer.forEach { answer ->
                                                                        item.answer.forEach { answerItem ->
                                                                            if (answer.select == answerItem) {
                                                                                answer.check = true
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        LogUtil.d("aineck, list:${QuestionnaireReportResultViewModel.questionList.toJson()}")

                                                        startScreen(
                                                            Screen.QuestionnaireReportResultScreen.route,
                                                            false
                                                        )
                                                    }
                                                }
                                            },
                                            onFailure = { _, throwable ->
                                                LogUtil.d("getAiNeckDetailHistory failure: ${throwable.message}")
                                            }
                                        )
                                    },
                                    onClickHealthScale = { healthStatus ->
                                    },
                                    onClickExercise = {
                                        MainViewModel.pageIndex = 2
                                        popScreen(Screen.Main.route)
                                    }
                                )
                            }
                    }
                }

            }

            else -> {
            }
        }
    }
    when (list.value) {
        is QuestionnaireResultLoadState.Success -> {
            val historyList = (list.value as QuestionnaireResultLoadState.Success).data
            if (historyList.isEmpty()) {
                NoDataScreen()
            }
        }

        is QuestionnaireResultLoadState.Error -> {
            ServerErrorScreen()
        }

        else -> {}
    }
}

@SuppressLint("SimpleDateFormat")
@Composable
fun AiNeckQuestionnaireHistoryItem(
    modifier: Modifier = Modifier,
    model: AiNeckQuestionnaireHistoryModel,
    onClickQuestionnaire: (String) -> Unit = {},
    onClickHealthScale: (String) -> Unit = {},
    onClickExercise: () -> Unit = {},
) {
    val status: Int = when (model.healthStatus) {
        "健康" -> {
            0
        }

        "亚健康" -> {
            1
        }

        "需要警惕" -> {
            2
        }

        "轻度受损" -> {
            3
        }

        "严重受损" -> {
            4
        }

        else -> {
            -1
        }
    }
    val health = when (status) {
        0 -> {
            stringResource(id = R.string.health)
        }

        1 -> {
            stringResource(id = R.string.subclinical)
        }

        2 -> {
            stringResource(id = R.string.needs_caution)
        }

        3 -> {
            stringResource(id = R.string.light_loss)
        }

        4 -> {
            stringResource(id = R.string.serious_loss)
        }

        else -> {
            "None"
        }
    }
    val colorList = listOf(
        Color(0xFF6EA944),
        Color(0xFFA0E14F),
        Color(0xFFD5ED28),
        Color(0xFFF1C114),
        Color(0xFFF17216),
        Color(0xFFFB593F)
    )

    val date = SimpleDateFormat(
        "yyyy-MM-dd",
        Locale.getDefault()
    ).format(Date(model.dateTime.toLong() * 1000))
    Column(
        modifier = modifier
            .background(color = Color(0xFF3C59CF), shape = RoundedCornerShape(size = 8.dp))
            .padding(12.dp)
            .fillMaxWidth(),
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.Bottom,
        ) {
            Text(
                text = stringResource(id = R.string.neck_assessment),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFFFFFFFF),
                    textAlign = TextAlign.Center,
                )
            )
            Text(
                text = date,
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFE1E8FB),
                    textAlign = TextAlign.Center,
                )
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 12.dp),
        ) {
            Column(
                modifier = Modifier
                    .height(22.dp)
                    .background(color = Color(0xB2E1E8FB), shape = RoundedCornerShape(size = 4.dp))
                    .padding(start = 10.dp, top = 4.dp, end = 10.dp, bottom = 4.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.assessment_duration, model.duration),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF2B56D7),
                        textAlign = TextAlign.Center,
                    )
                )
            }
            Row(
                modifier = Modifier
                    .padding(start = 8.dp)
                    .height(22.dp)
                    .background(color = Color(0xB2E1E8FB), shape = RoundedCornerShape(size = 4.dp))
                    .padding(start = 10.dp, top = 4.dp, end = 10.dp, bottom = 4.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.health_status) + ":",
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF2B56D7),
                        textAlign = TextAlign.Center,
                    )
                )
                Text(
                    text = health,
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(400),
                        color = colorList[status],
                        textAlign = TextAlign.Center,
                    )
                )
                LogUtil.i("health: $health")
            }
        }
        Column(
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth()
                .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 8.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                modifier = Modifier
                    .padding(top = 10.dp)
                    .width(265.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                var parentSize by remember { mutableStateOf(IntSize.Zero) }
                val childSize = parentSize.width.toFloat() / 5
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .onGloballyPositioned { coordinates ->
                            parentSize = coordinates.size
                        },
                    horizontalArrangement = Arrangement.SpaceAround,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = if (status == 0) R.drawable.icon_health_selected else R.drawable.icon_health),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 0) 18.dp else 20.dp)
                    )
                    Image(
                        painter = painterResource(id = if (status == 1) R.drawable.icon_poor_health_selected else R.drawable.icon_poor_health),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 1) 18.dp else 20.dp)

                    )
                    Image(
                        painter = painterResource(id = if (status == 2) R.drawable.icon_vigilant_health_selected else R.drawable.icon_vigilant_health),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 2) 18.dp else 20.dp)

                    )
                    Image(
                        painter = painterResource(id = if (status == 3) R.drawable.icon_poor_lose_selected else R.drawable.icon_poor_lose),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 3) 18.dp else 20.dp)

                    )
                    Image(
                        painter = painterResource(id = if (status == 4) R.drawable.icon_serious_lose_selected else R.drawable.icon_serious_lose),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 4) 18.dp else 20.dp)
                    )
                }
                Spacer(
                    modifier = Modifier
                        .height(8.dp)
                        .fillMaxWidth()
                )
                Box(
                    modifier = Modifier
                        .padding(top = 5.dp)
                        .fillMaxWidth(),
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_health_localtion),
                        contentDescription = "location",
                        modifier = Modifier
                            .padding(start = (status * childSize + childSize / 2).toDp())
                    )

                }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(7.dp)
                        .background(
                            shape = RoundedCornerShape(4.dp),
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color(0xFF6EA944),
                                    Color(0xFFA0E14F),
                                    Color(0xFFD5ED28),
                                    Color(0xFFF1C114),
                                    Color(0xFFF17216),
                                    Color(0xFFFB593F)
                                ),
                            )
                        )
                )

                Canvas(
                    modifier = Modifier
                        .padding(top = 5.dp)
                        .fillMaxWidth()
                        .height(10.dp)
                ) {
                    val path1 = Path().apply {
                        moveTo(childSize, 0.664062f)
                        lineTo(childSize, 6.16406f)
                        lineTo(1f, 6.16406f)
                        lineTo(1f, 0.164062f)
                    }
                    drawPath(path1, Color(0xFF93CA65), style = Stroke(width = 2f))
                    val path2 = Path().apply {
                        moveTo(2 * childSize, 0.664062f)
                        lineTo(2 * childSize, 6.16406f)
                        lineTo(childSize, 6.16406f)
                    }
                    drawPath(path2, Color(0xFFBFE951), style = Stroke(width = 2f))
                    val path3 = Path().apply {
                        moveTo(3 * childSize, 0.664062f)
                        lineTo(3 * childSize, 6.16406f)
                        lineTo(2 * childSize, 6.16406f)
                    }
                    drawPath(path3, Color(0xFFDEE73A), style = Stroke(width = 2f))
                    val path4 = Path().apply {
                        moveTo(4 * childSize, 0.664062f)
                        lineTo(4 * childSize, 6.16406f)
                        lineTo(3 * childSize, 6.16406f)
                    }
                    drawPath(path4, Color(0xFFF2C72C), style = Stroke(width = 2f))
                    val path5 = Path().apply {
                        moveTo(5 * childSize - 2, 0.664062f)
                        lineTo(5 * childSize - 2, 6.16406f)
                        lineTo(4 * childSize, 6.16406f)
                    }
                    drawPath(path5, Color(0xFFF37E30), style = Stroke(width = 2f))

                }

                Row(
                    modifier = Modifier
                        .padding(bottom = 5.dp)
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceAround,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        modifier = Modifier
                            .width(childSize.toDp()),
                        text = stringResource(id = R.string.health),
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight(400),
                            color = if (status == 0) Color(0xFF93CA65) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(childSize.toDp()),
                        text = stringResource(id = R.string.subclinical),
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight(400),
                            color = if (status == 1) Color(0xFFBFE951) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(childSize.toDp()),
                        text = stringResource(id = R.string.needs_caution),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(400),
                            color = if (status == 2) Color(0xFFDEE73A) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(childSize.toDp()),
                        text = stringResource(id = R.string.light_loss),
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight(400),
                            color = if (status == 3) Color(0xFFF2C72C) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(childSize.toDp()),
                        text = stringResource(id = R.string.serious_loss),
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight(400),
                            color = if (status == 4) Color(0xFFF37E30) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }

            Row(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround,
            ) {
                Spacer(modifier = Modifier.width(10.dp))
                AIHButton(
                    modifier = Modifier
                        .weight(1f)
                        .height(26.dp),
                    fontSize = 10.sp,
                    text = stringResource(id = R.string.questionnaire_info),
                    onClick = {
                        onClickQuestionnaire(model.info)
                    }
                )
                Spacer(modifier = Modifier.width(10.dp))
                AIHButton(
                    modifier = Modifier
                        .weight(1f)
                        .height(26.dp),
                    fontSize = 10.sp,
                    text = stringResource(id = R.string.go_exercise),
                    onClick = {
                        onClickExercise()
                    }
                )
                Spacer(modifier = Modifier.width(10.dp))

            }
        }

    }
}

//@Preview(showBackground = true)
//@Composable
//private fun PreviewItem() {
//    AiNeckQuestionnaireHistoryItem(
//        model = AiNeckQuestionnaireHistoryModel(
//            dateTime = "**********",
//            duration = "00:09",
//            healthStatus = "亚健康",
//            info = "111"
//        )
//    )
//}