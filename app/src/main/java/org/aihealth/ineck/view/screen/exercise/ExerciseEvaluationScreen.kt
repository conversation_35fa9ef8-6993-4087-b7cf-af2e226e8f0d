package org.aihealth.ineck.view.screen.exercise

import androidx.activity.compose.BackHandler
import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.Icon
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.improvement.ImproveProgram
import org.aihealth.ineck.ui.theme.Sizes
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.ImprovementDetailDirections
import org.aihealth.ineck.viewmodel.ImproveDetailViewModel
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun ExerciseEvaluationScreen(
    viewModel: ImproveDetailViewModel,
) {
    DisposableEffect(Unit) {
        onDispose {
            viewModel.hasPostEvaluation.value = false
        }
    }
    val programList = viewModel.programList.collectAsState()
    val hasPostEvaluation = viewModel.hasPostEvaluation.collectAsState()
    BackHandler(true) {
        popScreen(Screen.Main.route)
    }

    BasePageView(
        headerContent = {
            Box(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = stringResource(id = R.string.improve_neck),
                    style = Typography.displayMedium,
                    modifier = Modifier.align(Alignment.Center),
                    color = Color(0xFF333333)
                )
                Icon(painter = painterResource(id = R.drawable.img_back),
                    contentDescription = null,
                    tint = Color(0xFF333333),
                    modifier = Modifier
                        .padding(start = Sizes.actionHorizontalPadding)
                        .align(
                            Alignment.CenterStart
                        )
                        .size(24.dp)
                        .pointerInput(Unit) {
                            detectTapGestures {
                                popScreen(Screen.Main.route)
                            }
                        }
                )
            }
        }
    ) {

    Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(
                    rememberScrollState()
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (hasPostEvaluation.value) {
                Text(
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                        .padding(top = 28.dp),
                    text = stringResource(id = R.string.thank_you_for_feedback),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                    )
                )
                AIHOutlinedButton(
                    text = stringResource(id = R.string.finished_evaluation),
                    onClick = {
                        popScreen(Screen.Main.route)
                    },
                    modifier = Modifier
                        .padding(vertical = 20.dp)
                        .fillMaxWidth(0.8f),
                    fontColor = Color(0xFF333333)
                )

            } else {
                LogUtil.i("score:${viewModel.score.value.toInt()}")
                ScopeValue(
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                        .padding(top = 10.dp),
                    scope = viewModel.score.value.toInt(),
                )
                FeelingRecord(
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                        .padding(top = 10.dp, start = 10.dp),
                    onClick = {
                        // apiService.postFeelingRecord(it)
                        viewModel.postUserFeelingData(it)
                    },
                )
            }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color(0xFFF5F6F8))
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 12.dp)
                ) {
                    CardTitle(
                        modifier = Modifier.align(Alignment.CenterStart)
                    )
                }
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 8.dp)
                        .shadow(
                            elevation = 12.dp,
                            spotColor = Color(0x1A3EA3A0),
                            ambientColor = Color(0x1A3EA3A0)
                        ),
                    shape = RoundedCornerShape(size = 10.dp),
                    colors = CardColors(
                        disabledContainerColor = Color(0xFFFFFFFF),
                        disabledContentColor = Color(0xFFFFFFFF),
                        containerColor = Color(0xFFFFFFFF),
                        contentColor = Color(0xFFFFFFFF)
                    )
                ) {
                    programList.value?.programs?.forEachIndexed { index, program ->
                        CardItem(
                            modifier = Modifier.fillMaxWidth(),
                            program = program,
                            onclick = { it ->
                                startScreen(
                                    route = ImprovementDetailDirections.actionToImprovementDetailCompose(
                                        model = ImprovementDetailDirections.ImprovementDetailModel(
                                            materialId = it.materialId,
                                            type = it.type,
                                            needVip = true
                                        )
                                    ),
                                    finish = true
                                )
                            }
                        )

                    }
                }


                ReviewData(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .padding(top = 15.dp, bottom = 25.dp),
                    onClick = {
                        MainViewModel.pageIndex = 0
                        popScreen(Screen.Main.route)

                    },
                )

            }
    }

    }

}

@Preview(showBackground = true)
@Composable
fun ReviewData(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
) {
    Column {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(top = 12.dp)
        ) {

            Text(
                modifier = Modifier.align(Alignment.CenterStart),
                text = stringResource(id = R.string.view_pro_version_data),
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF000000),
                )
            )
            Button(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .shadow(
                        elevation = 2.dp,
                        spotColor = Color(0xFFE2E9FF),
                        ambientColor = Color(0xFFE2E9FF)
                    ),
                shape = RoundedCornerShape(size = 20.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFFFFFFF),
                ),
                contentPadding = PaddingValues(
                    horizontal = 8.dp,
                    vertical = 4.dp
                ),
                onClick = {
                    MainViewModel.pageIndex = 3
                    popScreen(Screen.Main.route)
                },
            ) {
                Text(
                    text = stringResource(id = R.string.learn_more),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF2B56D7),
                    )
                )
            }
        }

        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(151.dp)
                .clip(RoundedCornerShape(size = 12.dp))
        ) {
            Image(
                modifier = Modifier
                    .fillMaxSize(),
                painter = painterResource(id = R.drawable.background),
                contentDescription = "",
                contentScale = androidx.compose.ui.layout.ContentScale.FillBounds
            )
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0x66000000))
            )
            Button(
                modifier = Modifier
                    .align(Alignment.Center),
                colors = ButtonColors(
                    contentColor = Color.Transparent,
                    containerColor = Color(0x66FFFFFF),
                    disabledContentColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent,
                ),
                border = BorderStroke(1.dp, Color(0xFFFEFEFF)),
                shape = RoundedCornerShape(size = 20.dp),
                onClick = { onClick() }
            ) {
                Text(
                    text = stringResource(id = R.string.bind_device),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
        }
    }
}

@Composable
fun CardTitle(modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 6.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        Text(
            text = stringResource(id = R.string.recommend_from_experts),
            fontSize = 18.sp,
            fontWeight = FontWeight(600),
            color = Color(0xFF333333),
            modifier = Modifier
                .padding(horizontal = 8.dp)
        )
        Box(
            Modifier
                .padding(0.dp)
                .width(2.dp)
                .height(15.dp)
                .background(color = Color(0xFF333333))
        )
        Text(
            text = stringResource(id = R.string.targeted_improvement),
            fontSize = 18.sp,
            fontWeight = FontWeight(600),
            color = Color(0xFF999999),
            modifier = Modifier
                .padding(horizontal = 8.dp)
        )
    }
}

@Composable
fun CardItem(
    modifier: Modifier = Modifier,
    program: ImproveProgram,
    onclick: (ImproveProgram) -> Unit
) {
    val context = LocalContext.current
    // 时长
    val durationString = TimeUtil.convertSecondsToAnnotatedString(
        program.duration,
        LocalContext.current
    )

    // 锻炼人数
    val numberOfPeopleString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                color = Color(0xFF2B56D7),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal
            )
        ) {
            append(program.frequency.toString())
            append(" ")
        }
        withStyle(
            SpanStyle(
                color = Color(0xFF999999),
                fontSize = 14.sp,
                fontWeight = FontWeight.Normal
            )
        ) {
            append(context.getString(R.string.unit_participated))
        }
    }

    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onclick(program) }
    ) {
        val (programCover, programTitle, showDetail) = createRefs()
        /* 改善项目方案封面 */
        Surface(
            modifier = Modifier
                .size(96.dp)
                .constrainAs(programCover) {
                    start.linkTo(parent.start, 10.dp)
                    top.linkTo(parent.top, 10.dp)
                    bottom.linkTo(parent.bottom, 10.dp)
                },
            shape = RoundedCornerShape(6.dp)
        ) {
            AsyncImage(
                model = program.cover,
                contentDescription = program.title,
                placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                modifier = Modifier.fillMaxSize()
            )
        }
        /* 改善项目方案标题 */
        Text(
            text = program.title,
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF666666)
            ),
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(programTitle) {
                start.linkTo(programCover.end, 22.dp)
                top.linkTo(parent.top, 22.dp)
                end.linkTo(parent.end, 5.dp)
                width = Dimension.fillToConstraints
            }
        )
        /* 改善项目方案关键属性， 时长、已练习人数等 */
        Row(
            modifier = Modifier.constrainAs(showDetail) {
                start.linkTo(programCover.end, 22.dp)
                top.linkTo(programTitle.bottom, 22.dp)
                end.linkTo(parent.end, 16.dp)
                width = Dimension.fillToConstraints
            },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {

            /* 请求体响应结构中提供的“持续时间”参数是以秒为单位，这里需要再显示上做转换计算 */
            Text(
                text = durationString,
                maxLines = 2,
                modifier = Modifier,
            )
            Text(
                text = numberOfPeopleString,
                maxLines = 2,
                modifier = Modifier
                    .padding(start = 15.dp)
            )
        }

    }

}

@Preview(locale = "en")
@Composable
fun ScopeValue(
    modifier: Modifier = Modifier,
    scope: Int = 100,
) {
    val scopeTextStyle = buildAnnotatedString {
        withStyle(style = SpanStyle(color = Color.Gray, fontSize = 18.sp)) {
            append(stringResource(id = R.string.system_score))
        }
        withStyle(style = SpanStyle(color = Color(0xFF333333), fontSize = 20.sp)) {
            append(" $scope ")
        }
        withStyle(style = SpanStyle(color = Color.Gray, fontSize = 18.sp)) {
            append(stringResource(id = R.string.score))
        }
    }
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.congratulations_completed),
            style = TextStyle(
                fontSize = 20.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF333333),
            )
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 2.dp, top = 8.dp),
            text = scopeTextStyle,
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun FeelingRecord(
    modifier: Modifier = Modifier,
    onClick: (FeelingRecordData) -> Unit = {},
) {
    val feelingChosen = remember { mutableIntStateOf(0) }

    val feelingsList = listOf(
        Feeling(
            stringResource(id = R.string.very_good),
            R.drawable.icon_ok_unchosen,
            R.drawable.icon_ok_chosen,
            listOf(
                stringResource(id = R.string.feel_better),
                stringResource(id = R.string.neck_no_pain),
                stringResource(id = R.string.very_cool),
                stringResource(id = R.string.great_experience)
            )
        ),
        Feeling(
            stringResource(id = R.string.average),
            R.drawable.icon_general_unchosen,
            R.drawable.icon_general_chosen,
            listOf(
                stringResource(id = R.string.average_effect),
                stringResource(id = R.string.neck_relief),
            )
        ),
        Feeling(
            stringResource(id = R.string.no_feeling),
            R.drawable.icon_bad_unchosen,
            R.drawable.icon_bad_chosen,
            listOf(
                stringResource(id = R.string.bad_effect),
                stringResource(id = R.string.complex_operation),
                stringResource(id = R.string.no_use)
            )
        )
    )
    val tags = feelingsList[feelingChosen.intValue].descriptions.map {
        Tag(it, remember {
            mutableStateOf(false)
        })
    }

    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 5.dp),
            text = stringResource(id = R.string.how_was_exercise),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF444444),
            )
        )
        LazyRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 18.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            items(feelingsList.size) { index ->
                FeelingIconItem(
                    modifier = Modifier.padding(10.dp),
                    id = if (feelingChosen.intValue == index) feelingsList[index].iconChosen else feelingsList[index].iconDisChosen,
                    text = feelingsList[index].name,
                    onClick = {
                        feelingChosen.intValue = index
                    }
                )
            }
        }
        TagBar(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 18.dp),
            tags = tags
        ) { tag ->
            tag.isSelected.value = tag.isSelected.value.not()
        }
        val textState = remember { mutableStateOf("") }

        OutlinedTextField(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp),
            value = textState.value,
            colors = OutlinedTextFieldDefaults.colors(
                unfocusedBorderColor = Color(0xFFDDDDDD), // Set the border color to grey when unfocused
                focusedBorderColor = Color(0xFF2B56D7) // Optional: Set the border color to grey when focused as well
                // You can also customize other colors like cursorColor, textColor, etc.
            ),
            onValueChange = { textState.value = it },
            label = { },
            maxLines = 4, // Adjust based on your requirement
            keyboardOptions = KeyboardOptions.Default.copy(
                imeAction = ImeAction.Default,
                keyboardType = KeyboardType.Text
            )
        )
        AIHOutlinedButton(
            text = stringResource(id = R.string.complete_evaluation),
            onClick = {
                var feeling = textState.value
                tags.forEach { tag ->
                    if (tag.isSelected.value) {
                        feeling.plus(tag.name)
                    }
                }
                onClick(FeelingRecordData(feelingChosen.intValue, feeling))
            },
            modifier = Modifier
                .padding(vertical = 20.dp)
                .fillMaxWidth(0.8f),
            fontColor = Color(0xFF333333)
        )

    }
}


@Composable
fun FeelingIconItem(
    modifier: Modifier = Modifier,
    @DrawableRes id: Int,
    text: String,
    onClick: () -> Unit = {},
) {
    Column(
        modifier = modifier.clickable {
            onClick()
        },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            modifier = Modifier.size(40.dp),
            painter = painterResource(id = id),
            contentDescription = ""
        )
        Text(
            modifier = Modifier.padding(top = 8.dp),
            text = text,
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF444444),
            )
        )
    }

}

@Composable
fun TagItem(
    tag: Tag,
    onTagClicked: (Tag) -> Unit
) {
    val textColor = if (tag.isSelected.value) Color(0xFF2B56D7) else Color(0xFF666666)
    val borderColor = if (tag.isSelected.value) Color(0xFF5F7DD6) else Color(0xFFCECECE)
    val contentColor = if (tag.isSelected.value) Color(0x99CDD8F8) else Color.White
    Card(
        colors = CardColors(
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White,
            containerColor = contentColor,
            contentColor = contentColor
        ),
        shape = RoundedCornerShape(size = 21.dp),
        border = BorderStroke(width = 1.dp, color = borderColor),
        modifier = Modifier
            .wrapContentWidth()
            .widthIn(min = 80.dp)
            .padding(start = 5.dp, bottom = 5.dp)
            .clickable { onTagClicked(tag) }
    ) {
        Text(
            text = tag.name,
            color = textColor,
            modifier = Modifier
                .wrapContentWidth()
                .padding(8.dp),
            textAlign = TextAlign.Center
        )
    }
}

@ExperimentalLayoutApi
@Composable
fun TagBar(
    modifier: Modifier = Modifier,
    tags: List<Tag>,
    onTagClicked: (Tag) -> Unit
) {
    FlowRow(
        modifier = modifier.fillMaxWidth(),
    ) {
        tags.forEach { tag ->
            TagItem(tag = tag, onTagClicked = onTagClicked)
        }
    }
}


data class Feeling(
    val name: String,
    @DrawableRes val iconDisChosen: Int,
    @DrawableRes val iconChosen: Int,
    val descriptions: List<String>
)

data class Tag(val name: String, var isSelected: MutableState<Boolean>)
data class FeelingRecordData(
    val feeling: Int,
    val comment: String
)