package org.aihealth.ineck.view.custom

import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp

@Composable
fun AIHCard(
    modifier: Modifier =Modifier,
    color: Color = Color.White,
    shape: Shape = RoundedCornerShape(12.dp),
    paddingValues: PaddingValues = PaddingValues(start = 12.dp, end = 12.dp, top = 8.dp),
    elevation: Dp = 3.dp,
    enabled: Boolean = true,
    content: @Composable () -> Unit
){
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .padding(paddingValues)
            .then(modifier),
        color = if (enabled) color else Color.Transparent,
        shape = shape,
        shadowElevation = if (enabled) elevation else 0.dp,
        content =content
    )
}