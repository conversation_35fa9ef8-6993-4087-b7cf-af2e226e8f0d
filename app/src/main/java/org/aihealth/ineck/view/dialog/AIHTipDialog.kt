package org.aihealth.ineck.view.dialog

import android.widget.Toast
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.delay
import org.aihealth.ineck.activity

@Composable
fun AIHTipDialog(
    text: String?,
    alignment: Alignment = Alignment.BottomCenter,
    duration: Long = 1500L,
    onTimeOut: () -> Unit
) {
    if (text.isNullOrBlank()) {
        return
    }
    LaunchedEffect(Unit) {
        Toast.makeText(activity,text,Toast.LENGTH_SHORT).apply {
            show()
        }
        delay(duration)
        onTimeOut()
    }
    val coroutineScope = rememberCoroutineScope()

//    Snackbar(modifier = Modifier,) {
//        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter) {
//            Text(text = text)
//        }
//    }
//    Box(
//        modifier = Modifier
//            .fillMaxSize()
//            .statusBarsPadding()
//            .padding(vertical = 20.dp)
//    ) {
//        Box(
//            modifier = Modifier
//                .align(alignment)
//                .padding(horizontal = 30.dp)
//                .background(
//                    Color(0XFF333333),
//                    RoundedCornerShape(12.dp)
//                )
//                .pointerInput(Unit) {
//                    detectTapGestures {  }
//                }
//                .padding(horizontal = 20.dp), contentAlignment = Alignment.Center
//        ) {
//            Text(
//                text = text,
//                fontSize = 14.sp,
//                color = Color.White,
//                modifier = Modifier.padding(vertical = 16.dp)
//            )
//        }
//    }
}

@Composable
fun AIHTipButtonDialog(
    text: String?,
    buttonText: String,
    onClick: () -> Unit,
    alignment: Alignment = Alignment.BottomCenter,
    duration: Long = 1500L,
    onTimeOut: () -> Unit
) {
    if (text.isNullOrBlank()) {
        return
    }
    LaunchedEffect(Unit) {
        delay(duration)
        onTimeOut()
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .statusBarsPadding()
            .padding(vertical = 20.dp)
    ) {
        Row(
            modifier = Modifier
                .align(alignment)
                .padding(horizontal = 30.dp)
                .background(Color(0XFF333333), RoundedCornerShape(12.dp))
                .pointerInput(Unit) {
                    detectTapGestures { }
                }
                .padding(horizontal = 20.dp),
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = text,
                fontSize = 14.sp,
                color = Color.White,
                modifier = Modifier.padding(vertical = 16.dp)
            )
            Spacer(modifier = Modifier.width(10.dp))
            Text(
                text = buttonText,
                fontSize = 15.sp,
                color = Color(0XFFAA7AE4),
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            onClick()
                        }
                    },
            )
        }

    }
}