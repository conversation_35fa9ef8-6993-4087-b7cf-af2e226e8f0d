package org.aihealth.ineck.view.screen.onboarding.components

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.model.splash.Page

@SuppressLint("ResourceType")
@Composable
fun OnBoardingImagePage(
    modifier: Modifier = Modifier,
    page: Page,
) {
    Box(modifier = modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = page.image),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds
        )

        val gradientText = buildAnnotatedString {
            withStyle(
                style = SpanStyle(
                    color = Color(0XFF3161FF),
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0XFF73C5FF),
//                            Color(0XFF3161FF),
//                        )
//                    ),
                    fontSize = 32.sp,
                    fontWeight = FontWeight.Bold
                )
            ) {
                append(stringResource(id = page.text))
            }
        }
        Text(
            text = gradientText,
            modifier = Modifier
                .padding(top = 40.dp)
                .padding(horizontal = 8.dp)
                .align(alignment = Alignment.TopCenter),

            )
    }
}