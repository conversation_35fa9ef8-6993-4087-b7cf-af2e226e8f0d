package org.aihealth.ineck.view.preview

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.screen.my.VipMessageNoVipZh
import org.aihealth.ineck.view.screen.my.VipMessageZh


@Composable
fun PreviewVipMessageNoVip() {
    VipMessageNoVipZh(
        id = R.drawable.ic_vip_tag,
        vipHelloString = "Hi，成为 aiSpine会员",
        vipDescriptionString = "开启健康生活，随时随地改善预防颈椎病",
        nextDescriptionString = "免费领取",
        onClick = {},
        context = {
            VipBenefits(
                "新人特惠，海量课程7天免费试用",
                vipBenefitsList = vipBenefitsList,
                onClick = { LogUtil.i("onClick $it") })
        }
    )
}

@Composable
fun PreviewVipMessage() {
    VipMessageZh(
        id = R.drawable.ic_vip_2,
        vipHelloString = "Hi，aiSpine会员",
        vipDescriptionString = "开启健康生活，随时随地改善预防颈椎病",
        nextDescriptionString = "我的会员",
        onclick = { LogUtil.i("onClick") },
        context = {
            VipUsedMessage(
                10.toString()
            ) {LogUtil.i("onClick $it")}

        }
    )
}

@Composable
fun VipBenefits(
    vipDescriptionString: String,
    vipBenefitsList: List<VipBenefitObject>,
    onClick: (Int) -> Unit
) {
    Text(
        modifier = Modifier.padding(start = 7.dp,top = 7.dp),
        text = vipDescriptionString,
        style = TextStyle(
            fontSize = 13.sp,
            fontWeight = FontWeight.Normal,
            color = Color(0xFF3C2200),
            letterSpacing = 0.46.sp,
        )
    )
    LazyRow {
        items(vipBenefitsList.size) { index ->
            VipBenefitItem(
                modifier = Modifier.padding(horizontal = 10.dp, vertical = 10.dp),
                id = vipBenefitsList[index].id,
                title = vipBenefitsList[index].title,
                onClick = { onClick(index) }
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun VipUsedMessage(
    day: String = "1",
    onClick: (Int) -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier
                .padding(top = 15.dp)
                .weight(0.3f)
                .clickable(
                    onClick = { onClick(0) },
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            val beforeLast = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),
                    )
                ) { stringResource(id = R.string.last_practice) }
            }
            val beforeDay = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),
                    )
                ) { stringResource(id = R.string.passed_time) }
                withStyle(
                    SpanStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFFEEC570),
                        fontStyle = FontStyle.Italic,

                        )
                ) { append(" $day ") }
                withStyle(
                    SpanStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),

                        )
                ) { stringResource(id = R.string.days) }
            }
            Text(text = beforeLast)
            Text(text = beforeDay)
            VipButton(vipDescription = stringResource(id = R.string.to_exercise))

        }
        Image(
            modifier = Modifier
                .width(1.dp)
                .height(48.dp),
            painter = painterResource(id = R.drawable.vip_myscreen_divier),
            contentDescription = "divider"
        )
        Column(
            modifier = Modifier
                .padding(top = 25.dp)
                .weight(0.3f)
                .clickable(
                    onClick = { onClick(1) },
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                modifier = Modifier,
                text = stringResource(id = R.string.super_member_day),
                maxLines = 2,
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF666666),
                    textAlign = TextAlign.Center,
                )
            )
            VipButton(
                stringResource(id = R.string.go)
            )

        }

        Image(
            modifier = Modifier
                .width(1.dp)
                .height(48.dp),
            painter = painterResource(id = R.drawable.vip_myscreen_divier),
            contentDescription = "divider"
        )
        Column(
            modifier = Modifier
                .padding(top = 25.dp)
                .weight(0.3f)
                .clickable(
                    onClick = { onClick(2) },
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                modifier = Modifier,
                text = stringResource(id = R.string.the_new_lesson_is_practiced_first),
                maxLines = 2,
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF666666),
                    textAlign = TextAlign.Center,
                )
            )
            Spacer(modifier = Modifier.height(33.dp))

        }

    }
}

@Composable
private fun VipButton(
    vipDescription: String
) {
    Column(
        modifier = Modifier
            .padding(top = 1.dp, bottom = 12.dp)
            .width(47.dp)
            .height(20.dp)
            .background(Color(0xFFD6C196), shape = RoundedCornerShape(size = 10.dp)),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = vipDescription,
            style = TextStyle(
                fontSize = 9.sp,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFFFFFFF),
                letterSpacing = 0.32.sp,
            )
        )
    }
}

@Composable
fun VipBenefitItem(
    modifier: Modifier,
    @DrawableRes id: Int,
    title: String,
    onClick: () -> Unit,
) {
    Column(
        modifier = modifier
            .wrapContentWidth()
            .wrapContentHeight()
            .clickable(
                onClick = { onClick() },
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ),
        verticalArrangement = Arrangement.SpaceBetween,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(
            modifier = Modifier
                .width(26.dp)
                .height(18.74419.dp),
            painter = painterResource(id = id),
            contentDescription = title
        )
        Text(
            modifier = Modifier
                .width(80.dp)
                .wrapContentHeight(),
            text = title,
            style = TextStyle(
                fontSize = 10.sp,
                fontWeight = FontWeight.Normal,
                color = Color(0xFF777777),
            ),
            textAlign = TextAlign.Center
        )
    }
}

data class VipBenefitObject(
    @DrawableRes var id: Int,
    var title: String,
)

val vipBenefitsList = mutableListOf<VipBenefitObject>().apply {
    add(VipBenefitObject(R.drawable.ic_practiced_first, "新课抢先练"))
    add(VipBenefitObject(R.drawable.ic_vip_select_class, "VIP精选课"))
    add(VipBenefitObject(R.drawable.ic_24_hours_training, "24小时跟练"))
    add(VipBenefitObject(R.drawable.ic_member_benefits, "会员福利"))
//    add(VipBenefitObject(R.drawable.ic_member_welfare_community,"会员福利社区"))
}.toList()