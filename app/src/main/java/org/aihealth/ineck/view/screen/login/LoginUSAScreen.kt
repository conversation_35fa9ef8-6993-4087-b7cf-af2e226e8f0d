package org.aihealth.ineck.view.screen.login

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.AIHTextField
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.LoginUSAViewModel

/**
 * 国外服务器登录界面UI
 */
@Composable
fun LoginUSARoute() {
    val viewModel = viewModel<LoginUSAViewModel>()
    val isReadChecked by viewModel.isReadChecked.collectAsState()
    val isDialogVisible by viewModel.dialogVisible.collectAsState()
    val isEmailVerificationDialogVisible by viewModel.emailVerificationDialogVisible.collectAsState()
    val emailToVerify by viewModel.emailToVerify.collectAsState()
    var showForgetPasswordDialog = remember { mutableStateOf(false) }
    
    // 延迟显示协议对话框，让页面加载更自然
    LaunchedEffect(Unit) {
        delay(800) // 延迟800ms让页面先完全加载
        viewModel.checkAndShowAgreementDialog()
    }
    
    LogUtil.i("LoginUSARoute isReadChecked: $isReadChecked")
    LoginUSAScreen(
        modifier = Modifier.fillMaxSize(),
        email = viewModel.emailLogin,
        changeEmail = { viewModel.emailLogin = it },
        password = viewModel.passwordLogin,
        changePassword = { viewModel.passwordLogin = it },
        loginWithEmail = viewModel::emailLoginClick,
        loginWithGoogle = viewModel::googleLoginClick,
        isReadChecked = isReadChecked,
        changeIsReadChecked = {
            viewModel.changeIsReadChecked()
            LogUtil.i("LoginUSARoute isReadChecked: $isReadChecked")
        },
        isDialogVisible = isDialogVisible,
        changeIsDialogVisible = { viewModel.setDialogVisible(it) },
        showForgetPassword = showForgetPasswordDialog.value,
        changeShowForgetPassword = {showForgetPasswordDialog.value = it},
        forgetPassword = {
            viewModel.viewModelScope.launch{
                viewModel.forgetPassword(it).onSuccess {
                    showForgetPasswordDialog.value = false
                }

            }
        }
    )
    
    // Email verification dialog
    EmailVerificationDialog(
        visible = isEmailVerificationDialogVisible,
        email = emailToVerify,
        onResend = {
            viewModel.viewModelScope.launch {
                viewModel.resendVerificationEmail(emailToVerify)
                // 重发邮件后关闭对话框
                viewModel.setEmailVerificationDialogVisible(false)
            }
        },
        onConfirm = {
            viewModel.setEmailVerificationDialogVisible(false)
        }
    )
}

@Composable
fun LoginUSAScreen(
    modifier: Modifier = Modifier,
    email: String,
    changeEmail: (String) -> Unit,
    password: String,
    changePassword: (String) -> Unit,
    loginWithEmail: () -> Unit,
    loginWithGoogle: () -> Unit,
    isReadChecked: Boolean,
    changeIsReadChecked: () -> Unit,
    isDialogVisible: Boolean,
    changeIsDialogVisible: (Boolean) -> Unit,
    showForgetPassword: Boolean,
    changeShowForgetPassword: (Boolean) -> Unit,
    forgetPassword: (String) -> Unit
) {
    LogUtil.i("LoginUSARoute isReadChecked in LoginUSAScreen: $isReadChecked")
    BasePageView(
        background = {
            Image(
                painter = painterResource(id = R.drawable.bg_login_new),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = androidx.compose.ui.layout.ContentScale.FillBounds
            )

        }
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .align(Alignment.Center)
                .verticalScroll(rememberScrollState())
        ) {
            Row(
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(top = 40.dp, end = 16.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            startScreen(Screen.ChangeCity.route)
                        }
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                /**
                Text(
                    text = stringResource(id = R.string.united_states),
                    color = Color(0xFF333333),
                    fontSize = 14.sp
                )
                Image(
                    painter = painterResource(id = R.drawable.img_pulldown),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .size(10.dp)
                )
                */
            }
            Image(
                painter = painterResource(id = R.drawable.img_icon),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 38.dp)
                    .size(131.dp, 62.dp)
            )
            Card(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 61.dp),
                shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
                colors = CardColors(
                    containerColor = Color.White,
                    disabledContainerColor = Color.Transparent,
                    disabledContentColor = Color.Transparent,
                    contentColor = Color.Transparent
                ),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.email),
                        color = Color(0XFF444444),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 10.dp, vertical = 16.dp)
                    )
                    AIHTextField(
                        value = email,
                        onValueChange = {
                            changeEmail(it)
                        },
                        keyboardType = KeyboardType.Email,
                        placeholder = stringResource(id = R.string.please_enter_email_account)
                    )
                    Text(
                        text = stringResource(id = R.string.password),
                        color = Color(0XFF444444),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 10.dp, vertical = 16.dp),

                        )
                    AIHTextField(
                        value = password,
                        onValueChange = {
                            changePassword(it)
                        },
                        keyboardType = KeyboardType.Password,
                        placeholder = stringResource(id = R.string.please_enter_password),
                        morePlaceholder = {
                            if (password.isEmpty()) {
                                Text(
                                    text = stringResource(id = R.string.forget_password),
                                    fontSize = 14.sp, color = Color(0xFF1E4BDF),
                                    modifier = Modifier
                                        .align(Alignment.CenterEnd)
                                        .padding(end = 4.dp)
                                        .pointerInput(Unit) {
                                            detectTapGestures {
                                                changeShowForgetPassword(true)
                                            }
                                        },
                                )
                            }
                        }
                    )

                    AIHTextButton(
                        text = stringResource(id = R.string.login),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(top = 30.dp),
                        style = TextStyle(
                            fontWeight = FontWeight(400),
                            color = Color(0xFFF7F7F7),
                            fontSize = 20.sp,
                            textAlign = TextAlign.Center
                        ),
                        onClick = {
                            loginWithEmail()
                        }
                    )
                    AIHOutlinedButton(
                        text = stringResource(id = R.string.sign_up),
                        onClick = {
                            startScreen(
                                route = Screen.LoginSignUpEN.route,
                                finish = false
                            )
                        },
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                            .padding(vertical = 10.dp),
                        fontSize = 20.sp,
                        fontColor = Color(0XFF1E4BDF)
                    )

                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(top = 42.dp, bottom = 26.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        HorizontalDivider(
                            modifier = Modifier
                                .height(1.dp)
                                .weight(1F), color = Color(0XFFE6E6E6)
                        )
                        Text(
                            text = stringResource(id = R.string.other_login_methods),
                            fontSize = 14.sp,
                            color = Color(0XFF666666),
                            modifier = Modifier.padding(horizontal = 20.dp)
                        )
                        HorizontalDivider(
                            modifier = Modifier
                                .height(1.dp)
                                .weight(1F), color = Color(0XFFE6E6E6)
                        )
                    }
                    Row(Modifier.fillMaxWidth()) {
                        Spacer(modifier = Modifier.weight(1F))
                        Image(
                            painter = painterResource(id = R.drawable.img_google),
                            contentDescription = null,
                            modifier = Modifier
                                .size(40.dp)
                                .pointerInput(Unit) {
                                    detectTapGestures {
                                        loginWithGoogle()
                                    }
                                }
                        )
                        Spacer(modifier = Modifier.weight(1F))
                    }
                    Spacer(modifier = Modifier.height(40.dp))

                    LoginAgreementView(checked = isReadChecked) {
                        LogUtil.d("LoginUSARoute isReadChecked: $isReadChecked, change:${!isReadChecked}")
                        changeIsReadChecked()
                    }
                    Spacer(modifier = Modifier.height(42.dp))
                }
            }
        }

        AgreementDialog(
            visible = isDialogVisible,
            onConfirm = {
                changeIsReadChecked()
                changeIsDialogVisible(false)
                // 国外用户同意协议后初始化 FCM
                baseApplication.initFCM()
            },
            onCancel = {
                changeIsDialogVisible(false)
            }
        )
    }

    ForgotPasswordDialog(
        visible = showForgetPassword,
        email = email,
        onDismiss = { changeShowForgetPassword(false) },
        onConfirm = { email ->
            forgetPassword(email)
        }
    )

}


@Composable
fun ForgotPasswordDialog(
    visible: Boolean,
    email: String = "",
    onDismiss: () -> Unit,
    onConfirm: (String) -> Unit
) {
    if (visible) {
        var email = remember { mutableStateOf(email) }
        Dialog(onDismissRequest = onDismiss) {
            Column(
                modifier = Modifier
                    .background(
                        color = Color.White,
                        shape = RoundedCornerShape(size = 17.dp)
                    )
                    .padding(horizontal = 20.dp, vertical = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.forget_password),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
                Text(
                    modifier = Modifier.padding(top = 16.dp),
                    text = stringResource(id = R.string.we_will_send_you_an_email),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF333333),
                    )
                )
                AIHTextField(
                    value = email.value,
                    onValueChange = { email.value = it },
                    keyboardType = KeyboardType.Email,
                    placeholder = stringResource(id = R.string.please_enter_email_account),
                    modifier = Modifier.padding(vertical = 16.dp)
                )
                AIHTextButton(
                    text = stringResource(R.string.ok),
                    onClick = {
                        onConfirm(email.value)
                    },
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color.White
                    ),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp)
                )
            }
        }
    }
}

@Preview
@Composable
private fun Preview() {
    LoginUSAScreen(
        email = "",
        changeEmail = {},
        password = "",
        changePassword = {},
        loginWithEmail = {},
        loginWithGoogle = {},
        isReadChecked = false,
        changeIsReadChecked = {},
        isDialogVisible = false,
        changeIsDialogVisible = {},
        forgetPassword = {},
        showForgetPassword = false,
        changeShowForgetPassword = {}
    )
}

/**
 * 邮箱验证对话框
 */
@Composable
fun EmailVerificationDialog(
    visible: Boolean,
    email: String,
    onResend: () -> Unit,
    onConfirm: () -> Unit
) {
    if (visible) {
        Dialog(
            onDismissRequest = { onConfirm() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false
            )
        ) {
            AnimatedVisibility(
                visible = visible,
                enter = fadeIn(animationSpec = tween(300)) + scaleIn(
                    initialScale = 0.8f,
                    animationSpec = tween(300)
                ),
                exit = fadeOut(animationSpec = tween(200)) + scaleOut(
                    targetScale = 0.8f,
                    animationSpec = tween(200)
                )
            ) {
                Column(
                    Modifier
                        .fillMaxWidth(0.9f)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(horizontal = 16.dp, vertical = 24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.prompt),
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center,
                        color = Color(0xFF333333)
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = buildAnnotatedString {
                            val baseMessage = stringResource(id = R.string.email_verification_message)
                            val emailPlaceholder = "%s"
                            val emailIndex = baseMessage.indexOf(emailPlaceholder)
                            
                            if (emailIndex != -1) {
                                // 添加邮箱前的文本
                                append(baseMessage.substring(0, emailIndex))
                                // 添加蓝色的邮箱地址
                                withStyle(style = SpanStyle(color = Color(0xFF1E4BDF), fontWeight = FontWeight.Medium)) {
                                    append(email)
                                }
                                // 添加邮箱后的文本
                                append(baseMessage.substring(emailIndex + emailPlaceholder.length))
                            } else {
                                // 如果没有找到占位符，就显示原文本
                                append(baseMessage.replace(emailPlaceholder, email))
                            }
                        },
                        fontSize = 14.sp,
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Start,
                        lineHeight = 20.sp
                    )
                    Spacer(modifier = Modifier.height(24.dp))
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        AIHOutlinedButton(
                            text = stringResource(id = R.string.resend),
                            onClick = {
                                onResend()
                            },
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            modifier = Modifier
                                .weight(1f)
                                .height(44.dp)
                        )
                        AIHTextButton(
                            text = stringResource(id = R.string.ok),
                            onClick = {
                                onConfirm()
                            },
                            style = TextStyle(
                                fontWeight = FontWeight(400),
                                color = Color(0xFFF7F7F7),
                                fontSize = 16.sp,
                                textAlign = TextAlign.Center
                            ),
                            modifier = Modifier
                                .weight(1f)
                                .height(44.dp)
                        )
                    }
                }
            }
        }
    }
}