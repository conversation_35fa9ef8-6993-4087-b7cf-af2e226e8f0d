package org.aihealth.ineck.view.screen

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.gestures.DraggableState
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.BackPain
import org.aihealth.ineck.model.angles.NeckPain
import org.aihealth.ineck.model.angles.NeuralRecord
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toPx
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHSelectButton
import org.aihealth.ineck.view.custom.AIHSliderState
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.HistoryPainDirections
import org.aihealth.ineck.viewmodel.MainViewModel
import java.util.Locale
import kotlin.math.roundToInt

@Composable
fun NeuralRecordScreen(
    viewModel: MainViewModel,
    isHistory: Boolean
) {
    BasePageView(
        title = stringResource(id = R.string.neural_record),
        showBackIcon = true,
        headerContent = {
            /* 疼痛历史记录查兰 */
            IconButton(
                onClick = {
                    val model = HistoryPainDirections.HistoryPainModel(
                        when (viewModel.homeScreen.currentDeviceType) {
                            DeviceType.aiNeck, DeviceType.aiNeckCV -> 3
                            DeviceType.aiBack, DeviceType.aiBackCV -> 4
                            else -> 0
                        }
                    )

                    startScreen(
                        route = HistoryPainDirections.actionToHistoryPain(model)
                    )
                },
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.CenterEnd)
                    .padding(end = 8.dp)
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.icon_records_history),
                    contentDescription = "Pain Records History",
                    tint = Color.Black.copy(alpha = .8f),
                    modifier = Modifier.size(24.dp)
                )
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
//            Column(
//                modifier = Modifier
//                    .fillMaxWidth()
//                    .background(Color(0xFFCDCDCD))
//                    .padding(vertical = 3.dp),
//                horizontalAlignment = Alignment.CenterHorizontally
//            ) {
//                Text(
//                    text = stringResource(id = R.string.pain_record_time, "上午9:30"),
//                    fontSize = 8.sp,
//                    color = Color(0XFF777777)
//                )
//                Text(
//                    text = stringResource(id = R.string.pain_record_angle, 50),
//                    fontSize = 8.sp,
//                    color = Color(0XFF777777)
//                )
//            }
            val isToday =
                viewModel.reportScreen.dayCalendarState.selectedDay.timeInMillis == viewModel.reportScreen.dayCalendarState.today.timeInMillis
            when (viewModel.homeScreen.currentDeviceType) {
                DeviceType.aiNeck, DeviceType.aiNeckCV -> NeckPainView(
                    isHistory && !isToday,
                    neckPain = viewModel.reportScreen.dayRecordScales.neck_pain
                )

                DeviceType.aiBack, DeviceType.aiBackCV -> BackPainView(
                    isHistory && !isToday,
                    backPain = viewModel.reportScreen.dayRecordScales.back_pain
                )

                else -> {

                }
            }


        }

    }
}

@Composable
private fun ColumnScope.NeckPainView(
    isHistory: Boolean,
    neckPain: NeckPain
) {
    val density = LocalDensity.current

    // 忽略系统字体缩放
    val fixedFontSize18 = with(density) {
        18.sp / fontScale
    }
    /* 肌肉疼痛数据 */
    val muscleState by remember {
        mutableStateOf(AIHSliderState(neckPain.muscle))
    }
    /* 麻木数据 */
    var numbnessSelectedIndex by remember {
        mutableIntStateOf(neckPain.numb)
    }
    /* 平衡数据 */
    var balanceSelectedIndex by remember {
        mutableIntStateOf(if (neckPain.balance == 0) 1 else 0)
    }
    Column(
        modifier = Modifier
            .weight(1F)
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 40.dp, vertical = 20.dp)
    ) {
        PainTitle(titleResId = R.string.muscle_strength, scale = "0-5")
        Spacer(modifier = Modifier.height(10.dp))
        MuscleView(state = muscleState)
        Spacer(modifier = Modifier.height(25.dp))
        if (currentLocale == Locale.CHINESE) {
            Row {
                Text(
                    text = stringResource(id = R.string.numbness_tingling),
                    fontSize = fixedFontSize18,
                    color = Color(0XFF333333)
                )
                Spacer(modifier = Modifier.width(50.dp))
                AIHSelectButton(
                    selectedIndex = numbnessSelectedIndex,
                    array = stringArrayResource(id = R.array.pain_record_numbness_status),
                    onClick = {
                        numbnessSelectedIndex = it
                    },
                    modifier = Modifier.size(132.dp, 38.dp),
                    padding = PaddingValues(3.dp)
                )
            }
            Spacer(modifier = Modifier.height(25.dp))
            Row {
                Text(
                    text = stringResource(id = R.string.balance),
                    fontSize = fixedFontSize18,
                    color = Color(0XFF333333)
                )
                Spacer(modifier = Modifier.width(50.dp))
                AIHSelectButton(
                    selectedIndex = balanceSelectedIndex,
                    array = stringArrayResource(id = R.array.pain_record_balance_status),
                    onClick = {
                        balanceSelectedIndex = it
                    },
                    modifier = Modifier.size(132.dp, 38.dp),
                    padding = PaddingValues(3.dp)
                )
            }
        } else {
            Text(
                text = stringResource(id = R.string.numbness_tingling),
                fontSize = fixedFontSize18,
                color = Color(0XFF333333)
            )
            Spacer(modifier = Modifier.height(10.dp))
            AIHSelectButton(
                selectedIndex = numbnessSelectedIndex,
                array = stringArrayResource(id = R.array.pain_record_numbness_status),
                onClick = {
                    numbnessSelectedIndex = it
                },
                modifier = Modifier.wrapContentHeight(),
                padding = PaddingValues(3.dp)
            )
            Spacer(modifier = Modifier.height(25.dp))
            Text(
                text = stringResource(id = R.string.balance),
                fontSize = fixedFontSize18,
                color = Color(0XFF333333)
            )
            Spacer(modifier = Modifier.height(10.dp))
            AIHSelectButton(
                selectedIndex = balanceSelectedIndex,
                array = stringArrayResource(id = R.array.pain_record_balance_status),
                onClick = {
                    balanceSelectedIndex = it
                },
                modifier = Modifier,
                padding = PaddingValues(3.dp)
            )
        }
        if (!isHistory) {
            AIHButton(
                text = stringResource(id = R.string.finish),
                onClick = {
                    apiService.postNeckNeural(
                        neural = NeuralRecord(
                            balance = if (balanceSelectedIndex == 0) 1 else 0,
                            numb = numbnessSelectedIndex,
                            muscle = muscleState.value
                        )
                    ).enqueueBody {
                        DialogUtil.showToast(localeResources.getString(R.string.neural_record_added_successfully))
                        finish()
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 35.dp, vertical = 30.dp)
                    .fillMaxWidth(),
                fontSize = fixedFontSize18
            )
        }
    }
}

@Composable
private fun ColumnScope.BackPainView(
    isHistory: Boolean,
    backPain: BackPain
) {
    val density = LocalDensity.current

    // 忽略系统字体缩放
    val fixedFontSize18 = with(density) {
        18.sp / fontScale
    }

    val muscleState by remember {
        mutableStateOf(AIHSliderState(backPain.muscle))
    }
    var numbnessSelectedIndex by remember {
        mutableIntStateOf(backPain.numb)
    }
    var balanceSelectedIndex by remember {
        mutableIntStateOf(if (backPain.balance == 0) 1 else 0)
    }
    Column(
        modifier = Modifier
            .weight(1F)
            .verticalScroll(rememberScrollState())
            .padding(horizontal = 40.dp, vertical = 20.dp)
    ) {
        PainTitle(titleResId = R.string.muscle_strength, scale = "0-5")
        Spacer(modifier = Modifier.height(10.dp))
        MuscleView(state = muscleState)
        Spacer(modifier = Modifier.height(25.dp))
        if (currentLocale == Locale.CHINESE) {
            Row {
                Text(
                    text = stringResource(id = R.string.numbness_tingling),
                    fontSize = fixedFontSize18,
                    color = Color(0XFF333333)
                )
                Spacer(modifier = Modifier.width(50.dp))
                AIHSelectButton(
                    selectedIndex = numbnessSelectedIndex,
                    array = stringArrayResource(id = R.array.pain_record_numbness_status),
                    onClick = {
                        numbnessSelectedIndex = it
                    },
                    modifier = Modifier.size(132.dp, 38.dp),
                    padding = PaddingValues(3.dp)
                )
            }
            Spacer(modifier = Modifier.height(25.dp))
            Row {
                Text(
                    text = stringResource(id = R.string.balance),
                    fontSize = fixedFontSize18,
                    color = Color(0XFF333333)
                )
                Spacer(modifier = Modifier.width(50.dp))
                AIHSelectButton(
                    selectedIndex = balanceSelectedIndex,
                    array = stringArrayResource(id = R.array.pain_record_balance_status),
                    onClick = {
                        balanceSelectedIndex = it
                    },
                    modifier = Modifier.size(132.dp, 38.dp),
                    padding = PaddingValues(3.dp)
                )
            }
        } else {
            Text(
                text = stringResource(id = R.string.numbness_tingling),
                fontSize = fixedFontSize18,
                color = Color(0XFF333333)
            )
            Spacer(modifier = Modifier.height(10.dp))
            AIHSelectButton(
                selectedIndex = numbnessSelectedIndex,
                array = stringArrayResource(id = R.array.pain_record_numbness_status),
                onClick = {
                    numbnessSelectedIndex = it
                },
                modifier = Modifier,
                padding = PaddingValues(3.dp)
            )
            Spacer(modifier = Modifier.height(25.dp))
            Text(
                text = stringResource(id = R.string.balance),
                fontSize = fixedFontSize18,
                color = Color(0XFF333333)
            )
            Spacer(modifier = Modifier.height(10.dp))
            AIHSelectButton(
                selectedIndex = balanceSelectedIndex,
                array = stringArrayResource(id = R.array.pain_record_balance_status),
                onClick = {
                    balanceSelectedIndex = it
                },
                modifier = Modifier,
                padding = PaddingValues(3.dp)
            )
        }
        if (!isHistory) {
            AIHButton(
                text = stringResource(id = R.string.finish),
                onClick = {
                    apiService.postBackNeural(
                        neural = NeuralRecord(
                            balance = if (balanceSelectedIndex == 0) 1 else 0,
                            numb = numbnessSelectedIndex,
                            muscle = muscleState.value
                        )
                    ).enqueueBody {
                        DialogUtil.showToast(localeResources.getString(R.string.neural_record_added_successfully))
                        finish()
                    }
                },
                modifier = Modifier
                    .padding(horizontal = 35.dp, vertical = 30.dp)
                    .fillMaxWidth(),
                fontSize = fixedFontSize18
            )
        }
    }
}

@Composable
private fun PainTitle(
    titleResId: Int,
    scale: String
) {
    val density = LocalDensity.current

    // 忽略系统字体缩放
    val fixedFontSize18 = with(density) {
        18.sp / fontScale
    }
    // 忽略系统字体缩放
    val fixedFontSize15 = with(density) {
        15.sp / fontScale
    }
    Row(Modifier.fillMaxWidth()) {
        Text(
            text = stringResource(id = titleResId),
            fontSize = fixedFontSize18,
            color = Color(0XFF333333)
        )
        Spacer(modifier = Modifier.weight(1F))
        Text(
            text = stringResource(id = R.string.pain_record_scale, scale),
            fontSize = fixedFontSize15,
            color = Color(0XFF666666)
        )
    }
}

/**
 * 肌肉力量控件
 */
@Composable
private fun MuscleView(
    state: AIHSliderState
) {
    var offset by remember {
        mutableFloatStateOf(0F)
    }
    Column(
        Modifier
            .fillMaxWidth()
            .padding(horizontal = 7.dp)
    ) {
        BoxWithConstraints(
            Modifier
                .fillMaxWidth()
                .height(38.dp)
        ) {
            val mWidth = (constraints.maxWidth - 11.dp.toPx()) / 5
            val mHeight = (constraints.minHeight - 14.dp.toPx()) / 5
            Canvas(modifier = Modifier
                .fillMaxWidth()
                .padding(top = 7.dp)
                .padding(horizontal = 5.5.dp)
                .height(24.dp)
                .pointerInput(Unit) {
                    detectTapGestures {
                        state.value = (it.x / mWidth).roundToInt()
                    }
                }
            ) {
                val strokeWidth = 2.dp.toPx()
                drawLine(
                    color = Color(0XFFCECECE),
                    start = Offset(0F, size.height),
                    end = Offset(size.width, size.height),
                    strokeWidth = strokeWidth
                )
                repeat(6) {
                    drawLine(
                        color = Color(0XFFCECECE),
                        start = Offset(it * mWidth, size.height),
                        end = Offset(it * mWidth, (5 - it) * mHeight),
                        strokeWidth = strokeWidth
                    )
                }
                drawLine(
                    color = Color(0XFF85817C),
                    start = Offset(0F, size.height),
                    end = Offset(mWidth * state.value, (5 - state.value) * mHeight),
                    strokeWidth = strokeWidth
                )
                drawLine(
                    color = Color(0XFFCECECE),
                    start = Offset(mWidth * state.value, (5 - state.value) * mHeight),
                    end = Offset(size.width, 0F),
                    strokeWidth = strokeWidth
                )

            }
            val draggableState by remember {
                mutableStateOf(DraggableState {
                    offset += it
                    if (offset > mWidth / 2 && state.value < 5) {
                        offset -= mWidth
                        state.value += 1
                    } else if (offset < -mWidth / 2 && state.value > 0) {
                        offset += mWidth
                        state.value -= 1
                    }
                })
            }
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .offset {
                        IntOffset(
                            (mWidth * state.value).toInt() - 4.5.dp.roundToPx(),
                            (-mHeight * state.value).toInt()
                        )
                    }
                    .size(20.dp)
                    .draggable(
                        state = draggableState,
                        orientation = Orientation.Horizontal,
                        onDragStopped = {
                            offset = 0F
                        }
                    )
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.img_slider),
                    contentDescription = null,
                    tint = Color(0XFFEE652A),
                    modifier = Modifier
                        .size(20.dp, 28.dp)
                )
            }

        }
        Column(
            Modifier
                .fillMaxWidth()
                .padding(horizontal = 5.5.dp)
        ) {
            BoxWithConstraints(modifier = Modifier.fillMaxWidth()) {
                val mWidth = maxWidth / 5
                repeat(6) {
                    Text(
                        text = "$it",
                        fontSize = 12.sp,
                        color = Color(0XFF666666),
                        textAlign = TextAlign.Center,
                        modifier = Modifier
                            .offset(mWidth * (it - 1))
                            .width(mWidth * 2)
                    )
                }
            }
            val array = stringArrayResource(id = R.array.pain_record_muscle_strength_level)
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = array[state.value],
                fontSize = 10.sp,
                color = Color(0XFF333333),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth()
            )
        }

    }
}