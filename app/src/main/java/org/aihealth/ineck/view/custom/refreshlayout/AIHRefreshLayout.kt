package org.aihealth.ineck.view.custom.refreshlayout

import androidx.compose.animation.core.animate
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.Velocity
import kotlinx.coroutines.delay


internal enum class AIHRefreshType {
    /**
     * 下拉刷新
     */
    PULL,

    /**
     * 正在刷新
     */
    REFRESHING,

    /**
     * 释放刷新
     */
    RELEASE,

    /**
     * 刷新完成
     */
    FINISHED
}

class AIHRefreshState(
    refresh: Boolean
) {
    var offset by mutableFloatStateOf(0F)
    var refreshing by mutableStateOf(refresh)

    internal var refreshType by mutableStateOf(if (refresh) AIHRefreshType.REFRESHING else AIHRefreshType.PULL)
    internal var indicatorHeight by mutableFloatStateOf(1F)
}



@Composable
fun AIHRefreshLayout(
    state: AIHRefreshState,
    onRefresh: () -> Unit,
    indicator: @Composable () -> Unit = { AIHRefreshIndicator(state)},
    indicatorAlignment: Alignment = Alignment.BottomCenter,
    backgroudColor: Color = Color.Transparent,
    content: @Composable BoxScope.() -> Unit
) {
    LaunchedEffect(state.refreshing) {
        if (state.refreshing) {
            animate(initialValue = state.offset, targetValue = state.indicatorHeight) { value,_ ->
                state.offset = value
            }
            state.refreshType = AIHRefreshType.REFRESHING
            onRefresh()
        } else {
            state.refreshType = AIHRefreshType.FINISHED
            delay(500)
            animate(initialValue = state.offset, targetValue = 0F) { value,_ ->
                state.offset = value
            }
        }
    }
    val scrollState = rememberScrollState()
    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                return if (source == NestedScrollSource.Drag && available.y < 0 && state.offset > 0 && state.refreshType != AIHRefreshType.REFRESHING) {
                    state.offset = (available.y + state.offset).coerceAtLeast(0F)
                    Offset(0f, available.y)
                } else {
                    Offset.Zero
                }
            }

            override fun onPostScroll(
                consumed: Offset,
                available: Offset,
                source: NestedScrollSource
            ): Offset {


                return if (source == NestedScrollSource.Drag && available.y > 0  && state.refreshType != AIHRefreshType.REFRESHING) {
                    state.refreshType = if (state.offset <= state.indicatorHeight){
                        AIHRefreshType.PULL
                    }else{
                        AIHRefreshType.RELEASE
                    }
                    state.offset = (available.y + state.offset).coerceAtLeast(0F)
                    Offset(0f, available.y)
                } else {
                    Offset.Zero
                }

            }

            override suspend fun onPreFling(available: Velocity): Velocity {
                if (available.y + state.offset < 0 && state.refreshType != AIHRefreshType.REFRESHING) {
                    state.offset = 0F
                }
                return Velocity.Zero
            }

            override suspend fun onPostFling(consumed: Velocity, available: Velocity): Velocity {
                if (state.refreshType != AIHRefreshType.REFRESHING) {
                    if (state.offset > state.indicatorHeight) {
                        state.refreshing = true
                    } else {
                        animate(initialValue = state.offset, targetValue = 0F) { value, _ ->
                            state.offset = value
                        }
                    }
                }
                return available
            }

            private fun Float.toOffset(): Offset = Offset(0f, this)

            private fun Offset.toFloat(): Float = this.y
        }
    }

    Box(
        Modifier
            .fillMaxSize()
            .clip(RectangleShape)
    ) {
        if (state.offset > state.indicatorHeight) {
            Box(modifier = Modifier
                .fillMaxWidth()
                .height(with(LocalDensity.current) { state.offset.toDp() })
                .background(backgroudColor), contentAlignment = indicatorAlignment) {
                indicator()
            }
        } else {
            Box(modifier = Modifier
                .offset { IntOffset(0, (state.offset - state.indicatorHeight).toInt()) }
                .fillMaxWidth()
                .background(backgroudColor)
            ) {
                indicator()
            }
        }
        Box(modifier = Modifier
            .fillMaxSize()
            .offset { IntOffset(0, state.offset.toInt()) }
            .nestedScroll(nestedScrollConnection)
            .verticalScroll(scrollState)) {
            content()
        }
    }

}