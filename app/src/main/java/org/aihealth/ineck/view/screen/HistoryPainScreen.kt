package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.BackNeuralRecordItemCard
import org.aihealth.ineck.view.custom.BackPainRecordItemCard
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.custom.NeckNeuralRecordItemCard
import org.aihealth.ineck.view.custom.NeckPainRecordItemCard
import org.aihealth.ineck.view.custom.PainTypeState
import org.aihealth.ineck.viewmodel.HistoryPainViewModel

/**
 *  疼痛历史记录页
 *  @param  viewModel   疼痛历史记录页ViewModel
 *  @param  painNumberState    疼痛类型状态
 */
@Composable
fun HistoryPainScreen(
    viewModel: HistoryPainViewModel,
    painNumberState: Int,
) {
    BasePageView(
        title = when (painNumberState) {
            1 -> stringResource(id = R.string.pain_record_neck_pain)
            2 -> stringResource(id = R.string.pain_record_back_pain)
            3 -> stringResource(id = R.string.pain_record_neck_neural)
            4 -> stringResource(id = R.string.pain_record_back_neural)
            else -> ""
        },
        showBackIcon = true
    ) {
        when (painNumberState) {
            /* 颈部疼痛记录 */
            1 -> {
                /* 向服务端请求当前用户颈部疼痛历史记录数据 */
                viewModel.loadPainHistory(painType = PainTypeState.NECK_PAIN)
                val neckPainRecordsList = viewModel.neckPainHistoryList.collectAsState()
                if (neckPainRecordsList.value.isEmpty()) {
                    NullHistoryDataTemplate()
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(color = Color(0xFFE4E7E3)),
                        state = rememberLazyListState()
                    ) {
                        items(
                            /* 在没有分页机制的情况下， 为保证浏览性能， 取进三十次记录 */
                            items = if (neckPainRecordsList.value.size > 30) neckPainRecordsList.value.subList(
                                0,
                                30
                            ) else neckPainRecordsList.value,
                            key = { it.datetime }
                        ) { unit ->
                            NeckPainRecordItemCard(
                                neckPain = unit,
                                onClickEvent = { /* TODO */ }
                            )
                        }
                    }
                }
            }
            /* 背部疼痛记录*/
            2 -> {
                viewModel.loadPainHistory(painType = PainTypeState.BACK_PAIN)
                val backPainRecordsList = viewModel.backPainHistoryList.collectAsState()
                if (backPainRecordsList.value.isEmpty()) {
                    NullHistoryDataTemplate()
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(color = Color(0xFFE4E7E3)),
                        state = rememberLazyListState()
                    ) {
                        items(
                            /* 在没有分页机制的情况下， 为保证浏览性能， 取进三十次记录 */
                            items = if (backPainRecordsList.value.size > 30) backPainRecordsList.value.subList(
                                0,
                                30
                            ) else backPainRecordsList.value,
                            key = { it.datetime }
                        ) { unit ->
                            BackPainRecordItemCard(
                                backPain = unit,
                                onClickEvent = { /* TODO */ }
                            )
                        }
                    }
                }
            }
            /* 颈部神经记录 */
            3 -> {
                /* 向服务端请求当前用户颈部疼痛历史记录数据 */
                viewModel.loadPainHistory(painType = PainTypeState.NECK_NEURAL)
                val neckNeuralRecordsList = viewModel.neckNeuralHistoryList.collectAsState()
                if (neckNeuralRecordsList.value.isEmpty()) {
                    NullHistoryDataTemplate()
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(color = Color(0xFFE4E7E3)),
                        state = rememberLazyListState()
                    ) {
                        items(
                            /* 在没有分页机制的情况下， 为保证浏览性能， 取进三十次记录 */
                            items = if (neckNeuralRecordsList.value.size > 30) neckNeuralRecordsList.value.subList(
                                0,
                                30
                            ) else neckNeuralRecordsList.value,
                            key = { it.datetime }
                        ) { unit ->
                            NeckNeuralRecordItemCard(
                                neckNeural = unit,
                                onClickEvent = { /* TODO */ }
                            )
                        }
                    }
                }
            }
            /* 背部神经记录*/
            4 -> {
                viewModel.loadPainHistory(painType = PainTypeState.BACK_NEURAL)
                val backNeuralRecordsList = viewModel.backNeuralHistoryList.collectAsState()
                if (backNeuralRecordsList.value.isEmpty()) {
                    NullHistoryDataTemplate()
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxSize()
                            .background(color = Color(0xFFE4E7E3)),
                        state = rememberLazyListState()
                    ) {
                        items(
                            /* 在没有分页机制的情况下， 为保证浏览性能， 取进三十次记录 */
                            items = if (backNeuralRecordsList.value.size > 30) backNeuralRecordsList.value.subList(
                                0,
                                30
                            ) else backNeuralRecordsList.value,
                            key = { it.datetime }
                        ) { unit ->
                            BackNeuralRecordItemCard(
                                backNeural = unit,
                                onClickEvent = { /* TODO */ }
                            )
                        }
                    }
                }
            }

            else -> {

            }
        }
    }
}