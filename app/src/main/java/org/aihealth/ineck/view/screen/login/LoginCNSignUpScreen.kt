package org.aihealth.ineck.view.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHPasswordTextField
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.AIHTextField
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.LoginChinaViewModel

@Composable
fun LoginCNSignUpScreen(
    viewModel: LoginChinaViewModel
) {
    val isReadChecked = viewModel.isReadChecked.collectAsState()
    val dialogVisible = viewModel.dialogVisible.collectAsState()
    BasePageView(
        title = stringResource(id = R.string.sign_up),
        showBackIcon = true
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
                .padding(bottom = 32.dp)
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(48.dp))
            AIHTextField(
                modifier = Modifier
                    .height(IntrinsicSize.Min)
                    .fillMaxWidth(),
                value = viewModel.emailSignUp,
                onValueChange = { viewModel.emailSignUp = it },
                keyboardType = KeyboardType.Email,
                placeholder = stringResource(id = R.string.please_enter_email_account)
            )
            Spacer(modifier = Modifier.height(16.dp))
            AIHPasswordTextField(
                value = viewModel.passwordSignUp,
                onValueChange = {
                    viewModel.passwordSignUp = it
                    viewModel.confirmPassword()
                },
                keyboardType = KeyboardType.Password,
                placeholder = stringResource(id = R.string.please_enter_password)
            )
            if (viewModel.passwordSignUp.isNotBlank()) {
                Spacer(modifier = Modifier.height(16.dp))
                AIHTextField(
                    value = viewModel.passwordConfirm,
                    onValueChange = { viewModel.passwordConfirm = it },
                    keyboardType = KeyboardType.Password,
                    placeholder = stringResource(id = R.string.please_enter_password_again)
                )
                Spacer(modifier = Modifier.height(16.dp))
                PasswordCheckList(
                    modifier = Modifier.fillMaxWidth(),
                    validLength = viewModel.passwordSignUp.length >= 8,
                    validLowercase = viewModel.passwordSignUp.any { it.isDigit() },
                    validUppercase = viewModel.passwordSignUp.any { it.isUpperCase() },
                    validNumber = viewModel.passwordSignUp.any { it.isLowerCase() },
                    validSpecial = viewModel.passwordSignUp.any { it in "!@#$%" },
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            LoginAgreementView(
                checked = isReadChecked.value,
                onCheckedChanged = {
                    viewModel.setIsReadChecked(!isReadChecked.value)
                }
            )
            AIHTextButton(
                text = stringResource(id = R.string.sign_up),
                onClick = {
                    viewModel.signUpClick()
                },
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .padding(top = 30.dp),
                style = TextStyle(
                    fontWeight = FontWeight(400),
                    color = Color(0xFFF7F7F7),
                    fontSize = 20.sp,
                    textAlign = TextAlign.Center
                ),
            )
        }

    }
    AgreementDialog(
        visible = dialogVisible.value,
        onConfirm = {
            viewModel.setIsReadChecked(true)
            viewModel.setDialogVisible(false)
        },
        onCancel = {
            viewModel.setDialogVisible(false)
        }
    )
}

@Preview
@Composable
fun PasswordCheckList(
    modifier: Modifier = Modifier,
    validLength: Boolean = false,
    validLowercase: Boolean = false,
    validUppercase: Boolean = false,
    validNumber: Boolean = false,
    validSpecial: Boolean = false
) {
    val vaild3To4 =
        listOf(validLowercase, validUppercase, validNumber, validSpecial).count { it } in 3..4
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(Color.White, shape = RoundedCornerShape(12.dp)),
        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.Top
    ) {
        Text(
            text = stringResource(id = R.string.password_rule),
            fontSize = 14.sp,
            modifier = Modifier.padding(top = 10.dp, start = 16.dp)
        )
        Column(
            modifier = Modifier
                .padding(start = 20.dp)
                .fillMaxWidth()
        ) {

            ValidationCheckItem(
                stringResource(id = R.string.password_rule_limit_length),
                validLength
            )
            ValidationCheckItem(
                stringResource(id = R.string.password_rule_limit_3_to_4),
                isValid = vaild3To4
            )
            Column(
                modifier = Modifier
                    .padding(start = 24.dp)
                    .fillMaxWidth()
            ) {
                CheckItem(
                    stringResource(id = R.string.password_rule_limit_lowercase),
                    validLowercase
                )
                CheckItem(
                    stringResource(id = R.string.password_rule_limit_uppercase),
                    validUppercase
                )
                CheckItem(
                    stringResource(id = R.string.password_rule_limit_number),
                    validNumber
                )
                CheckItem(
                    stringResource(id = R.string.password_rule_limit_special),
                    validSpecial
                )

            }
        }
    }
}

@Composable
fun ValidationCheckItem(text: String, isValid: Boolean) {
    Row(
        modifier = Modifier.padding(4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        val color = if (isValid) Color(0xFF12783F) else Color.Red
        Icon(
            imageVector = if (isValid) Icons.Default.Check else Icons.Default.Close,
            contentDescription = null,
            tint = color,
            modifier = Modifier.size(16.dp)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(text = text, color = color)
    }
}

@Composable
fun CheckItem(text: String, isValid: Boolean) {
    Row(
        modifier = Modifier.padding(4.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        val color = if (isValid) Color(0xFF12783F) else Color.Gray
        if (isValid) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = color,
                modifier = Modifier.size(16.dp)
            )
        } else {
            Image(
                painter = painterResource(id = R.drawable.spot),
                contentDescription = "",
                modifier = Modifier.size(16.dp)
            )
        }

        Spacer(modifier = Modifier.width(8.dp))
        Text(text = text, color = color)
    }
}