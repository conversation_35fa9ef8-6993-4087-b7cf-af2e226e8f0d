package org.aihealth.ineck.view.screen

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun HelpFeedbackScreen() {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize13 = with(density) { 13.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }
    val fontSize18 = with(density) { 18.sp / fontScale }
    BasePageView(
        showBackIcon = true,
        headerContent = {
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(0.8f),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    modifier = Modifier,
                    text = stringResource(id = R.string.help_feedback),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )

            }
        }
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
                .animateContentSize()
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(36.dp)
                    .background(Color(0XFFCDCDCD))
                    .padding(start = 21.dp),
                contentAlignment = Alignment.CenterStart
            ) {
                Text(
                    text = stringResource(id = R.string.common_problem),
                    fontSize = fontSize18,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF5B5959)
                )

            }
            val problems = stringArrayResource(id = R.array.help_feedback_problem)
            val answers = stringArrayResource(id = R.array.help_feedback_answer)
            for (i in problems.indices) {
                Item(i, problem = problems[i], answer = answers[i])
            }
            AIHButton(
                text = stringResource(id = R.string.want_feedback),
                onClick = {
                    startScreen(Screen.Feedback.route)
                },
                modifier = Modifier
                    .padding(42.dp)
                    .fillMaxWidth(),
                fontSize = 20.sp
            )
        }

    }
}

@Composable
private fun ColumnScope.Item(
    index: Int,
    problem: String,
    answer: String
) {
    var visible by remember {
        mutableStateOf(false)
    }
    val angle by animateFloatAsState(targetValue = if (visible) 90F else 0F, label = "")
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(50.dp)
            .pointerInput(Unit) {
                detectTapGestures {
                    visible = !visible
                }
            }
            .padding(horizontal = 20.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Column(
            modifier = Modifier.weight(0.95f)
        ) {
            Text(
                text = "${index + 1}.$problem",
                fontSize = 14.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0XFF444444),
                modifier = Modifier
            )
        }
        Column(
            modifier = Modifier.weight(0.05f)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.img_next),
                contentDescription = null,
                tint = Color(0XFFD6D6D6),
                modifier = Modifier
                    .size(14.dp)
                    .rotate(angle)
            )
        }
    }
    AnimatedVisibility(visible = visible) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 20.dp, vertical = 10.dp)
        ) {
            Text(text = answer, fontSize = 14.sp, color = Color(0XFF444444))
        }
    }
    AIHDivider()

}