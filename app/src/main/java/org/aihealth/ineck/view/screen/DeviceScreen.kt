package org.aihealth.ineck.view.screen

import android.Manifest
import android.os.Build
import androidx.annotation.DrawableRes
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CardElevation
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hjq.permissions.OnPermissionCallback
import com.hjq.permissions.XXPermissions
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.ActionBar
import org.aihealth.ineck.view.screen.device.MatchingDeviceSheet
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.device.DeviceViewModel

/**
 * 设备页面UI
 */
@Composable
fun DeviceScreen(
    viewmodel: MainViewModel
) {
    // 获取设备ViewModel
    val deviceViewModel = ViewModelProvider(activity)[DeviceViewModel::class.java]

    val context = LocalContext.current
    // 使用rememberCoroutineScope创建协程作用域
    val coroutineScope = rememberCoroutineScope()
    
// 控制 MatchingDeviceSheet 的显示
    var showMatchingDeviceSheet by remember {
        mutableStateOf(false)
    }
    /**
     * 是否显示权限对话框
     */
    var showPowerDialogVisibleState by  remember {
        mutableStateOf(false)
    }

    // 获取各种设备状态
    val neckDeviceConnected by deviceViewModel.neckDeviceConnected.collectAsStateWithLifecycle()
    val backDeviceConnected by deviceViewModel.backDeviceConnected.collectAsStateWithLifecycle()
    val neckDeviceConnecting by deviceViewModel.neckDeviceConnecting.collectAsStateWithLifecycle()
    val backDeviceConnecting by deviceViewModel.backDeviceConnecting.collectAsStateWithLifecycle()
    Column(
        Modifier.fillMaxSize()
    ) {
        ActionBar(
            title = stringResource(id = R.string.device_connection),
            centerTitle = true
        )
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .padding(paddingValues = PaddingValues(vertical = 4.dp, horizontal = 8.dp)),
            verticalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            // aiNeck
            OnlineItem(
                name = stringResource(id = R.string.aiSpine_health_manager),
                description = stringResource(id = R.string.cervical_vertebra_monitoring),
                resourceId = R.drawable.img_aispine,
                isConnection = neckDeviceConnected,
                isConnecting = neckDeviceConnecting,
                onConnectionClick = {
                    viewmodel.deviceScreen.deviceTypeChosen = DeviceType.aiNeck
                    viewmodel.homeScreen.currentDeviceType = DeviceType.aiNeck
                    viewmodel.deviceScreen.checkPermissions(
                        onAllGranted = {
                            LogUtil.d("DeviceScreen", "onAllGranted aiNeck")
                            // 显示匹配设备列表，用户选择后会通过deviceViewModel连接
                            viewmodel.deviceScreen.currentMatchingDeviceType =
                                DeviceType.aiNeck.name
                            showMatchingDeviceSheet = true
                            deviceViewModel.stopScan()
                            deviceViewModel.startScan(DeviceType.aiNeck)
                        },
                        notAllGranted = {
                            LogUtil.d("DeviceScreen", "notAllGranted aiNeck")
                            showPowerDialogVisibleState = true
                        }
                    )
                },
                onConnectingClick ={
                    showMatchingDeviceSheet = !showMatchingDeviceSheet
                },
                onCancelConnectionClick = {
                    deviceViewModel.disconnect(DeviceType.aiNeck)
                },
                onNoneConnectionClick = {
                    viewmodel.homeScreen.currentDeviceType = DeviceType.aiNeckCV
                    MainViewModel.pageIndex = 1
                }
            )
            // aiBack
            OnlineItem(
                name = stringResource(id = R.string.aiBack_health_manager),
                description = stringResource(id = R.string.lumbar_monitoring),
                resourceId = R.drawable.img_aiback,
                isConnection = backDeviceConnected,
                isConnecting = backDeviceConnecting,
                onConnectionClick = {
                    viewmodel.deviceScreen.deviceTypeChosen = DeviceType.aiBack
                    viewmodel.homeScreen.currentDeviceType = DeviceType.aiBack
                    viewmodel.deviceScreen.checkPermissions(
                        onAllGranted = {
                            // 显示匹配设备列表，用户选择后会通过deviceViewModel连接
                            viewmodel.deviceScreen.currentMatchingDeviceType =
                                DeviceType.aiBack.name
                            showMatchingDeviceSheet = true
                            deviceViewModel.startScan(DeviceType.aiBack)
                        },
                        notAllGranted = {
                            LogUtil.d("DeviceScreen", "notAllGranted aiBack")

                            showPowerDialogVisibleState = true
                        }
                    )
                },
                onConnectingClick ={
                    showMatchingDeviceSheet = !showMatchingDeviceSheet
                },
                onCancelConnectionClick = {
                    deviceViewModel.disconnect(DeviceType.aiBack)
                },
                onNoneConnectionClick = {
                    viewmodel.homeScreen.currentDeviceType = DeviceType.aiBackCV
                    MainViewModel.pageIndex = 1
                }
            )
            NotOnlineItem(name = "aiJoint", resourceId = R.drawable.img_aijoint)
            NotOnlineItem(
                name = stringResource(id = R.string.ecg_monitor),
                resourceId = R.drawable.img_ecg_monitor
            )
            NotOnlineItem(
                name = stringResource(id = R.string.oximeter),
                resourceId = R.drawable.img_oximeter
            )
            NotOnlineItem(
                name = stringResource(id = R.string.blood_pressure_monitor),
                resourceId = R.drawable.img_blood_pressure_monitor
            )
            Spacer(modifier = Modifier.height(13.dp))
        }
        if (showPowerDialogVisibleState) {
            PermissionGrantDialog(
                modifier = Modifier.fillMaxWidth(0.9f),
                confirmEvent = {
                    showPowerDialogVisibleState = false
                    val permissions = when {
                        Build.VERSION.SDK_INT < Build.VERSION_CODES.S -> {
                            arrayOf(
                                Manifest.permission.ACCESS_COARSE_LOCATION,
                                Manifest.permission.ACCESS_FINE_LOCATION
                            )
                        }

                        else -> arrayOf(
                            Manifest.permission.BLUETOOTH_CONNECT,
                            Manifest.permission.BLUETOOTH_SCAN
                        )
                    }
                    XXPermissions.with(context)
                        .permission(permissions)
                        .request(object : OnPermissionCallback {
                            override fun onGranted(p0: MutableList<String>, allGranted: Boolean) {
                                if (allGranted) {
                                    if (viewmodel.deviceScreen.deviceTypeChosen == DeviceType.aiJoint) {
                                        startScreen(Screen.AiJoint.route)
                                    } else {
                                        viewmodel.deviceScreen.currentMatchingDeviceType =
                                            viewmodel.deviceScreen.deviceTypeChosen.name
                                        showMatchingDeviceSheet = true
                                        deviceViewModel.stopScan()
                                        deviceViewModel.startScan(viewmodel.deviceScreen.deviceTypeChosen)
                                        // 使用DeviceViewModel替代viewmodel.deviceScreen
//                                        viewmodel.deviceScreen.onConnectDeviceClick(viewmodel.deviceScreen.deviceTypeChosen.name)
                                        // 需要重构搜索设备的流程
                                        // deviceViewModel.connect(mac, viewmodel.deviceScreen.deviceTypeChosen)
                                    }
                                } else {
                                    DialogUtil.showToast(context.getString(R.string.please_grant_permission))
                                }
                            }

                            override fun onDenied(
                                permissions: MutableList<String>,
                                doNotAskAgain: Boolean
                            ) {
                                super.onDenied(permissions, doNotAskAgain)
                                if (doNotAskAgain) {
                                    DialogUtil.showToast(context.getString(R.string.please_grant_permission_manual))
                                    XXPermissions.startPermissionActivity(context, permissions)
                                } else {
                                    DialogUtil.showToast(context.getString(R.string.please_grant_permission))
                                }
                            }

                        })

                },
                cancelEvent = {
                    viewmodel.deviceScreen.showPowerDialogVisibleState = false
                }
            )
        }

        // 使用 AnimatedVisibility 包装 MatchingDeviceSheet
        AnimatedVisibility(
            visible = showMatchingDeviceSheet,
            // 添加进入和退出动画效果
            enter = fadeIn(tween(300)) + slideInVertically(
                initialOffsetY = { it / 2 },
                animationSpec = tween(300)
            ),
            exit = fadeOut(tween(300)) + slideOutVertically(
                targetOffsetY = { it / 2 },
                animationSpec = tween(300)
            ),
            // 动画完成后的回调
            label = "MatchingDeviceSheetAnimation"
        ) {
            MatchingDeviceSheet(
                viewModel = deviceViewModel,
                deviceType = viewmodel.deviceScreen.currentMatchingDeviceType,
                onDismiss = {
                    // 用户主动关闭
                    showMatchingDeviceSheet = false
                }
            )
        }
    }
}

/**
 * 已上线产品item UI
 */
@Preview(showBackground = true)
@Composable
private fun OnlineItem(
    name: String = "aispine series",
    description: String = "aiNeck",
    @DrawableRes resourceId: Int = R.drawable.img_aispine,
    isConnection: Boolean = false,
    isConnecting: Boolean = false,
    onConnectionClick: () -> Unit = {},
    onConnectingClick: () -> Unit = {},
    onCancelConnectionClick: () -> Unit = {},
    onNoneConnectionClick: () -> Unit = {}
) {
    WhiteCard(Modifier.height(163.dp)) {
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(0.7f)
                    .padding(horizontal = 4.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Text(text = name, fontSize = 18.sp, color = Color(0XFF222222))
                Spacer(modifier = Modifier.height(4.dp))
                Text(
                    text = description,
                    color = Color(0XFF555555),
                    fontSize = 14.sp,
                    modifier = Modifier.padding(top = 6.dp, bottom = 19.dp)
                )
                Spacer(modifier = Modifier.height(4.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(IntrinsicSize.Max)
                ) {
                    if (isConnection) {
                        // 已连接状态
                        AIHButton(
                            text = stringResource(id = R.string.disconnect),
                            onClick = onCancelConnectionClick,
                            modifier = Modifier.weight(0.3f),
                            backgroundColor = Color.Red
                        )
                    } else if (isConnecting) {
                        // 连接中状态
                        AIHButton(
                            text = stringResource(id = R.string.connecting),
                            onClick = onConnectingClick,
                            enabled = true,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight(),
                            backgroundColor = Color(0xFF888888)
                        )
                    } else {
                        // 未连接状态
                        AIHButton(
                            text = stringResource(id = R.string.connection_device),
                            onClick = onConnectionClick,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight(),
                            backgroundColor = Color(0XFF1E4BDF)
                        )
                        Spacer(modifier = Modifier.width(5.dp))
                        AIHOutlinedButton(
                            text = stringResource(id = R.string.no_device),
                            onClick = onNoneConnectionClick,
                            modifier = Modifier
                                .weight(1f)
                                .fillMaxHeight()
                        )
                    }
                }
            }
            Image(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(0.3f)
                    .weight(0.2f),
                painter = painterResource(id = resourceId),
                contentDescription = null,
                contentScale = ContentScale.Fit
            )
        }
    }
}

/**
 * 未上线产品item UI
 */
@Preview(showBackground = true)
@Composable
private fun NotOnlineItem(
    name: String = "",
    @DrawableRes resourceId: Int = R.drawable.img_not_online
) {
    WhiteCard(Modifier.height(163.dp)) {
        Row(
            modifier = Modifier.fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(0.7f)
                    .padding(horizontal = 4.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Text(text = name, fontSize = 18.sp, color = Color(0XFF222222))
                Spacer(modifier = Modifier.height(20.dp))
                AIHButton(
                    text = "  " + stringResource(id = R.string.coming_online) + "  ",
                    onClick = { },
                    enabled = false,
                    modifier = Modifier
                )
            }
            Image(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth(0.3f)
                    .weight(0.2f),
                painter = painterResource(id = resourceId),
                contentDescription = null,
                contentScale = ContentScale.Fit
            )
        }
    }
}

/**
 * 权限弹窗
 */
@Composable
private fun PermissionGrantDialog(
    modifier: Modifier = Modifier,
    confirmEvent: () -> Unit,
    cancelEvent: () -> Unit
) {
    Dialog(
        onDismissRequest = { cancelEvent() },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(12.dp))
                .background(Color.White)
                .padding(horizontal = 16.dp),
        ) {
            Text(
                text = stringResource(id = R.string.permission_description),
                style = TextStyle(
                    textAlign = TextAlign.Center,
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Bold,
                    fontSize = 18.sp
                ),
                modifier = Modifier
                    .padding(top = 20.dp)
                    .fillMaxWidth(),
            )
            Text(
                text = stringResource(id = R.string.location_permission),
                modifier = Modifier,
                textAlign = TextAlign.Start,
                color = Color(0XFF333333),
                fontWeight = FontWeight.Medium
            )
            Text(
                // AIHealth在绑定和连接蓝牙设备时收集位置信息以搜索附近的蓝牙设备。
                text = stringResource(id = R.string.location_permission_info),
                modifier = Modifier.padding(top = 8.dp, bottom = 10.dp),
                textAlign = TextAlign.Start,
                color = Color(0x7C353434),
                fontWeight = FontWeight.Light
            )
            AIHDivider()
            Row(
                Modifier
                    .fillMaxWidth()
                    .height(40.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .weight(1F)
                        .clickable {
                            cancelEvent()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = R.string.cancel),
                        color = Color(0XFF1E4BDF),
                        fontSize = 16.sp
                    )
                }
                AIHDivider(isVertical = true)
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .weight(1F)
                        .clickable {
                            confirmEvent()
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = R.string.confirm),
                        color = Color(0XFF1E4BDF),
                        fontSize = 16.sp
                    )
                }
            }
        }
    }

}

@Preview
@Composable
private fun PermissionGrantDialogPreview() {
    AIH_UserTheme {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            PermissionGrantDialog(
                modifier = Modifier.fillMaxWidth(0.9f),
                confirmEvent = {},
                cancelEvent = {}
            )
        }

    }
}

@Composable
fun WhiteCard(
    modifier: Modifier = Modifier,
    shape: Shape = RoundedCornerShape(8.dp),
    elevation: CardElevation = CardDefaults.cardElevation(
        defaultElevation = 1.dp
    ),
    border: BorderStroke? = BorderStroke(1.dp, Color.White),
    content: @Composable ColumnScope.() -> Unit
) {
    Card(
        modifier = modifier,
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.White,
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White
        ),
        shape = shape,
        elevation = elevation,
        border = border
    ) {
        Column(content = content)
    }
}