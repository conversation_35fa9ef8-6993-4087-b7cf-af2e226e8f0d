package org.aihealth.ineck.view.screen

import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Badge
import androidx.compose.material3.Icon
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.NavigationRoute
import org.aihealth.ineck.model.SPConstant.DOCTOR_UUID
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.saveToLocal
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.util.token
import org.aihealth.ineck.view.dialog.AddAttentionDialog
import org.aihealth.ineck.view.screen.exercise.ImprovementScreen
import org.aihealth.ineck.view.screen.my.MyScreen
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.device.DeviceViewModel
import org.aihealth.ineck.viewmodel.impl.ConnectUiState
import org.aihealth.ineck.viewmodel.user

@Composable
fun MainScreen(
    viewModel: MainViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current
) {
    val context = LocalContext.current
    var showConnectFailDialog by remember {
        mutableStateOf(false)
    }
    var showConnectedDialog by remember {
        mutableStateOf(false)
    }
    var showAddPeopleDialog by remember {
        mutableStateOf(false)
    }
    // 获取设备ViewModel
    val deviceViewModel = ViewModelProvider(activity)[DeviceViewModel::class.java]

    val uiState by viewModel.deviceScreen.uiState.collectAsState()
    var previousUiState by remember {
        mutableStateOf(ConnectUiState.ConnectingError(true, 0, "", DeviceType.None.name))
    }
    LaunchedEffect(uiState) {
        if (uiState is ConnectUiState.Connecting) {
            DialogUtil.showLoading()
            previousUiState = ConnectUiState.ConnectingError(true, 0, "", DeviceType.None.name)
        } else {
            DialogUtil.hideLoading()
        }
        if (uiState is ConnectUiState.ConnectingSuccess) {
            DialogUtil.hideLoading()
            showConnectedDialog = true
        }
        if (uiState is ConnectUiState.ConnectingError) {
            DialogUtil.hideLoading()
            val currentError = uiState as ConnectUiState.ConnectingError
            if (previousUiState.res != currentError.res) {
                showConnectFailDialog = true
                previousUiState = currentError
            }
        }
    }
    if (showConnectedDialog) {
        DialogUtil.showToast(
            stringResource(id = R.string.device_connect_success)
        )
        showConnectedDialog = false
        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
    }
    if (showConnectFailDialog) {
        if (uiState is ConnectUiState.ConnectingError) {
            val uiError = uiState as ConnectUiState.ConnectingError
            if (uiError.visibility && uiError.res != null && uiError.address != null && uiError.deviceType != null) {
                ConnectedFailDialog(
                    title = stringResource(id = uiError.res, uiError.deviceType),
                    onDismiss = {
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        showConnectFailDialog = false
                    },
                    onConfirm = {
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        viewModel.deviceScreen.clearConnectedData(viewModel.homeScreen.currentDeviceType.name)
                        showConnectFailDialog = false
                        viewModel.deviceScreen.connectRemoteGatt(
                            uiError.address,
                            uiError.deviceType
                        )
                    }
                )
            }
        }
    }

    LogUtil.i("MainScreen indexPage:${MainViewModel.pageIndex}")
    LaunchedEffect(viewModel) {
        if (token.isNotEmpty()) {
//            DialogUtil.showLoading()
            apiService.getInfo().enqueueBody { response ->
//                DialogUtil.hideLoading()
                if (response?.code == 1) {
                    LogUtil.i("user: ${response.data.toJson()}")
                    try {
                        val gson = Gson()
                        val info = gson.fromJson(response.data, User::class.java)
                        LogUtil.i("user: $user")
                        user = info
                        user.saveToLocal()

                    } catch (e: Exception) {
                        DialogUtil.showToast(context.getString(R.string.online_data_error))
                    }
                } else if (response?.code == 0) {
                    startScreen(Screen.FirstUpdateData.route, true)
                } else {
                    LogUtil.i("user info error")
                    startScreen(Screen.Login.route, true)
                }
            }
        } else {
            LogUtil.i("no token")
            startScreen(Screen.Login.route, true)
        }
    }
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                LogUtil.i("lifeCycle start,uuid:${SPUtil.getString(DOCTOR_UUID, "")}")
                if (SPUtil.getString(DOCTOR_UUID, "").isNotEmpty()) {
                    viewModel.homeScreen.scanUUID = SPUtil.getString(DOCTOR_UUID, "")
                    SPUtil.putString(DOCTOR_UUID, "")
                    apiService.getUserInfoWithoutToken(viewModel.homeScreen.scanUUID)
                        .enqueueBody { res ->
//                            DialogUtil.hideLoading()
                            if (res?.code == 1) {
                                try {
                                    val gson = Gson()
                                    val i = gson.fromJson(res.data, User::class.java)
                                    viewModel.homeScreen.userInfoName = i.name
                                    viewModel.homeScreen.addAttentionDialogVisible = true
                                } catch (e: Exception) {
                                    DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                                }
                            } else {
                                DialogUtil.showToast(localeResources.getString(R.string.online_data_error))
                            }
                        }
                }
            }
        }

        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    LaunchedEffect(Unit) {
        activity.initZoomVideoSDK()
    }
    LaunchedEffect(Unit) {
        withContext(Dispatchers.IO) {
            while (true) {
                delay(1000)
                MainViewModel.onlineTime += 1
            }
        }

    }
    BackHandler(true) {
        activity.moveTaskToBack(true)
    }

    // 创建 PagerState 并根据 MainViewModel.pageIndex 同步
    val pagerState = rememberPagerState(
        initialPage = MainViewModel.pageIndex,
        pageCount = { 6 } // 总共有6个页面
    )
    

    // 当 MainViewModel.pageIndex 变化时更新 pagerState.currentPage
    LaunchedEffect(MainViewModel.pageIndex) {
        if (pagerState.currentPage != MainViewModel.pageIndex) {
            pagerState.scrollToPage(MainViewModel.pageIndex)
        }
    }

    Column(
        Modifier
            .fillMaxSize()
            .navigationBarsPadding()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1F)
                .background(Color(0XFFF7F7F7))
        ) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize(),
                userScrollEnabled = false,
            ) { page ->
                when (page) {
                    0 -> DeviceScreen(viewModel)
                    1 -> HomeScreen(viewModel,deviceViewModel)
                    2 -> ImprovementScreen(viewModel)
                    3 -> ReportScreen(viewModel)
                    4 -> MessageNotification(viewModel)
                    5 -> MyScreen(viewModel)
                }
            }
        }
        
        BottomNavBarView(
            selectedIndex = MainViewModel.pageIndex,
            viewModel = viewModel
        )
    }
    AddAttentionDialog(
        isShowAddAttentionDialog = viewModel.homeScreen.addAttentionDialogVisible,
        name = viewModel.homeScreen.userInfoName,
        onDismissRequest = {
            viewModel.homeScreen.addAttentionDialogVisible = false
        },
        onConfirm = {
            viewModel.addFollow(viewModel.homeScreen.scanUUID)
            viewModel.homeScreen.addAttentionDialogVisible = false
        },
    )

}

@Composable
private fun BottomNavBarView(
    selectedIndex: Int,
    viewModel: MainViewModel = androidx.lifecycle.viewmodel.compose.viewModel()
) {
    val lr = remember(localeResources) {
        localeResources
    }
    val bottomNavigationList =
        lr.let {
            listOf(
                NavigationRoute(R.string.module, R.drawable.img_device),
                NavigationRoute(R.string.monitors, R.drawable.ic_monitors),
                NavigationRoute(
                    R.string.improvement_page,
                    R.drawable.img_improvement
                ),
                NavigationRoute(R.string.report, R.drawable.img_report),
                NavigationRoute(R.string.chat, R.drawable.ic_chat),
                NavigationRoute(R.string.mine, R.drawable.img_my),
            )
        }

    NavigationBar(
        containerColor = Color.White,
        tonalElevation = 1.dp
    ) {
        Item(
            isSelected = selectedIndex == 0,
            item = bottomNavigationList[0],
            onClick = { MainViewModel.pageIndex = 0 })
        Item(
            isSelected = selectedIndex == 1,
            item = bottomNavigationList[1],
            onClick = { MainViewModel.pageIndex = 1 })
        Item(
            isSelected = selectedIndex == 2,
            item = bottomNavigationList[2],
            onClick = { MainViewModel.pageIndex = 2 })
        Item(
            isSelected = selectedIndex == 3,
            item = bottomNavigationList[3],
            onClick = { MainViewModel.pageIndex = 3 })
        Item(
            isSelected = selectedIndex == 4,
            item = bottomNavigationList[4],
            badgeCount = viewModel.unreadMessageCount,
            onClick = { MainViewModel.pageIndex = 4 })
        Item(
            isSelected = selectedIndex == 5,
            item = bottomNavigationList[5],
            onClick = { MainViewModel.pageIndex = 5 })
    }
}

@Composable
private fun RowScope.Item(
    isSelected: Boolean,
    item: NavigationRoute,
    badgeCount: Int = 0,
    onClick: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize = with(density) { 12.dp.toSp() }
    val it = rememberUpdatedState(newValue = item)
    NavigationBarItem(
        selected = isSelected,
        onClick = {
            if (!isSelected) {
                onClick()
            }
        },
        label = {
            Text(
                text = stringResource(id = it.value.title),
                fontSize = fontSize,
                maxLines = 1
            )
        },
        icon = {
            Box {
                Icon(
                    painter = painterResource(id = it.value.icon),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(bottom = 2.dp, top = 2.dp)
                        .size(18.dp),
                )
                
                // 显示未读消息徽章
                if (badgeCount > 0) {
                    Badge(
                        modifier = Modifier
                            .size(16.dp)
                            .align(Alignment.TopEnd)
                            .offset(x = 8.dp, y = (-4).dp),
                        containerColor = Color.Red
                    ) {
                        val displayText = when {
                            badgeCount > 99 -> "99+"
                            else -> badgeCount.toString()
                        }
                        
                        // 根据数字位数动态调整字体大小
                        val dynamicFontSize = when {
                            badgeCount > 99 -> with(density) { 4.dp.toSp() }  // 三位数或更多（99+）
                            badgeCount > 9 -> with(density) { 5.dp.toSp() }   // 两位数
                            else -> with(density) { 8.dp.toSp() }            // 一位数
                        }
                        
                        Text(
                            text = displayText,
                            fontSize = dynamicFontSize,
                            color = Color.White,
                            modifier = Modifier.padding(horizontal = 1.dp) // 减少内部padding
                        )
                    }
                }
            }
        },
        colors = NavigationBarItemDefaults.colors(
            selectedIconColor = Color(0XFF1E4BDF),
            selectedTextColor = Color(0XFF1E4BDF),
            indicatorColor = Color.White,
            unselectedIconColor = Color(0XFF999999),
            unselectedTextColor = Color(0XFF999999),
            disabledIconColor = Color.Transparent,
            disabledTextColor = Color.Transparent,
        )
    )
}
