package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import android.widget.Toast
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.vitalsigns.BloodGlucoseHistoryUnit
import org.aihealth.ineck.model.vitalsigns.BloodOxygenHistoryUnit
import org.aihealth.ineck.model.vitalsigns.BloodPressureHistoryUnit
import org.aihealth.ineck.model.vitalsigns.BodyTemperatureHistoryUnit
import org.aihealth.ineck.model.vitalsigns.HeartRateHistoryUnit
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.UnitUtil.celsiusToFahrenheit
import org.aihealth.ineck.util.UnitUtil.glucoseMmolLToMgDl
import org.aihealth.ineck.util.convertUTCToLocal
import org.aihealth.ineck.util.getDayStatus
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHSwitch
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState
import org.aihealth.ineck.view.custom.VitalSignsPostBottomDrawer
import org.aihealth.ineck.viewmodel.HistoryVitalSignsViewModel
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.VitalSignHistoryResultState
import org.aihealth.ineck.viewmodel.user
import java.util.Calendar

/**
 *  生命体征历史记录页
 *  @param  viewModel   生命体征页ViewModel
 *  @param  vitalSignNumberState    生命体征类型状态
 */
@Composable
fun HistoryVitalSignsScreen(
    viewModel: HistoryVitalSignsViewModel,
    vitalSignNumberState: Int,
    mainViewModel: MainViewModel
) {

    /** 上下文 */
    val context = LocalContext.current

    val resultState = viewModel.resultList.collectAsState()
    /* 是否采用摄氏度作为体温历史记录显示单位状态 */
    val isCelsiusState = remember(user.preferences.temperatureUnit) {
        mutableStateOf(user.preferences.temperatureUnit == "M")
    }
    /* 生命体征卡片血糖测量值测量单位是否采用 毫摩尔/升(mmol/L) 状态 */
    val isMmolL = remember(user.preferences.bloodSugarUnit) {
        mutableStateOf(user.preferences.bloodSugarUnit == "I")
    }

    /** 上传生命体征数据底部抽屉显示标识 */
    val vitalSignPostDialogVisible =
        viewModel.vitalSignPostDialogVisibleState.collectAsState()

    BasePageView(
        title = when (vitalSignNumberState.mapToVitalSignState()) {
            VitalSignPostDialogVisibleState.NONE -> stringResource(id = R.string.history_record_title_text)
            VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> stringResource(id = R.string.heart_rate)
            VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> stringResource(id = R.string.body_temperature)
            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> stringResource(id = R.string.blood_oxygen)
            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> stringResource(id = R.string.blood_pressure)
            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> stringResource(id = R.string.blood_glucose)
        },
        showBackIcon = true,
        headerContent = {
            if (resultState.value is VitalSignHistoryResultState.Success) {
                if (vitalSignNumberState.mapToVitalSignState() == VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE) {
                    AIHSwitch(
                        checked = isCelsiusState.value,
                        onCheckedChange = { celsiusState ->
                            LogUtil.i("isCelsiusState.value: ${celsiusState}")
                            user = user.copy(
                                preferences = user.preferences.copy(
                                    temperatureUnit = if (celsiusState) "M" else "I"
                                )
                            )
                            apiService.updateInfo(
                                body = hashMapOf(
                                    Pair(
                                        "preferences",
                                        user.preferences
                                    )
                                )
                            ).enqueueBody {
                                isCelsiusState.value = celsiusState
                            }
                        },
                        checkedText = "℃",
                        unCheckedText = "℉",
                        trackUnCheckedColor = Color(0xFF7893EC),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(vertical = 4.dp, horizontal = 12.dp)
                    )
                } else if (vitalSignNumberState.mapToVitalSignState() == VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE) {
                    AIHSwitch(
                        checked = isMmolL.value,
                        onCheckedChange = { mmol ->
                            user = user.copy(
                                preferences = user.preferences.copy(
                                    bloodSugarUnit = if (mmol) "I" else "M"
                                )
                            )
                            user.preferences.bloodSugarUnit = if (mmol) "I" else "M"
                            apiService.updateInfo(
                                body = hashMapOf(
                                    Pair(
                                        "preferences",
                                        user.preferences
                                    )
                                )
                            ).enqueueBody {
                                isMmolL.value = mmol
                            }
                        },
                        checkedText = "mmol/L",
                        unCheckedText = "mg/dL",
                        trackUnCheckedColor = Color(0xFF7893EC),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(vertical = 4.dp, horizontal = 12.dp),
                        switchWidth = 74.dp
                    )
                }
            }
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(Color(0xFFF7F7F7)),
        ) {
            Spacer(modifier = Modifier.height(1.dp))
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.4f)
                    .background(Color.White)
            ) {
                when (resultState.value) {
                    VitalSignHistoryResultState.Loading -> {
                        /* 历史数据加载中... */
                        LoadingHistoryDataTemplate()
                    }

                    is VitalSignHistoryResultState.Failure -> {
                        /* 请求出现错误，点击重试 */
                        FailureHistoryDataTemplate {
                            viewModel.loadVitalSignHistoryData(vitalSignsState = vitalSignNumberState.mapToVitalSignState())
                        }
                    }

                    is VitalSignHistoryResultState.Success -> {
                        val successList = resultState.value as VitalSignHistoryResultState.Success
                        when (vitalSignNumberState.mapToVitalSignState()) {
                            VitalSignPostDialogVisibleState.NONE -> {}

                            VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> {
                                val heartRateList = successList.data
                                VitalSignValuePanelOfTemperatureAndBloodSurgeAndHeartRate(
                                    value = if (heartRateList.isEmpty()) {
                                        "--"
                                    } else {
                                        (heartRateList.first() as HeartRateHistoryUnit).value.toString()
                                    },
                                    unit = R.string.heart_Unit,
                                    suggestion = 0

                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> {
                                val bodyTemperatureList = successList.data
                                VitalSignValuePanelOfTemperatureAndBloodSurgeAndHeartRate(
                                    value = if (bodyTemperatureList.isEmpty()) {
                                        "--"
                                    } else {
                                        if (isCelsiusState.value) {
                                            (bodyTemperatureList.first() as BodyTemperatureHistoryUnit).temperature.toString()
                                        } else {
                                            "%.1f".format(
                                                celsiusToFahrenheit((bodyTemperatureList.first() as BodyTemperatureHistoryUnit).temperature.toDouble())
                                            )
                                        }
                                    },
                                    unit = if (isCelsiusState.value) R.string.temperature_Unit_Celsius else R.string.temperature_Unit_Fahrenheit,
                                    suggestion = 0
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> {
                                val bloodOxygenList = successList.data
                                VitalSignValuePanelOfBloodOxygen(
                                    value = if (bloodOxygenList.isEmpty()) {
                                        "-- %"
                                    } else {
                                        "${(bloodOxygenList.first() as BloodOxygenHistoryUnit).saturation} %"
                                    },
                                    suggestion = ""
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> {
                                val bloodPressureList = successList.data
                                VitalSignValuePanelOfBloodPressure(
                                    value = if (bloodPressureList.isEmpty()) {
                                        "--/--"
                                    } else {
                                        "${(bloodPressureList.first() as BloodPressureHistoryUnit).systolic}/${(bloodPressureList.first() as BloodPressureHistoryUnit).diastolic}"
                                    },
                                    unit = R.string.blood_pressure_Unit,
                                    suggestion = ""
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> {
                                val bloodGlucoseList = successList.data
                                VitalSignValuePanelOfTemperatureAndBloodSurgeAndHeartRate(
                                    value = if (bloodGlucoseList.isEmpty()) {
                                        "--"
                                    } else {
                                        if (isMmolL.value) {
                                            (bloodGlucoseList.first() as BloodGlucoseHistoryUnit).level.toString()
                                        } else {
                                            "%.1f".format(
                                                glucoseMmolLToMgDl((bloodGlucoseList.first() as BloodGlucoseHistoryUnit).level.toDouble())
                                            )
                                        }
                                    },
                                    unit = if (isMmolL.value) R.string.blood_glucose_Unit_mmolL else R.string.blood_glucose_Unit_mgdl,
                                    suggestion = 0
                                )
                            }
                        }

                    }
                }
            }

            Spacer(modifier = Modifier.height(10.dp))
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(0.45f)
                    .background(Color.White)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_vital_sign_history),
                        contentDescription = "image description",
                        contentScale = ContentScale.None,
                        modifier = Modifier.padding(start = 16.dp, end = 4.dp)
                    )
                    Text(
                        text = stringResource(id = R.string.history_record),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier
                    )

                }
                when (resultState.value) {
                    VitalSignHistoryResultState.Loading -> {
                        /* 历史数据加载中... */
                        LoadingHistoryDataTemplate()
                    }

                    is VitalSignHistoryResultState.Failure -> {
                        /* 请求出现错误，点击重试 */
                        FailureHistoryDataTemplate {
                            viewModel.loadVitalSignHistoryData(vitalSignsState = vitalSignNumberState.mapToVitalSignState())
                        }
                    }

                    is VitalSignHistoryResultState.Success -> {
                        val successList = resultState.value as VitalSignHistoryResultState.Success
                        if (successList.data.isEmpty()) {
                            /* 没有数据 */
                            NullHistoryDataTemplateOfVitalSign(
                            )
                        } else {
                            when (vitalSignNumberState.mapToVitalSignState()) {
                                VitalSignPostDialogVisibleState.NONE -> {
                                    /* 没有数据 */
                                    NullHistoryDataTemplate()
                                }

                                VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> {
                                    val heartRateList = successList.data
                                    LazyColumn(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color.White),
                                        state = rememberLazyListState()
                                    ) {
                                        itemsIndexed(
                                            items = heartRateList,
                                        ) { index, unit ->
                                            HistoryVitalSignsUnitItemOfHeartRate(
                                                heartRateHistoryUnit = unit as HeartRateHistoryUnit,
                                                indexOfItem = index
                                            )
                                            AIHDivider(
                                                color = Color(0XFFDCDCDC),
                                            )
                                        }
                                    }
                                }

                                VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> {
                                    val bodyTemperatureList = successList.data
                                    LazyColumn(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color.White),
                                        state = rememberLazyListState()
                                    ) {
                                        itemsIndexed(
                                            items = bodyTemperatureList,
                                        ) { index, unit ->
                                            HistoryVitalSignsUnitItemOfBodyTemperature(
                                                bodyTemperatureHistoryUnit = unit as BodyTemperatureHistoryUnit,
                                                isCelsius = isCelsiusState.value,
                                                indexOfItem = index
                                            )

                                            AIHDivider(
                                                color = Color(0XFFDCDCDC),
                                            )
                                        }
                                    }
                                }

                                VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> {
                                    val bloodOxygenList = successList.data
                                    LazyColumn(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color.White),
                                        state = rememberLazyListState()
                                    ) {
                                        itemsIndexed(
                                            items = bloodOxygenList,
                                        ) { index, unit ->
                                            HistoryVitalSignsUnitItemOfBloodOxygen(
                                                bloodOxygenHistoryUnit = unit as BloodOxygenHistoryUnit,
                                                indexOfItem = index
                                            )

                                            AIHDivider(
                                                color = Color(0XFFDCDCDC),
                                            )
                                        }
                                    }
                                }

                                VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> {
                                    val bloodPressureList = successList.data
                                    LazyColumn(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color.White),
                                        state = rememberLazyListState()
                                    ) {
                                        itemsIndexed(
                                            items = bloodPressureList,
                                        ) { index, unit ->
                                            HistoryVitalSignsUnitItemOfBloodPressure(
                                                bloodPressureHistoryUnit = unit as BloodPressureHistoryUnit,
                                                indexOfItem = index
                                            )

                                            AIHDivider(
                                                color = Color(0XFFDCDCDC),
                                            )
                                        }
                                    }
                                }

                                VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> {
                                    val bloodGlucoseList = successList.data
                                    LazyColumn(
                                        modifier = Modifier
                                            .fillMaxSize()
                                            .background(Color(0xFFF5F6F8)),
                                        state = rememberLazyListState()
                                    ) {
                                        itemsIndexed(
                                            items = bloodGlucoseList,
                                        ) { index, unit ->
                                            HistoryVitalSignsUnitItemOfBloodGlucose(
                                                bloodGlucoseHistoryUnit = unit as BloodGlucoseHistoryUnit,
                                                isMmolL = isMmolL.value,
                                                indexOfItem = index
                                            )
                                            AIHDivider(
                                                color = Color(0XFFDCDCDC),
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

            }
            AddVitalSignsButton(
                modifier = Modifier
                    .fillMaxWidth(),
                onClick = {
                    if (resultState.value is VitalSignHistoryResultState.Success) {
                        when (vitalSignNumberState.mapToVitalSignState()) {
                            VitalSignPostDialogVisibleState.NONE -> Unit
                            VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE -> {
                                viewModel.changeVitalSignPostDialogVisibleState(
                                    VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE -> {
                                viewModel.changeVitalSignPostDialogVisibleState(
                                    VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN -> {
                                viewModel.changeVitalSignPostDialogVisibleState(
                                    VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE -> {
                                viewModel.changeVitalSignPostDialogVisibleState(
                                    VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE
                                )
                            }

                            VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE -> {
                                viewModel.changeVitalSignPostDialogVisibleState(
                                    VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE
                                )
                            }
                        }
                    } else {
                        Unit
                    }
                }
            )
        }

    }

    /* 生命体征数据手动上传底部抽屉 */
    VitalSignsPostBottomDrawer(
        visible = vitalSignPostDialogVisible.value,
        context = context,
        isCelsiusState = isCelsiusState,
        isMmolL = isMmolL,
        postVitalSignData = { stringData ->
            viewModel.postVitalSignData(
                data = stringData,
                toastEvent = { success, title ->
                    if (success) {
                        LogUtil.i("upload success")
                        Toast.makeText(
                            context,
                            context.getString(title) + context.getString(R.string.data_upload_success),
                            Toast.LENGTH_SHORT
                        ).show()
                    } else {
                        Toast.makeText(
                            context,
                            context.getString(title) + context.getString(R.string.data_upload_failed),
                            Toast.LENGTH_SHORT
                        ).show()
                    }
                    /* 若发生数据提交动作，则更新生命体征数据面板数据 */
                    viewModel.loadVitalSignHistoryData(vitalSignsState = vitalSignNumberState.mapToVitalSignState())
                    mainViewModel.homeScreen.loadVitalSignData()
                }
            )
        },
        onDismissEvent = { isPost ->
            viewModel.changeVitalSignPostDialogVisibleState(
                VitalSignPostDialogVisibleState.NONE
            )
        },
        onChangeCelsiusStateEvent = {
            user = user.copy(
                preferences = user.preferences.copy(
                    temperatureUnit = if (!isCelsiusState.value) "I" else "M"
                )
            )
            apiService.updateInfo(
                body = hashMapOf(
                    Pair(
                        "preferences",
                        user.preferences
                    )
                )
            ).enqueueBody {
                isCelsiusState.value = !isCelsiusState.value
            }
        },
        onChangeMmolLStateEvent = {
            user = user.copy(
                preferences = user.preferences.copy(
                    bloodSugarUnit = if (!isMmolL.value) "I" else "M"
                )
            )
            apiService.updateInfo(
                body = hashMapOf(
                    Pair(
                        "preferences",
                        user.preferences
                    )
                )
            ).enqueueBody {
                isMmolL.value = !isMmolL.value
            }
        }
    )
}

/**
 *  历史数据单元 - 心率♥
 */
@Composable
fun HistoryVitalSignsUnitItemOfHeartRate(
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
        .fillMaxWidth()
        .height(80.dp)
        .background(Color.White),
    heartRateHistoryUnit: HeartRateHistoryUnit,
    indexOfItem: Int,
) {
    Row(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = 12.dp)
                .weight(5f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(id = if (indexOfItem != 0) R.string.post_time_of_history_record else R.string.last_post_time_of_history_record),
                fontSize = 16.sp,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(8.dp))
            val timestamp = convertUTCToLocal(heartRateHistoryUnit.dateTime)
            Text(
                text = when (getDayStatus(timestamp)) {
                    1 -> {
                        stringResource(R.string.post_time)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    0 -> {
                        stringResource(R.string.yesterday)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    else -> {
                        TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            stringResource(id = R.string.year_month_format),
                        )
                    }
                },
                fontSize = 15.sp,
                color = Color.Black.copy(alpha = .8f),
                fontWeight = FontWeight.Light
            )
        }
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1.5f),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "${heartRateHistoryUnit.value} bpm",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
    }
}

/**
 *  历史数据单元条目 - 体温
 */
@Composable
fun HistoryVitalSignsUnitItemOfBodyTemperature(
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
        .fillMaxWidth()
        .height(80.dp)
        .background(Color.White),
    bodyTemperatureHistoryUnit: BodyTemperatureHistoryUnit,
    isCelsius: Boolean,
    indexOfItem: Int
) {
    Row(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = 12.dp)
                .weight(5f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(id = if (indexOfItem != 0) R.string.post_time_of_history_record else R.string.last_post_time_of_history_record),
                fontSize = 16.sp,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(8.dp))
            val timestamp = convertUTCToLocal(bodyTemperatureHistoryUnit.dateTime)

            Text(
                text = when (getDayStatus(timestamp)) {
                    1 -> {
                        stringResource(R.string.post_time)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    0 -> {
                        stringResource(R.string.yesterday)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    else -> {
                        TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            stringResource(id = R.string.year_month_format),
                        )
                    }
                },
                fontSize = 15.sp,
                color = Color.Black.copy(alpha = .8f),
                fontWeight = FontWeight.Light
            )
        }
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1.5f),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (isCelsius) "${bodyTemperatureHistoryUnit.temperature} ℃" else "${
                    "%.1f".format(
                        celsiusToFahrenheit(bodyTemperatureHistoryUnit.temperature.toDouble())
                    )
                } ℉",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
    }
}

/**
 *  历史数据单元 - 血压
 */
@Composable
fun HistoryVitalSignsUnitItemOfBloodPressure(
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
        .fillMaxWidth()
        .height(80.dp)
        .background(Color.White),
    bloodPressureHistoryUnit: BloodPressureHistoryUnit,
    indexOfItem: Int
) {
    Row(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = 12.dp)
                .weight(5f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(id = if (indexOfItem != 0) R.string.post_time_of_history_record else R.string.last_post_time_of_history_record),
                fontSize = 16.sp,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(8.dp))
            val timestamp = convertUTCToLocal(bloodPressureHistoryUnit.dateTime)

            Text(
                text = when (getDayStatus(timestamp)) {
                    1 -> {
                        stringResource(R.string.post_time)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    0 -> {
                        stringResource(R.string.yesterday)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    else -> {
                        TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            stringResource(id = R.string.year_month_format),
                        )
                    }
                },
                fontSize = 15.sp,
                color = Color.Black.copy(alpha = .8f),
                fontWeight = FontWeight.Light
            )
        }
        val spacerDivider = ": "
        val quantificationUnit = "mmhg"
        val dataContentOfDiastolic = buildAnnotatedString {
            withStyle(
                SpanStyle(
                    color = Color(0xFF999999),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Light
                )
            ) {
                append(stringResource(id = R.string.diastolic))
                append(spacerDivider)
            }
            withStyle(SpanStyle(color = Color(0xFF6181E9), fontSize = 14.sp)) {
                append((bloodPressureHistoryUnit.diastolic).let { diastolic -> if (diastolic >= 0) diastolic.toString() else "--" })
                append(quantificationUnit)
            }
        }
        val dataContentOfSystolic = buildAnnotatedString {
            withStyle(
                SpanStyle(
                    color = Color(0xFF999999),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Light
                )
            ) {
                append(stringResource(id = R.string.systolic))
                append(spacerDivider)
            }
            withStyle(SpanStyle(color = Color(0xFF6181E9), fontSize = 14.sp)) {
                append((bloodPressureHistoryUnit.systolic).let { systolic -> if (systolic >= 0) systolic.toString() else "--" })
                append(quantificationUnit)
            }
        }
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .weight(3f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = dataContentOfDiastolic,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 6.dp, vertical = 1.dp)
            )
            Text(
                text = dataContentOfSystolic,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 6.dp, vertical = 1.dp)
            )
        }
    }
}

/**
 *  历史数据单元条目 - 血氧饱和度
 */
@Composable
fun HistoryVitalSignsUnitItemOfBloodOxygen(
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
        .fillMaxWidth()
        .height(80.dp)
        .background(Color.White),
    bloodOxygenHistoryUnit: BloodOxygenHistoryUnit,
    indexOfItem: Int
) {
    Row(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = 12.dp)
                .weight(5f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(id = if (indexOfItem != 0) R.string.post_time_of_history_record else R.string.last_post_time_of_history_record),
                fontSize = 16.sp,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(8.dp))
            val timestamp = convertUTCToLocal(bloodOxygenHistoryUnit.dateTime)
            LogUtil.i("original Time:${bloodOxygenHistoryUnit.dateTime},timestamp: $timestamp,${getDayStatus(timestamp)}")
            Text(
                text = when (getDayStatus(timestamp)) {
                    1 -> {
                        stringResource(R.string.post_time)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    0 -> {
                        stringResource(R.string.yesterday)+" " + TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            "HH:mm"
                        )
                    }

                    else -> {
                        TimeUtil.dateToSimpleDateFormat(
                            timestamp,
                            stringResource(id = R.string.year_month_format),
                        )
                    }
                },
                fontSize = 15.sp,
                color = Color.Black.copy(alpha = .8f),
                fontWeight = FontWeight.Light
            )
        }
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1.5f),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "${bloodOxygenHistoryUnit.saturation} %",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
    }
}

/**
 *  历史数据单元条目 - 血糖
 */
@Composable
fun HistoryVitalSignsUnitItemOfBloodGlucose(
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
        .fillMaxWidth()
        .height(80.dp)
        .background(Color.White),
    bloodGlucoseHistoryUnit: BloodGlucoseHistoryUnit,
    isMmolL: Boolean,
    indexOfItem: Int
) {
    Row(modifier = modifier) {
        Column(
            modifier = Modifier
                .fillMaxHeight()
                .padding(horizontal = 12.dp)
                .weight(4f),
            verticalArrangement = Arrangement.Center
        ) {
            Text(
                text = stringResource(id = if (indexOfItem != 0) R.string.post_time_of_history_record else R.string.last_post_time_of_history_record),
                fontSize = 16.sp,
                color = Color.Black
            )
            Spacer(modifier = Modifier.height(8.dp))
            val timestamp = convertUTCToLocal(bloodGlucoseHistoryUnit.dateTime)
            
            // 添加调试日志 - 血糖记录时间转换
            LogUtil.d("BloodGlucose", "原始UTC时间: ${bloodGlucoseHistoryUnit.dateTime} -> 本地时间: $timestamp | 血糖值: ${bloodGlucoseHistoryUnit.level}")

            val dayStatus = getDayStatus(timestamp)
            val displayText = when (dayStatus) {
                1 -> {
                    stringResource(R.string.post_time)+" " + TimeUtil.dateToSimpleDateFormat(
                        timestamp,
                        "HH:mm"
                    )
                }

                0 -> {
                    stringResource(R.string.yesterday)+" " + TimeUtil.dateToSimpleDateFormat(
                        timestamp,
                        "HH:mm"
                    )
                }

                else -> {
                    TimeUtil.dateToSimpleDateFormat(
                        timestamp,
                        stringResource(id = R.string.year_month_format),
                    )
                }
            }
            
            LogUtil.d("BloodGlucose", "最终显示文本: $displayText")

            Text(
                text = displayText,
                fontSize = 15.sp,
                color = Color.Black.copy(alpha = .8f),
                fontWeight = FontWeight.Light
            )
        }
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .weight(2f),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = if (isMmolL) "${bloodGlucoseHistoryUnit.level} mmol/L" else "${
                    "%.1f".format(
                        glucoseMmolLToMgDl(bloodGlucoseHistoryUnit.level.toDouble())
                    )
                } mg/dL",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
    }
}

/**
 *  将整形数字对应的生命体征标识类型转述
 */
internal fun Int.mapToVitalSignState(): VitalSignPostDialogVisibleState {
    when (this) {
        0 -> {
            /* 心率 */
            return VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE
        }

        1 -> {
            /* 体温 */
            return VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE
        }

        2 -> {
            /* 血氧 */
            return VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN
        }

        3 -> {
            /* 血压 */
            return VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE
        }

        4 -> {
            /* 血糖 */
            return VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE
        }

        else -> {
            /* 心率 */
            return VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE
        }
    }
}

/**
 *  判断目标时间戳相对于现在的时间而言，是今天、昨天还是更早时间
 *  @param  timestamp   目标时间戳
 *  @return 1 代表今天， 0 代表昨天， -1 代表昨天之前
 */
private fun judgeTimestamp(timestamp: Long): Int {
    val today = getDefaultDate()
    val yesterday = getDefaultDate().apply {
        add(Calendar.DATE, -1)
    }
    val targetDay = Calendar.getInstance().apply {
        timeInMillis = timestamp * 1000
    }
    return if (targetDay > today) 1
    else if (targetDay > yesterday) 0
    else -1
}

/**
 *  VitalSign 空数据显示模板
 */
@Composable
internal fun NullHistoryDataTemplateOfVitalSign() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(color = Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceAround
    ) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                painter = painterResource(id = R.drawable.icon_empty_record_here),
                contentDescription = stringResource(id = R.string.empty_records),
                tint = Color.Black.copy(alpha = .4f),
                modifier = Modifier
                    .size(80.dp)
                    .padding(12.dp)
            )
            Text(
                text = stringResource(id = R.string.empty_records),
                color = Color.Black.copy(alpha = .4f),
                fontSize = 14.sp,
                fontWeight = FontWeight.Light,
            )

        }

    }
}

@Composable
fun AddVitalSignsButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight()
            .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Button(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF6181E9)
            ),
            shape = RoundedCornerShape(size = 4.dp),
            onClick = { onClick() }
        ) {
            Text(
                text = stringResource(id = R.string.add_vital_signs),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFFFFFFFF),
                )
            )
        }
    }

}

@Composable
fun VitalSignValuePanelOfTemperatureAndBloodSurgeAndHeartRate(
    value: String = "78",
    @StringRes unit: Int,
    status: Int = 0,
    @StringRes suggestion: Int,
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = value,
            style = TextStyle(
                fontSize = 36.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
            ),
            modifier = Modifier.padding(top = 32.dp)
        )
        Text(
            text = stringResource(id = unit),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF999999),
                textAlign = TextAlign.Center,
            ),
            modifier = Modifier.padding(top = 4.dp)
        )
    }

}

@Composable
fun VitalSignValuePanelOfBloodPressure(
    value: String = "85/133",
    @StringRes unit: Int,
    status: Int = 0,
    suggestion: String = "收缩压正常，舒张压正常，血压正常。"
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = value,
            style = TextStyle(
                fontSize = 36.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
            ),
            modifier = Modifier.padding(top = 32.dp)
        )
        Text(
            text = stringResource(id = unit),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF999999),
                textAlign = TextAlign.Center,
            ),
            modifier = Modifier.padding(top = 4.dp)
        )
    }

}

@Composable
fun VitalSignValuePanelOfBloodOxygen(
    value: String = "99%",
    status: Int = 0,
    suggestion: String = "",
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = value,
            style = TextStyle(
                fontSize = 36.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
            ),
            modifier = Modifier.padding(top = 32.dp)
        )
    }

}