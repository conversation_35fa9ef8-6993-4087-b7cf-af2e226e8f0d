package org.aihealth.ineck.view.screen.vcguide

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import kotlinx.coroutines.delay
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TextToSpeech

@Composable
fun VCNeckDetectingScreen(
    modifier: Modifier = Modifier,
    isMuteState: Boolean = false,
    isVoiceEnable: Boolean = true,
    changeMuteState: () -> Unit = {},
    nextPage: () -> Unit = {},
    timeOut: () -> Unit = {},
) {
    var isNextPage by remember {
        mutableStateOf(false)
    }
    var isTimeOut by remember {
        mutableStateOf(false)
    }
    var validPitchCount by remember { mutableIntStateOf(0) }
    var isValidPose by remember { mutableStateOf(false) }
    val circleProgress = remember { Animatable(0f) }
    val (oPitch, oRoll) = rememberOrientation()
    val pitch by animateIntAsState(
        targetValue = oPitch,
        animationSpec = tween(durationMillis = 300),
        label = "pitch"
    )
    val roll by animateIntAsState(
        targetValue = oRoll,
        animationSpec = tween(durationMillis = 300),
        label = "roll",
    )
    LogUtil.i("pitch:$pitch roll:$roll")
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 静音按钮
        Box(
            modifier = modifier.fillMaxWidth()
        ) {
            MuteButton(
                isMuteState = isMuteState,
                isVoiceEnable = isVoiceEnable,
                changeMuteState = { changeMuteState() },
                modifier = Modifier
                    .padding(16.dp)
                    .size(30.dp)
                    .align(Alignment.CenterEnd)
            )
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 10.dp),
            horizontalArrangement = Arrangement.Center,
        ) {
            if (isNextPage) {
                /** 垂直校准完成动画 */
                val finishComposition by rememberLottieComposition(
                    spec = LottieCompositionSpec.RawRes(
                        R.raw.vertical_calibration_finish_animation
                    )
                )
                LottieAnimation(
                    composition = finishComposition,
                    iterations = 1,
                    modifier = Modifier.size(50.dp)
                )

            } else {
                val speechText = stringResource(id = R.string.detect_guide_text)
                Text(
                    modifier = Modifier.padding(horizontal = 8.dp),
                    text = speechText,
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFD9352E),
                    )
                )
                LaunchedEffect(isMuteState) {
                    if (isMuteState) {
                        TextToSpeech.ttsStop()
                    } else {
                        TextToSpeech.ttsSpeaking(speechText)
                    }
                }
            }
        }

        LaunchedEffect(isNextPage) {
            if (isNextPage) {
                delay(1000)
                nextPage()
            }
        }
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 10.dp, bottom = 20.dp)
                .height(220.dp)
                .background(color = Color.Transparent)
        ) {
            val frameBlockColor: Color by animateColorAsState(
                if (isValidPose) Color(0xFF4CAF50) else Color(0xFFFC7349), 
                label = "frameBlockColor"
            )
            val circleProgressColor: Color by animateColorAsState(
                if (isValidPose) Color(0xFF4CAF50) else Color(0xFFFC7349),
                label = "circleProgressColor"
            )
            /* 外围检测框 */
            Icon(
                painter = painterResource(id = R.drawable.img_vc_detect_block),
                contentDescription = "Detection frame",
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
                tint = frameBlockColor
            )
            /* 检测框周边圆圈进度条 */
            CircularProgressIndicator(
                progress = { circleProgress.value },
                modifier = Modifier
                    .size(196.dp)
                    .clip(CircleShape)
                    .background(color = Color(0x80CCCCCC))
                    .align(Alignment.Center),
                color = circleProgressColor,
                strokeWidth = 10.dp,
            )
            Surface(
                modifier = Modifier
                    .size(180.dp)
                    .align(Alignment.Center),
                shape = CircleShape
            ) {
                Image(
                    painter = painterResource(id = R.drawable.neck_cv_sample),
                    contentDescription = "Neck sample",
                    modifier = Modifier
                        .fillMaxSize()
                        .align(Alignment.Center)
                )
            }

        }
    }
    LaunchedEffect(Unit) {
        isTimeOut = true
        delay(15000L) // 10 seconds
        if (isTimeOut) {
            timeOut()
        }
    }
    LaunchedEffect(Unit) {
        while (true) {
            delay(300)
            val isCurrentPoseValid = pitch in 75..115 && roll in -15..15
            isValidPose = isCurrentPoseValid
            
            if (isCurrentPoseValid) {
                validPitchCount++
                val newProgress = (validPitchCount / 3f).coerceAtMost(1f)
                
                // 只在进度实际改变时更新
                if (circleProgress.value != newProgress) {
                    circleProgress.animateTo(
                        targetValue = newProgress,
                        animationSpec = tween(durationMillis = 200)
                    )
                }
                
                if (validPitchCount >= 3) { // Check for 3 seconds (3 * 300ms)
                    isTimeOut = false
                    isNextPage = true
                    return@LaunchedEffect
                }
            } else {
                if (validPitchCount > 0) {
                    validPitchCount = 0
                    // 只在需要重置时才调用动画
                    if (circleProgress.value != 0f) {
                        circleProgress.animateTo(
                            targetValue = 0f,
                            animationSpec = tween(durationMillis = 200)
                        )
                    }
                }
            }
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            TextToSpeech.ttsStop()
        }
    }
}
@Preview(showBackground = true)
@Composable
fun VCNeckDetectingScreenPreview() {
    VCNeckDetectingScreen()
}