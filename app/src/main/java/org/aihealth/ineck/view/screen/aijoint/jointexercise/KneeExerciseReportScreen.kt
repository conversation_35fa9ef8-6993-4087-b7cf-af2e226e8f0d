package org.aihealth.ineck.view.screen.aijoint.jointexercise

import android.annotation.SuppressLint
import android.text.TextPaint
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.view.custom.AIHSelectButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.custom.DayCalendarState
import org.aihealth.ineck.view.screen.home.HomeStatusCard
import org.aihealth.ineck.view.screen.home.aijoint.CustomSlider
import org.aihealth.ineck.viewmodel.MainViewModel
import java.text.SimpleDateFormat
import java.util.Calendar


@Composable
fun KneeExerciseRoute(
    viewModel: MainViewModel
) {
    val finishedCount by viewModel.deviceScreen.kneeExercise.collectAsState()
    KneeExerciseReportScreen(
        maxValueOne = 10,
        valueOne = finishedCount.first,
        maxValueTwo = 10,
        valueTwo = finishedCount.second,
    )
}

@Composable
fun KneeExerciseReportScreen(
    maxValueOne: Int = 10,
    valueOne: Int = 0,
    maxValueTwo: Int = 10,
    valueTwo: Int = 0,
) {
    // 当天日期
    var today = getDefaultDate()
    // 日报告日历状态
    val dayCalendarState = DayCalendarState(today)

    BasePageView(
        showBackIcon = true,
        title = stringResource(id = R.string.knne_exercise),
    ) {
        Column(
            modifier = Modifier
                .padding(vertical = 5.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState())
        ) {
            ExerciseDataCard(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp),
                maxValueOne = maxValueOne,
                valueOne = valueOne,
                maxValueTwo = maxValueTwo,
                valueTwo = valueTwo,
            )
            ExerciseDataChartCard(
                modifier = Modifier
                    .fillMaxWidth(),
                dayCalendarState = dayCalendarState,
                changeDate = {}
            )

        }

    }
}

@Composable
fun ExerciseDataCard(
    modifier: Modifier = Modifier,
    maxValueOne: Int = 10,
    valueOne: Int = 0,
    maxValueTwo: Int = 10,
    valueTwo: Int = 0,
) {
    HomeStatusCard(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = 8.dp),
    ) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp, start = 16.dp),
            text = "今日锻炼状态",
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF444444),
                textAlign = TextAlign.Start,
            )
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 20.dp, top = 16.dp),
            text = "动作1",
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF999999),
            )
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 20.dp, top = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            CustomSlider(
                modifier = Modifier
                    .weight(0.5f)
                    .height(11.dp),
                value = valueOne,
                maxValue = maxValueOne,
            )
            Column(
                modifier = Modifier
                    .weight(0.2f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${valueOne}/${maxValueOne}",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF999999),
                        textAlign = TextAlign.Center
                    )
                )
            }

        }
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 20.dp, top = 16.dp),
            text = "动作2",
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF999999),
            )
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 20.dp, top = 8.dp, bottom = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            CustomSlider(
                modifier = Modifier
                    .weight(0.5f)
                    .height(11.dp),
                color = Color(0xFFDF9754),
                value = valueTwo,
                maxValue = maxValueTwo,
            )
            Column(
                modifier = Modifier
                    .weight(0.2f),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "${valueTwo}/${maxValueTwo}",
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF999999),
                        textAlign = TextAlign.Center
                    )
                )
            }

        }

    }
}

@Composable
fun ExerciseDataChartCard(
    modifier: Modifier = Modifier,
    dayCalendarState: DayCalendarState,
    changeDate: () -> Unit = {},
) {
    HomeStatusCard(
        modifier = modifier
            .fillMaxWidth()
            .padding(top = 8.dp, bottom = 40.dp),
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AIHSelectButton(
                selectedIndex = 0,
                array = stringArrayResource(id = R.array.week_month),
                onClick = { },
                modifier = Modifier
                    .width(200.dp)
                    .height(50.dp)
                    .height(44.dp),
                backgroundColor = Color(0XFFEBEEF8),
                padding = PaddingValues(5.dp)
            )
            WeekDateTime(
                selectedDay = dayCalendarState.selectedDay,
                onShowWeekSelectEvent = {
                    changeDate()
                }
            )
            BarChartWithAxesScreen()
        }


    }
}

/**
 *  周报告卡片
 *  @param  selectedDay 选择日期
 *  @param  onShowWeekSelectEvent   打开月历选择面板事件
 */
@SuppressLint("SimpleDateFormat")
@Composable
private fun WeekDateTime(
    selectedDay: Calendar,
    onShowWeekSelectEvent: () -> Unit
) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        val calender = selectedDay.clone() as Calendar
        calender.add(Calendar.DATE, -7)
        Text(
            text = SimpleDateFormat(stringResource(id = R.string.dateFormat)).format(calender.time),
            fontSize = 14.sp,
            fontWeight = FontWeight.W400,
            color = Color(0XFF444444)
        )
        /* 选择星期触发月历 */
        IconButton(
            onClick = { onShowWeekSelectEvent() }
        ) {
            Image(
                painter = painterResource(id = R.drawable.img_report_calender),
                contentDescription = null,
                modifier = Modifier
                    .padding(horizontal = 3.dp)
                    .size(24.dp, 26.dp)
            )
        }

        Text(
            text = SimpleDateFormat(stringResource(id = R.string.dateFormat)).format(selectedDay.time),
            fontSize = 14.sp,
            fontWeight = FontWeight.W400,
            color = Color(0xFF444444)
        )
    }
}

@Preview
@Composable
private fun Preview() {
    KneeExerciseReportScreen()
}

@Composable
fun BarChartWithAxes(
    modifier: Modifier = Modifier,
    data: List<Pair<Float, Float>>,
    colors: List<Color>,
    maxDataValue: Float = 10f,
    xAxisLabels: List<String>,
    yAxisLabels: List<String>
) {
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Drawing the chart with axes
        Canvas(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {
            val canvasWidth = size.width
            val canvasHeight = size.height
            val yAxisWidth = 40.dp.toPx()
            val barWidth = (canvasWidth - yAxisWidth) / 22f

            // Draw Y-axis
            drawLine(
                color = Color.Gray,
                start = Offset(yAxisWidth, 0f),
                end = Offset(yAxisWidth, canvasHeight),
                strokeWidth = 0.5.dp.toPx()
            )

            // Draw X-axis
            drawLine(
                color = Color.Gray,
                start = Offset(yAxisWidth, canvasHeight),
                end = Offset(canvasWidth, canvasHeight),
                strokeWidth = 0.5.dp.toPx()
            )

            // Draw Y-axis labels and grid lines
            val yStep = canvasHeight / (yAxisLabels.size - 1)
            yAxisLabels.forEachIndexed { index, label ->
                val yOffset = canvasHeight - (index * yStep)
                // Draw Y-axis label
                drawContext.canvas.nativeCanvas.drawText(
                    label,
                    0f,
                    yOffset + 5.dp.toPx(),
                    TextPaint().apply {
                        color = Color.Gray.toArgb()
                        textSize = 14.sp.toPx()
                    }
                )
            }

            // Draw bars
            data.forEachIndexed { index, (valueOne, valueTwo) ->
                val barHeightOne = (valueOne / maxDataValue) * (canvasHeight)
                val barHeightTwo = (valueTwo / maxDataValue) * (canvasHeight)
                val startWidth = yAxisWidth + index * 3 * barWidth + barWidth
                drawRoundRect(
                    color = colors[0],
                    topLeft = Offset(
                        x = startWidth,
                        y = canvasHeight - barHeightOne
                    ),
                    size = Size(barWidth, barHeightOne),
                    cornerRadius = CornerRadius(4.dp.toPx(), 4.dp.toPx()),
                )
                drawRoundRect(
                    color = colors[1],
                    topLeft = Offset(
                        x = startWidth + barWidth,
                        y = canvasHeight - barHeightTwo
                    ),
                    size = Size(barWidth, barHeightTwo),
                    cornerRadius = CornerRadius(4.dp.toPx(), 4.dp.toPx()),
                )
                if (valueOne != 0f) {
                    drawRoundRect(
                        color = colors[0],
                        topLeft = Offset(
                            x = startWidth,
                            y = canvasHeight - 4.dp.toPx()
                        ),
                        size = Size(barWidth, 4.dp.toPx())
                    )
                }
                if (valueTwo != 0f) {
                    drawRoundRect(
                        color = colors[1],
                        topLeft = Offset(
                            x = startWidth + barWidth,
                            y = canvasHeight - 4.dp.toPx()
                        ),
                        size = Size(barWidth, 4.dp.toPx())
                    )
                }
            }

            xAxisLabels.forEachIndexed { index, label ->
                val startWidth = yAxisWidth + index * 3 * barWidth + barWidth
                // Draw X-axis label
                drawContext.canvas.nativeCanvas.drawText(
                    label,
                    startWidth + barWidth / 2,
                    canvasHeight + 20.dp.toPx(),
                    TextPaint().apply {
                        color = Color.Gray.toArgb()
                        textSize = 14.sp.toPx()
                    }
                )
            }
        }
    }
}

@Composable
fun BarChartWithAxesScreen() {
    val data = listOf(
        Pair(10f, 0f),
        Pair(5f, 5f),
        Pair(10f, 10f),
        Pair(0f, 0f),
        Pair(1f, 2f),
        Pair(2f, 2f),
        Pair(10f, 8f)
    )
    val colors = listOf(Color(0xFF7AAF55), Color(0xFFDF9754))  // Green and Brown colors
    val xLabels = listOf("01", "02", "03", "04", "05", "06", "07")
    val yLabels = listOf("0%", "20%", "40%", "60%", "80%", "100%")

    BarChartWithAxes(
        modifier = Modifier
            .padding(top = 5.dp, bottom = 30.dp)
            .fillMaxWidth()
            .height(300.dp),
        data = data,
        colors = colors,
        xAxisLabels = xLabels,  // Drop the first "03" to align with the bars
        yAxisLabels = yLabels
    )
}