package org.aihealth.ineck.view.custom.toptipsblock

import androidx.compose.animation.animateColor
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Divider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

/**
 *  未连接aiNeck但处于“视频监测的启动状态下的状态”
 */
@Composable
fun InnerTopTopsOfInCameraMonitorOfAiNeck(
    gravityValueState: Float,
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.height(18.dp))
        Text(
            text = stringResource(id = R.string.angle_monitoring_running_title),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black,
            modifier = Modifier
                .padding(horizontal = 8.dp, vertical = 4.dp)
        )
        Divider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 28.dp, top = 10.dp, end = 28.dp),
            color = Color.Black.copy(alpha = .05f),
            thickness = 1.dp,
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(86.dp)
                .padding(horizontal = 8.dp, vertical = 14.dp)
                .clip(RoundedCornerShape(14.dp))
                .background(color = Color(0x48A9BBF3)),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val infiniteTransition = rememberInfiniteTransition(label = "")
            val verticalCircleSignColor by infiniteTransition.animateColor(
                initialValue = Color(0x48A9BBF3),
                targetValue = Color(0xFF37BDBE),
                animationSpec = infiniteRepeatable(
                    animation = tween(500, easing = LinearEasing),
                    repeatMode = RepeatMode.Reverse
                ), label = ""
            )
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .weight(1f)
                    .padding(start = 10.dp),
                contentAlignment = Alignment.Center
            ) {
                if (gravityValueState > 9.2) {
                    Box(
                        modifier = Modifier
                            .size(14.dp)
                            .clip(CircleShape)
                            .background(color = verticalCircleSignColor)
                    )
                } else {
                    Box(modifier = Modifier
                        .size(14.dp)
                        .clip(CircleShape)
                        .background(color = Color(0XFFFC7349)))
                }
            }
            Text(
                text = if (gravityValueState > 9.2) stringResource(id = R.string.device_vertical_monitoring_ok)
                    else stringResource(id = R.string.device_vertical_monitoring_tips),
                modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .weight(6f),
                fontSize = 12.sp,
                color = Color(0xFF93AAEF)
            )
        }
        Spacer(modifier = Modifier.height(8.dp))

    }
}