package org.aihealth.ineck.view.screen

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.networkTime
import java.util.GregorianCalendar


@Preview
@Composable
fun TextNetWorkScreen() {
    val calendar = GregorianCalendar(2022, 6, 1)
    val startTime = calendar.networkTime
    calendar.set(2022,12,1)
    val endTime = calendar.networkTime
    var data by remember {
        mutableStateOf("")
    }
    Column(
        Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState()), verticalArrangement = Arrangement.Center, horizontalAlignment = Alignment.CenterHorizontally) {
        Text(text = data)
        Button(onClick = {

            apiService.getRecord(from_date = startTime, to_date = endTime)
                .enqueueBody { response ->
                    response?.data?.let { data = it.toString() }
                }
        }) {
            Text(text = "请求数据")
        }
    }
}
