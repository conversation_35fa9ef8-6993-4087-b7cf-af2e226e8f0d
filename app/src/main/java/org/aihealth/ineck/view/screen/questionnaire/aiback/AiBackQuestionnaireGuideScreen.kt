package org.aihealth.ineck.view.screen.questionnaire.aiback

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import kotlinx.coroutines.launch
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.Constants
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.screen.questionnaire.CSHEvaluationScreen
import org.aihealth.ineck.view.screen.questionnaire.MeasureCollectionScreen
import org.aihealth.ineck.view.screen.questionnaire.MeasureCollectionScreenODI
import org.aihealth.ineck.view.screen.questionnaire.MeasureCollectionScreenPromis
import org.aihealth.ineck.view.screen.questionnaire.QuestionnaireDescriptionScreen
import org.aihealth.ineck.view.screen.questionnaire.QuestionnaireModeSettingScreen
import org.aihealth.ineck.view.screen.questionnaire.QuestionnaireResultScreen
import org.aihealth.ineck.viewmodel.CSHEvaluationViewModel
import org.aihealth.ineck.viewmodel.QuestionnaireViewModel

@Composable
fun AiBackQuestionnaireGuideScreen(
    modifier: Modifier = Modifier,
    viewModel: QuestionnaireViewModel = QuestionnaireViewModel(),
) {
    val pagerState = rememberPagerState(
        initialPage = 0,
    )
    val questionnaireScope = rememberCoroutineScope()
    val cshEvaluationViewModel: CSHEvaluationViewModel = viewModel()
    Box(
        modifier = modifier.fillMaxSize(),
    ) {
        HorizontalPager(
            modifier = Modifier.fillMaxSize(),
            state = pagerState,
            count = 5,
            userScrollEnabled = false,
        ) { page ->
            when (page) {
                0 -> {
                    val innerPagerState = rememberPagerState(initialPage = 0)
                    HorizontalPager(
                        count = 2,
                        state = innerPagerState,
                        modifier = Modifier.fillMaxSize(),
                        userScrollEnabled = false
                    ) { innerPage ->
                        when (innerPage) {
                            0 -> {
                                QuestionnaireDescriptionScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    target = 0,
                                    type = DeviceType.aiBackCV,
                                    onStart = {
                                        LogUtil.i("onClick start")
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(0, 0)
                                            innerPagerState.animateScrollToPage(1)
                                            LogUtil.i("innerPagerState: ${innerPagerState.currentPage}")
                                        }
                                    },
                                    onSkip = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(0, 0)
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    0,
                                                    0
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    },
                                )
                            }

                            1 -> {
                                CSHEvaluationScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    type = DeviceType.aiBackCV,
                                    onSkip = {
                                        questionnaireScope.launch {
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    0,
                                                    1
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    },
                                    onStart = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(0, 1)
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    0,
                                                    1
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    },
                                )
                            }
                        }

                    }
                }

                1 -> {
                    val innerPagerState = rememberPagerState(initialPage = 0)
                    HorizontalPager(
                        count = 2,
                        state = innerPagerState,
                        modifier = Modifier.fillMaxSize(),
                        userScrollEnabled = false
                    ) { innerPage ->
                        when (innerPage) {
                            0 -> {
                                QuestionnaireDescriptionScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    target = 1,
                                    type = DeviceType.aiBackCV,
                                    onStart = {
                                        LogUtil.i("onClick start")
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(1, 0)
                                            innerPagerState.animateScrollToPage(1)
                                            LogUtil.i("innerPagerState: ${innerPagerState.currentPage}")
                                        }
                                    },
                                    onSkip = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(1, 0)
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    1,
                                                    0
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    },
                                )
                            }

                            1 -> {
                                val isOdiFinished = remember { mutableStateOf(false) }
                                val isPromiseFinished = remember { mutableStateOf(false) }
                                val inner = rememberPagerState(initialPage = 0)
                                val enable = remember {
                                    mutableStateOf(false)
                                }
                                MeasureCollectionScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    pagerState = inner,
                                    pagerCount = 2,
                                    onSkip = {
                                        enable.value = true
                                    }
                                ) { i ->
                                    when (i) {
                                        0 -> {
                                            MeasureCollectionScreenODI(
                                                modifier = Modifier.fillMaxSize(),
                                                enable = enable.value,
                                                onDismissRequest = { enable.value = false },
                                                onSkip = {
                                                    enable.value = false
                                                    questionnaireScope.launch {
                                                        if (isPromiseFinished.value) {
                                                            pagerState.animateScrollToPage(
                                                                viewModel.jumpToTargetScreen(
                                                                    1,
                                                                    1
                                                                )
                                                            )
                                                        } else {
                                                            inner.animateScrollToPage(2)
                                                        }
                                                        LogUtil.i("pagerState: ${pagerState.currentPage}")
                                                    }
                                                },
                                                onStart = {
                                                    questionnaireScope.launch {
                                                        viewModel.targetRecord(1, 1)
                                                        isOdiFinished.value = true
                                                        if (isPromiseFinished.value) {
                                                            pagerState.animateScrollToPage(
                                                                viewModel.jumpToTargetScreen(
                                                                    1,
                                                                    1
                                                                )
                                                            )
                                                        } else {
                                                            inner.animateScrollToPage(2)
                                                        }
                                                        LogUtil.i("pagerState: ${pagerState.currentPage}")
                                                    }
                                                },
                                            )
                                        }

                                        1 -> {
                                            MeasureCollectionScreenPromis(
                                                modifier = Modifier.fillMaxSize(),
                                                enable = enable.value,
                                                onDismissRequest = { enable.value = false },
                                                onSkip = {
                                                    enable.value = false
                                                    questionnaireScope.launch {
                                                        if (!isOdiFinished.value) {
                                                            inner.scrollToPage(0)
                                                        }
                                                        pagerState.animateScrollToPage(
                                                            viewModel.jumpToTargetScreen(
                                                                1,
                                                                2
                                                            )
                                                        )

                                                        LogUtil.i("pagerState: ${pagerState.currentPage}")
                                                    }
                                                },
                                                onStart = {
                                                    questionnaireScope.launch {
                                                        if (!isOdiFinished.value) {
                                                            inner.scrollToPage(0)
                                                        }
                                                        viewModel.targetRecord(1, 2)
                                                        isPromiseFinished.value = true
                                                        pagerState.animateScrollToPage(
                                                            viewModel.jumpToTargetScreen(
                                                                1,
                                                                2
                                                            )
                                                        )
                                                        LogUtil.i("pagerState: ${pagerState.currentPage}")
                                                    }
                                                },
                                            )
                                        }
                                    }
                                }
                            }
                        }

                    }
                }


                2 -> {
                    val innerPagerState = rememberPagerState(initialPage = 0)
                    HorizontalPager(
                        count = 2,
                        state = innerPagerState,
                        modifier = Modifier.fillMaxSize(),
                        userScrollEnabled = false
                    ) { innerPage ->
                        when (innerPage) {
                            0 -> {
                                QuestionnaireDescriptionScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    target = 2,
                                    type = DeviceType.aiBackCV,
                                    onStart = {
                                        LogUtil.i("onClick start")
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(2, 0)
                                            innerPagerState.animateScrollToPage(1)
                                            LogUtil.i("innerPagerState: ${innerPagerState.currentPage}")
                                        }
                                    },
                                    onSkip = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(2, 0)
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    2,
                                                    0
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    },
                                )
                            }

                            1 -> AiBackQuestionnaireNeckDetectionScreen(
                                modifier = Modifier.fillMaxSize(),
                                onSkip = {
                                    questionnaireScope.launch {
                                        pagerState.animateScrollToPage(
                                            viewModel.jumpToTargetScreen(
                                                2,
                                                1
                                            )
                                        )
                                        LogUtil.i("pagerState: ${pagerState.currentPage}")
                                    }
                                },
                                onStart = {
                                    questionnaireScope.launch {
                                        viewModel.targetRecord(2, 1)
                                        pagerState.animateScrollToPage(
                                            viewModel.jumpToTargetScreen(
                                                2,
                                                1
                                            )
                                        )
                                        LogUtil.i("pagerState: ${pagerState.currentPage}")
                                    }
                                },
                            )
                        }
                    }
                }

                3 -> {
                    val innerPagerState = rememberPagerState(initialPage = 0)
                    HorizontalPager(
                        count = 2,
                        state = innerPagerState,
                        modifier = Modifier.fillMaxSize(),
                        userScrollEnabled = false
                    ) { innerPage ->
                        when (innerPage) {
                            0 -> {
                                QuestionnaireDescriptionScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    target = 3,
                                    type = DeviceType.aiBackCV,
                                    onStart = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(3, 0)
                                            innerPagerState.animateScrollToPage(1)
                                            LogUtil.i("innerPagerState: ${innerPagerState.currentPage}")
                                        }
                                    },
                                    onSkip = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(3, 0)
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    3,
                                                    0
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    },
                                )
                            }

                            1 -> {
                                QuestionnaireModeSettingScreen(
                                    modifier = Modifier.fillMaxSize(),
                                    onClick = {
                                        questionnaireScope.launch {
                                            viewModel.targetRecord(3, 1)
                                            pagerState.animateScrollToPage(
                                                viewModel.jumpToTargetScreen(
                                                    3,
                                                    1
                                                )
                                            )
                                            LogUtil.i("pagerState: ${pagerState.currentPage}")
                                        }
                                    }
                                )
                            }
                        }

                    }
                }

                4 -> {
                    QuestionnaireResultScreen(
                        modifier = Modifier.fillMaxSize(),
                        type = DeviceType.aiBackCV,
                        data = viewModel.questionnaireRecord,
                        onStart = {
                            cshEvaluationViewModel.endTime = System.currentTimeMillis()
                            cshEvaluationViewModel.answer.duration =
                                (cshEvaluationViewModel.endTime - cshEvaluationViewModel.startTime) / 1000
                            apiService.postLumbarVertebrae(cshEvaluationViewModel.getQuestionAnswer())
                                .enqueueBody {}

                            questionnaireScope.launch {
                                userSP.edit().putLong(
                                    Constants.LAST_FINISHED_BACK_QUESTIONNAIRE,
                                    System.currentTimeMillis()
                                ).apply()
                                startScreen(Screen.AiBackQuestionnaireReportScreen.route)
                            }
                        },
                        onBack = {
                            questionnaireScope.launch {
                                pagerState.scrollToPage(
                                    viewModel.jumpToTargetScreen(-1, 0)
                                )
                                LogUtil.i("pagerState: ${pagerState.currentPage}")
                            }
                        }
                    )
                }

            }

        }
    }
}