package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material3.Badge
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import androidx.compose.ui.platform.LocalDensity
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.screen.article.ArticleRoute
import org.aihealth.ineck.view.screen.chat.DynamicMessagePage
import org.aihealth.ineck.view.screen.defaultscreen.NoMessageScreen
import org.aihealth.ineck.viewmodel.ChatViewModel
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun MessageNotification(
    viewmodel: MainViewModel
) {

    val chatViewModel = ViewModelProvider(activity)[ChatViewModel::class.java]
    BasePageView(
        headerContent = {
            Column {
                ChatHeader(
                    modifier = Modifier.fillMaxWidth(),
                    onClearClick = {
                        LogUtil.d("click clear message")
                       viewmodel.viewModelScope.launch {
                           val result = chatViewModel.markAllMessagesAsRead()
                           viewmodel.getTheUnreadMessageCount()
                       }
                    }
                )
            }
        },
        headerColor = Color.White,
    ) {
        // Content here
        ChatTabs(
            modifier = Modifier.Companion.padding(top = 4.dp),
            unreadMessageCount = viewmodel.unreadMessageCount,
            chatViewModel = chatViewModel,  // 传递viewmodel到ChatTabs
            viewmodel = viewmodel
        )
    }
}

@SuppressLint("UnusedBoxWithConstraintsScope")
@Composable
fun ChatTabs(
    modifier: Modifier = Modifier.Companion,
    unreadMessageCount: Int = 0,
    chatViewModel: ChatViewModel,  // 接收viewmodel参数
    viewmodel: MainViewModel,
) {
    val context = LocalContext.current
    // 从SharedPreferences读取上次选中的页面索引
    val initialPage by remember { mutableIntStateOf(0) }
    // 创建pagerState，设置初始页面
    val pagerState = rememberPagerState(initialPage = initialPage) { 3 }
    // 使用derivedStateOf从pagerState获取当前页面，以优化性能
    val selectedTab by remember { derivedStateOf { pagerState.currentPage } }
    // 创建协程作用域用于启动动画
    val scope = rememberCoroutineScope()

    // 在组件首次加载时获取聊天列表数据
    BoxWithConstraints(
        modifier = Modifier
            .background(Color.White)
            .fillMaxWidth()
            .padding(horizontal = 8.dp)
    ) {
        // 每个 Tab 的宽度为父容器宽度除以 Tab 数量
        val realWidth = maxWidth / 3
        val tabHalfWidth = realWidth / 2
        // 添加动画蓝条
        val indicatorOffset: Dp by animateDpAsState(
            targetValue = realWidth * selectedTab + tabHalfWidth - 12.dp,
            animationSpec = tween(durationMillis = 300, easing = FastOutSlowInEasing)
        )
        Column(
            modifier = modifier
                .fillMaxWidth()
        ) {
            Row(
                modifier = Modifier.Companion
                    .fillMaxWidth(),
            ) {
                TabItem(
                    modifier = Modifier.Companion.weight(1f),
                    icon = R.drawable.ic_medical_advice_chat,
                    label = stringResource(R.string.medical_advice),
                    isSelected = selectedTab == 0,
                    unreadCount = null,
                    onClick = {
                        // 启动协程以动画方式滚动到选定页面
                        scope.launch {
                            pagerState.animateScrollToPage(0)
                        }
                    }
                )
                TabItem(
                    modifier = Modifier.Companion.weight(1f),
                    icon = R.drawable.ic_system_notification,
                    label = stringResource(R.string.system_notification),
                    isSelected = selectedTab == 1,
                    unreadCount = null,
                    onClick = {
                        // 启动协程以动画方式滚动到选定页面
                        scope.launch {
                            pagerState.animateScrollToPage(1)
                        }
                    }
                )
                TabItem(
                    modifier = Modifier.Companion.weight(1f),
                    icon = R.drawable.ic_dynamic_message,
                    label = stringResource(R.string.dynamic_message),
                    isSelected = selectedTab == 2,
                    unreadCount = unreadMessageCount,
                    onClick = {
                        // 启动协程以动画方式滚动到选定页面
                        scope.launch {
                            pagerState.animateScrollToPage(2)
                        }
                    }
                )
            }
            Box(
                modifier = Modifier.Companion
                    .fillMaxWidth()
                    .height(4.dp)  // 指示条高度
            ) {
                Box(
                    modifier = Modifier.Companion
                        .offset(
                            x = indicatorOffset
                        )
                        .width(24.dp)
                        .height(2.dp)
                        .background(Color(0xFF4285F4))

                )
            }

            HorizontalPager(
                state = pagerState,
                modifier = Modifier.Companion
                    .fillMaxWidth()
                    .weight(1f)
            ) { page ->
                when (page) {
                    0 -> ArticleRoute()
                    1 -> SystemNotificationPage()
                    2 -> DynamicMessagePage(viewmodel = chatViewModel)  // 传递viewmodel到DynamicMessagePage
                }
            }
        }
    }
}

@Composable
fun TabItem(
    modifier: Modifier = Modifier.Companion,
    icon: Int,
    label: String,
    isSelected: Boolean,
    unreadCount: Int?,
    onClick: () -> Unit
) {
    val density = LocalDensity.current
    
    Column(
        horizontalAlignment = Alignment.Companion.CenterHorizontally,
        modifier = modifier
            .clickable(
                interactionSource = remember { MutableInteractionSource() },
                indication = null,
                onClick = onClick
            )
            .height(80.dp),
        verticalArrangement = Arrangement.Center
    ) {
        Box(
            contentAlignment = Alignment.Companion.TopEnd,
            modifier = Modifier.Companion.size(48.dp)
        ) {
            Image(
                painter = painterResource(id = icon),
                contentDescription = null,
                modifier = Modifier.Companion.fillMaxSize(),
                contentScale = ContentScale.Companion.Fit,
            )

            if (unreadCount != null && unreadCount > 0) {
                Badge(
                    modifier = Modifier
                        .size(24.dp)
                        .align(Alignment.TopEnd)
                        .offset(x = 8.dp, y = (-4).dp),
                    containerColor = Color.Red
                ) {
                    val displayText = when {
                        unreadCount > 99 -> "99+"
                        else -> unreadCount.toString()
                    }

                    // 根据数字位数动态调整字体大小
                    val dynamicFontSize = when {
                        unreadCount > 99 -> with(density) { 6.dp.toSp() }  // 三位数或更多（99+）
                        unreadCount > 9 -> with(density) { 8.dp.toSp() }   // 两位数
                        else -> with(density) { 12.dp.toSp() }            // 一位数
                    }

                    Text(
                        text = displayText,
                        fontSize = dynamicFontSize,
                        color = Color.White,
                        modifier = Modifier.padding(horizontal = 1.dp) // 减少内部padding
                    )
                }

            }
        }

        Text(
            text = label,
            color = if (isSelected) Color(0xFF4285F4) else Color(0xFF666666),
            fontSize = 12.sp,
            modifier = Modifier.Companion.padding(top = 4.dp),
            textAlign = TextAlign.Companion.Center
        )
    }
}

@Composable
fun ChatHeader(
    modifier: Modifier = Modifier.Companion,
    onClearClick: () -> Unit = {}
) {
    Column(modifier = modifier.fillMaxWidth().background(Color.White)) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp)
        ) {
            Row(
                modifier = Modifier
                    .align(Alignment.Center)
            ) {
                Text(
                    text = stringResource(id = R.string.my_message),
                    style = Typography.displayLarge
                )
            }
            Row(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp),
                verticalAlignment = Alignment.Companion.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_clear_message),
                    contentDescription = null,
                    tint = Color(0xFF666666),
                    modifier = Modifier.Companion
                        .size(16.dp)
                        .clickable{
                            LogUtil.d("clear message")
                            onClearClick()
                        },
                )
            }
        }
    }
}

@Composable
private fun SystemNotificationPage() {
    Box(
        modifier = Modifier.Companion.fillMaxSize(),
        contentAlignment = Alignment.Companion.Center
    ) {
        NoMessageScreen()
    }
}