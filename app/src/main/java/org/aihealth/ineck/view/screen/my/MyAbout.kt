package org.aihealth.ineck.view.screen.my

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.util.VersionUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView
import java.util.Locale

@Preview
@Composable
fun MyAboutScreen(modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val text =
        "Version ${VersionUtil.getVersionName(context)} (${VersionUtil.getVersionCode(context)})"
    BasePageView(title = "", showBackIcon = true) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .fillMaxWidth()
                    .padding(top = 58.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Image(
                    modifier = Modifier
                        .size(72.dp)
                        .clip(shape = RoundedCornerShape(12.dp)),
                    painter = painterResource(id = R.drawable.my_logo),
                    contentDescription = "logo",
                    contentScale = ContentScale.Crop
                )
                Text(
                    modifier = Modifier
                        .padding(top = 20.dp),
                    text = text,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
            }
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth()
            ) {
                Item(
                    title = stringResource(id = R.string.function_introduction),
                    onClick = {
                        startScreen(Screen.FunctionIntroduction.route)
                    }
                )
                Item(
                    title = stringResource(id = R.string.help_center),
                    onClick = {
                        startScreen(Screen.HelpFeedback.route)
                    }
                )
                Item(
                    title = stringResource(id = R.string.privacy_security),
                    onClick = {
                        startScreen(Screen.PrivacyAndSecurity.route)
                    }
                )
            }
            if (Locale.getDefault().country == "CN") {
                Row(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 48.dp)
                        .clickable {
                            val intent = Intent(Intent.ACTION_VIEW)
                            intent.data = Uri.parse("https://beian.miit.gov.cn/")
                            activity.startActivity(intent)
                        },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "浙ICP备2021023293号-2A",
                        style = TextStyle(
                            fontSize = 10.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF999999),
                        )
                    )
                    Image(
                        modifier = Modifier.size(12.dp),
                        painter = painterResource(id = R.drawable.small_arrow),
                        contentDescription = "small icon"
                    )

                }
            }
        }
    }

}

@Composable
private fun Item(
    title: String,
    subContent: @Composable () -> Unit = {},
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .height(50.dp)
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectTapGestures {
                    onClick()
                }
            }
            .background(Color.White),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(10.dp))
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        subContent()
        Spacer(modifier = Modifier.width(9.dp))
        Icon(
            painter = painterResource(id = R.drawable.img_next),
            contentDescription = null,
            modifier = Modifier.size(22.dp),
            tint = Color(0XFF999999)
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
    AIHDivider(modifier = Modifier.padding(horizontal = 10.dp))
}