package org.aihealth.ineck.view.screen.my

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.RadioButton
import androidx.compose.material3.RadioButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.angles.Preferences
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.user

@Composable
fun UnitSettingRoute() {
    UnitSettingScreen(
        modifier = Modifier.fillMaxSize(),
        preferences = user.preferences,
        commit = {
            user.preferences = it
            apiService.updateInfo(
                body = hashMapOf(
                    Pair(
                        "preferences",
                        user.preferences
                    )
                )
            ).enqueueBody {
                finish()
            }
        }
    )
}

@Composable
fun UnitSettingScreen(
    modifier: Modifier = Modifier,
    preferences: Preferences,
    commit: (Preferences) -> Unit = {}
) {
    var selected by remember(preferences) {
        mutableStateOf(preferences)
    }
    BasePageView(
        title = "Unit Setting",
        showBackIcon = true,
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFFF7F7F7))

    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 16.dp)
                    .padding(horizontal = 16.dp)
            ) {
                Text(
                    text = stringResource(R.string.length_unit),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF999999),
                    )
                )
                Card(
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 16.dp),
                    elevation = CardDefaults.cardElevation(1.dp),
                    colors = CardDefaults.cardColors(Color.White),
                    shape = RoundedCornerShape(8.dp),
                ) {
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.metric_length_unit),
                        isSelected = selected.heightUnit == "M",
                        onchange = {
                            selected = selected.copy(heightUnit = "M")
                        }
                    )
                    HorizontalDivider(
                        modifier = Modifier.padding(start = 12.dp, end = 24.dp),
                        thickness = 1.dp,
                        color = Color(0xFFE0E0E0)
                    )
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.imperial_length_unit),
                        isSelected = selected.heightUnit == "I",
                        onchange = {
                            selected = selected.copy(heightUnit = "I")
                        }
                    )
                }
                Spacer(modifier = Modifier.padding(top = 12.dp))
                Text(
                    text = stringResource(R.string.weight_unit),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF999999),
                    )
                )
                Card(
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 16.dp),
                    elevation = CardDefaults.cardElevation(1.dp),
                    colors = CardDefaults.cardColors(Color.White),
                    shape = RoundedCornerShape(8.dp),
                ) {
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.metric_weight_unit),
                        isSelected = selected.weightUnit == "M",
                        onchange = {
                            selected = selected.copy(weightUnit = "M")
                        }
                    )
                    HorizontalDivider(
                        modifier = Modifier.padding(start = 12.dp, end = 24.dp),
                        thickness = 1.dp,
                        color = Color(0xFFE0E0E0)
                    )
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.imperial_weight_unit),
                        isSelected = selected.weightUnit == "I",
                        onchange = {
                            selected = selected.copy(weightUnit = "I")
                        }
                    )
                }
                Text(
                    text = stringResource(R.string.temperature_unit),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF999999),
                    )
                )
                Card(
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 16.dp),
                    elevation = CardDefaults.cardElevation(1.dp),
                    colors = CardDefaults.cardColors(Color.White),
                    shape = RoundedCornerShape(8.dp),
                ) {
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.centigrade_unit),
                        isSelected = selected.temperatureUnit == "M",
                        onchange = {
                            selected = selected.copy(temperatureUnit = "M")
                        }
                    )
                    HorizontalDivider(
                        modifier = Modifier.padding(start = 12.dp, end = 24.dp),
                        thickness = 1.dp,
                        color = Color(0xFFE0E0E0)
                    )
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.fahrenheit_degree_unit),
                        isSelected = selected.temperatureUnit == "I",
                        onchange = {
                            selected = selected.copy(temperatureUnit = "I")
                        }
                    )
                }
                Spacer(modifier = Modifier.padding(top = 12.dp))
                Text(
                    text = stringResource(R.string.blood_glucose_unit),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF999999),
                    )
                )
                Card(
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 16.dp),
                    elevation = CardDefaults.cardElevation(1.dp),
                    colors = CardDefaults.cardColors(Color.White),
                    shape = RoundedCornerShape(8.dp),
                ) {
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.blood_glucose_Unit_mgdl),
                        isSelected = selected.bloodSugarUnit == "M",
                        onchange = {
                            selected = selected.copy(bloodSugarUnit = "M")
                        }
                    )
                    HorizontalDivider(
                        modifier = Modifier.padding(start = 12.dp, end = 24.dp),
                        thickness = 1.dp,
                        color = Color(0xFFE0E0E0)
                    )
                    SelectItem(
                        modifier = Modifier.padding(horizontal = 12.dp),
                        text = stringResource(R.string.blood_glucose_Unit_mmolL),
                        isSelected = selected.bloodSugarUnit == "I",
                        onchange = {
                            selected = selected.copy(bloodSugarUnit = "I")
                        }
                    )
                }
                Spacer(modifier = Modifier.padding(top = 12.dp))
                Button(
                    onClick = { commit(selected) },
                    modifier = Modifier
                        .padding(start = 25.dp, top = 40.dp, bottom = 16.dp, end = 25.dp)
                        .fillMaxWidth()
                        .height(42.dp)
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color(0XFF73C5FF),
                                    Color(0XFF3161FF),
                                )
                            ),
                            shape = CircleShape
                        ),
                    colors = ButtonDefaults.buttonColors(
                        contentColor = Color.Transparent,
                        containerColor = Color.Transparent
                    )
                ) {
                    Text(
                        text = stringResource(id = R.string.save),
                        fontWeight = FontWeight(400),
                        color = Color(0xFFF7F7F7),
                        fontSize = 20.sp,
                    )
                }
            }
        }
    }
}

@Composable
private fun SelectItem(
    modifier: Modifier = Modifier,
    text: String,
    isSelected: Boolean = true,
    onchange: () -> Unit
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = text,
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
            )
        )
        RadioButton(
            selected = isSelected,
            colors = RadioButtonDefaults.colors().copy(
                selectedColor = Color(0xFF3161FF),
                unselectedColor = Color(0xFFE0E0E0),
            ),
            onClick = { onchange() }
        )
    }
}

@Preview
@Composable
private fun Preview() {
    UnitSettingScreen(
        modifier = Modifier.fillMaxSize(),
        Preferences()
    )
}