package org.aihealth.ineck.view.screen.my

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun LanguageSettingsScreen() {
    val localeOptions = mapOf(
        R.string.en to "en",
        R.string.zh to "zh",
    ).mapKeys { stringResource(it.key) }

    val context = LocalContext.current
    BasePageView(
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true,
        title = stringResource(id = R.string.multilingual)
    ) {
        LaunchedEffect(key1 = currentLocale) {
            LogUtil.i("currentLocale:$currentLocale ----")
        }
        Column(
            modifier = Modifier.fillMaxHeight(),
        ) {
            localeOptions.keys.forEach { selectionLocale ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp)
                        .height(50.dp)
                        .clickable {
                            APPUtil.setLocale(localeOptions[selectionLocale]!!, context)
                            LogUtil.i("setLocale:${localeOptions[selectionLocale]}")
                            finish()
                        },
                    verticalArrangement = androidx.compose.foundation.layout.Arrangement.Center
                ) {
                    Text(
                        text = selectionLocale,
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight(500),
                            color = Color.Black,
                        ),
                    )
                }

                HorizontalDivider()
            }
        }
    }
}