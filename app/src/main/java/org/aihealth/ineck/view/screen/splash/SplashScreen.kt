package org.aihealth.ineck.view.screen.splash

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.auth0.android.jwt.JWT
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.isLoadSuccess
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.loadByLocal
import org.aihealth.ineck.util.token
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.viewmodel.user

/**
 * 启动页，在启动页中对是否是第一次打开app，是否登入进行判断，并跳转到相应页面
 */
@Composable
fun SplashScreen(
    event: (String) -> Unit
) {
    // 默认跳转至登录页面
    var startDestination: String = Screen.Login.route

    // 组合界面启动时执行异步操作
    LaunchedEffect(true) {
        val startTime = System.currentTimeMillis()
        val isFirstUse = SPUtil.getBoolean(SPConstant.IS_FIRST_START, true)
        token = userSP.getString(SPConstant.TOKEN, "").toString()

        startDestination = if (isFirstUse) {
            // 第一次启动APP时，启动轮播页
            Screen.OnBoarding.route
        } else if (token.isNotEmpty()) {
            // 获取用户信息
            user.loadByLocal()
            // 跳转到主界面
            Screen.Main.route
        } else {
            // 跳转到登录界面
            Screen.Login.route
        }
        LogUtil.i("SplashScreen token:$token, isLogin:${isLogin(token, isInChina)}")
        val endTime = System.currentTimeMillis()
        // 整个过程至少500ms ???
        if (endTime - startTime < 500) {
            delay(500 - endTime + startTime)
        }
        isLoadSuccess = true
        event(startDestination)

    }
//    // 为了避免在重新组合时丢失系统 UI 控制器的状态，rememberSystemUiController() 来在组合函数中保留该状态。
//    val systemUiController = rememberSystemUiController()
//    // 当前跳转的目标界面不是轮播页时，显示状态栏，在本组合被清理时执行
//    DisposableEffect(Unit) {
//        onDispose {
//            if (startDestination != Screen.OnBoarding.route) {
//                systemUiController.isStatusBarVisible = true
//            }
//        }
//    }
    Box(modifier = Modifier.fillMaxSize()) {
        Image(
            painter = painterResource(id = R.drawable.splash),
            contentDescription = null,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds
        )
    }

}


suspend fun isLogin(token: String, inChina: Boolean): Boolean = withContext(Dispatchers.IO) {
    if (token.isEmpty()) {
        return@withContext false
    }

    try {
        val jwt = JWT(token)
        !jwt.isExpired(10)
    } catch (e: Exception) {
        false
    }
}