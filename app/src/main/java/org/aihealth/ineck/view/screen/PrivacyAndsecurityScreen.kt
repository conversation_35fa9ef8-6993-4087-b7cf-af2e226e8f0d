package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView

@SuppressLint("UnrememberedMutableState")
@Preview
@Composable
fun PrivacyAndSecurityScreen() {
    var switchCheckedState = userSP.getBoolean("switchCheckedState", false)
    val check = remember { mutableStateOf(switchCheckedState) }
    BasePageView(
        title = stringResource(id = R.string.privacy_nd_security),
        showBackIcon = true
    ) {
        Column(modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
        ){
            PrivacyItem(
                title = stringResource(id = R.string.privacy_statement),
                onClick = {
                    startScreen(Screen.PrivateTerm.route,false)
                }
            )
            PrivacyItem(
                title = stringResource(id = R.string.user_agreement),
                onClick = {
                    startScreen(Screen.UserAgreement.route, false)
                }
            )
            PrivacyItem(
                title = stringResource(id = R.string.membership_agreement),
                onClick = {
                    startScreen(Screen.MemberAgreement.route, false)
                }
            )
            PrivacyItem(
                title = stringResource(id = R.string.logoff_account),
                onClick = {
                    startScreen(Screen.LogOff.route, false)
                }
            )
            PrivacySwitchItem(
                title = stringResource(id = R.string.recommendation),
                switchCheckedState = check.value,
                onClick = {
                    check.value = !check.value
                    switchCheckedState = check.value
                     LogUtil.i("switchCheckedState:${check.value}")
                    apiService.getInfo().enqueueBody { }
                    userSP.edit().putBoolean("switchCheckedState", switchCheckedState).apply()
                }
            )

        }

    }
}

@Composable
fun PrivacyItem(
    title: String,
    subContent: @Composable () -> Unit = {},
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .height(50.dp)
            .fillMaxWidth()
            .pointerInput(Unit) {
                detectTapGestures {
                    onClick()
                }
            }
            .background(Color.White),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(10.dp))
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        subContent()
        Spacer(modifier = Modifier.width(9.dp))
        Icon(
            painter = painterResource(id = R.drawable.img_next),
            contentDescription = null,
            modifier = Modifier.size(22.dp),
            tint = Color(0XFF999999)
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
    AIHDivider(modifier = Modifier.padding(horizontal = 10.dp))
}
@Composable
fun PrivacySwitchItem(
    title: String,
    subContent: @Composable () -> Unit = {},
    onClick: () -> Unit = {},
    switchCheckedState:Boolean = false,
) {
    LogUtil.i("check:$switchCheckedState")
    Row(
        modifier = Modifier
            .height(50.dp)
            .fillMaxWidth()
            .background(Color.White),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(10.dp))
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        subContent()
        Spacer(modifier = Modifier.width(9.dp))
        Switch(
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color(0XFF789AF7),
                checkedTrackColor = Color(0XFF3654CD),
                uncheckedThumbColor = Color(0XFF999999),
                uncheckedTrackColor = Color.White,
            ),
            checked = switchCheckedState,
            onCheckedChange = {onClick()},
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
    AIHDivider(modifier = Modifier.padding(horizontal = 10.dp))
}

