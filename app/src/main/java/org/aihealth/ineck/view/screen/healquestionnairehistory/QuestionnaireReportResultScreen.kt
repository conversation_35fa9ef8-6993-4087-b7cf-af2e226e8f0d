package org.aihealth.ineck.view.screen.healquestionnairehistory

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.screen.questionnaire.MultipleChoice
import org.aihealth.ineck.view.screen.questionnaire.SingleChoice
import org.aihealth.ineck.viewmodel.QuestionnaireReportResultViewModel

@Composable
fun QuestionnaireReportResultScreen(
) {
    val scrollState = rememberScrollState()
    BasePageView(
        showBackIcon = true,
        headerContent = {
            Column(
                modifier = Modifier.fillMaxWidth(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.health_questionnaire),
                    style = TextStyle(
                        fontSize = 22.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )

            }
        }
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {

            Column(
                modifier = Modifier.padding(horizontal = 24.dp)
            ) {
                QuestionnaireReportResultViewModel.questionList.forEachIndexed { _, questionModel ->
                    when (questionModel.type) {
                        "SCQ" -> SingleChoice(
                            modifier = Modifier
                                .padding(top = 32.dp),
                            enable = false,
                            questionModel = questionModel,
                            onClick = {}
                        )

                        "MCQs" -> MultipleChoice(
                            modifier = Modifier
                                .padding(top = 32.dp),
                            questionModel = questionModel,
                            enable = false,
                            onClick = {}
                        )
                    }
                }
            }
        }
    }
}