package org.aihealth.ineck.view.custom

import android.content.pm.ActivityInfo
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.unit.Dp
import org.aihealth.ineck.activity
import org.aihealth.ineck.ui.theme.Sizes
import org.aihealth.ineck.util.finish

@Composable
fun BasePageView(
    modifier: Modifier = Modifier,
    title: String = "",
    showBackIcon: Boolean = false,
    headerHeight: Dp = Sizes.actionBarHeight,
    headerColor: Color = Color(0XFF333333),
    headerContent: @Composable BoxScope.() -> Unit = {},
    headerBackgroundColor: Color = Color.Transparent,
    background: @Composable BoxScope.() -> Unit = {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.White)
        )
    },
    statusBarDarkContentEnabled: Boolean = true,
    onBackPressed: () -> Unit = {
        if(activity.requestedOrientation == ActivityInfo.SCREEN_ORIENTATION_SENSOR_LANDSCAPE){
            activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
        }
        finish()
    },
    content: @Composable BoxScope.() -> Unit = {},
) {
    val headerText = rememberUpdatedState(newValue = title)
    BackHandler(showBackIcon) {
        onBackPressed()
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .navigationBarsPadding()
    ) {

        background()
        if (LocalConfiguration.current.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
            ActionBar(
                headerText.value,
                showBackIcon,
                headerHeight,
                headerColor,
                headerBackgroundColor,
                statusBarDarkContentEnabled,
                headerContent,
                onBackPressed = onBackPressed
            )
            Box(modifier) {
                content()
            }
        }
        else{
            Column(modifier = Modifier.fillMaxSize()) {
                ActionBar(
                    headerText.value,
                    showBackIcon,
                    headerHeight,
                    headerColor,
                    headerBackgroundColor,
                    statusBarDarkContentEnabled,
                    headerContent
                )
                Box(modifier) {
                    content()
                }
            }
        }
    }
}
