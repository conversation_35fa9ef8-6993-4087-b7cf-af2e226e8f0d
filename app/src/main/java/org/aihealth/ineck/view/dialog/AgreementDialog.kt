package org.aihealth.ineck.view.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.viewmodel.LoginUSAViewModel

@Composable
fun AgreementDialog() {
    val viewModel = viewModel<LoginUSAViewModel>()
    Column(
        Modifier
            .width(295.dp)
            .background(Color.White, RoundedCornerShape(10.dp))
            .padding(horizontal = 16.dp, vertical = 30.dp), horizontalAlignment = Alignment.CenterHorizontally) {
        Text(text = "User Agreement and Privacy Statement", fontSize = 18.sp, fontWeight = FontWeight.Bold, textAlign = TextAlign.Center, modifier = Modifier.padding(horizontal = 12.dp))
        Spacer(modifier = Modifier.height(16.dp))
        val annotatedText = buildAnnotatedString {
            withStyle(style = SpanStyle()) {
                append("In order to better protect your legitimate rights and interests, please read and agree to the agreement")
            }
            pushStringAnnotation("ystk", "《Privacy Statement》")
            withStyle(style = SpanStyle(color = Color(0XFF1E4BDF))) {
                append("《Privacy Statement》")
            }
            pop()
            withStyle(style = SpanStyle()) {
                append(",")
            }
            pushStringAnnotation("mzsm", "《User Agreement》")
            withStyle(style = SpanStyle(color = Color(0XFF1E4BDF))) {
                append("《User Agreement》")
            }
            pop()


        }
        ClickableText(
            text = annotatedText,
            onClick = { offset ->
                LogUtil.d("mytag", "LoginScreen: $offset")
                annotatedText.getStringAnnotations(
                    tag = "ystk", start = offset,
                    end = offset
                ).firstOrNull()?.let { _ ->
                    startScreen(Screen.PrivateTerm.route)

                }
                annotatedText.getStringAnnotations(
                    tag = "mzsm", start = offset,
                    end = offset
                ).firstOrNull()?.let { _ ->
                    startScreen(Screen.UserAgreement.route)
                }
            }
        )
        Spacer(modifier = Modifier.height(20.dp))
        Row(modifier = Modifier) {
            AIHOutlinedButton(
                text = "Disagree",
                onClick = {
                    finish()
                },
                modifier = Modifier.size(90.dp,40.dp)
            )
            Spacer(modifier = Modifier.width(10.dp))
            AIHButton(
                text = "Agree",
                onClick = {
                    viewModel.setIsReadChecked(true)
                    finish()
                },
                modifier = Modifier.size(90.dp,40.dp)
            )
        }
    }
}