package org.aihealth.ineck.view.custom

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectHorizontalDragGestures
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.input.pointer.PointerInputChange
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.model.angles.Record
import org.aihealth.ineck.model.angles.Severity
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.dayOfWeek
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.monthLastDay
import org.aihealth.ineck.util.toDp
import org.aihealth.ineck.util.toPx
import org.aihealth.ineck.util.year
import java.util.Calendar
import java.util.GregorianCalendar
import kotlin.math.absoluteValue
import kotlin.math.ceil
import kotlin.random.Random

private val minDragWidth by lazy {
    40.dp.toPx()
}

/**
 * 月报表的单位数据类
 * @param bitmap
 * @param records day odi promise neck_pain back_pain
 * @param severities
 */
data class MonthCalendarData(
    var bitmap: ImageBitmap ?= null,
    var records: SnapshotStateList<Record> = mutableStateListOf(),
    var severities: SnapshotStateList<Severity> = mutableStateListOf()
)

/**
 * 月报表的数据类
 * @property preData 为当前月份上一个的数据
 * @property currentData 为当前月份的数据
 * @property nextData 为当前月份下一个月的数据
 */
class MonthCalendarState (
    val today: Calendar
){
    var isHasConnected = false
    var selectedDay by mutableStateOf(today)
    val offset = Animatable(0F)
    var preData = MonthCalendarData()
    var currentData = MonthCalendarData()
    var nextData = MonthCalendarData()
    private val width: Int
        get() = currentData.bitmap?.width?:0
    val itemWidth: Int
        get() = (currentData.bitmap?.width?:0) / 7
    var year = today.year
    var month = today.month + 1
    var height by mutableIntStateOf(0)

    fun getRandomRecords(): MutableList<Record> {
        val list = mutableListOf<Record>()
        repeat(31) {
            list.add(Record(0, Random.nextInt(2), Random.nextInt(2), Random.nextInt(2), Random.nextInt(2)))
        }
        return list
    }

    fun getRandomSeverites(): MutableList<Severity> {
        val list = mutableListOf<Severity>()
        repeat(31) {
            list.add(Severity(Random.nextInt(100), Random.nextInt(100),Random.nextInt(100),0))
        }
        return list
    }

    fun drawPreBitmap(width: Int): ImageBitmap {
        return getBitmap(width,year, month - 1, selectedDay, preData.severities,preData.records,isHasConnected)
    }

    fun drawCurrentBitmap(width: Int): ImageBitmap {
        return getBitmap(width,year,month, selectedDay, currentData.severities,currentData.records,isHasConnected)
    }

    fun drawNextBitmap(width: Int): ImageBitmap {
        return getBitmap(width,year,month + 1, selectedDay, nextData.severities,nextData.records,isHasConnected)
    }

    fun getBitmapHeight(): Int{
        val calendar = GregorianCalendar(year, month - 1, 1)
        return (ceil((calendar.dayOfWeek + calendar.monthLastDay) / 7.0) *  if (isHasConnected) 90.dp.toPx() else 60.dp.toPx()).toInt()
    }

    private var displayTimeChanged: () -> Unit = { }

    internal fun onTimeChanged(){
        displayTimeChanged()
    }

    suspend fun nextPage() {
        if (year == today.year && month == today.month + 1) {
            return
        }
        val calendar = GregorianCalendar(year, month - 1, 1)
        calendar.month++
        year = calendar.year
        month = calendar.month + 1
        preData = currentData
        currentData = nextData
        onTimeChanged()
        height = currentData.bitmap?.height?:0
        nextData = MonthCalendarData()
//        nextData.records.clear()
//        nextData.severities.clear()
//        GlobalScope.launch {
//            withContext(Dispatchers.IO) {
//                delay(300)
//        nextData.records.addAll(getRandomRecords())
//        nextData.severities.addAll(getRandomSeverites())
//            }
//        }
        nextData.bitmap = drawNextBitmap(width)
        offset.snapTo(offset.value + width)
        offset.animateTo(0F)
    }

    suspend fun prePage() {
        val calendar = GregorianCalendar(year, month - 1, 1)
        calendar.month--
        year = calendar.year
        month = calendar.month + 1
        onTimeChanged()
        nextData = currentData
        currentData = preData
        height = currentData.bitmap?.height?:0
        preData = MonthCalendarData()
//        preData.records.clear()
//        preData.severities.clear()
//        GlobalScope.launch {
//            delay(300)
//            preData.records.addAll(getRandomRecords())
//            preData.severities.addAll(getRandomSeverites())
//        }
        preData.bitmap = drawPreBitmap(width)
        offset.snapTo(offset.value - width)
        offset.animateTo(0F)
    }

    fun onDisplayTimeChanged(displayTimeChanged: () -> Unit) {
        this.displayTimeChanged = displayTimeChanged
    }
}

/**
 *  依据给定数据获取月历中描述日记录的单元Bitmap单位
 *  @param width
 *  @param year
 *  @param month
 *  @param selectedDay Calendar
 *  @param severities List <Severity>
 *  @param records List <Record>
 *  @param isHasConnected
 */
private fun getBitmap(
    width: Int,
    year: Int,
    month: Int,
    selectedDay: Calendar,
    severities: SnapshotStateList<Severity>,
    records: List<Record>,
    isHasConnected: Boolean
): ImageBitmap {
    val calendar: Calendar = GregorianCalendar(year, month - 1, 1)
    val firstWeekDay = calendar.dayOfWeek
    val dayCount = calendar.monthLastDay
    val height = (ceil((dayCount + firstWeekDay) / 7.0) *  if (isHasConnected)90.dp.toPx() else 60.dp.toPx()).toInt()
    calendar.set(year, month - 1, 1)
    val mBitmap = Bitmap.createBitmap(width,height,Bitmap.Config.ARGB_8888)
    val canvas = Canvas(mBitmap)

    val itemWidth = width / 7
    val radius1 = 12.dp.toPx()
    val radius2 = 2.dp.toPx()
    //和3个点之间的间距
    val padding = 8.dp.toPx()
    val linePadding = 10.dp.toPx()
    val ringWidth = 3.3.dp.toPx()
    val paint = Paint()
    paint.isAntiAlias = true
    paint.textSize = 10.dp.toPx()
    paint.textAlign = Paint.Align.CENTER
    val fontHeight = 5.dp.toPx()
    var x: Float = firstWeekDay * itemWidth + itemWidth / 2.0f
    var y: Float = radius1

    for (i in 1..dayCount) {
        if (selectedDay.timeInMillis == calendar.timeInMillis) {
            paint.color = Color.parseColor("#FF6483E8")
            canvas.drawCircle(x, y, radius1, paint)
            paint.color = Color.WHITE
            canvas.drawText(i.toString(), x, y + fontHeight / 2, paint)
        } else {
            paint.color = Color.parseColor("#FF999999")
            canvas.drawText(i.toString(), x, y + fontHeight / 2, paint)
        }
        y += radius1 + padding + radius2
        if (records.size > i - 1 && records[i - 1].odi != 0) {
            paint.color = Color.parseColor("#FF52DDAA")
        } else {
            paint.color = Color.parseColor("#FFC4C4C4")
        }
        canvas.drawCircle(x, y, radius2, paint)
        y += radius2 * 3
        if (records.size > i - 1 && records[i - 1].promise != 0) {
            paint.color = Color.parseColor("#FFFAE108")
        } else {
            paint.color = Color.parseColor("#FFC4C4C4")
        }
        canvas.drawCircle(x, y, radius2, paint)
        y += radius2 * 3
        if (records.size > i - 1 && records[i - 1].neck_pain != 0) {
            paint.color = Color.parseColor("#FFEE6860")
        } else {
            paint.color = Color.parseColor("#FFC4C4C4")
        }
        canvas.drawCircle(x, y, radius2, paint)
        y += radius2
        if (isHasConnected) {
            y += padding
            if (severities.size > i - 1) {
                val slightCount = severities[i - 1].slight_count
                val severeCount = severities[i - 1].severe_count
                val moderateCount = severities[i - 1].moderate_count
                if (slightCount == 0 && severeCount == 0 && moderateCount == 0) {
                    paint.color = Color.parseColor("#FFC4C4C4")
                    canvas.drawCircle(x, y + radius1, radius1, paint)
                } else {
                    val slightDegree =
                        slightCount * 360 / (slightCount + severeCount + moderateCount)
                    paint.color = Color.parseColor("#FF64CECD")
                    canvas.drawArc(
                        x - radius1,
                        y,
                        x + radius1,
                        y + 2 * radius1,
                        0f,
                        slightDegree.toFloat(),
                        true,
                        paint
                    )
                    val moderateDegree =
                        moderateCount * 360 / (slightCount + severeCount + moderateCount)
                    paint.color = Color.parseColor("#FFFFD772")
                    canvas.drawArc(
                        x - radius1,
                        y,
                        x + radius1,
                        y + 2 * radius1,
                        slightDegree.toFloat(),
                        moderateDegree.toFloat(),
                        true,
                        paint
                    )
                    paint.color = Color.parseColor("#FFE87C76")
                    canvas.drawArc(
                        x - radius1,
                        y,
                        x + radius1,
                        y + 2 * radius1,
                        (slightDegree + moderateDegree).toFloat(),
                        (360 - slightDegree - moderateDegree).toFloat(),
                        true,
                        paint
                    )
                    paint.color = Color.WHITE
                }
            } else {
                paint.color = Color.parseColor("#FFC4C4C4")
                canvas.drawCircle(x, y + radius1, radius1, paint)
            }
            paint.color = Color.WHITE
            canvas.drawCircle(x, y + radius1, radius1 - ringWidth, paint)
            y -= padding
        }
        y = y - radius1 - padding - radius2 * 8
        if (x + itemWidth > width) {
            x = itemWidth / 2.0f
            y += radius1 * 2 + radius2 * 8 + padding + linePadding
            if (isHasConnected) {
                y += padding + radius1 * 2
            }
        } else {
            x += itemWidth
        }
        calendar.add(Calendar.DATE,1)
    }
    return mBitmap.asImageBitmap()
}

/**
 * 报告页中的月日历
 * @param calendarTime 当前选择的日期
 * @param calendarState 月日历的数据源
 *
 */
@Composable
fun MonthCalendar(
    calendarTime: String,
    calendarState: MonthCalendarState,
    isHasConnected: Boolean,
    onClick: (Calendar) -> Unit
) {
    val week = stringArrayResource(id = R.array.week3)
    calendarState.isHasConnected = isHasConnected
    /* Bitmap高度显示不全，这里设置高度余量的冗余，预设高度多 158 */
    calendarState.height = calendarState.getBitmapHeight() + 158
    val coroutineScope = rememberCoroutineScope()

    Column {
        Box(
            modifier = Modifier
                .padding(horizontal = 12.dp)
                .fillMaxWidth()
                .wrapContentHeight()
        )
        {
            Image(
                painter = painterResource(id = R.drawable.img_report_calender_pre),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .size(14.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            coroutineScope.launch {
                                calendarState.prePage()
                            }
                        }
                    }
            )
            Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier.align(
                Alignment.Center)) {
                Text(
                    text = calendarTime,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Medium,
                    color = androidx.compose.ui.graphics.Color(0XFF444444),
                    modifier = Modifier.padding(start = 10.dp)
                )
                Spacer(modifier = Modifier.width(10.dp))
                Image(
                    painter = painterResource(id = R.drawable.img_report_calender),
                    contentDescription = null,
                    modifier = Modifier
                        .size(24.dp, 26.dp)
                )
            }
            Image(
                painter = painterResource(id = R.drawable.img_report_calender_next),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .size(14.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            coroutineScope.launch {
                                calendarState.nextPage()
                            }
                        }
                    }
            )
        }
        Column(
            Modifier
                .fillMaxWidth()
                .padding(top = 20.dp, start = 12.dp, end = 12.dp)) {
            /* 星期表头 */
            Row(Modifier.fillMaxWidth()) {
                repeat(7) {
                    Text(text = week[it], fontSize = 12.sp, color = androidx.compose.ui.graphics.Color(0XFF999999), textAlign = TextAlign.Center, modifier = Modifier.weight(1F))
                }
            }
            Spacer(modifier = Modifier.height(12.dp))
            /* Bitmap容器 */
            BoxWithConstraints(
                Modifier
                    .fillMaxWidth()
                    .height(calendarState.height.toDp())
                    .clip(RectangleShape)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            val x = it.x
                            val y = it.y
                            val height = if (isHasConnected) 90.dp.toPx() else 58.dp.toPx()
                            if (y % height <= height - 10.dp.toPx()) {
                                val clickRow = ceil((y / height).toDouble()).toInt()
                                val clickIndex =
                                    (ceil((x / calendarState.itemWidth).toDouble()) + (clickRow - 1) * 7).toInt()
                                val calendar = GregorianCalendar(
                                    calendarState.year,
                                    calendarState.month - 1,
                                    1
                                )
                                val dayOfWeek = calendar.dayOfWeek
                                if (clickIndex >= dayOfWeek + 1 && clickIndex <= dayOfWeek + calendar.monthLastDay) {
                                    calendar.date = clickIndex - dayOfWeek
                                    if (calendar.timeInMillis <= calendarState.today.timeInMillis) {
                                        onClick(calendar)
                                    }
                                }
                            }
                        }
                    }

            ) {
                calendarState.onTimeChanged()
                calendarState.preData.bitmap = calendarState.drawPreBitmap(constraints.maxWidth)
                calendarState.currentData.bitmap = calendarState.drawCurrentBitmap(constraints.maxWidth)
                calendarState.nextData.bitmap = calendarState.drawNextBitmap(constraints.maxWidth)
                Canvas(
                    modifier = Modifier
                        .fillMaxSize()
                        .pointerInput(Unit) {
                            detectHorizontalDragGestures(
                                onHorizontalDrag = { _: PointerInputChange, dragAmount: Float ->
                                    coroutineScope.launch {
                                        calendarState.offset.snapTo(calendarState.offset.value + dragAmount)
                                    }
                                },
                                onDragEnd = {
                                    if (calendarState.offset.value.absoluteValue < minDragWidth) {
                                        coroutineScope.launch {
                                            calendarState.offset.animateTo(0F)
                                        }
                                    } else {
                                        coroutineScope.launch {
                                            if (calendarState.offset.value < 0) {
                                                calendarState.nextPage()
                                            } else {
                                                calendarState.prePage()
                                            }
                                            calendarState.offset.animateTo(0F)
                                        }
                                    }
                                }
                            )
                        }
                ) {
                    calendarState.preData.bitmap?.let { drawImage(image = it, topLeft = Offset(calendarState.offset.value - size.width,0F)) }
                    calendarState.currentData.bitmap?.let { drawImage(image = it, topLeft = Offset(calendarState.offset.value,0F)) }
                    calendarState.nextData.bitmap?.let { drawImage(image = it, topLeft = Offset(calendarState.offset.value + size.width,0F)) }
                }

            }

        }
    }
}