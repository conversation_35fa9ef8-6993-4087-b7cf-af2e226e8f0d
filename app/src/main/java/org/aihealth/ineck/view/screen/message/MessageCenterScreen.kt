package org.aihealth.ineck.view.screen.message

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.dialog.ClearMessageDialog
import org.aihealth.ineck.view.screen.defaultscreen.NoMessageScreen

@Composable
fun MessageCenterScreen(
//    viewModel: MainViewModel,
) {
    var showClearMessageDialog by remember { mutableStateOf(false) }
    BasePageView(
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true,
        title = stringResource(id = R.string.message),
        headerContent = {
            IconButton(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .size(28.dp),
                onClick = {
                    showClearMessageDialog = true
                }
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_clear_message),
                    contentDescription = "clear message",
                )
            }
        }
    ) {
        NoMessageScreen(
            modifier = Modifier.fillMaxSize()
        )
    }

    ClearMessageDialog(
        modifier = Modifier.padding(16.dp),
        visible = showClearMessageDialog,
        onDismiss = {
            showClearMessageDialog = false
        },
        onConfirm = {
            showClearMessageDialog = false
        }
    )
}

@Preview
@Composable
private fun PreviewMessageCenter() {
    MessageCenterScreen()
}

