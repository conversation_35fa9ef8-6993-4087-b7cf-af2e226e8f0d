package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.monthLastDay
import org.aihealth.ineck.util.toStringList
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.AIHWheel
import org.aihealth.ineck.view.custom.AIHWheelState
import org.aihealth.ineck.viewmodel.PersonalDataViewModel
import org.aihealth.ineck.viewmodel.user
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

@Composable
fun BirthdateEditRoute(
    viewModel: PersonalDataViewModel = viewModel()
) {
    // 获取当前用户的出生日期
    val currentBirthdate = user.birthdate

    BirthdateEditScreen(
        initialBirthdate = currentBirthdate,
        onSave = { selectedDate ->
            viewModel.setBirthdate(selectedDate)
            finish()
        },
        onCancel = { finish() }
    )
}

@Composable
fun BirthdateEditScreen(
    initialBirthdate: String = "",
    onSave: (Long) -> Unit = { },
    onCancel: () -> Unit = {}
) {
    var showDatePicker by remember { mutableStateOf(false) }
    var selectedDateMillis by remember {
        mutableStateOf(
            if (initialBirthdate.isNotBlank()) {
                try {
                    // 解析服务器返回的 UTC 时间
                    val utcFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                    utcFormat.timeZone = TimeZone.getTimeZone("UTC")
                    val utcDate = utcFormat.parse(initialBirthdate)

                    if (utcDate != null) {
                        // 转换为本地时区的同一日期（不考虑时间，只考虑日期）
                        val utcCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
                            time = utcDate
                        }
                        val localCalendar = Calendar.getInstance().apply {
                            set(Calendar.YEAR, utcCalendar.get(Calendar.YEAR))
                            set(Calendar.MONTH, utcCalendar.get(Calendar.MONTH))
                            set(Calendar.DAY_OF_MONTH, utcCalendar.get(Calendar.DAY_OF_MONTH))
                            set(Calendar.HOUR_OF_DAY, 0)
                            set(Calendar.MINUTE, 0)
                            set(Calendar.SECOND, 0)
                            set(Calendar.MILLISECOND, 0)
                        }
                        localCalendar.timeInMillis
                    } else {
                        System.currentTimeMillis()
                    }
                } catch (e: Exception) {
                    try {
                        // 尝试解析简单日期格式
                        val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                        format.parse(initialBirthdate)?.time ?: System.currentTimeMillis()
                    } catch (e: Exception) {
                        System.currentTimeMillis()
                    }
                }
            } else {
                System.currentTimeMillis()
            }
        )
    }

    val displayDate = remember(selectedDateMillis) {
        val format = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        format.format(Date(selectedDateMillis))
    }

    BaseEditScreen(
        onSave = {
            onSave(selectedDateMillis)
        },
        onCancel = onCancel
    ) {
        // Date Field Container
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(16.dp)
        ) {
            BirthdateField(
                label = stringResource(R.string.date_of_birth),
                value = displayDate,
                onClick = { showDatePicker = true }
            )
        }
    }

    // Date Picker Bottom Sheet
    if (showDatePicker) {
        BirthdatePicker(
            initialDateMillis = selectedDateMillis,
            onDateSelected = { dateMillis ->
                selectedDateMillis = dateMillis
                LogUtil.i("Birthdate ${dateMillis},${selectedDateMillis}")
                showDatePicker = false

            },
            onDismiss = { showDatePicker = false }
        )
    }
}

@Composable
private fun BirthdateField(
    label: String,
    value: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ) { onClick() },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            color = Color(0xFF666666)
        )

        Text(
            text = value,
            fontSize = 16.sp,
            color = Color(0xFF333333),
            textAlign = TextAlign.End,
            fontWeight = FontWeight.Medium,
        )
    }
}

@Composable
private fun BirthdatePicker(
    initialDateMillis: Long,
    onDateSelected: (Long) -> Unit,
    onDismiss: () -> Unit
) {
    val maxYear = Calendar.getInstance().get(Calendar.YEAR) - 3
    val minYear = maxYear - 90

    // 解析初始日期
    val initialCalendar = Calendar.getInstance().apply {
        timeInMillis = initialDateMillis
    }

    // 创建滚轮状态
    val yearState = remember {
        AIHWheelState(initialIndex = initialCalendar.get(Calendar.YEAR) - minYear)
    }
    val monthState = remember {
        AIHWheelState(initialIndex = initialCalendar.get(Calendar.MONTH))
    }
    val dayState = remember {
        AIHWheelState(initialIndex = initialCalendar.get(Calendar.DAY_OF_MONTH) - 1)
    }

    // 计算当前选择的年月对应的天数
    val selectedYear by remember { derivedStateOf { minYear + yearState.selectedIndex } }
    val selectedMonth by remember { derivedStateOf { monthState.selectedIndex + 1 } }
    val dayCount by remember { derivedStateOf {
        Calendar.getInstance().apply {
            set(Calendar.YEAR, selectedYear)
            set(Calendar.MONTH, selectedMonth - 1)
            set(Calendar.DAY_OF_MONTH, 1)
        }.monthLastDay
    } }

    AIHBottomSheet(
        onDismissRequest = onDismiss
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Header
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.cancel),
                    fontSize = 16.sp,
                    color = Color(0xFF666666),
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier.clickable { onDismiss() }
                )

                Text(
                    text = stringResource(R.string.date),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF333333),
                )

                Text(
                    text = stringResource(R.string.save),
                    fontSize = 16.sp,
                    color = MaterialTheme.colorScheme.primary,
                    fontWeight = FontWeight.Normal,
                    modifier = Modifier.clickable {
                        // 构建用户选择的本地日期，然后转换为 UTC 存储
                        val localCalendar = Calendar.getInstance().apply {
                            set(Calendar.YEAR, selectedYear)
                            set(Calendar.MONTH, selectedMonth - 1)
                            set(
                                Calendar.DAY_OF_MONTH,
                                (dayState.selectedIndex + 1).coerceAtMost(dayCount)
                            )
                            set(Calendar.HOUR_OF_DAY, 0)
                            set(Calendar.MINUTE, 0)
                            set(Calendar.SECOND, 0)
                            set(Calendar.MILLISECOND, 0)
                        }

                        // 转换为 UTC 时区的同一日期
                        val utcCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
                            set(Calendar.YEAR, localCalendar.get(Calendar.YEAR))
                            set(Calendar.MONTH, localCalendar.get(Calendar.MONTH))
                            set(Calendar.DAY_OF_MONTH, localCalendar.get(Calendar.DAY_OF_MONTH))
                            set(Calendar.HOUR_OF_DAY, 0)
                            set(Calendar.MINUTE, 0)
                            set(Calendar.SECOND, 0)
                            set(Calendar.MILLISECOND, 0)
                        }
                        onDateSelected(utcCalendar.timeInMillis)
                    }
                )
            }

            // 滚轮式日期选择器
            Row(
                modifier = Modifier.fillMaxWidth()
            ) {
                AIHWheel(
                    state = yearState,
                    list = (minYear..maxYear).toStringList(),
                    modifier = Modifier.weight(1f)
                )
                AIHWheel(
                    state = monthState,
                    list = (1..12).map { String.format("%02d", it) },
                    modifier = Modifier.weight(1f)
                )
                AIHWheel(
                    state = dayState,
                    list = (1..dayCount).map { String.format("%02d", it) },
                    modifier = Modifier.weight(1f)
                )
            }
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun PreviewBirthdateEditScreen() {
    AIH_UserTheme {
        BirthdateEditScreen()
    }
}
