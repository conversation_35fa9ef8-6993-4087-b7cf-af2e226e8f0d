package org.aihealth.ineck.view.screen.healquestionnairehistory

import androidx.compose.animation.animateColorAsState
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.SecondaryIndicator
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.accompanist.pager.ExperimentalPagerApi
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.PagerState
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.QuestionnaireResultTabType

@Composable
fun HealthQuestionnaireHistoryScreen() {

    val pagerState = PagerState(0)
    val tabs = listOf(QuestionnaireResultTabType.AiNeck, QuestionnaireResultTabType.AiBack)
    BasePageView(
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true,
        headerContent = {
            Text(
                text = stringResource(id = R.string.health_questionnaire),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF444444),
                    textAlign = TextAlign.Center,
                ),
                modifier = Modifier
                    .padding(top = 10.dp)
                    .align(Alignment.Center),
            )
            Text(
                text = stringResource(id = R.string.assess_once),
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF999999),
                ),
                modifier = Modifier
                    .padding(end = 10.dp, top = 10.dp)
                    .align(Alignment.CenterEnd)
                    .clickable {
                        when (pagerState.currentPage) {
                            0 -> startScreen(Screen.QuestionnaireWelcome.route, true)
                            1 -> startScreen(Screen.AiBackQuestionnaireWelcome.route, true)
                        }
                    },
            )
        }
    ) {
        HorizontalPagerLayout(
            pagerState = pagerState,
            tabs = tabs
        ) {
            when (pagerState.currentPage) {
                0 -> {
                    AiNeckQuestionnaireHistoryScreen(
                        modifier = Modifier.fillMaxSize(),
                        viewModel = viewModel(),
                    )
                }

                1 -> {
                    AiBackQuestionnaireHistoryScreen(
                        modifier = Modifier.fillMaxSize(),
                        viewModel = viewModel(),
                    )
                }
            }
        }
    }
}

@Composable
fun HorizontalPagerLayout(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    tabs: List<QuestionnaireResultTabType>,
    context: @Composable () -> Unit,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
    ) {
        HealthQuestionnaireHistoryTabBar(
            modifier = Modifier.padding(top = 7.dp),
            pagerState = pagerState,
            tabs = tabs
        )
        HorizontalPager(
            state = pagerState,
            count = tabs.size,
            modifier = Modifier.fillMaxSize()
        ) {
            context()
        }
    }
}

@OptIn(ExperimentalPagerApi::class)
@Composable
private fun HealthQuestionnaireHistoryTabBar(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    tabs: List<QuestionnaireResultTabType>,
) {
    val scope = rememberCoroutineScope()
    TabRow(
        modifier = modifier,
        selectedTabIndex = pagerState.currentPage,
        containerColor = Color.White,
        indicator = { tabPositions ->
            SecondaryIndicator(
                Modifier.tabIndicatorOffset(tabPositions[pagerState.currentPage]),
                height = 4.dp,
                color = Color.Blue
            )
        }
    ) {
        tabs.forEachIndexed { index, tab ->
            HealthQuestionnaireHistoryTab(
                title = tab.name,
                onClick = {
                    scope.launch { pagerState.animateScrollToPage(index) }
                },
                isSelected = pagerState.currentPage == index
            )
        }
    }
}

@Composable
private fun HealthQuestionnaireHistoryTab(
    title: String,
    onClick: () -> Unit,
    isSelected: Boolean,
    modifier: Modifier = Modifier
) {
    val textColor: Color by animateColorAsState(
        if (isSelected) Color(0xFF3C59CF) else Color(0xFF999999),
        label = ""
    )
    Row(
        modifier = modifier
            .pointerInput(Unit) {
                detectTapGestures { onClick() }
            }
            .padding(horizontal = 4.dp, vertical = 8.dp)
            .zIndex(1f),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(500),
                color = textColor
            )
        )
    }
}



