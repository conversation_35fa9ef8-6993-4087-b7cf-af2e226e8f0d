package org.aihealth.ineck.view.custom

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarDuration
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.core.text.isDigitsOnly
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.util.UnitUtil.glucoseMgDlToMmolL
import org.aihealth.ineck.util.unitTool.TemperatureConverter
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState.NONE
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState.VISIBLE_BLOOD_GLUCOSE
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState.VISIBLE_BLOOD_OXYGEN
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState.VISIBLE_BLOOD_PRESSURE
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState.VISIBLE_BODY_TEMPERATURE
import org.aihealth.ineck.view.custom.VitalSignPostDialogVisibleState.VISIBLE_HEART_RATE
import kotlin.math.roundToInt

/* 体温上限与体温下限 - 摄氏度 */
private const val LOWER_BODY_TEMPERATURE_CELSIUS = 32.0f
private const val UPPER_BODY_TEMPERATURE_CELSIUS = 43.0f
/* 体温上限与体温下限 - 华氏度 */
private const val LOWER_BODY_TEMPERATURE_FAHRENHEIT = 93.2f
private const val UPPER_BODY_TEMPERATURE_FAHRENHEIT = 109.4f

/* 舒张压上限与下限 */
private const val LOWER_DIASTOLIC = 40
private const val UPPER_DIASTOLIC = 150
/* 收缩压上限与下限 */
private const val LOWER_SYSTOLIC = 70
private const val UPPER_SYSTOLIC = 300
/* 血糖上限与下限 - Mmol/L */
private const val LOWER_BLOOD_GLUCOSE_MMOL = 0f
private const val UPPER_BLOOD_GLUCOSE_MMOL = 56f
/* 血糖上限与下限 - mg/dL */
private const val LOWER_BLOOD_GLUCOSE_MGDL = 0f
private const val UPPER_BLOOD_GLUCOSE_MGDL = 1000f
/* 血氧上限与下限 */
private const val LOWER_BLOOD_OXYGEN = 60f
private const val UPPER_BLOOD_OXYGEN = 100f
/* 心率上限与下限 - 符合医学标准的正常成人静息心率范围 */
private const val LOWER_HEART_RATE = 60
private const val UPPER_HEART_RATE = 100

/**
 *  生命体征数据手动上传 底部抽屉
 *  @param  visible 上传生命体征数据底部抽屉显示标识
 *  @param  context 上下文参数
 *  @param  isCelsiusState  是否采用摄氏度单位
 *  @param  postVitalSignData   上传生命体征数据逻辑方法
 *  @param  onDismissEvent  抽屉栏关闭触发事件: 若Boolean形参为true表示该对话框已经处理过数据提交逻辑，反之false为没有
 *  @param  onChangeCelsiusStateEvent   变更当前体温所用单位事件
 */
@Composable
fun VitalSignsPostBottomDrawer(
    visible: VitalSignPostDialogVisibleState,
    isCelsiusState: State<Boolean>,
    isMmolL: State<Boolean>,
    context: Context,
    postVitalSignData: (String) -> Unit,
    onDismissEvent: (Boolean) -> Unit,
    onChangeCelsiusStateEvent: () -> Unit,
    onChangeMmolLStateEvent: () -> Unit
) {
    /* 待上传生命体征数据 */
    val (tempVitalSignForPost, setTempVitalSignForPost) = remember { mutableStateOf("") }

    val snackBarHost = remember { SnackbarHostState() }
    val snackBarBackground = Color.Gray.copy(alpha = .9f)
    val snackBarTextColor = Color.White

    if (visible != NONE) {
        Box(modifier = Modifier
            .fillMaxSize()
            .background(Color.Transparent)) {
            val coroutineScope = rememberCoroutineScope()
            AIHBottomSheet(
                onDismissRequest = {
                    onDismissEvent(false)
                    coroutineScope.cancel()
                }
            ) {
                SnackbarHost(
                    hostState = snackBarHost,
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Center)
                        .padding(horizontal = 16.dp, vertical = 16.dp)
                        .zIndex(10f)
                ) {
                    Snackbar(
                        containerColor = snackBarBackground,
                        contentColor = snackBarTextColor,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = it.visuals.message,
                                style = MaterialTheme.typography.bodyMedium,
                                textAlign = TextAlign.Center,
                                modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                            )
                        }
                    }
                }
                ConstraintLayout(
                    modifier = Modifier
                        .imePadding()
                        .height(330.dp)
                        .fillMaxWidth()
                ) {
                    val (titleBlock, inputBlock) = createRefs()
                    Text(
                        text = when(visible) {
                            VISIBLE_HEART_RATE -> stringResource(id = R.string.heart_rate)
                            VISIBLE_BODY_TEMPERATURE -> stringResource(id = R.string.body_temperature)
                            VISIBLE_BLOOD_PRESSURE -> stringResource(id = R.string.blood_pressure)
                            VISIBLE_BLOOD_OXYGEN -> stringResource(id = R.string.blood_oxygen)
                            VISIBLE_BLOOD_GLUCOSE -> stringResource(id = R.string.blood_glucose)
                            NONE -> ""
                        },
                        textAlign = TextAlign.Center,
                        fontSize = 20.sp,
                        color = Color(0xFF244CD2),
                        fontWeight = FontWeight.Normal,
                        modifier = Modifier.constrainAs(titleBlock) {
                            start.linkTo(parent.start)
                            top.linkTo(parent.top, 36.dp)
                            end.linkTo(parent.end)
                            bottom.linkTo(titleBlock.top)
                        }
                    )
                    val contentModifier = Modifier
                        .constrainAs(inputBlock) {
                            start.linkTo(parent.start)
                            top.linkTo(parent.top, 34.dp)
                            end.linkTo(parent.end)
                            bottom.linkTo(parent.bottom)
                        }
                    when(visible) {
                        NONE -> {
                            /* Nothing */
                        }
                        VISIBLE_HEART_RATE -> {
                            /* 上传心率数据组件 */
                            HeartRatePostComponent(
                                modifier = contentModifier,
                                textValue = tempVitalSignForPost,
                                setTextValue = { setTempVitalSignForPost(it) },
                                onConfirmEvent = {
                                    if ((tempVitalSignForPost.isNotEmpty() || tempVitalSignForPost.isNotBlank()) && tempVitalSignForPost.isDigitsOnly()) {
                                        val effectiveNumber = tempVitalSignForPost.toInt()
                                        if (effectiveNumber in LOWER_HEART_RATE..UPPER_HEART_RATE) {
                                            postVitalSignData(effectiveNumber.toString())
                                            onDismissEvent(true)
                                        } else {
                                            coroutineScope.launch(Dispatchers.Main) {
                                                snackBarHost.showSnackbar(
                                                    context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                        R.string.vital_signal_limit_of_rate_heard
                                                    ),
                                                    duration = SnackbarDuration.Short
                                                )
                                            }
                                        }
                                    } else {
                                        coroutineScope.launch(Dispatchers.Main) {
                                            snackBarHost.showSnackbar(
                                                context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                    R.string.vital_signal_limit_of_rate_heard
                                                ),
                                                duration = SnackbarDuration.Short
                                            )
                                        }
                                    }
                                    setTempVitalSignForPost("")
                                }
                            )
                        }
                        VISIBLE_BODY_TEMPERATURE -> {
                            /* 上产体温数据组件 */
                            BodyTemperaturePostComponent(
                                modifier = contentModifier,
                                isCelsiusState = isCelsiusState,
                                textValue = tempVitalSignForPost,
                                setTextValue = { setTempVitalSignForPost(it) },
                                onConfirmEvent = {
                                    if ((tempVitalSignForPost.isNotEmpty() || tempVitalSignForPost.isNotBlank()) && tempVitalSignForPost.isFloat()) {
                                        val effectiveNumber = tempVitalSignForPost.toFloat()
                                        if (isCelsiusState.value && LOWER_BODY_TEMPERATURE_CELSIUS < effectiveNumber && effectiveNumber < UPPER_BODY_TEMPERATURE_CELSIUS) {
                                            postVitalSignData(effectiveNumber.toString())
                                            onDismissEvent(true)
                                        } else if (!isCelsiusState.value && LOWER_BODY_TEMPERATURE_FAHRENHEIT < effectiveNumber && effectiveNumber < UPPER_BODY_TEMPERATURE_FAHRENHEIT) {
                                            postVitalSignData(
                                                TemperatureConverter.fahrenheitToCelsius(
                                                    effectiveNumber.toDouble()
                                                ).toString()
                                            )
                                            onDismissEvent(true)
                                        } else {
                                            coroutineScope.launch(Dispatchers.Main) {
                                                snackBarHost.showSnackbar(
                                                    context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                        R.string.vital_signal_limit_of_body_temperature
                                                    ),
                                                    duration = SnackbarDuration.Short
                                                )
                                            }
                                        }
                                    } else {
                                        coroutineScope.launch(Dispatchers.Main) {
                                            snackBarHost.showSnackbar(
                                                context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                    R.string.vital_signal_limit_of_body_temperature
                                                ),
                                                duration = SnackbarDuration.Short
                                            )
                                        }
                                    }
                                    setTempVitalSignForPost("")
                                },
                                onChangeCelsiusStateEvent = onChangeCelsiusStateEvent
                            )
                        }
                        VISIBLE_BLOOD_OXYGEN -> {
                            /* 上传血氧数据组件 */
                            BloodOxygenPostComponent(
                                modifier = contentModifier,
                                textValue = tempVitalSignForPost,
                                setTextValue = { setTempVitalSignForPost(it) },
                                onConfirmEvent = {
                                    if ((tempVitalSignForPost.isNotEmpty() || tempVitalSignForPost.isNotBlank()) && tempVitalSignForPost.isFloat()) {
                                        val effectiveNumber = tempVitalSignForPost.toFloat()
                                        if (effectiveNumber in LOWER_BLOOD_OXYGEN..UPPER_BLOOD_OXYGEN) {
                                            postVitalSignData(((effectiveNumber * 10).roundToInt() / 10.0).toString())
                                            onDismissEvent(true)
                                        } else {
                                            coroutineScope.launch(Dispatchers.Main) {
                                                snackBarHost.showSnackbar(
                                                    context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                        R.string.vital_signal_limit_of_blood_oxygen
                                                    ),
                                                    duration = SnackbarDuration.Short
                                                )
                                            }
                                        }
                                    } else {
                                        coroutineScope.launch(Dispatchers.Main) {
                                            snackBarHost.showSnackbar(
                                                context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                    R.string.vital_signal_limit_of_blood_oxygen
                                                ),
                                                duration = SnackbarDuration.Short
                                            )
                                        }
                                    }
                                    setTempVitalSignForPost("")
                                }
                            )
                        }
                        VISIBLE_BLOOD_PRESSURE -> {
                            /* 上传血压数据组件 */
                            val (diastolic, setDiastolic) = remember { mutableStateOf("") }
                            val (systolic, setSystolic) = remember { mutableStateOf("") }
                            BloodPressurePostComponent(
                                modifier = contentModifier,
                                diastolicTextValue = diastolic,
                                systolicTextValue = systolic,
                                setDiastolicTextValue = {
                                    setDiastolic(it)
                                },
                                setSystolicTextValue = {
                                    setSystolic(it)
                                },
                                onConfirmEvent = {
                                    if ((diastolic.isNotEmpty() || diastolic.isNotBlank()) &&
                                        (systolic.isNotEmpty() || systolic.isNotBlank()) &&
                                        diastolic.isDigitsOnly() && systolic.isDigitsOnly()
                                    ) {
                                        if (systolic.toInt() in LOWER_SYSTOLIC until UPPER_SYSTOLIC && diastolic.toInt() in LOWER_DIASTOLIC until UPPER_DIASTOLIC) {
                                            postVitalSignData("$diastolic/$systolic")
                                            onDismissEvent(true)
                                        } else {
                                            coroutineScope.launch(Dispatchers.Main) {
                                                snackBarHost.showSnackbar(
                                                    context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                        R.string.vital_signal_limit_of_blood_pressure
                                                    ),
                                                    duration = SnackbarDuration.Short
                                                )
                                            }
                                        }
                                    } else {
                                        coroutineScope.launch(Dispatchers.Main) {
                                            snackBarHost.showSnackbar(
                                                context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                    R.string.vital_signal_limit_of_blood_pressure
                                                ),
                                                duration = SnackbarDuration.Short
                                            )
                                        }
                                    }
                                }
                            )
                        }
                        VISIBLE_BLOOD_GLUCOSE -> {
                            /* 上传血糖数据组件 */
                            BloodGlucosePostComponent(
                                modifier = contentModifier,
                                textValue = tempVitalSignForPost,
                                setTextValue = { setTempVitalSignForPost(it) },
                                isMmolL = isMmolL,
                                onConfirmEvent = {
                                    if ((tempVitalSignForPost.isNotEmpty() || tempVitalSignForPost.isNotBlank()) && tempVitalSignForPost.isFloat()) {
                                        val effectiveNumber = tempVitalSignForPost.toFloat()
                                        if (isMmolL.value) {
                                            if (effectiveNumber in LOWER_BLOOD_GLUCOSE_MMOL..UPPER_BLOOD_GLUCOSE_MMOL) {
                                                if (isMmolL.value) {
                                                    postVitalSignData(((effectiveNumber * 10).roundToInt() / 10.0).toString())
                                                } else {
                                                    postVitalSignData("%.1f".format(
                                                        glucoseMgDlToMmolL(((effectiveNumber * 10).roundToInt() / 10.0))
                                                    ))
                                                }
                                                onDismissEvent(true)
                                            } else {
                                                coroutineScope.launch(Dispatchers.Main) {
                                                    snackBarHost.showSnackbar(
                                                        context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                            R.string.vital_signal_limit_of_blood_glucose
                                                        ),
                                                        duration = SnackbarDuration.Short
                                                    )
                                                }
                                            }
                                        } else {
                                            if (effectiveNumber in LOWER_BLOOD_GLUCOSE_MGDL..UPPER_BLOOD_GLUCOSE_MGDL) {
                                                if (isMmolL.value) {
                                                    postVitalSignData(((effectiveNumber * 10).roundToInt() / 10.0).toString())
                                                } else {
                                                    postVitalSignData("%.1f".format(
                                                        glucoseMgDlToMmolL(((effectiveNumber * 10).roundToInt() / 10.0))
                                                    ))
                                                }
                                                onDismissEvent(true)
                                            } else {
                                                coroutineScope.launch(Dispatchers.Main) {
                                                    snackBarHost.showSnackbar(
                                                        context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                            R.string.vital_signal_limit_of_blood_glucose
                                                        ),
                                                        duration = SnackbarDuration.Short
                                                    )
                                                }
                                            }
                                        }
                                    } else {
                                        coroutineScope.launch(Dispatchers.Main) {
                                            snackBarHost.showSnackbar(
                                                context.resources.getString(R.string.vital_signal_limit_wanning) + "\n" + context.resources.getString(
                                                    R.string.vital_signal_limit_of_blood_glucose
                                                ),
                                                duration = SnackbarDuration.Short
                                            )
                                        }
                                    }
                                    setTempVitalSignForPost("")
                                },
                                onChangeMmolLStateEvent = onChangeMmolLStateEvent,
                            )
                        }
                    }
                }
            }
        }
    }
}

/**
 *  上传体温数据内容输入组件
 *  @param  modifier    Compose可组合项修饰符
 *  @param  isCelsiusState  当前是否采用摄氏度
 *  @param  textValue   文本内容
 *  @param  setTextValue    修改文本内容事件
 *  @param  onConfirmEvent  确认按键点击事件
 *  @param  onChangeCelsiusStateEvent   变更当前体温测量单位事件
 */

@Composable
fun BodyTemperaturePostComponent(
    modifier: Modifier = Modifier,
    isCelsiusState: State<Boolean>,
    textValue: String,
    setTextValue: (String) -> Unit,
    onConfirmEvent: () -> Unit,
    onChangeCelsiusStateEvent: () -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        /* 切换温度单位 */
        Row(
            modifier = Modifier.fillMaxWidth(.9f),
            horizontalArrangement = Arrangement.End
        ) {
            Card(
                Modifier.height(26.dp),
                shape = RoundedCornerShape(size = 12.dp),
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .clickable { onChangeCelsiusStateEvent() },
                    contentAlignment = Center,
                ) {
                    Text(
                        text = stringResource(id = R.string.change_to) + (if (isCelsiusState.value) "℉" else "℃"),
                        color = Color.Black.copy(0.3f),
                        fontWeight = FontWeight.Light,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(horizontal = 10.dp),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = CenterVertically
        ) {
            /* 输入文本框 */
            TextField(
                value = textValue,
                onValueChange = setTextValue,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
                modifier = Modifier.width(100.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.Black,
                    disabledTextColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color(0xFF244CD2),
                    unfocusedBorderColor = Color(0xFF999999),
                    disabledBorderColor = Color.Transparent,
                ),
                keyboardActions = KeyboardActions(onDone = { keyboardController?.hide() }),
                singleLine = true,
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = if (isCelsiusState.value) "℃" else "℉",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
        Spacer(modifier = modifier.height(38.dp))
        OutlinedButton(
            onClick = {
                keyboardController?.hide()
                onConfirmEvent()
            },
            shape = RoundedCornerShape(21.dp),
            colors = ButtonDefaults.outlinedButtonColors(containerColor = Color(0xFF2A4AD3)),
            modifier = Modifier
                .align(CenterHorizontally)
                .fillMaxWidth(0.8f),
        ) {
            Text(
                text = stringResource(id = R.string.confirm),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Light,
                modifier = Modifier
                    .weight(1f)
                    .align(CenterVertically)
            )
        }
    }
}

/**
 *  上传心率数据内容输入组件
 *  @param  modifier    Compose可组合项修饰符
 *  @param  textValue   文本内容
 *  @param  setTextValue    修改文本内容事件
 *  @param  onConfirmEvent  确认按键点击事件
 */
@Preview()
@Composable
fun HeartRatePostComponent(
    modifier: Modifier = Modifier,
    textValue: String = "",
    setTextValue: (String) -> Unit = {},
    onConfirmEvent: () -> Unit = {}
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = CenterVertically
        ) {
            TextField(
                value = textValue,
                onValueChange = setTextValue,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number,imeAction = ImeAction.Done),
                modifier = Modifier.width(100.dp),
                colors = OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.Black,
                    disabledTextColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color(0xFF244CD2),
                    unfocusedBorderColor = Color(0xFF999999),
                    disabledBorderColor = Color.Transparent,
                ),
                keyboardActions = KeyboardActions(onDone = { keyboardController?.hide() }),
                singleLine = true,
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = "bpm",
                textAlign = TextAlign.Center,
                fontSize = 22.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
        Spacer(modifier = modifier.height(38.dp))
        OutlinedButton(
            onClick = {
                keyboardController?.hide()
                onConfirmEvent()
            },
            shape = RoundedCornerShape(21.dp),
            colors = ButtonDefaults.outlinedButtonColors(containerColor = Color(0xFF2A4AD3)),
            modifier = Modifier
                .align(CenterHorizontally)
                .fillMaxWidth(0.8f),
        ) {
            Text(
                text = stringResource(id = R.string.confirm),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Light,
                modifier = Modifier
                    .weight(1f)
                    .align(CenterVertically)
            )
        }
    }
}

/**
 *  上传血氧数据内容输入组件
 *  @param  modifier    Compose可组合项修饰符
 *  @param  textValue   文本内容
 *  @param  setTextValue    修改文本内容事件
 *  @param  onConfirmEvent  确认按键点击事件
 */
@Composable
fun BloodOxygenPostComponent(
    modifier: Modifier = Modifier,
    textValue: String,
    setTextValue: (String) -> Unit,
    onConfirmEvent: () -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = CenterVertically
        ) {
            TextField(
                value = textValue,
                onValueChange = setTextValue,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number,imeAction = ImeAction.Done),
                modifier = Modifier.width(100.dp),
                colors = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.Black,
                    disabledTextColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color(0xFF244CD2),
                    unfocusedBorderColor = Color(0xFF999999),
                    disabledBorderColor = Color.Transparent,
                ),
                keyboardActions = KeyboardActions(onDone = { keyboardController?.hide() }),
                singleLine = true,
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = "%",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
        Spacer(modifier = modifier.height(38.dp))
        OutlinedButton(
            onClick = {
                keyboardController?.hide()
                onConfirmEvent()
            },
            shape = RoundedCornerShape(21.dp),
            colors = ButtonDefaults.outlinedButtonColors(containerColor = Color(0xFF2A4AD3)),
            modifier = Modifier
                .align(CenterHorizontally)
                .fillMaxWidth(0.8f),
        ) {
            Text(
                text = stringResource(id = R.string.confirm),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Light,
                modifier = Modifier
                    .weight(1f)
                    .align(CenterVertically)
            )
        }
    }
}

/**
 *  上传血糖数据内容输入组件
 *  @param  modifier    Compose可组合项修饰符
 *  @param  textValue   文本内容
 *  @param  setTextValue    修改文本内容事件
 *  @param  onConfirmEvent  确认按键点击事件
 */
@Composable
fun BloodGlucosePostComponent(
    modifier: Modifier = Modifier,
    isMmolL: State<Boolean>,
    textValue: String,
    setTextValue: (String) -> Unit,
    onConfirmEvent: () -> Unit,
    onChangeMmolLStateEvent: () -> Unit,
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        /* 切换血糖单位 */
        Row(
            modifier = Modifier.fillMaxWidth(.9f),
            horizontalArrangement = Arrangement.End
        ) {
            Card(
                Modifier.height(26.dp),
                shape = RoundedCornerShape(size = 12.dp),
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .clickable { onChangeMmolLStateEvent() },
                    contentAlignment = Center,
                ) {
                    Text(
                        text = stringResource(id = R.string.change_to) + (if (isMmolL.value) "mg/dL" else "mmol/L"),
                        color = Color.Black.copy(0.3f),
                        fontWeight = FontWeight.Light,
                        fontSize = 12.sp,
                        modifier = Modifier.padding(horizontal = 10.dp),
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
        Row(
            horizontalArrangement = Arrangement.Center,
            verticalAlignment = CenterVertically
        ) {
            TextField(
                value = textValue,
                onValueChange = setTextValue,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number,imeAction = ImeAction.Done),
                modifier = Modifier.width(100.dp),
                colors = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.Black,
                    disabledTextColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color(0xFF244CD2),
                    unfocusedBorderColor = Color(0xFF999999),
                    disabledBorderColor = Color.Transparent,
                ),
                keyboardActions = KeyboardActions(onDone = { keyboardController?.hide() }),
                singleLine = true,
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = if (isMmolL.value) "mmol/L" else "mg/dL",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
        Spacer(modifier = modifier.height(38.dp))
        OutlinedButton(
            onClick = {
                keyboardController?.hide()
                onConfirmEvent()
            },
            shape = RoundedCornerShape(21.dp),
            colors = ButtonDefaults.outlinedButtonColors(containerColor = Color(0xFF2A4AD3)),
            modifier = Modifier
                .align(CenterHorizontally)
                .fillMaxWidth(0.8f),
        ) {
            Text(
                text = stringResource(id = R.string.confirm),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Light,
                modifier = Modifier
                    .weight(1f)
                    .align(CenterVertically)
            )
        }
    }
}

/**
 *  上传血压数据内容输入组件
 *  @param  modifier    Compose可组合项修饰符
 *  @param  diastolicTextValue   舒张压文本内容
 *  @param  setDiastolicTextValue    修改舒张压文本内容事件
 *  @param  systolicTextValue   收缩压文本内容
 *  @param  setSystolicTextValue    修改收缩压文本内容事件
 *  @param  onConfirmEvent  确认按键点击事件
 */
@Composable
fun BloodPressurePostComponent(
    modifier: Modifier = Modifier,
    diastolicTextValue: String,
    systolicTextValue: String,
    setDiastolicTextValue: (String) -> Unit,
    setSystolicTextValue: (String) -> Unit,
    onConfirmEvent: () -> Unit
) {
    val keyboardController = LocalSoftwareKeyboardController.current
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Row(modifier = Modifier.padding(vertical = 3.dp), horizontalArrangement = Arrangement.Center, verticalAlignment = CenterVertically) {
            TextField(
                value = diastolicTextValue,
                onValueChange = { setDiastolicTextValue(it) },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number,imeAction = ImeAction.Done),
                modifier = Modifier.width(100.dp),
                colors = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.Black,
                    disabledTextColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color(0xFF244CD2),
                    unfocusedBorderColor = Color(0xFF999999),
                    disabledBorderColor = Color.Transparent,
                ),
                keyboardActions = KeyboardActions(onDone = { keyboardController?.hide() }),
                placeholder = {
                    Text(stringResource(id = R.string.diastolic), color = Color(0xFF999999), maxLines = 1)
                }
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = "mmhg",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
        Row(modifier = Modifier.padding(vertical = 3.dp), horizontalArrangement = Arrangement.Center, verticalAlignment = CenterVertically) {
            TextField(
                value = systolicTextValue,
                onValueChange = { setSystolicTextValue(it) },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                modifier = Modifier.width(100.dp),
                colors = androidx.compose.material3.OutlinedTextFieldDefaults.colors(
                    focusedTextColor = Color.Black,
                    disabledTextColor = Color.Transparent,
                    focusedContainerColor = Color.Transparent,
                    focusedBorderColor = Color(0xFF244CD2),
                    unfocusedBorderColor = Color(0xFF999999),
                    disabledBorderColor = Color.Transparent,
                ),
                singleLine = true,
                placeholder = {
                    Text(stringResource(id = R.string.systolic), color = Color(0xFF999999), maxLines = 1)
                }
            )
            Spacer(modifier = Modifier.width(6.dp))
            Text(
                text = "mmhg",
                textAlign = TextAlign.Center,
                fontSize = 20.sp,
                color = Color(0xFF244CD2),
                fontWeight = FontWeight.Medium,
            )
        }
        Spacer(modifier = modifier.height(38.dp))
        OutlinedButton(
            onClick = {
                keyboardController?.hide()
                onConfirmEvent()
            },
            shape = RoundedCornerShape(21.dp),
            colors = ButtonDefaults.outlinedButtonColors(containerColor = Color(0xFF2A4AD3)),
            modifier = Modifier
                .align(CenterHorizontally)
                .fillMaxWidth(0.8f),
        ) {
            Text(
                text = stringResource(id = R.string.confirm),
                color = Color.White,
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Light,
                modifier = Modifier
                    .weight(1f)
                    .align(CenterVertically)
            )
        }
    }

}


/**
 *  生命体征数据上传窗口状态枚举类型
 *  @property   NONE    不显示
 *  @property   VISIBLE_HEART_RATE  显示上传心率数据底栏
 *  @property   VISIBLE_BODY_TEMPERATURE    显示上传体温数据底栏
 *  @property   VISIBLE_BLOOD_OXYGEN    显示上传血氧数据底栏
 *  @property   VISIBLE_BLOOD_PRESSURE  示上传血压数据底栏
 *  @property   VISIBLE_BLOOD_GLUCOSE   显示上传血糖数据底栏
 */
enum class VitalSignPostDialogVisibleState {
    NONE,
    VISIBLE_HEART_RATE,
    VISIBLE_BODY_TEMPERATURE,
    VISIBLE_BLOOD_OXYGEN,
    VISIBLE_BLOOD_PRESSURE,
    VISIBLE_BLOOD_GLUCOSE
}

private fun String.isFloat(): Boolean {
    return try {
        this.toFloat()
        true
    } catch (e: NumberFormatException) {
        false
    }
}

