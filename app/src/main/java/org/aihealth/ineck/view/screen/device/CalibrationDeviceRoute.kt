package org.aihealth.ineck.view.screen.device

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.delay
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.device.DeviceViewModel
import org.aihealth.ineck.bluetooth.ConnectionState

@Composable
fun CalibrationDeviceRoute(
    deviceType: DeviceType = DeviceType.aiNeck,
) {
    val viewModel = ViewModelProvider(activity)[DeviceViewModel::class.java]
    var isStart by remember {
        mutableStateOf(false)
    }
    val isFirstCalibrated = when (deviceType) {
        DeviceType.aiNeck -> viewModel.neckIsFirstCalibrated.collectAsStateWithLifecycle()
        DeviceType.aiBack -> viewModel.backIsFirstCalibrated.collectAsStateWithLifecycle()
        else -> viewModel.neckIsFirstCalibrated.collectAsStateWithLifecycle()
    }
    val isSecondCalibratedSuccess = when (deviceType) {
        DeviceType.aiNeck -> viewModel.neckIsSecondCalibrated.collectAsStateWithLifecycle()
        DeviceType.aiBack -> viewModel.backIsSecondCalibrated.collectAsStateWithLifecycle()
        else -> viewModel.neckIsSecondCalibrated.collectAsStateWithLifecycle()
    }
    var calibrationType by remember {
        mutableStateOf(CalibrationType.HeadUp)
    }
    var isCalibrationSuccess by remember {
        mutableStateOf(false)
    }
    var showFailureDialog by remember {
        mutableStateOf(false)
    }
    var failureMessage by remember {
        mutableStateOf("")
    }
    var showDisconnectDialog by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(isStart, calibrationType) {
        if (isStart) {
            if (calibrationType == CalibrationType.HeadUp) {
                calibrationType = if (viewModel.firstCalibrating(deviceType.name)) {
                    CalibrationType.BendNeck
                } else {
                    CalibrationType.HeadUpFailed
                }
            } else if (calibrationType == CalibrationType.BendNeck) {
                if (viewModel.secondCalibrating(deviceType.name)) {
                    isCalibrationSuccess = true
                } else {
                    calibrationType = CalibrationType.BendNeckFailed
                }
            }
        }
    }

    // 获取失败消息的字符串资源
    val firstFailureMessage = when (deviceType) {
        DeviceType.aiNeck -> stringResource(R.string.calibration_first_failed_aineck)
        DeviceType.aiBack -> stringResource(R.string.calibration_first_failed_aiback)
        DeviceType.KneeJoint -> stringResource(R.string.calibration_first_failed_knee)
        DeviceType.ShoulderJoint -> stringResource(R.string.calibration_first_failed_shoulder)
        DeviceType.ElbowJoint -> stringResource(R.string.calibration_first_failed_elbow)
        DeviceType.HipJoint -> stringResource(R.string.calibration_first_failed_hip)
        else -> stringResource(R.string.calibration_first_failed_aineck)
    }
    
    val secondFailureMessage = when (deviceType) {
        DeviceType.aiNeck -> stringResource(R.string.calibration_second_failed_aineck)
        DeviceType.aiBack -> stringResource(R.string.calibration_second_failed_aiback)
        DeviceType.KneeJoint -> stringResource(R.string.calibration_second_failed_knee)
        DeviceType.ShoulderJoint -> stringResource(R.string.calibration_second_failed_shoulder)
        DeviceType.ElbowJoint -> stringResource(R.string.calibration_second_failed_elbow)
        DeviceType.HipJoint -> stringResource(R.string.calibration_second_failed_hip)
        else -> stringResource(R.string.calibration_second_failed_aineck)
    }

    // 监听校准失败状态，显示弹窗
    LaunchedEffect(calibrationType) {
        when (calibrationType) {
            CalibrationType.HeadUpFailed -> {
                failureMessage = firstFailureMessage
                showFailureDialog = true
            }
            CalibrationType.BendNeckFailed -> {
                failureMessage = secondFailureMessage
                showFailureDialog = true
            }
            else -> {
                showFailureDialog = false
            }
        }
    }

    LaunchedEffect(isCalibrationSuccess) {
        if (isCalibrationSuccess){
            popScreen(Screen.Main.route)
        }
    }

    // 监听设备连接状态
    val deviceConnectionState = when (deviceType) {
        DeviceType.aiNeck -> viewModel.neckConnectionState.collectAsStateWithLifecycle()
        DeviceType.aiBack -> viewModel.backConnectionState.collectAsStateWithLifecycle()
        else -> viewModel.neckConnectionState.collectAsStateWithLifecycle()
    }

    // 监听设备连接状态变化
    LaunchedEffect(deviceConnectionState.value, isStart) {
        // 只有在校准开始后才监听断开状态，避免页面初始化时误触发
        if (isStart) {
            deviceConnectionState.value?.let { state ->
                when (state) {
                    is ConnectionState.Disconnected,
                    is ConnectionState.Failed -> {
                        // 设备断开连接，显示断开提示弹窗
                        if (!showDisconnectDialog && !showFailureDialog && !isCalibrationSuccess) {
                            showDisconnectDialog = true
                        }
                    }
                    else -> {
                        // 其他状态不处理
                    }
                }
            }
        }
    }

    // 校准失败弹窗
    if (showFailureDialog) {
        AlertDialog(
            onDismissRequest = { showFailureDialog = false },
            title = {
                Text(
                    text = stringResource(R.string.calibration_failed_title),
                    fontSize = 18.sp,
                    color = Color.Black
                )
            },
            text = {
                Text(
                    text = failureMessage,
                    fontSize = 16.sp,
                    color = Color(0xFF666666)
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showFailureDialog = false
                        // 重置校准状态，重新开始校准
                        viewModel.resetCalibrationState(deviceType.name)
                        isStart = false
                        calibrationType = CalibrationType.HeadUp
                    }
                ) {
                    Text(
                        text = stringResource(R.string.calibration_failed_retry),
                        color = Color(0xFF007AFF)
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showFailureDialog = false
                        // 返回上一页
                        finish()
                    }
                ) {
                    Text(
                        text = stringResource(R.string.calibration_failed_cancel),
                        color = Color(0xFF999999)
                    )
                }
            }
        )
    }

    // 设备断开连接弹窗
    if (showDisconnectDialog) {
        AlertDialog(
            onDismissRequest = { 
                // 不允许通过点击外部关闭弹窗
            },
            title = {
                Text(
                    text = stringResource(R.string.device_disconnect, 
                        when (deviceType) {
                            DeviceType.aiNeck -> stringResource(R.string.cervical_vertebra_monitoring)
                            DeviceType.aiBack -> stringResource(R.string.lumbar_monitoring)
                            DeviceType.KneeJoint -> stringResource(R.string.joint_knee)
                            DeviceType.ShoulderJoint -> stringResource(R.string.joint_shoulder)
                            DeviceType.ElbowJoint -> stringResource(R.string.joint_elbow)
                            DeviceType.HipJoint -> stringResource(R.string.joint_hip)
                            else -> stringResource(R.string.device)
                        }
                    ),
                    fontSize = 18.sp,
                    color = Color.Black
                )
            },
            text = {
                Text(
                    text = stringResource(R.string.device_calibration_disconnect_message),
                    fontSize = 16.sp,
                    color = Color(0xFF666666)
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDisconnectDialog = false
                        // 结束当前页面
                        finish()
                    }
                ) {
                    Text(
                        text = stringResource(R.string.confirm),
                        color = Color(0xFF007AFF)
                    )
                }
            }
        )
    }

    CalibrationDeviceScreen(
        deviceType = deviceType,
        calibrationType = calibrationType,
        isStart = isStart,
        isSecondCalibratedSuccess = isSecondCalibratedSuccess.value,
        onStartCalibration = {
            if (!isStart) {
                isStart = true
            } else if (calibrationType == CalibrationType.HeadUpFailed) {
                calibrationType = CalibrationType.HeadUp
            } else if (calibrationType == CalibrationType.BendNeckFailed) {
                calibrationType = CalibrationType.BendNeck
            }
        },
    )
}

@Composable
private fun CalibrationDeviceScreen(
    deviceType: DeviceType = DeviceType.aiNeck,
    calibrationType: CalibrationType = CalibrationType.HeadUp,
    isStart: Boolean = false,
    isSecondCalibratedSuccess: Boolean,
    onStartCalibration: () -> Unit,
) {
    val imageUpRes = when (deviceType) {
        DeviceType.aiNeck -> R.drawable.img_calibration_headup_aineck
        DeviceType.aiBack -> R.drawable.img_calibration_headup_aiback
        DeviceType.KneeJoint -> R.drawable.knee_0
        DeviceType.ShoulderJoint -> R.drawable.shoulder_0
        DeviceType.ElbowJoint -> R.drawable.elbow_0
        DeviceType.HipJoint -> R.drawable.hip_0
        else -> R.drawable.img_calibration_headup_aineck
    }
    val imageDownRes = when (deviceType) {
        DeviceType.aiNeck -> calibrationType.aiNeckDrawable
        DeviceType.aiBack -> calibrationType.aiBackDrawable
        DeviceType.KneeJoint -> calibrationType.aiKneeDrawable
        DeviceType.ShoulderJoint -> calibrationType.aiShoulderDrawable
        DeviceType.ElbowJoint -> calibrationType.aiElbowDrawable
        DeviceType.HipJoint -> calibrationType.aiHipDrawable
        else -> calibrationType.aiNeckDrawable
    }
    var tipIndex by remember {
        mutableIntStateOf(0)
    }
    BasePageView(
        title = stringResource(id = R.string.device_calibration),
        showBackIcon = true
    ) {
        LazyColumn(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
            if (!isStart) {
                item {
                    val speechText =
                        stringResource(id = R.string.device_calibration_unstart_tip)
                    DisposableEffect(Unit) {
                        TextToSpeech.ttsSpeaking(speechText)
                        onDispose {
                            TextToSpeech.ttsStop()
                        }
                    }
                    Text(
                        text = stringResource(id = R.string.device_calibration_unstart),
                        fontSize = 24.sp,
                        color = Color.Black,
                        modifier = Modifier
                            .padding(vertical = 28.dp, horizontal = 20.dp)
                    )
                    Image(
                        painter = painterResource(id = imageUpRes),
                        contentDescription = null,
                        modifier = Modifier.size(318.dp)
                    )
                    Spacer(modifier = Modifier.height(23.dp))
                    Text(
                        text = speechText,
                        fontSize = 16.sp,
                        color = Color(0XFF444444),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                    )
                }
            } else {
                item {
                    val firstTipList = when (deviceType) {
                        DeviceType.aiNeck -> stringArrayResource(id = R.array.calibration_first_tip_aineck)
                        DeviceType.aiBack -> stringArrayResource(id = R.array.calibration_first_tip_aiback)
                        DeviceType.ElbowJoint -> stringArrayResource(id = R.array.calibration_first_tip_elbow_joint)
                        DeviceType.KneeJoint -> stringArrayResource(id = R.array.calibration_first_tip_knee_joint)
                        DeviceType.HipJoint -> stringArrayResource(id = R.array.calibration_first_tip_hip_joint)
                        DeviceType.ShoulderJoint -> stringArrayResource(id = R.array.calibration_first_tip_shoulder_joint)
                        else -> stringArrayResource(id = R.array.calibration_first_tip_aineck)
                    }
                    val secondTipList = when (deviceType) {
                        DeviceType.aiNeck -> stringArrayResource(id = R.array.calibration_second_tip_aineck)
                        DeviceType.aiBack -> stringArrayResource(id = R.array.calibration_second_tip_aiback)
                        DeviceType.ElbowJoint -> stringArrayResource(id = R.array.calibration_second_tip_elbow_joint)
                        DeviceType.KneeJoint -> stringArrayResource(id = R.array.calibration_second_tip_knee_joint)
                        DeviceType.HipJoint -> stringArrayResource(id = R.array.calibration_second_tip_hip_joint)
                        DeviceType.ShoulderJoint -> stringArrayResource(id = R.array.calibration_second_tip_shoulder_joint)
                        else -> stringArrayResource(id = R.array.calibration_second_tip_aineck)
                    }
                    val tip by remember {
                        derivedStateOf {
                            when (calibrationType) {
                                CalibrationType.HeadUp -> firstTipList[tipIndex % 3]
                                CalibrationType.BendNeck -> if (!isSecondCalibratedSuccess) {
                                    secondTipList[tipIndex % 3]
                                } else {
                                    tipIndex = 0
                                    secondTipList[tipIndex]
                                }

                                else -> ""
                            }
                        }
                    }
                    LaunchedEffect(Unit) {
                        while (true) {
                            delay(3000)
                            tipIndex++
                        }
                    }
                    var firstSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    var secondSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    LaunchedEffect(tip) {
                        if (tip == firstTipList[0] && firstSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            firstSpeechEnabled = false
                        }
                        if (tip == secondTipList[0] && secondSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            secondSpeechEnabled = false
                        }
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 43.dp)
                            .padding(horizontal = 16.dp),
                    ) {
                        Text(
                            text = tip,
                            fontSize = 24.sp,
                            color = if (calibrationType == CalibrationType.HeadUp || calibrationType == CalibrationType.BendNeck) Color.Black else Color(
                                0XFFCC4141
                            ),
                            textAlign = TextAlign.Start,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    Image(
                        painter = painterResource(id = imageDownRes),
                        contentDescription = null,
                        modifier = Modifier.size(318.dp)
                    )
                }
            }
            item {
                Spacer(modifier = Modifier.height(50.dp))
                AIHButton(
                    text = stringResource(
                        id = if (!isStart) {
                            R.string.device_start_calibration
                        } else if (calibrationType == CalibrationType.HeadUp || calibrationType == CalibrationType.BendNeck) {
                            R.string.device_calibrating
                        } else {
                            R.string.device_recalibrate
                        }
                    ),
                    onClick = {
                        onStartCalibration()
                    },
                    enabled = !isStart || (calibrationType == CalibrationType.HeadUpFailed || calibrationType == CalibrationType.BendNeckFailed),
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                )
                Spacer(modifier = Modifier.height(50.dp))
            }

        }
    }
}

/**
 * 设备校准过程中的类别
 */
private enum class CalibrationType(
    @DrawableRes val aiNeckDrawable: Int = 0,
    @DrawableRes val aiBackDrawable: Int = 0,
    @DrawableRes val aiKneeDrawable: Int = 0,
    @DrawableRes val aiShoulderDrawable: Int = 0,
    @DrawableRes val aiElbowDrawable: Int = 0,
    @DrawableRes val aiHipDrawable: Int = 0,
) {
    /**
     * 抬头状态
     */
    HeadUp(
        R.drawable.img_calibration_headup_aineck,
        R.drawable.img_calibration_headup_aiback,
        R.drawable.knee_0,
        R.drawable.shoulder_0,
        R.drawable.elbow_0,
        R.drawable.hip_0
    ),

    /**
     * 弯曲颈部状态
     */
    BendNeck(
        R.drawable.img_calibration_bendneck,
        R.drawable.img_calibration_bendback,
        R.drawable.knee_80,
        R.drawable.shoulder_90,
        R.drawable.elbow_80,
        R.drawable.hip_90
    ),

    /**
     * 抬头校准失败
     */
    HeadUpFailed(
        R.drawable.img_calibration_headup_aineck,
        R.drawable.img_calibration_headup_aiback,
        R.drawable.knee_unconnect,
        R.drawable.shoulder_unconnect,
        R.drawable.elbow_unconnect,
        R.drawable.hip_unconnect

    ),

    /**
     * 弯曲颈部校准失败
     */
    BendNeckFailed(
        R.drawable.img_calibration_headup_aineck,
        R.drawable.img_calibration_headup_aiback,
        R.drawable.knee_unconnect,
        R.drawable.shoulder_unconnect,
        R.drawable.elbow_unconnect,
        R.drawable.hip_unconnect
    )

}

/**
 * Knee 设备校准状态
 */
private enum class CalibrationKneeType(
    @DrawableRes val kneeDrawable: Int = 0,
) {
    InitialState(
        R.drawable.knee_0
    ),
    StretchState(
        R.drawable.knee_80
    ),
    BucklingState(
        R.drawable.knee_p15
    ),
    FailedState(
        R.drawable.knee_unconnect
    )
}
