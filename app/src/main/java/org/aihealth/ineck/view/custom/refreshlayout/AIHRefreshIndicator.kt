package org.aihealth.ineck.view.custom.refreshlayout

import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import org.aihealth.ineck.R

@Composable
fun AIHRefreshIndicator(
    state: AIHRefreshState,
    fontColor: Color = Color(0XFF333333),
    iconColor: Color = Color(0XFF838383)
) {
    val tip by rememberUpdatedState(
        when(state.refreshType) {
            AIHRefreshType.PULL -> stringResource(id = R.string.pull_to_refresh)
            AIHRefreshType.REFRESHING -> stringResource(id = R.string.refreshing)
            AIHRefreshType.RELEASE -> stringResource(id = R.string.release_to_refresh)
            AIHRefreshType.FINISHED -> stringResource(id = R.string.refresh_completed)
        }
    )
    val angle = animateFloatAsState(targetValue = if (state.refreshType == AIHRefreshType.RELEASE) 180F else 0F,
        label = ""
    )
    val density = LocalDensity.current
    state.indicatorHeight = with(density) {50.dp.toPx()}
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(50.dp),
        contentAlignment = Alignment.Center
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            when(state.refreshType) {
                AIHRefreshType.REFRESHING -> {
                    CircularProgressIndicator(strokeWidth = 2.dp, modifier = Modifier.size(20.dp), color = iconColor)
                }
                AIHRefreshType.FINISHED -> {

                }
                else -> {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier
                            .size(20.dp)
                            .drawBehind {
                                drawArc(
                                    color = iconColor,
                                    startAngle = -90F,
                                    sweepAngle = state.offset * 360 / state.indicatorHeight,
                                    useCenter = false,
                                    style = Stroke(
                                        width = with(density) { 2.dp.toPx() },
                                        cap = StrokeCap.Round
                                    )
                                )
                            }
                    ){
                        Icon(
                            painter = painterResource(id = R.drawable.img_down_arrow),
                            contentDescription = null,
                            modifier = Modifier
                                .size(16.dp)
                                .rotate(angle.value),
                            tint = iconColor
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.width(10.dp))
            Text(text = tip,color = fontColor)
        }

    }
}