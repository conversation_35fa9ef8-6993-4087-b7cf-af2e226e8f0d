package org.aihealth.ineck.view.dialog

import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import org.aihealth.ineck.R

@Composable
fun LoadingDialog() {

    Surface(
        modifier = Modifier.fillMaxSize(),
        color = Color.Transparent
    ) {
        val animateValue by rememberInfiniteTransition(label = "").animateFloat(
            initialValue = 0F,
            targetValue = 360F,
            animationSpec = infiniteRepeatable(animation = tween(durationMillis = 3000)), label = ""
        )
        Box(modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) { detectTapGestures { } }){
            Box(
                modifier = Modifier
                    .size(100.dp)
                    .background(Color(0x37000000), RoundedCornerShape(12.dp))
                    .align(Alignment.Center),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.img_loading),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(50.dp)
                        .rotate(animateValue)
                )
            }
        }
    }
}

@Composable
fun LoadingDialog(modifier: Modifier) {

    Surface(
        modifier = modifier,
        color = Color.Transparent
    ) {
        val animateValue by rememberInfiniteTransition(label = "").animateFloat(
            initialValue = 0F,
            targetValue = 360F,
            animationSpec = infiniteRepeatable(animation = tween(durationMillis = 3000)), label = ""
        )
        Box(modifier = Modifier
            .size(100.dp)
            .pointerInput(Unit) { detectTapGestures { } }){
            Box(
                modifier = Modifier
                    .size(100.dp)
                    .background(Color(0x37000000), RoundedCornerShape(12.dp))
                    .align(Alignment.Center),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.img_loading),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(50.dp)
                        .rotate(animateValue)
                )
            }
        }
    }
}