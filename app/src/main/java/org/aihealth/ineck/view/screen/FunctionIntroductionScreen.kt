package org.aihealth.ineck.view.screen

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView

@Composable
fun FunctionIntroductionScreen() {
    BasePageView(
        showBackIcon = true,
        headerContent = {
            Column(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(0.8f),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    modifier = Modifier,
                    text = stringResource(id = R.string.function_introduction),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )

            }
        }
    ) {
        Column(Modifier.fillMaxSize()) {
            Item(title = stringResource(R.string.agreement_update_1), date = "2022-06-27")
            Item(title = stringResource(R.string.aih_update_1), date = "2022-06-27")
        }
    }
}

@Composable
private fun Item(
    title: String,
    date: String,
    onClick: () -> Unit = {}
) {
    Column(
        Modifier
            .fillMaxWidth()
            .heightIn(60.dp)
            .clickable {
                onClick()
            }
            .padding(horizontal = 20.dp), verticalArrangement = Arrangement.Center) {
        Text(text = title, fontSize = 16.sp, color = Color(0XFF333333), fontWeight = FontWeight.Bold)
        Spacer(modifier = Modifier.height(7.dp))
        Text(text = date, fontSize = 12.sp, color = Color(0XFF666666))
    }
    AIHDivider()
}