package org.aihealth.ineck.view.screen.home.aijoint

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.button.RecalibrateButton
import org.aihealth.ineck.view.button.TypeSelectButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiNeck
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiNeckSmartVersion
import org.aihealth.ineck.view.screen.TopTipsBlockOfConnectionStatusCard
import org.aihealth.ineck.view.screen.home.AboutDevice
import org.aihealth.ineck.view.screen.home.AngleStatue
import org.aihealth.ineck.view.screen.home.HomeStatusCard
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun AiJoinShoulderHomeModule(
    modifier: Modifier = Modifier,
    viewModel: MainViewModel,
) {
    val scope = rememberCoroutineScope()
    // 当前设备是否连接
    var isConnected by remember {
        mutableStateOf(false)
    }
    // 设备角度
    val deviceAngle = remember { Animatable(0f) }
    var maxAngle by remember { mutableIntStateOf(0) }

    // 设备电量
    var power by remember {
        mutableIntStateOf(0)
    }
    val imgRes = remember {
        mutableIntStateOf(R.drawable.shoulder_unconnect)
    }
    LaunchedEffect(isConnected) {
        while (isConnected) {
            delay(250)
            val angle = deviceAngle.value.toInt()
            when (angle) {
                in 0..10 -> {
                    imgRes.intValue = R.drawable.shoulder_0
                }

                in 11..20 -> {
                    imgRes.intValue = R.drawable.shoulder_10
                }

                in 21..30 -> {
                    imgRes.intValue = R.drawable.shoulder_20
                }

                in 31..40 -> {
                    imgRes.intValue = R.drawable.shoulder_30
                }

                in 41..50 -> {
                    imgRes.intValue = R.drawable.shoulder_40
                }

                in 51..60 -> {
                    imgRes.intValue = R.drawable.shoulder_50
                }

                in 61..70 -> {
                    imgRes.intValue = R.drawable.shoulder_60
                }

                in 71..80 -> {
                    imgRes.intValue = R.drawable.shoulder_70
                }

                in 81..100 -> {
                    imgRes.intValue = R.drawable.shoulder_90
                }

                in 101..110 -> {
                    imgRes.intValue = R.drawable.shoulder_100
                }

                in 111..120 -> {
                    imgRes.intValue = R.drawable.shoulder_110
                }

                in 121..130 -> {
                    imgRes.intValue = R.drawable.shoulder_120
                }

                in 130..160 -> {
                    imgRes.intValue = R.drawable.shoulder_150
                }

                in 161..170 -> {
                    imgRes.intValue = R.drawable.shoulder_160
                }

                in 171..180 -> {
                    imgRes.intValue = R.drawable.shoulder_170
                }

                else -> {
                    imgRes.intValue = R.drawable.shoulder_0
                }
            }
        }
        imgRes.intValue = R.drawable.shoulder_unconnect
    }
    HomeStatusCard(
        modifier = modifier.fillMaxWidth()
    ) {
        TopTipsBlockOfConnectionStatusCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            if (isConnected) {
                GuideToNeckImproveOfAiNeck()
            } else {
                GuideToNeckImproveOfAiNeckSmartVersion()
            }
        }
        // 分割线
        AIHDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )
        Box(
            modifier = Modifier
                .padding(top = 5.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            Column(
                modifier = Modifier.align(Center)
            ) {
                Image(
                    painter = painterResource(id = imgRes.intValue),
                    contentDescription = "",
                    contentScale = ContentScale.Fit,
                    modifier = Modifier.size(200.dp)
                )

            }
            if (isConnected) {
                /* 重新校准Button位置 */
                RecalibrateButton(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(vertical = 5.dp)
                        .fillMaxWidth()
                        .wrapContentHeight(),
                    onClick = { startScreen(Screen.CalibrationDevice.route) }
                )
                AngleStatue(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 16.dp, bottom = 16.dp),
                    angle = deviceAngle.value.toInt()
                )
            }
        }
        if (isConnected) {
            AboutDevice(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 5.dp),
                power = power,
                maxAngle = maxAngle
            )
            TypeSelectButton(
                selectedIndex = if (viewModel.deviceScreen.shoulderJointDeviceConfig.isVibration) 0 else 1,
                array = stringArrayResource(id = R.array.device_mode),
                onClick = {
                    viewModel.deviceScreen.setVibration(it == 0)
                },
                modifier = Modifier
                    .padding(horizontal = 20.dp, vertical = 5.dp)
                    .size(220.dp, 38.dp)
                    .align(CenterHorizontally)
            )
        }

    }
    // 获取设备状态
    LaunchedEffect(Unit) {
        viewModel.deviceScreen.shoulderDeviceState.collect {
            isConnected = it.isDeviceConnected ?: false
            it.angle?.let { angle ->
                deviceAngle.animateTo(angle.toFloat(), tween(250))
            } ?: 0
            power = it.power ?: 0
            maxAngle = it.maxAngle ?: 0
        }
    }
// 上传数据到服务器
    DisposableEffect(Unit) {
        var currentTime = System.currentTimeMillis()
        if (currentTime - viewModel.homeScreen.lastUpdateTime >= 1000 * 10) {
            viewModel.homeScreen.lastUpdateTime = currentTime
            viewModel.postAngle(viewModel.deviceScreen.shoulderJointDeviceConfig.deviceType)
        }
        onDispose {
            scope.launch(Dispatchers.IO) {
                currentTime = System.currentTimeMillis()
                if (currentTime - viewModel.homeScreen.lastUpdateTime >= 1000 * 10) {
                    viewModel.homeScreen.lastUpdateTime = currentTime
                    viewModel.postAngle(viewModel.deviceScreen.shoulderJointDeviceConfig.deviceType)
                }
            }
        }
    }

}
