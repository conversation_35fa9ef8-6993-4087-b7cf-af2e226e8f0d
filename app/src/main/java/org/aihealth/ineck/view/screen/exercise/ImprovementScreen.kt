package org.aihealth.ineck.view.screen.exercise

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.widget.Toast
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateDp
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.updateTransition
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.ProgressIndicatorDefaults
import androidx.compose.material3.Surface
import androidx.compose.material3.TabPosition
import androidx.compose.material3.TabRow
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterVertically
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModelProvider
import coil.compose.AsyncImage
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.improvement.ImproveProgramUserExercise
import org.aihealth.ineck.model.improvement.ImproveProgramUserExerciseLoadState
import org.aihealth.ineck.model.improvement.ImprovementProgramsLoadState
import org.aihealth.ineck.notification.VideoUploadService
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.getDefaultDate
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHCard
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshIndicator
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshLayout
import org.aihealth.ineck.view.custom.refreshlayout.AIHRefreshType
import org.aihealth.ineck.view.custom.vieweffect.shimmerEffect
import org.aihealth.ineck.view.dialog.MonthPickerDialog
import org.aihealth.ineck.view.directions.ImprovementDetailDirections
import org.aihealth.ineck.view.screen.AiJointHeaderDropdownMenu
import org.aihealth.ineck.view.screen.AiNeckAndAiBackHeaderDropdownMenu
import org.aihealth.ineck.view.screen.AiNeckCVAndAiBackCVHeaderDropdownMenu
import org.aihealth.ineck.view.screen.meeting.MeetingListCard
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.SharedViewModel
import org.aihealth.ineck.viewmodel.dao.ImprovementEvent
import org.aihealth.ineck.viewmodel.dao.convertPageStateToNumberPagerState
import org.aihealth.ineck.viewmodel.impl.ImprovementCardProgramsType
import org.aihealth.ineck.viewmodel.impl.ImprovementPageTabType
import org.aihealth.ineck.viewmodel.impl.ImprovementPageTabType.ImprovementPlan
import org.aihealth.ineck.viewmodel.impl.ImprovementPageTabType.MyPractice
import org.aihealth.ineck.viewmodel.impl.convertNumberPagerStateToPageState
import org.aihealth.ineck.viewmodel.impl.rememberNewPagerState
import java.util.Calendar
import java.util.Locale

/**
 *  改善页
 */
@Composable
fun ImprovementScreen(
    mainViewModel: MainViewModel,
    context: Context = LocalContext.current,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
) {
    val coroutineScope = rememberCoroutineScope()

    var dropdownVisible by remember {
        mutableStateOf(false)
    }
    val sharedViewModel: SharedViewModel =
        ViewModelProvider(activity).get(SharedViewModel::class.java)
    LogUtil.i("improve Recording ${sharedViewModel}")

    var showUploadProgressDialog by remember { mutableStateOf(false) }

    /** 当前改善页显示内容页类型状态 */
    val currentTabPage =
        mainViewModel.improvementScreen.currentImprovementTabPageState.collectAsState()
//    LogUtil.i("currentImprovementTabPageState:${mainViewModel.improvementScreen.currentImprovementTabPageState},currentTabPage:$currentTabPage")

    /** Horizontal Pager 状态 */
    val pagerState = mainViewModel.improvementScreen.rememberNewPagerState()

    /** 周厉视图模型 */
    val weekPagerState = rememberPagerState(initialPage = 1) { 3 }

    val improvementRecommendProgramDataState =
        mainViewModel.improvementScreen.improvementDataState.collectAsState()
    val improveProgramUserExerciseLoadState =
        mainViewModel.improvementScreen.improveProgramUserExerciseDataState.collectAsState()

    /**
     * 我的会议数据列表
     */
    val userMeetingDataStateLoadState =
        mainViewModel.improvementScreen.userMeetingDataState.collectAsState()

    /* 月份选择底部对话框显示状态信号 */
    var monthChooseDialogVisible by remember { mutableStateOf(false) }
// 为了避免在重新组合时丢失系统 UI 控制器的状态，rememberSystemUiController() 来在组合函数中保留该状态。
    val systemUiController = rememberSystemUiController()
    // 当前跳转的目标界面不是轮播页时，显示状态栏，在本组合被清理时执行
    systemUiController.isStatusBarVisible = true

//    LogUtil.i("inIm MainViewModel:$mainViewModel")
    BasePageView(
        headerContent = {
            Column {
                Box(modifier = Modifier.fillMaxWidth()) {
                    Row(
                        verticalAlignment = CenterVertically,
                        modifier = Modifier
                            .align(Alignment.CenterStart)
                            .padding(start = 28.dp)
                            .pointerInput(Unit) {
                                detectTapGestures {
                                    dropdownVisible = !dropdownVisible
                                }
                            },
                    ) {
                        Text(
                            text = when (mainViewModel.homeScreen.currentDeviceType.netWorkName) {
                                DeviceType.aiNeck.netWorkName -> {
                                    stringResource(id = R.string.ai_neck)
                                }

                                DeviceType.aiBack.netWorkName -> {
                                    stringResource(id = R.string.ai_back)

                                }

                                DeviceType.aiNeckCV.netWorkName -> {
                                    stringResource(id = R.string.ai_neck_cv)
                                }

                                DeviceType.aiBackCV.netWorkName -> {
                                    stringResource(id = R.string.ai_back_cv)
                                }

                                DeviceType.KneeJoint.netWorkName -> {
                                    stringResource(id = R.string.joint_knee)
                                }

                                DeviceType.KneeJoint.netWorkName -> {
                                    stringResource(id = R.string.joint_knee)

                                }

                                DeviceType.ElbowJoint.netWorkName -> {
                                    stringResource(id = R.string.joint_elbow)
                                }

                                DeviceType.HipJoint.netWorkName -> {
                                    stringResource(id = R.string.joint_hip)
                                }

                                DeviceType.ShoulderJoint.netWorkName -> {
                                    stringResource(id = R.string.joint_shoulder)
                                }

                                DeviceType.aiJointCV.netWorkName -> {
                                    stringResource(id = R.string.joint_knee)
                                }

                                else -> stringResource(id = R.string.ai_neck_cv)
                            },
                            style = Typography.displayLarge,
                            color = Color(0XFFF2F2F2)
                        )
                        Spacer(modifier = Modifier.width(18.dp))
                        Icon(
                            painter = painterResource(id = R.drawable.img_pulldown),
                            contentDescription = null,
                            tint = Color(0XFFF4F4F4),
                            modifier = Modifier.size(16.dp)
                        )

                        when (mainViewModel.homeScreen.currentDeviceType) {
                            DeviceType.aiNeck, DeviceType.aiBack -> {
                                AiNeckAndAiBackHeaderDropdownMenu(
                                    visible = dropdownVisible,
                                    onDismissRequest = { dropdownVisible = false },
                                    onClick = {
                                        mainViewModel.homeScreen.toggleDeviceType(it)
                                        dropdownVisible = false
                                    }
                                )
                            }

                            DeviceType.aiNeckCV, DeviceType.aiBackCV -> {
                                AiNeckCVAndAiBackCVHeaderDropdownMenu(
                                    visible = dropdownVisible,
                                    onDismissRequest = { dropdownVisible = false },
                                    onClick = {
                                        mainViewModel.homeScreen.toggleDeviceType(it)
                                        dropdownVisible = false
                                    }
                                )
                            }

                            DeviceType.aiJoint, DeviceType.KneeJoint, DeviceType.HipJoint, DeviceType.ElbowJoint, DeviceType.ShoulderJoint -> {
                                AiJointHeaderDropdownMenu(
                                    visible = dropdownVisible,
                                    onDismissRequest = { dropdownVisible = false },
                                    onClick = {
                                        mainViewModel.homeScreen.toggleDeviceType(it)
                                        dropdownVisible = false
                                    }
                                )
                            }

                            else -> {}
                        }

                    }
                }
                Spacer(modifier = Modifier.height(8.dp))
                ImprovementTabBar(
                    modifier = Modifier
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                        .clip(RoundedCornerShape(12.dp)),
                    backgroundColor = Color.White.copy(alpha = 0.1f),
                    tabPage = currentTabPage.value,
                    onTabSelected = { tabType ->
                        if (currentTabPage.value != tabType) {
                            /* 变更当前内容页码状态 */
                            mainViewModel.improvementScreen.changeCurrentImprovementPageTabState(
                                tabType
                            )
                            /* 自动滑动到目标页 */
                            coroutineScope.launch {
                                mainViewModel.improvementScreen.animateScrollToPageWithNewPager(
                                    convertPageStateToNumberPagerState(
                                        tabType
                                    )
                                )
                            }
                            /* 加载对饮页的数据 */
                            when (tabType) {
                                ImprovementPlan -> {
//                                    LogUtil.i("loadImprovementProgramsData ImprovementTabBar ${currentLocale}")
                                    mainViewModel.improvementScreen.loadImprovementProgramsData(
                                        loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                    )
                                }

                                MyPractice -> {
                                    mainViewModel.improvementScreen.loadImproveProgramUserExerciseData()
                                }
                            }
                        }
                    }
                )
            }
        },
        background = {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(
                        brush = Brush.linearGradient(
                            colors = listOf(
                                Color(0XFF3654CD), Color(0XFF3F6CE3), Color(0XFF789AF7)
                            ),
                            start = Offset(0f, 0f),
                            end = Offset(0f, Float.POSITIVE_INFINITY)
                        )
                    )
            )
        },
        statusBarDarkContentEnabled = false
    ) {
//        LogUtil.i("currentLocal:${currentLocale}")
        AIHRefreshLayout(
            state = mainViewModel.improvementScreen.refreshState,
            onRefresh = {
                LogUtil.i("loadImprovementProgramsData refresh")
                coroutineScope.launch {
                    mainViewModel.improvementScreen.onRefresh()
                }
            },
            indicator = {
                AIHRefreshIndicator(
                    state = mainViewModel.improvementScreen.refreshState,
                    fontColor = Color.White,
                    iconColor = Color(0XFFF1F1F1)
                )
            }
        ) {
            HorizontalPager(
                state = pagerState,
                verticalAlignment = Alignment.Top,
            ) { page ->
//                LogUtil.i("page:$page")
                when (convertNumberPagerStateToPageState(page)) {
                    /* 改善方案页内容 */
                    ImprovementPlan -> {
                        val type =
                            mainViewModel.improvementScreen.currentImprovementCardRecommendType.collectAsState()
                        if (currentLocale == Locale.CHINESE) {
                            ImprovementProgramCardsZh(
                                improvementCardRecommendDataType = type.value,
                                content = {
                                    when (type.value) {
                                        ImprovementCardProgramsType.RecommendFromExperts -> {
                                            when (improvementRecommendProgramDataState.value) {
                                                is ImprovementProgramsLoadState.InitLoading -> {
                                                    /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                                                    repeat(3) { count ->
                                                        UserExerciseCardLoadingContent(count = count)
                                                    }
                                                }

                                                is ImprovementProgramsLoadState.Loading -> {
                                                    repeat(3) { count ->
                                                        UserExerciseCardLoadingContent(count = count)
                                                    }
                                                }

                                                is ImprovementProgramsLoadState.Success -> {
                                                    val data =
                                                        (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                                                    ImprovementProgramCardSuccessContentWithVipZh(
                                                        programsData = data,
                                                        onclick = { it ->
                                                            startScreen(
                                                                route = ImprovementDetailDirections.actionToImprovementDetailCompose(
                                                                    model = it
                                                                ),
                                                                finish = false
                                                            )
                                                        }
                                                    )
                                                }

                                                is ImprovementProgramsLoadState.Failure -> {
                                                    LogUtil.d("ImprovementProgramsLoadState Failure ZH")
                                                    ImprovementProgramCardFailureContent {
                                                        mainViewModel.improvementScreen.loadImprovementProgramsData(
                                                            loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                                        )
                                                    }
                                                }

                                            }
                                        }

                                        else -> {}
                                    }
                                }
                            )
                        } else {
                            ImprovementProgramCardsEn(
                                improvementCardRecommendDataType = type.value,
                                content = {
                                    when (type.value) {
                                        ImprovementCardProgramsType.RecommendFromExperts -> {
                                            when (improvementRecommendProgramDataState.value) {
                                                is ImprovementProgramsLoadState.InitLoading -> {
                                                    /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                                                    repeat(3) { count ->
                                                        UserExerciseCardLoadingContent(count = count)
                                                    }
                                                }

                                                is ImprovementProgramsLoadState.Loading -> {
                                                    repeat(3) { count ->
                                                        UserExerciseCardLoadingContent(count = count)
                                                    }
                                                }

                                                is ImprovementProgramsLoadState.Success -> {
                                                    val data =
                                                        (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                                                    ImprovementProgramCardSuccessContentWithVipEn(
                                                        programsData = data,
                                                        onclick = { it ->
                                                            startScreen(
                                                                route = ImprovementDetailDirections.actionToImprovementDetailCompose(
                                                                    model = it
                                                                ),
                                                                finish = false
                                                            )
                                                        }
                                                    )
                                                }

                                                is ImprovementProgramsLoadState.Failure -> {
                                                    LogUtil.d("ImprovementProgramsLoadState Failure EN")
                                                    ImprovementProgramCardFailureContent {
                                                        mainViewModel.improvementScreen.loadImprovementProgramsData(
                                                            loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                                                        )
                                                    }
                                                }
                                            }
                                        }

                                        else -> {}
                                    }
                                }
                            )
                        }

                    }
                    /* 我的练习页内容 */
                    MyPractice -> {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            ChooseExerciseDayComponent(
                                modifier = Modifier
                                    .height(114.dp)
                                    .fillMaxWidth(0.95f),
                                weekPagerState = weekPagerState,
                                viewModel = mainViewModel.improvementScreen,
                                onClick = {
                                    monthChooseDialogVisible = true
                                }
                            )
                            MeetingListCard(
                                meetingListLoadState = userMeetingDataStateLoadState.value
                            )

                            /* 我的练习数据 */
                            ImproveProgramUserExerciseCards(
                                improveViewModel = mainViewModel.improvementScreen,
                                improveProgramUserExerciseLoadState = improveProgramUserExerciseLoadState.value
                            )

                            /* 查看专业版练习数据 */
//                            ViewProfessionDataCard()
                        }
                    }
                }
            }
        }
    }

    /* 月份选择器对话框， 选择需要查看的所属月份的第一个星期 */
    if (monthChooseDialogVisible) {
        LogUtil.i("show the bottom Dialog $monthChooseDialogVisible")
        AIHBottomSheet(
            onDismissRequest = {
                monthChooseDialogVisible = false
                LogUtil.i("click month cancel $monthChooseDialogVisible")
//                mainViewModel.improvementScreen.changeChooseMonthVisibleState(false)
            }
        ) {
            MonthPickerDialog(
                onConfirmClick = { newCalendarOfMonth ->
                    if (newCalendarOfMonth > mainViewModel.improvementScreen.todayCalendar.value) {
                        /* 如果选择日期为无效日期（未来的日期） */
                        Toast.makeText(
                            context,
                            baseApplication.getString(R.string.choose_future_date),
                            Toast.LENGTH_SHORT
                        ).show()
                    } else if (newCalendarOfMonth.get(Calendar.YEAR) == mainViewModel.improvementScreen.currentWeek[3].get(
                            Calendar.YEAR
                        ) &&
                        newCalendarOfMonth.get(Calendar.MONTH) == mainViewModel.improvementScreen.currentWeek[3].get(
                            Calendar.MONTH
                        )
                    ) {
                        /* 选择为指定日期当月，则不执行任何逻辑 */
                    } else {
                        mainViewModel.improvementScreen.changeCurrentMonthCalendar(
                            newCalendarOfMonth
                        )
                        /* 判断日期做动画行进轨迹渲染 */
                        if (newCalendarOfMonth.get(Calendar.MONTH) < mainViewModel.improvementScreen.targetCalendar.value.get(
                                Calendar.MONTH
                            )
                        ) {
                            /* 选择月份比指定日期早 */
                            coroutineScope.launch {
                                mainViewModel.improvementScreen.calibrateTargetWeeksData(
                                    indicatorCalendar = newCalendarOfMonth,
                                    weekType = ImprovementEvent.WeekCalendarType.PreviousWeek
                                )
                                mainViewModel.improvementScreen.copyPreviousToCurrentWeek()
                                mainViewModel.improvementScreen.calibrateTargetWeeksData(
                                    indicatorCalendar = (newCalendarOfMonth.clone() as Calendar).apply {
                                        add(
                                            Calendar.DATE,
                                            7
                                        )
                                    },
                                    ImprovementEvent.WeekCalendarType.NextWeek
                                )
                                weekPagerState.animateScrollToPage(0)
                                weekPagerState.scrollToPage(1)
                                mainViewModel.improvementScreen.calibrateTargetWeeksData(
                                    indicatorCalendar = (newCalendarOfMonth.clone() as Calendar).apply {
                                        add(
                                            Calendar.DATE,
                                            -7
                                        )
                                    },
                                    ImprovementEvent.WeekCalendarType.PreviousWeek
                                )
                            }

                        } else {
                            /* 选择月份比指定日期晚 */
                            coroutineScope.launch {
                                mainViewModel.improvementScreen.calibrateTargetWeeksData(
                                    indicatorCalendar = newCalendarOfMonth,
                                    weekType = ImprovementEvent.WeekCalendarType.NextWeek
                                )
                                mainViewModel.improvementScreen.copyNextToCurrentWeek()
                                mainViewModel.improvementScreen.calibrateTargetWeeksData(
                                    indicatorCalendar = (newCalendarOfMonth.clone() as Calendar).apply {
                                        add(
                                            Calendar.DATE,
                                            -7
                                        )
                                    },
                                    ImprovementEvent.WeekCalendarType.PreviousWeek
                                )
                                weekPagerState.animateScrollToPage(2)
                                weekPagerState.scrollToPage(1)
                                mainViewModel.improvementScreen.calibrateTargetWeeksData(
                                    indicatorCalendar = (newCalendarOfMonth.clone() as Calendar).apply {
                                        add(
                                            Calendar.DATE,
                                            7
                                        )
                                    },
                                    ImprovementEvent.WeekCalendarType.NextWeek
                                )
                            }
                        }
                    }
                    monthChooseDialogVisible = false
                },
                maxCalendar = getDefaultDate(),
                onCancelClick = {
                    LogUtil.i("click month cancel $monthChooseDialogVisible")
                    monthChooseDialogVisible = false
//                    mainViewModel.improvementScreen.changeChooseMonthVisibleState(false)
                }
            )
        }
    }
    /* 当Pager的当前页发生变化时 (尤其针对滑动切换页面的情况)， 立即对当前内容页码进行更改，执行Tab游标移动效果等 */
    LaunchedEffect(pagerState) {
        snapshotFlow { pagerState.currentPage }.collect { page ->
            /* 切换也不为当前页的情况 */
            if (convertPageStateToNumberPagerState(currentTabPage.value) != page) {
                mainViewModel.improvementScreen.changeCurrentImprovementPageTabState(
                    convertNumberPagerStateToPageState(page)
                )
                when (convertNumberPagerStateToPageState(page = page)) {
                    /* 需要判断是否为初次加载 ，
                        若不是初次加载，则保持原有状态；
                        否则进行该页面获取数据的第一次请求
                     */
                    ImprovementPlan -> {
                        if (improvementRecommendProgramDataState.value == ImprovementProgramsLoadState.InitLoading) {
                            LogUtil.i("loadImprovementProgramsData Pager的当前页发生变化 ${pagerState}")
                            mainViewModel.improvementScreen.loadImprovementProgramsData(
                                loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                            )
                        }
                    }

                    MyPractice -> {
                        if (improveProgramUserExerciseLoadState.value == ImproveProgramUserExerciseLoadState.InitLoading) {
                            mainViewModel.improvementScreen.loadImproveProgramUserExerciseData()
                        }
                    }
                }
            }
        }
    }
    /* 处理生命周期 */
    DisposableEffect(key1 = lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_START) {
                /* 进行初始化数据请求加载 */
                mainViewModel.improvementScreen.apply {
                    this.refreshState.apply {
                        refreshing = true
                        refreshType = AIHRefreshType.RELEASE
                    }
//                    LogUtil.i("loadImprovementProgramsData when screen start")
//                    /* 加载改善项目内容 */
//                    loadImprovementProgramsData(loadType = this.currentImprovementCardRecommendType.value)
//                    /* 加载用户已练习改善项目内容 */
//                    loadImproveProgramUserExerciseData()
                    this.refreshState.apply {
                        refreshing = true
                        refreshType = AIHRefreshType.FINISHED
                    }
                }
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    // Add upload dialog
    if (sharedViewModel.showUploadDialog) {
        AlertDialog(
            onDismissRequest = { sharedViewModel.showUploadDialog = false },
            shape = RoundedCornerShape(16.dp),
            containerColor =  Color.White,
            text = { Text(stringResource(R.string.upload_video_tips)) },
            confirmButton = {
                AIHButton(
                    modifier = Modifier.widthIn(min = 104.dp).heightIn(min = 34.dp),
                    text = stringResource(R.string.upload),
                    onClick = {
                        sharedViewModel.showUploadDialog = false
                        showUploadProgressDialog = true
                        // Start upload service
                        val intent = Intent(context, VideoUploadService::class.java).apply {
                            action = VideoUploadService.ACTION_START_UPLOAD
                            putExtra(VideoUploadService.EXTRA_VIDEO_PATH, sharedViewModel.videoUrl)
                        }
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                            context.startForegroundService(intent)
                        } else {
                            context.startService(intent)
                        }
                        LogUtil.i("Upload service started")
                        finish()
                    }
                )
            },
            dismissButton = {
                AIHOutlinedButton(
                    modifier = Modifier.widthIn(min = 104.dp).heightIn(min = 34.dp),
                    text = stringResource(R.string.discard),
                    onClick = {
                        sharedViewModel.showUploadDialog = false
                        finish()
                    }
                )
            }
        )
    }

    // Add upload progress dialog
    if (showUploadProgressDialog) {
        var uploadProgress by remember { mutableFloatStateOf(0f) }
        val animatedProgress by animateFloatAsState(
            targetValue = uploadProgress,
            animationSpec = ProgressIndicatorDefaults.ProgressAnimationSpec
        )
        
        DisposableEffect(Unit) {
            val uploadProgressReceiver = object : BroadcastReceiver() {
                override fun onReceive(context: Context?, intent: Intent?) {
                    if (intent?.action == VideoUploadService.ACTION_UPLOAD_PROGRESS) {
                        val progress = intent.getIntExtra("progress", 0)
                        LogUtil.i("Upload progress received: $progress")
                        uploadProgress = progress / 100f
                        showUploadProgressDialog = progress < 100
                    }
                }
            }

            val filter = IntentFilter(VideoUploadService.ACTION_UPLOAD_PROGRESS)
            
            try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    context.registerReceiver(
                        uploadProgressReceiver,
                        filter,
                        Context.RECEIVER_NOT_EXPORTED
                    )
                } else {
                    context.registerReceiver(uploadProgressReceiver, filter)
                }
                LogUtil.i("Broadcast receiver registered for ${VideoUploadService.ACTION_UPLOAD_PROGRESS}")
            } catch (e: Exception) {
                LogUtil.e("Error registering receiver: ${e.message}")
            }

            onDispose {
                try {
                    context.unregisterReceiver(uploadProgressReceiver)
                } catch (e: Exception) {
                    LogUtil.e("Error unregistering receiver: ${e.message}")
                }
            }
        }

        AlertDialog(
            onDismissRequest = { showUploadProgressDialog = false  },
            shape = RoundedCornerShape(16.dp),
            containerColor =  Color.White,
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Row {
                        Image(
                            painter = painterResource(id = R.drawable.ic_upload_process),
                            contentDescription = "photo upload",
                            modifier = Modifier.width(64.dp)
                                .height(70.dp)
                        )
                        Text(
                            text = "${(animatedProgress * 100).toInt()}%",
                            style = TextStyle(
                                fontSize = 24.sp,
                                fontWeight = FontWeight(600),
                                color = Color(0xFF2B56D7),
                                textAlign = TextAlign.Justify,
                            )
                        )
                    }


                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = stringResource(R.string.upload_video_message),
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center
                    )
                }
            },
            confirmButton = { /* No confirm button during upload */ }
        )
    }
}

/**
 *  我的练习卡片 空数据状态
 *  @param  onClickEvent    重新请求盖上方案数据点击事件
 */
@Composable
fun ImprovementProgramCardFailureContent(
    onClickEvent: () -> Unit
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(320.dp),
    ) {
        val (icon, text) = createRefs()
        Icon(
            painter = painterResource(id = R.drawable.icon_query_result_failure),
            contentDescription = stringResource(id = R.string.failure_load_improvement_plan),
            tint = Color(0xFF666666),
            modifier = Modifier
                .size(42.dp)
                .constrainAs(icon) {
                    start.linkTo(parent.start, 12.dp)
                    top.linkTo(parent.top, 24.dp)
                    end.linkTo(parent.end, 24.dp)
                    bottom.linkTo(parent.bottom, 24.dp)
                }
        )
        Text(
            text = stringResource(id = R.string.failure_load_improvement_plan),
            fontWeight = FontWeight.Light,
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            color = Color(0xFF666666),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(text) {
                start.linkTo(parent.start, 12.dp)
                top.linkTo(icon.bottom, 12.dp)
                end.linkTo(parent.end, 12.dp)
            }
        )
    }
}

/**
 *  用户练习已改善项目方案卡片
 *  @param  improveViewModel    改善视图模型
 *  @param  improveProgramUserExerciseLoadState 用户练习项目数据加载参数
 */
@Composable
fun ImproveProgramUserExerciseCards(
    improveViewModel: ImprovementEvent,
    improveProgramUserExerciseLoadState: ImproveProgramUserExerciseLoadState
) {
    AIHCard {
        Column(
            modifier = Modifier
                .padding(horizontal = 16.dp, vertical = 12.dp)
                .fillMaxWidth()
                .animateContentSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            /* 卡片表头Tab选择 */
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Text(
                    text = stringResource(id = R.string.title_of_history_exercises),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                )
            }
//            LogUtil.i("我的练习:${improveProgramUserExerciseLoadState.toJson()}")
            when (improveProgramUserExerciseLoadState) {
                /* 初次加载 */
                ImproveProgramUserExerciseLoadState.InitLoading -> {
                    repeat(3) { count ->
                        UserExerciseCardLoadingContent(count = count)
                    }
                }
                /* 空数据 */
                ImproveProgramUserExerciseLoadState.EmptyData -> {
                    UserExerciseCardEmptyContent()
                }
                /* 加载失败 */
                is ImproveProgramUserExerciseLoadState.Failure -> {
                    UserExerciseCardFailureContent {
                        improveViewModel.loadImproveProgramUserExerciseData()
                    }
                }
                /* 非初次加载 */
                ImproveProgramUserExerciseLoadState.Loading -> {
                    val exerciseDataCache =
                        improveViewModel.improveProgramUserExerciseDataCacheState.collectAsState()
                    /* 复用缓存列表池中的最近一次请求数据 */
                    UserExerciseCardSuccessContent(
                        exerciseData = exerciseDataCache.value,
                        improveViewModel = improveViewModel,
                        enableClick = true
                    ) {
                        improveViewModel.jumpToExerciseDetailPage(it)
                    }
                }
                /* 加载成功 */
                is ImproveProgramUserExerciseLoadState.Success -> {
//                    LogUtil.i("我的练习加载成功")
                    val exerciseData: List<ImproveProgramUserExercise> =
                        improveProgramUserExerciseLoadState.dataList
//                    LogUtil.i("我的练习：${exerciseData.toJson()}")
                    UserExerciseCardSuccessContent(
                        exerciseData = exerciseData,
                        improveViewModel = improveViewModel,
                        enableClick = true
                    ) {
                        improveViewModel.jumpToExerciseDetailPage(it)
                    }
                }

            }
        }
    }
}


/**
 *  改善页Tab游标
 *  @param  modifier    修饰符参数
 *  @param  backgroundColor 背景颜色
 *  @param  tabPage     当前页
 *  @param  onTabSelected   Tab选择事件
 */
@Composable
private fun ImprovementTabBar(
    modifier: Modifier = Modifier,
    backgroundColor: Color,
    tabPage: ImprovementPageTabType,
    onTabSelected: (tabPage: ImprovementPageTabType) -> Unit
) {
    TabRow(
        modifier = modifier,
        selectedTabIndex = tabPage.ordinal,
        containerColor = backgroundColor,
        indicator = { tabPositions ->
            ImprovementTabIndicator(tabPositions, tabPage)
        }
    ) {
        ImprovementTab(
            title = stringResource(R.string.improvement_plan),
            onClick = { onTabSelected(ImprovementPlan) },
            isSelected = tabPage == ImprovementPlan
        )
        ImprovementTab(
            title = stringResource(R.string.my_practice),
            onClick = { onTabSelected(MyPractice) },
            isSelected = tabPage == MyPractice
        )
    }
}

/**
 * 选项卡
 */
@Composable
private fun ImprovementTab(
    title: String,
    onClick: () -> Unit,
    isSelected: Boolean,
    modifier: Modifier = Modifier
) {
    val textColor: Color by animateColorAsState(
        if (isSelected) Color.Black else Color.White,
        label = ""
    )
    Row(
        modifier = modifier
            .pointerInput(Unit) {
                detectTapGestures { onClick() }
            }
            .padding(horizontal = 4.dp, vertical = 8.dp)
            .zIndex(1f),
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = CenterVertically
    ) {
        Text(
            text = title,
            fontSize = 14.sp,
            fontWeight = FontWeight.Normal,
            color = textColor
        )
    }
}

/**
 * 切换时的动画效果
 */
@Composable
private fun ImprovementTabIndicator(
    tabPositions: List<TabPosition>,
    tabPage: ImprovementPageTabType
) {
    val transition = updateTransition(
        tabPage,
        label = "Tab indicator"
    )
    val indicatorLeft by transition.animateDp(
        transitionSpec = {
            if (ImprovementPlan isTransitioningTo MyPractice) {
                spring(stiffness = Spring.StiffnessVeryLow)
            } else {
                spring(stiffness = Spring.StiffnessMedium)
            }
        },
        label = "Indicator left"
    ) { page ->
        tabPositions[page.ordinal].left
    }
    val indicatorRight by transition.animateDp(
        transitionSpec = {
            if (ImprovementPlan isTransitioningTo MyPractice) {
                spring(stiffness = Spring.StiffnessMedium)
            } else {
                spring(stiffness = Spring.StiffnessVeryLow)
            }
        },
        label = "Indicator right"
    ) { page ->
        tabPositions[page.ordinal].right
    }
    Box(
        Modifier
            .fillMaxSize()
            .wrapContentSize(align = Alignment.BottomStart)
            .offset(x = indicatorLeft)
            .width(indicatorRight - indicatorLeft)
            .padding(4.dp)
            .fillMaxSize()
            .clip(RoundedCornerShape(8.dp))
            .background(Color.White)
    )
}

/**
 *  选择我的练习日期组件
 *  @param  modifier    修饰符参数
 *  @param  weekPagerState  星期周历HorizontalPagerState状态
 *  @param  viewModel   改善页视图模型
 */
@Composable
private fun ChooseExerciseDayComponent(
    modifier: Modifier = Modifier,
    weekPagerState: PagerState,
    viewModel: ImprovementEvent,
    onClick: () -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    val today = viewModel.todayCalendar.collectAsState()
    LogUtil.i("today$today")
    val currentMonthOfViewWeekCalendar by viewModel.currentMonthOfViewWeekCalendar.collectAsState()
    val currentYearOfViewWeekCalendar by viewModel.currentYearOfViewWeekCalendar.collectAsState()
    ConstraintLayout(
        modifier = modifier
    ) {
        val (monthBlock, returnToTodayBlock, weekCalendarBlock) = createRefs()
        val centerGuideLine = createGuidelineFromTop(0.4f)
        TextButton(
            modifier = Modifier
                .constrainAs(monthBlock) {
                    start.linkTo(parent.start, 20.dp)
                    top.linkTo(parent.top)
                    bottom.linkTo(centerGuideLine)
                },
            onClick = {
                onClick()
                LogUtil.i("click month")
//                viewModel.changeChooseMonthVisibleState(true)
                      },
            shape = RoundedCornerShape(12.dp),
        ) {
            Row(verticalAlignment = CenterVertically) {
                Text(
                    text = stringResource(
                        id = R.string.year_month,
                        currentYearOfViewWeekCalendar,
                        currentMonthOfViewWeekCalendar
                    ),
                    color = Color.White,
                    fontWeight = FontWeight.Normal,
                    fontSize = 20.sp
                )
                Spacer(modifier = Modifier.width(16.dp))
                Icon(
                    painter = painterResource(id = R.drawable.img_report_calender_next),
                    contentDescription = stringResource(id = R.string.choose_month),
                    modifier = Modifier.size(16.dp),
                    tint = Color(0xFFCBCBCB)
                )
            }
        }
        TextButton(
            modifier = Modifier
                .constrainAs(returnToTodayBlock) {
                    top.linkTo(parent.top)
                    end.linkTo(parent.end, 20.dp)
                    bottom.linkTo(centerGuideLine)
                },
            onClick = {
                coroutineScope.launch {
                    viewModel.changeTargetCalendar(today.value)
                    /* 寻找Today是否所在上一周、这一周、下一周的视图中 */
                    if (viewModel.currentWeek.contains(today.value)) {
                        /* 今日存在于这一周， 则不执行任何逻辑 */
                    } else if (viewModel.previousWeek.contains(today.value)) {
                        /* 今日存在于上一周， 做向上一周移动的动画渲染 */
                        weekPagerState.animateScrollToPage(0)
                        viewModel.copyPreviousToCurrentWeek()
                        viewModel.calibrateTargetWeeksData((today.value.clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                7
                            )
                        }, ImprovementEvent.WeekCalendarType.NextWeek)
                        weekPagerState.scrollToPage(1)
                        viewModel.calibrateTargetWeeksData((today.value.clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                -7
                            )
                        }, ImprovementEvent.WeekCalendarType.PreviousWeek)
                    } else if (viewModel.nextWeek.contains(today.value)) {
                        /* 今日存在于下一周， 做向下一周移动的动画渲染 */
                        weekPagerState.animateScrollToPage(2)
                        viewModel.copyNextToCurrentWeek()
                        viewModel.calibrateTargetWeeksData((today.value.clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                -7
                            )
                        }, ImprovementEvent.WeekCalendarType.PreviousWeek)
                        weekPagerState.scrollToPage(1)
                        viewModel.calibrateTargetWeeksData((today.value.clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                7
                            )
                        }, ImprovementEvent.WeekCalendarType.NextWeek)
                    } else {
                        /* 今日不存在与当今范围内，则说明当前周日历视图远早于今日所在星期， 则进行向后移动渲染逻辑 */
                        viewModel.calibrateTargetWeeksData(
                            today.value,
                            ImprovementEvent.WeekCalendarType.NextWeek
                        )
                        weekPagerState.animateScrollToPage(2)
                        viewModel.copyNextToCurrentWeek()
                        viewModel.calibrateTargetWeeksData((today.value.clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                -7
                            )
                        }, ImprovementEvent.WeekCalendarType.PreviousWeek)
                        weekPagerState.scrollToPage(1)
                        viewModel.calibrateTargetWeeksData((today.value.clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                7
                            )
                        }, ImprovementEvent.WeekCalendarType.NextWeek)
                    }
                }
            },
            shape = RoundedCornerShape(12.dp),
        ) {
            Text(
                text = stringResource(id = R.string.return_to_today),
                color = Color.White.copy(alpha = 0.9f),
                fontWeight = FontWeight.Normal,
                fontSize = 12.sp
            )
        }
        WeekCalendarComponent(
            modifier = Modifier
                .constrainAs(weekCalendarBlock) {
                    start.linkTo(parent.start)
                    top.linkTo(centerGuideLine, 10.dp)
                    end.linkTo(parent.end)
                    bottom.linkTo(parent.bottom)
                },
            weekPagerState = weekPagerState,
            viewModel = viewModel
        )
    }
}


/**
 *  改善页我的练习记录周日历组件
 *  @param  modifier    修饰符参数
 *  @param  weekPagerState  pagerState翻页组件状态
 *  @param  viewModel   改善页视图模型
 */
@Composable
private fun WeekCalendarComponent(
    modifier: Modifier = Modifier,
    weekPagerState: PagerState = rememberPagerState(initialPage = 1) { 3 },
    viewModel: ImprovementEvent
) {
    val coroutineScope = rememberCoroutineScope()
    val targetDay = viewModel.targetCalendar.collectAsState()
//    LogUtil.i("targetDay:${targetDay.value}")
    /** 当前目标日期所在周历startX坐标 */
    val anchorStartXOfCurrentWeek: MutableState<Float> = remember { mutableFloatStateOf(0f) }

    /** 当前目标日期上一周所在周历startX坐标 */
    val anchorStartXOfPreviousWeek: MutableState<Float> = remember { mutableFloatStateOf(0f) }

    /** 当前目标日期下一周所在周历的startY坐标 */
    val anchorStartXOfNextWeek: MutableState<Float> = remember { mutableFloatStateOf(0f) }

    Column(
        modifier = Modifier
            .then(modifier)
            .padding(vertical = 8.dp, horizontal = 32.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        /* 周历本体 */
        HorizontalPager(
            state = weekPagerState,
            userScrollEnabled = true,
            modifier = Modifier.fillMaxWidth()
        ) { page ->
            /* 周历内容 */
            when (page) {
                0 -> {
                    /* 上一周 */
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .onGloballyPositioned { coordinates ->
                                anchorStartXOfPreviousWeek.value = coordinates.boundsInRoot().left
                            },
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        viewModel.previousWeek.forEach { calendar ->
                            calendar.apply {
                                // 时
                                set(Calendar.HOUR_OF_DAY, 0)
                                // 分
                                set(Calendar.MINUTE, 0)
                                // 秒
                                set(Calendar.SECOND, 0)
                                // 毫秒
                                set(Calendar.MILLISECOND, 0)
                            }
                            CalendarUnitComponent(
                                calendar = calendar,
                                enableClick = true,
                                isTargetDate = targetDay.value.let {
                                    it.get(Calendar.YEAR) == calendar.get(Calendar.YEAR) &&
                                            it.get(Calendar.MONTH) == calendar.get(Calendar.MONTH) &&
                                            it.get(Calendar.DATE) == calendar.get(Calendar.DATE)
                                }
                            ) {
                                LogUtil.i("last week calendar:$calendar")
                                viewModel.changeTargetCalendar(calendar)
                                /* 大于今日的日期不可选择 */
//                                viewModel.todayCalendar.value.let {  today ->
//                                    if(today.timeInMillis > calendar.timeInMillis) {
//                                        viewModel.changeTargetCalendar(calendar)
//                                    }
//                                }
                            }
                        }
                    }
                }

                1 -> {
                    /* 这一周 */
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .onGloballyPositioned { coordinates ->
                                anchorStartXOfCurrentWeek.value = coordinates.boundsInRoot().left
                            },
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        viewModel.currentWeek.forEach { calendar ->
                            calendar.apply {
                                // 时
                                set(Calendar.HOUR_OF_DAY, 0)
                                // 分
                                set(Calendar.MINUTE, 0)
                                // 秒
                                set(Calendar.SECOND, 0)
                                // 毫秒
                                set(Calendar.MILLISECOND, 0)
                            }
                            CalendarUnitComponent(
                                calendar = calendar,
                                enableClick = true,
                                isTargetDate = targetDay.value.let {
                                    it.get(Calendar.YEAR) == calendar.get(Calendar.YEAR) &&
                                            it.get(Calendar.MONTH) == calendar.get(Calendar.MONTH) &&
                                            it.get(Calendar.DATE) == calendar.get(Calendar.DATE)
                                }
                            ) {
                                /* 大于今日的日期不可选择 */
                                viewModel.todayCalendar.value.let { today ->
                                    LogUtil.i("today:$today \n calendar:$calendar")
                                    if (today.timeInMillis >= calendar.timeInMillis) {
                                        viewModel.changeTargetCalendar(calendar)
                                    }
                                }
                            }
                        }
                    }
                }

                2 -> {
                    /* 下一周 */
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .onGloballyPositioned { coordinates ->
                                anchorStartXOfNextWeek.value = coordinates.boundsInRoot().left
                            },
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        viewModel.nextWeek.forEach { calendar ->
                            CalendarUnitComponent(
                                calendar = calendar,
                                enableClick = false,
                                isTargetDate = targetDay.value.let {
                                    it.get(Calendar.YEAR) == calendar.get(Calendar.YEAR) &&
                                            it.get(Calendar.MONTH) == calendar.get(Calendar.MONTH) &&
                                            it.get(Calendar.DATE) == calendar.get(Calendar.DATE)
                                }
                            ) {
                                /* 大于今日的日期不可选择 */
//                                viewModel.todayCalendar.value.let {  today ->
//                                    if(today.timeInMillis > calendar.timeInMillis) {
//                                        viewModel.changeTargetCalendar(calendar)
//                                    }
//                                }
                            }
                        }
                    }
                }
            }
        }
    }
    /* 处理循环翻页逻辑 */
    LaunchedEffect(
        key1 = anchorStartXOfPreviousWeek.value,
        key2 = anchorStartXOfCurrentWeek.value,
        key3 = anchorStartXOfNextWeek.value
    ) {
        coroutineScope.launch {
            if (weekPagerState.currentPage == 1 &&
                anchorStartXOfPreviousWeek.value == anchorStartXOfCurrentWeek.value &&
                anchorStartXOfNextWeek.value == 0f
            ) {
                /* 显示到指定日期当前星期 */
                viewModel.apply {
                    this.calibrateTargetWeeksData(
                        (this.currentWeek.first().clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                -1
                            )
                        },
                        ImprovementEvent.WeekCalendarType.PreviousWeek
                    )
                    this.calibrateTargetWeeksData(
                        (this.currentWeek.last().clone() as Calendar).apply {
                            add(
                                Calendar.DATE,
                                1
                            )
                        },
                        ImprovementEvent.WeekCalendarType.NextWeek
                    )
                }
            } else if (weekPagerState.currentPage == 0 &&
                anchorStartXOfCurrentWeek.value == 0f &&
                anchorStartXOfNextWeek.value == 0f
            ) {
                /* 显示到指定日期上一周的星期 */
                viewModel.copyPreviousToCurrentWeek()
                weekPagerState.scrollToPage(1)
            } else if (weekPagerState.currentPage == 2 &&
                anchorStartXOfPreviousWeek.value == anchorStartXOfCurrentWeek.value &&
                anchorStartXOfPreviousWeek.value == anchorStartXOfNextWeek.value
            ) {
                /* 显示到指定日期下一周的星期 */
                if (viewModel.nextWeek.last()
                        .get(Calendar.WEEK_OF_YEAR) <= viewModel.todayCalendar.value.get(Calendar.WEEK_OF_YEAR)
                ) {
                    /* 若范围不超过今天所在星期的下一星期 */
                    viewModel.copyNextToCurrentWeek()
                    weekPagerState.scrollToPage(1)
                }
            } else {
                /* empty */
            }
        }
    }
}

/**
 *  日历内容单元组件
 *  @param  calendar    日期单元
 *  @param  isTargetDate    是否为当前选择日期
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun CalendarUnitComponent(
    calendar: Calendar,
    isTargetDate: Boolean,
    enableClick: Boolean,
    onClickEvent: () -> Unit
) {
    Card(
        modifier = Modifier
            .size(30.dp, 68.dp),
        shape = RoundedCornerShape(4.dp),
        elevation = CardDefaults.cardElevation(0.dp),
        colors = if (isTargetDate) CardDefaults.cardColors(Color(0xFFE9EEFF)) else CardDefaults.cardColors(
            Color.Transparent
        ),
        enabled = enableClick,
        onClick = onClickEvent
    ) {
        ConstraintLayout(modifier = Modifier.fillMaxSize()) {
            val (weekSignal, date) = createRefs()
            val centerGuideline = createGuidelineFromTop(.5f)
            Text(
                text = when (calendar.get(Calendar.DAY_OF_WEEK)) {
                    1 -> {
                        "S"
                    }

                    2 -> {
                        "M"
                    }

                    3 -> {
                        "T"
                    }

                    4 -> {
                        "W"
                    }

                    5 -> {
                        "T"
                    }

                    6 -> {
                        "F"
                    }

                    7 -> {
                        "S"
                    }

                    else -> {
                        "N/A"
                    }
                },
                fontSize = 11.sp,
                fontWeight = FontWeight.Light,
                textAlign = TextAlign.Center,
                color = Color(0xFFCBCBCB),
                modifier = Modifier.constrainAs(weekSignal) {
                    start.linkTo(parent.start)
                    top.linkTo(parent.top, 6.dp)
                    end.linkTo(parent.end)
                    bottom.linkTo(centerGuideline, 10.dp)
                }
            )
            Text(
                text = calendar.get(Calendar.DATE).toString(),
                fontSize = 13.sp,
                fontWeight = FontWeight.Light,
                textAlign = TextAlign.Center,
                color = if (isTargetDate) Color(0xFF3C59CF) else Color.White,
                modifier = Modifier.constrainAs(date) {
                    start.linkTo(parent.start)
                    top.linkTo(centerGuideline)
                    end.linkTo(parent.end)
                }
            )
        }
    }
}

/**
 *  我的练习卡片加载状态内容
 *  @param  count   计数位置
 */
@Composable
fun UserExerciseCardLoadingContent(count: Int) {
    if (count != 0) {
        HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
    }
    Card(
        elevation = CardDefaults.cardElevation(0.dp),
        colors = CardDefaults.cardColors(Color.White),
        shape = RoundedCornerShape(16.dp),
    ) {
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (programCover, programTitle, showDetail, enableDevice) = createRefs()
            /* 改善项目方案封面 */
            Surface(
                modifier = Modifier
                    .size(96.dp)
                    .constrainAs(programCover) {
                        start.linkTo(parent.start, 10.dp)
                        top.linkTo(parent.top, 10.dp)
                        bottom.linkTo(parent.bottom, 10.dp)
                    },
                shape = RoundedCornerShape(6.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .shimmerEffect()
                )
            }
            /* 改善项目方案标题 */
            Box(
                modifier = Modifier
                    .height(20.dp)
                    .constrainAs(programTitle) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(parent.top, 10.dp)
                        end.linkTo(parent.end, 10.dp)
                        width = Dimension.fillToConstraints
                    }
                    .shimmerEffect()
            )
            /* 改善项目方案关键属性， 时长、已练习人数等 */
            Row(
                modifier = Modifier
                    .height(20.dp)
                    .constrainAs(showDetail) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(programTitle.bottom, 8.dp)
                        end.linkTo(parent.end, 10.dp)
                        width = Dimension.fillToConstraints
                    },
                verticalAlignment = CenterVertically
            ) {
                Spacer(modifier = Modifier.weight(.5f))
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .shimmerEffect()
                        .weight(2f)
                )
                Spacer(modifier = Modifier.weight(1f))
                Box(
                    modifier = Modifier
                        .fillMaxHeight()
                        .shimmerEffect()
                        .weight(3f)
                )
                Spacer(modifier = Modifier.weight(1f))
            }
            Box(
                modifier = Modifier
                    .height(34.dp)
                    .width(118.dp)
                    .padding(8.dp)
                    .constrainAs(enableDevice) {
                        start.linkTo(programCover.end, 6.dp)
                        top.linkTo(showDetail.bottom, 4.dp)
                    }
                    .shimmerEffect()
            )
        }
    }
}

/**
 *  我的练习卡片 空数据状态
 */
@Composable
private fun UserExerciseCardEmptyContent() {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(150.dp),
    ) {
        val (icon, text) = createRefs()
        Icon(
            painter = painterResource(id = R.drawable.icon_empty_record_here),
            contentDescription = stringResource(id = R.string.empty_your_practice),
            tint = Color(0xFF666666),
            modifier = Modifier
                .size(42.dp)
                .constrainAs(icon) {
                    start.linkTo(parent.start, 12.dp)
                    top.linkTo(parent.top, 24.dp)
                    end.linkTo(parent.end, 24.dp)
                    bottom.linkTo(parent.bottom, 24.dp)
                }
        )
        Text(
            text = stringResource(id = R.string.empty_your_practice),
            fontWeight = FontWeight.Light,
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            color = Color(0xFF666666),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(text) {
                start.linkTo(parent.start, 12.dp)
                top.linkTo(icon.bottom, 12.dp)
                end.linkTo(parent.end, 12.dp)
            }
        )
    }
}

/**
 *  我的练习卡片 请求出错状态
 *  @param  onClickEvent    点击重试触发事件
 */
@Composable
private fun UserExerciseCardFailureContent(
    onClickEvent: () -> Unit
) {
    ConstraintLayout(
        modifier = Modifier
            .fillMaxWidth()
            .height(320.dp)
            .clickable { onClickEvent() }
    ) {
        val (icon, text) = createRefs()
        Icon(
            painter = painterResource(id = R.drawable.icon_empty_record_here),
            contentDescription = stringResource(id = R.string.empty_your_practice),
            tint = Color(0xFF666666),
            modifier = Modifier
                .size(42.dp)
                .constrainAs(icon) {
                    start.linkTo(parent.start, 12.dp)
                    top.linkTo(parent.top, 24.dp)
                    end.linkTo(parent.end, 24.dp)
                    bottom.linkTo(parent.bottom, 24.dp)
                }
        )
        Text(
            text = stringResource(id = R.string.empty_your_practice),
            fontWeight = FontWeight.Light,
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            color = Color(0xFF666666),
            maxLines = 2,
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier.constrainAs(text) {
                start.linkTo(parent.start, 12.dp)
                top.linkTo(icon.bottom, 12.dp)
                end.linkTo(parent.end, 12.dp)
            }
        )
    }
}

/**
 *  我的练习卡片 请求成功状态
 *  @param  onClickEvent    点击重试触发事件
 */
@Composable
private fun UserExerciseCardSuccessContent(
    exerciseData: List<ImproveProgramUserExercise>,
    improveViewModel: ImprovementEvent,
    enableClick: Boolean,
    onClickEvent: (Int) -> Unit
) {
    exerciseData.forEachIndexed { index, exercise ->
        if (index != 0) {
            HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
        }
        Card(
            colors = CardDefaults.cardColors(Color.White),
            elevation = CardDefaults.cardElevation(0.dp),
            shape = RoundedCornerShape(16.dp),
            enabled = enableClick,
            onClick = { onClickEvent(exercise.materialId) }
        ) {
            ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
                val (programCover, programTitle, showDetail, showScore, showExerciseTime) = createRefs()
                /* 改善项目方案封面 */
                Surface(
                    modifier = Modifier
                        .size(96.dp)
                        .constrainAs(programCover) {
                            start.linkTo(parent.start, 10.dp)
                            top.linkTo(parent.top, 10.dp)
                            bottom.linkTo(parent.bottom, 10.dp)
                        },
                    shape = RoundedCornerShape(6.dp)
                ) {

                    AsyncImage(
                        model = improveViewModel.getUserExerciseCardImg(exercise.materialId),
                        contentScale = ContentScale.Crop,
                        contentDescription = improveViewModel.getUserExerciseCardTitle(exercise.materialId)
                            .toString(),
                        placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                        modifier = Modifier.fillMaxSize()
                    )
                }
                /* 改善项目方案标题 */
                Text(
                    text = improveViewModel.getUserExerciseCardTitle(exercise.materialId)
                        .toString(),
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0xFF666666),
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier.constrainAs(programTitle) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(parent.top, 10.dp)
                        end.linkTo(parent.end, 0.dp)
                        width = Dimension.fillToConstraints
                    }
                )
                /* 改善项目方案关键属性， 时长、已练习人数等 */
                Row(
                    modifier = Modifier.constrainAs(showDetail) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(programTitle.bottom, 4.dp)
                        width = Dimension.fillToConstraints
                    },
                    verticalAlignment = CenterVertically
                ) {
                    val durationString = TimeUtil.convertSecondsToAnnotatedString(
                        exercise.duration,
                        LocalContext.current
                    )
                    Image(
                        painter = painterResource(id = R.drawable.img_duration_time),
                        contentDescription = "Duration Time",
                        modifier = Modifier
                            .size(10.dp)
                    )
                    /* 请求体响应结构中提供的"持续时间"参数是以秒为单位，这里需要再显示上做转换计算 */
                    Text(
                        text = durationString,
                        maxLines = 1,
                        modifier = Modifier
                            .weight(4f),
                    )
                }
                /* 练习得分 */ /* 人数*/
                Row(
                    modifier = Modifier.constrainAs(showScore) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(showDetail.bottom, 4.dp)
                        width = Dimension.fillToConstraints
                    },
                    verticalAlignment = CenterVertically
                ) {
                    val descriptionStringOfFr = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF666666),
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Light
                            )
                        ) {
                            append(
                                stringResource(id = R.string.participated_count)
                            )
                        }
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF6181E9),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(exercise.numberOfPeople.toString())
                        }
                    }
                    Image(
                        painter = painterResource(id = R.drawable.participated),
                        contentDescription = "Score",
                        modifier = Modifier.size(10.dp)
                    )
                    Text(
                        text = descriptionStringOfFr,
                        modifier = Modifier
                            .padding(horizontal = 2.dp)
                    )
                }
                /* 练习时间 */
                Row(
                    modifier = Modifier.constrainAs(showExerciseTime) {
                        start.linkTo(programCover.end, 12.dp)
                        top.linkTo(showScore.bottom, 4.dp)
                        width = Dimension.fillToConstraints
                    },
                    verticalAlignment = CenterVertically
                ) {
                    val descriptionStringOfExerciseTime = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF666666),
                                fontSize = 10.sp,
                                fontWeight = FontWeight.Light
                            )
                        ) {
                            append(
                                stringResource(id = R.string.exercise_time)
                            )
                        }
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF6181E9),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(TimeUtil.convertSecondsToStandardString(exercise.actualDurationOfIndividual))
                            append("  ")
                        }
                    }
                    Image(
                        painter = painterResource(id = R.drawable.img_duration_my_execrise),
                        contentDescription = "Score",
                        modifier = Modifier.size(10.dp)
                    )
                    Text(
                        text = descriptionStringOfExerciseTime,
                        modifier = Modifier
                            .padding(horizontal = 2.dp)
                    )
                }
            }
        }
    }
}

/** 从页码状态切换到页类型状态 */
fun convertNumberPagerStateToPageState(page: Int): ImprovementPageTabType {
    return when (page) {
        0 -> {
            ImprovementPlan
        }

        1 -> {
            MyPractice
        }

        else -> {
            ImprovementPlan
        }
    }
}

