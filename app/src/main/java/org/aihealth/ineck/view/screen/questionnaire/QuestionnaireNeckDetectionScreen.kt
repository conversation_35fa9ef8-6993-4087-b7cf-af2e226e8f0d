package org.aihealth.ineck.view.screen.questionnaire

import android.content.Context
import android.graphics.Bitmap
import android.view.TextureView
import androidx.annotation.OptIn
import androidx.camera.core.ImageCapture
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.model.ExerciseResult
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.screen.exercise.CameraView
import org.aihealth.ineck.view.screen.exercise.VerticalVideoPlayer
import org.aihealth.ineck.view.screen.exercise.rememberExoPlayer
import org.aihealth.ineck.viewmodel.ImproveDetailViewModel
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectedError
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel

@Composable
fun QuestionnaireNeckDetectionScreen(
    modifier: Modifier = Modifier,
    onSkip: () -> Unit = {},
    onStart: () -> Unit = {},
) {
    val enable = remember {
        mutableStateOf(false)
    }
    val pager = rememberPagerState(0)
    val context = LocalContext.current
    val viewModel = viewModel<VCGuideViewModel>(factory = object : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(VCGuideViewModel::class.java))
                return modelClass.getConstructor(Context::class.java)
                    .newInstance(context)
            throw IllegalArgumentException("")
        }
    })
    val improveDetailViewModel: ImproveDetailViewModel = viewModel()
    val scope = rememberCoroutineScope()

    BasePageView(
        modifier = modifier,
        title = stringResource(id = R.string.neck_detection),
        showBackIcon = true,
        headerContent = {
            Text(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(horizontal = 10.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {
                        enable.value = true
                    },
                text = stringResource(id = R.string.skip),
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF3888FF),
                )
            )
        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            QuestionnaireTagHeader(3)
            HorizontalPager(count = 2, state = pager, userScrollEnabled = false) { page ->
                when (page) {
                    0 -> {
                        NeckDetectionScreen(
                            modifier = Modifier.fillMaxSize(),
                            viewModel = viewModel,
                            onDismissEvent = { result ->
                                when (result) {
                                    is VCDetectingResult.DetectingSuccess -> {
                                        scope.launch {
                                            pager.scrollToPage(1)
                                        }
                                    }

                                    is VCDetectingResult.DetectingError -> {
                                        if (result.error == VCDetectedError.Cancel()) {
                                            enable.value = true
                                        }
                                    }

                                    else -> {}
                                }
                            }
                        )
                    }

                    1 -> {
                        QuestionnaireOneVideoPlayerView(
                            modifier = Modifier.fillMaxSize(),
                            viewModel = improveDetailViewModel,
                            onStart = onStart
                        )
                    }
                }
            }
        }

    }
    SkipDialog(
        enable = enable.value,
        onDismissRequest = {
            enable.value = false
        },
        onConfirm = {
            enable.value = false
            onSkip()
        }
    )
}

@OptIn(UnstableApi::class)
@Composable
fun QuestionnaireOneVideoPlayerView(
    modifier: Modifier = Modifier,
    viewModel: ImproveDetailViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onStart: () -> Unit,
) {
    val context = LocalContext.current
    val playerView = remember {
        TextureView(context)
    }
    val ioScope = CoroutineScope(Dispatchers.IO)
    val imageCapture = remember {
        ImageCapture.Builder().build()
    }
    val exoPlayer = rememberExoPlayer(
        context = context,
        playerListener = viewModel.listener,
        videoFrameMetadataListener = { presentationTimeUs, releaseTimeNs, format, mediaFormat ->
            ioScope.launch {
                if (viewModel.duration.contains(presentationTimeUs)) {
                    LogUtil.i("presentationTimeUs:$presentationTimeUs")
                    if (playerView.bitmap != null) {
                        val bitmap: Bitmap? =
                            playerView.bitmap?.copy(Bitmap.Config.ARGB_8888, false)
                        viewModel.getVideoFrame(
                            bitmap!!,
                            presentationTimeUs
                        )
                    }
                }
            }
            ioScope.launch {
                if (viewModel.durationList.contains(presentationTimeUs)) {
                    LogUtil.i("presentationTimeUs:$presentationTimeUs")
                    viewModel.savePeopleFace(imageCapture, presentationTimeUs)
                }
            }
        },
    )

    /** 获取屏幕长宽高 */
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp

    LaunchedEffect(exoPlayer) {
        LogUtil.d("load exoplayer mediates")
        viewModel.setMediaSample()
        exoPlayer.apply {
            setMediaItems(viewModel.downloadMediaItems)
            LogUtil.i("init exoplayer seekTo:${viewModel.id.value},0")
            seekTo(viewModel.id.value, 0)
            repeatMode = Player.REPEAT_MODE_OFF
            prepare()
        }

        LogUtil.i("load the mediaItems index:${viewModel.id.value}, isPlaySub:${viewModel.isPlaySub.value}")
    }
    LaunchedEffect(key1 = exoPlayer) {
        LogUtil.d("change exoplayer,currentTime:${viewModel.currentTime}")
        exoPlayer.setVideoTextureView(playerView)
        exoPlayer.prepare()
        if (viewModel.currentTime != 0L) {
            if (!viewModel.isPlaySub.value) {
                exoPlayer.seekTo(viewModel.currentTime)
            } else {
                exoPlayer.seekTo(viewModel.id.value, viewModel.currentTime)
            }
        } else {
            if (!viewModel.isPlaySub.value) {
                exoPlayer.seekTo(0)
            } else {
                LogUtil.i("seek to id:${viewModel.id.value}")
                exoPlayer.seekTo(viewModel.id.value, 0)
            }
        }

        while (true) {
            if (exoPlayer.isPlaying) {
                viewModel.currentTime = exoPlayer.currentPosition
                viewModel.totalDuration = exoPlayer.duration
                if (!viewModel.isPlaySub.value) {
                    val time = viewModel.videoStartList.firstOrNull {
                        it > exoPlayer.currentPosition / 1000
                    } ?: viewModel.videoStartList.first()
                    val index = viewModel.videoStartList.indexOf(time)
                    viewModel.id.update { if (index > 0) index - 1 else 0 }
                }
            }
            delay(500)
        }
    }

    DisposableEffect(Unit) {
//        viewModel.extractFrames()
        viewModel.finishedDialogVisible = false
        LogUtil.i("DisposableEffect finishedDialogVisible ${viewModel.finishedDialogVisible}")
        onDispose {
            DialogUtil.hideLoading()
            viewModel.finishedDialogVisible = false
            LogUtil.i("DisposableEffect onDispose finishedDialogVisible ${viewModel.finishedDialogVisible}")
            viewModel.hasFinishedScore.value = false
            viewModel.videoEulerDic.clear()
            viewModel.valueEulerDic.clear()
            viewModel.count.value = 0
        }
    }
    Column(
        modifier = modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        /**
         * 视频播放器
         */
        VerticalVideoPlayer(
            modifier = Modifier
                .fillMaxWidth(0.9f),
            exoPlayer = exoPlayer,
            textureView = playerView,
            viewModel = viewModel,
            imageUrl = "https://myaih.net/material/image/get/12",
            enable = false
        )

        /**
         * 视屏监测
         */
        Box(
            modifier = Modifier.fillMaxWidth(0.9f),
        ) {
            CameraView(
                viewModel = viewModel,
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(vertical = 30.dp)
                    .aspectRatio(screenWidth / screenHeight),
                lifecycleOwner = lifecycleOwner,
                imageCapture = imageCapture,
                content = {}
            )

        }
    }

    FinishedDialog(
        viewModel = viewModel,
        onStart = onStart
    )

}

@Composable
private fun FinishedDialog(
    viewModel: ImproveDetailViewModel,
    onStart: () -> Unit
) {

    val finished = viewModel.hasFinishedScore.collectAsState()
    if (viewModel.finishedDialogVisible) {
        if (finished.value.not()) {
            LogUtil.i("finishedDialogVisible show Loading")
            DialogUtil.showLoading()
            LaunchedEffect(finished.value) {
                viewModel.analyzerVideo()
                viewModel.analyzerPicture()
            }
        } else {
            DialogUtil.hideLoading()
            val body = ExerciseResult(
                viewModel.improveProgramDetail.materialId,
                viewModel.score.value.toInt(),
                viewModel.totalDuration
            )
            apiService.postImprovementProgramUserExerciseData(body).enqueueBody {
                LogUtil.i("upload")
            }
            Dialog(onDismissRequest = {
                viewModel.finishedDialogVisible = false
                onStart()
//                LogUtil.i("DialogDismiss finishedDialogVisible:${viewModel.finishedDialogVisible}")
            }) {
                Column(
                    Modifier
                        .width(330.dp)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(horizontal = 16.dp, vertical = 30.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        modifier = Modifier,
//                    text = viewModel.valueEulerDic.toString(),
                        text = stringResource(id = R.string.please_it_up),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFF2B56D7),
                            letterSpacing = 0.49.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    Text(
                        text = stringResource(id = R.string.cervical_comprehensive_evaluation),
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF464646),
                            textAlign = TextAlign.Center,
                            letterSpacing = 0.63.sp,
                        )
                    )
                    Text(
                        text = stringResource(id = R.string.your_score, viewModel.score.value),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF464646),
                            textAlign = TextAlign.Center,
                            letterSpacing = 0.63.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                    Spacer(modifier = Modifier.height(15.dp))
                    Text(
                        modifier = Modifier
                            .clickable {
                                viewModel.finishedDialogVisible = false
                                onStart()
                            },
                        text = stringResource(id = R.string.enter_part_four),
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF2B56D7),
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                }
            }
        }
    }

}

