package org.aihealth.ineck.view.screen

import android.Manifest
import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.Constants.HEIGHT_CM_MIN_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_KG_MIN_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_LB_MIN_LIMIT
import org.aihealth.ineck.ui.theme.Sizes.actionBarHeight
import org.aihealth.ineck.ui.theme.Sizes.actionHorizontalPadding
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.UnitUtil.convertCmToInches
import org.aihealth.ineck.util.UnitUtil.convertInchesToCm
import org.aihealth.ineck.util.UnitUtil.convertKgToLbs
import org.aihealth.ineck.util.UnitUtil.convertLbsToKg
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeDate
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.dialog.ChooseBottomDialog
import org.aihealth.ineck.view.screen.info.HeightUnit
import org.aihealth.ineck.view.screen.info.HeightVerticalPickerWithInputDialog
import org.aihealth.ineck.view.screen.info.WeightUnit
import org.aihealth.ineck.view.screen.info.WeightVerticalPickerWithInputDialog
import org.aihealth.ineck.viewmodel.PersonalDataViewModel
import org.aihealth.ineck.viewmodel.user
import java.lang.Integer.min
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import java.util.TimeZone


@Composable
fun PersonalDataRoute(
    viewModel: PersonalDataViewModel = viewModel()
) {
    var heightUnit by remember(user.preferences.heightUnit) {
        mutableStateOf(user.preferences.heightUnit)
    }
    var weightUnit by remember(user.preferences.weightUnit) {
        mutableStateOf(user.preferences.weightUnit)
    }

    var birthdate by remember { mutableStateOf("") }
    var mobileNumberState by remember(user.phone) { mutableStateOf(user.phone ?: "") }

    val calendar by rememberUpdatedState(user.birthdate.let {
        Calendar.getInstance().apply {
            if (it.isNotBlank()) {
                try {
                    // 解析服务器返回的 UTC 时间
                    val utcFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
                    utcFormat.timeZone = TimeZone.getTimeZone("UTC")
                    val utcDate = utcFormat.parse(it)

                    if (utcDate != null) {
                        // 获取 UTC 日期的年月日
                        val utcCalendar = Calendar.getInstance(TimeZone.getTimeZone("UTC")).apply {
                            time = utcDate
                        }
                        // 设置为本地时区的同一日期
                        set(Calendar.YEAR, utcCalendar.get(Calendar.YEAR))
                        set(Calendar.MONTH, utcCalendar.get(Calendar.MONTH))
                        set(Calendar.DAY_OF_MONTH, utcCalendar.get(Calendar.DAY_OF_MONTH))
                        set(Calendar.HOUR_OF_DAY, 0)
                        set(Calendar.MINUTE, 0)
                        set(Calendar.SECOND, 0)
                        set(Calendar.MILLISECOND, 0)
                    } else {
                        birthdate = ""
                    }
                } catch (e: Exception) {
                    try {
                        // 尝试解析简单日期格式
                        time = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).parse(it)!!
                    } catch (e: Exception) {
                        birthdate = ""
                    }
                }
            } else {
                birthdate = ""
            }
        }
    })
    birthdate = try {
        if (user.birthdate.isNotBlank()) {
            calendar.localeDate
        } else {
            ""
        }
    } catch (e: Exception) {
        ""
    }

    PersonalDataScreen(
        uuid = user.uuid,
        userId = user.userId,
        name = user.name,
        photo = user.photo,
        gender = when (user.gender) {
            "M" -> stringResource(R.string.male)
            "F" -> stringResource(R.string.female)
            else -> stringResource(R.string.unknown)
        },
        birthdate = birthdate,
        mobileNumber = mobileNumberState,
        height = user.height,
        heightUnit = heightUnit,
        weight = user.weight,
        weightUnit = weightUnit,
        email = user.email,
        address = user.address,
        ssn = user.ssn,
        medicalHistory = user.medicalHistory,
        toCamera = viewModel::getPictureFromCamera,
        getPictureFromAlbum = viewModel::getPictureFromAlbum,
        setBirthdate = viewModel::setBirthdate,
        setMobileNumber = { newNumber ->
            mobileNumberState = newNumber
            viewModel.setMobileNumber(newNumber)
        },
        setHeight = {
            heightUnit = it.unit
            viewModel.setHeight(it)
        },
        setWeight = {
            weightUnit = it.unit
            viewModel.setWeight(it)
        }
    )
}

@SuppressLint("SimpleDateFormat")
@Composable
fun PersonalDataScreen(
    uuid: String,
    userId: String,
    name: String,
    photo: String,
    gender: String,
    birthdate: String,
    mobileNumber: String,
    email: String,
    address: String,
    height: Double,
    heightUnit: String,
    weight: Double,
    weightUnit: String,
    ssn: String,
    medicalHistory: String,
    toCamera: () -> Unit,
    getPictureFromAlbum: () -> Unit,
    setBirthdate: (Long) -> Unit,
    setMobileNumber: (String) -> Unit,
    setHeight: (HeightUnit) -> Unit,
    setWeight: (WeightUnit) -> Unit,
) {
    val context = LocalContext.current
    val locale = LocalConfiguration.current.locales[0]

    val heightString = remember(heightUnit, height) {
        derivedStateOf {
            when (heightUnit) {
                "M" -> {
                    if (height >= HEIGHT_CM_MIN_LIMIT) String.format(
                        locale,
                        "%.1f %s",
                        height,
                        context.getString(R.string.height_unit_cm)
                    )
                    else ""
                }

                "I" -> {
                    if (height >= HEIGHT_CM_MIN_LIMIT) {
                        val tmp = (height * 0.393701)
                        String.format(
                            locale,
                            "%.1f %s",
                            tmp,
                            context.getString(R.string.inch)
                        )
                    } else {
                        ""
                    }
                }

                else -> ""
            }
        }
    }
    val weightString by remember(weightUnit, weight) {
        derivedStateOf {
            when (weightUnit) {
                "M" -> {
                    if (user.weight >= WEIGHT_KG_MIN_LIMIT)
                        String.format(
                            locale,
                            "%.1f %s",
                            user.weight,
                            context.getString(R.string.weight_unit_kg)
                        )
                    else ""
                }

                "I" -> {
                    if (user.weight * 2.2046226218 >= WEIGHT_LB_MIN_LIMIT)
                        String.format(
                            locale,
                            "%.1f %s",
                            user.weight * 2.2046226218,
                            context.getString(R.string.weight_unit_lb)
                        )
                    else ""
                }

                else -> ""

            }
        }
    }

    LogUtil.i("heightString: $heightString, height: $height, heightUnit: $heightUnit")
    var showPhoneNumberDialog by remember { mutableStateOf(false) }
    /* 升高输入抽屉 */
    var heightBottomVisible by remember {
        mutableStateOf(false)
    }
    /* 体重输入抽屉 */
    var weightBottomVisible by remember {
        mutableStateOf(false)
    }
    var showPermissionDialog by remember {
        mutableStateOf(false)
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .statusBarsPadding()
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(actionBarHeight)
                .background(Color.White)
                .padding(start = actionHorizontalPadding)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.img_back),
                contentDescription = null,
                tint = Color(0xff666666),
                modifier = Modifier
                    .size(24.dp)
                    .align(Alignment.CenterStart)
                    .clickable {
                        finish()
                    }
            )

            Text(
                text = stringResource(id = R.string.personal_information),
                fontSize = 22.sp,
                color = Color(0xFF444444),
                modifier = Modifier.align(
                    Alignment.Center
                ),
                fontWeight = FontWeight.SemiBold
            )
        }
        Column(Modifier.fillMaxSize()) {
            // Remove AIHCard wrapper and use direct Column
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White)
            ) {
                var avatarBottomDialogVisible by remember {
                    mutableStateOf(false)
                }
                ChooseBottomDialog(
                    visible = avatarBottomDialogVisible,
                    array = stringArrayResource(id = R.array.camera),
                    onClick = {
                        when (it) {
                            0 -> {
                                if (ActivityResultUtils.checkPermissions(arrayOf(Manifest.permission.CAMERA))) {
                                    toCamera()
                                } else {
                                    showPermissionDialog = true
                                }
                            }

                            1 -> {
                                getPictureFromAlbum()
                            }
                        }
                        avatarBottomDialogVisible = false
                    },
                    onCancel = {
                        avatarBottomDialogVisible = false
                    }
                )

                // Profile Picture Item with larger size
                Item(
                    modifer = Modifier.height(80.dp), // Made taller for better proportion
                    title = stringResource(id = R.string.profile_picture),
                    onClick = {
                        getPictureFromAlbum()
                    }
                ) {
                    Box(
                        modifier = Modifier
                            .size(60.dp) // Slightly smaller but still prominent
                            .clip(CircleShape)
                            .background(Color(0x70CECECE)),
                    ) {
                        AsyncImage(
                            model = ImageRequest.Builder(LocalContext.current)
                                .data(photo)
                                .crossfade(true)
                                .build(),
                            contentDescription = null,
                            modifier = Modifier
                                .align(Alignment.Center)
                                .size(60.dp)
                                .border(
                                    border = BorderStroke(
                                        width = 1.dp,
                                        color = Color(0x30CECECE),
                                    ),
                                    shape = CircleShape,
                                )
                                .clip(CircleShape),
                            contentScale = ContentScale.Crop,
                            placeholder = painterResource(R.drawable.header_2),
                            error = painterResource(R.drawable.header_2)
                        )
                    }
                }

                Item(
                    title = stringResource(id = R.string.name),
                    onClick = {
                        startScreen(Screen.NameEdit.route)
                    }
                ) {
                    Text(
                        text = name.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp, // Slightly larger
                        color = if (name.isNotBlank()) Color(0xFF666666) else Color(0xFF999999),
                        fontWeight = if (name.isNotBlank()) FontWeight.Medium else FontWeight.Normal

                    )
                }

                Item(
                    title = stringResource(id = R.string.uuid),
                    nextIconVisible = false, // No arrow for read-only fields
                    onClick = {
                        if (uuid.isNotBlank()) {
                            // Copy UUID to clipboard
                            val clipboard =
                                context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val clip = ClipData.newPlainText("UUID", uuid)
                            clipboard.setPrimaryClip(clip)
                            DialogUtil.showToast(context.getString(R.string.uuid_copied))
                        }
                    }
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = uuid.ifBlank { stringResource(R.string.please_enter) },
                            fontSize = 14.sp,
                            color = if (uuid.isNotBlank()) Color(0xFF666666) else Color(0xFF999999),
                            fontWeight = FontWeight.Normal
                        )
                        if (uuid.isNotBlank()) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Icon(
                                painter = painterResource(id = R.drawable.ic_copy),
                                contentDescription = "Copy UUID",
                                tint = Color(0XFF999999),
                                modifier = Modifier.size(12.dp)
                            )
                        }
                    }
                }

                Item(
                    title = stringResource(id = R.string.user_id),
                    nextIconVisible = false, // No arrow for read-only fields
                    onClick = {
                        if (userId.isNotBlank()) {
                            // Copy User ID to clipboard
                            val clipboard =
                                context.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                            val clip = ClipData.newPlainText("User ID", userId)
                            clipboard.setPrimaryClip(clip)
                            DialogUtil.showToast(context.getString(R.string.user_id_copied))
                        }
                    }
                ) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Text(
                            text = userId.ifBlank { stringResource(R.string.please_enter) },
                            fontSize = 14.sp,
                            color = if (userId.isNotBlank()) Color(0xFF666666) else Color(0xFF999999),
                            fontWeight = FontWeight.Normal
                        )
                        if (userId.isNotBlank()) {
                            Spacer(modifier = Modifier.width(8.dp))
                            Icon(
                                painter = painterResource(id = R.drawable.ic_copy),
                                contentDescription = "Copy User ID",
                                tint = Color(0XFF999999),
                                modifier = Modifier.size(12.dp)
                            )
                        }
                    }
                }

                Item(
                    title = stringResource(id = R.string.gender),
                    onClick = {
                        startScreen(Screen.GenderEdit.route)
                    }
                ) {
                    Text(
                        text = gender,
                        fontSize = 14.sp,
                        color = Color(0xFF666666),
                        fontWeight = FontWeight.Medium
                    )
                }

                Item(
                    title = stringResource(id = R.string.birthdate),
                    onClick = {
                        startScreen(Screen.BirthdateEdit.route)
                    }
                ) {
                    Text(
                        text = birthdate.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp,
                        color = if (birthdate.isNotBlank()) Color(0xFF666666) else Color(0xFF999999),
                        fontWeight = if (birthdate.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                Item(
                    title = stringResource(id = R.string.phone),
                    onClick = {
                        startScreen(Screen.PhoneEdit.route)
                    }
                ) {
                    Text(
                        text = mobileNumber.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp,
                        color = if (mobileNumber.isNotBlank()) Color(0xFF666666) else Color(
                            0xFF999999
                        ),
                        fontWeight = if (mobileNumber.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                // Add Email item
                Item(
                    title = stringResource(id = R.string.e_mail),
                    onClick = {
                        startScreen(Screen.EmailEdit.route)
                    }
                ) {
                    Text(
                        text = email.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp,
                        color = if (email.isNotBlank()) Color(0xFF666666) else Color(
                            0xFF999999
                        ),
                        fontWeight = if (email.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                // Add Address item (placeholder)
                Item(
                    title = stringResource(id = R.string.address),
                    onClick = { /* TODO: Add address edit functionality */ }
                ) {
                    Text(
                        text = address.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp,
                        color = if (address.isNotBlank()) Color(0xFF666666) else Color(
                            0xFF999999
                        ),
                        fontWeight = if (address.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                Item(
                    title = stringResource(id = R.string.height),
                    onClick = {
                        heightBottomVisible = true
                    }
                ) {
                    Text(
                        text = heightString.value.ifBlank {
                            stringResource(
                                R.string.please_enter
                            )
                        },
                        fontSize = 14.sp,
                        color = if (heightString.value.isNotBlank()) Color(0xFF666666) else Color(
                            0xFF999999
                        ),
                        fontWeight = if (heightString.value.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                Item(
                    title = stringResource(id = R.string.weight),
                    onClick = {
                        weightBottomVisible = true
                    }
                ) {
                    Text(
                        text = weightString.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp,
                        color = if (weightString.isNotBlank()) Color(0xFF666666) else Color(
                            0xFF999999
                        ),
                        fontWeight = if (weightString.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                // Add Social Security Code item (placeholder)
                Item(
                    title = stringResource(id = R.string.social_security_code),
                    onClick = { /* TODO: Add social security code edit functionality */ }
                ) {
                    Text(
                        text = ssn.ifBlank { stringResource(R.string.please_enter) },
                        fontSize = 14.sp,
                        color = if (ssn.isNotBlank()) Color(0xFF666666) else Color(
                            0xFF999999
                        ),
                        fontWeight = if (ssn.isNotBlank()) FontWeight.Medium else FontWeight.Normal
                    )
                }

                // Add Medical History item (placeholder)
                Item(
                    title = stringResource(id = R.string.medical_history),
                    nextIconVisible = false, // We'll show dropdown arrow instead
                    onClick = { /* TODO: Add medical history functionality */ }
                ){}
            }
        }
//        InputNameBottomDialog(
//            visible = nameBottomVisible,
//            title = stringResource(id = R.string.name),
//            defaultValue = user.name,
//            placeholder = localeResources.getString(R.string.input_name),
//            onClick = {
//                setName(it)
//                nameBottomVisible = false
//            },
//            onCancel = {
//                nameBottomVisible = false
//            }
//        )


        HeightVerticalPickerWithInputDialog(
            visible = heightBottomVisible,
            initialHeight = user.height,
            selectedUnit = heightUnit,
            onHeightSelected = {
                setHeight(it)
                heightBottomVisible = false
            },
            onDismiss = { heightBottomVisible = false }
        )


        WeightVerticalPickerWithInputDialog(
            visible = weightBottomVisible,
            initialWeight = user.weight,
            selectedUnit = weightUnit,
            onWeightSelected = {
                setWeight(it)
                weightBottomVisible = false
            },
            onDismiss = { weightBottomVisible = false }
        )

        PhoneNumberEditDialog(
            visible = showPhoneNumberDialog,
            initialPhoneNumber = mobileNumber,
            onConfirm = {
                setMobileNumber(it)
                showPhoneNumberDialog = false
            },
            onDismiss = { showPhoneNumberDialog = false }
        )

    }

    PermissionGrantDialog(
        visible = showPermissionDialog,
        toCamera = toCamera,
        onDismiss = { showPermissionDialog = false }
    )
}

@Composable
private fun ColumnScope.Item(
    modifer: Modifier = Modifier,
    title: String,
    nextIconVisible: Boolean = true,
    onClick: () -> Unit = {},
    content: @Composable RowScope.() -> Unit
) {
    Box(
        modifier = modifer
            .fillMaxWidth()
            .height(52.dp)
            .background(Color.White)
            .clickable {
                onClick()
            }
            .padding(start = 16.dp, end = 20.dp)) {
        Text(
            text = title,
            fontSize = 14.sp,
            color = Color(0xFF666666),
            modifier = Modifier.align(
                Alignment.CenterStart
            ),
            fontWeight = FontWeight.SemiBold
        )
        Row(Modifier.align(Alignment.CenterEnd), verticalAlignment = Alignment.CenterVertically) {
            content()
            if (nextIconVisible) {
                Spacer(modifier = Modifier.width(5.dp))
                Icon(
                    painter = painterResource(id = R.drawable.img_next),
                    contentDescription = null,
                    tint = Color(0XFFD6D6D6),
                    modifier = Modifier.size(12.dp)
                )
            }

        }
    }
    AIHDivider()
}

@Composable
fun InputNameBottomDialog(
    visible: Boolean,
    title: String,
    defaultValue: String = "",
    placeholder: String = "",
    onClick: (String) -> Unit,
    onCancel: () -> Unit
) {
    if (visible) {
        AIHBottomSheet(onDismissRequest = { onCancel() }) {
            var value by remember {
                mutableStateOf(defaultValue)
            }
            val focusRequester = remember { FocusRequester() }
            Row(
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(horizontal = 20.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = title)
                Spacer(modifier = Modifier.width(50.dp))
                BasicTextField(
                    value = value,
                    onValueChange = {
                        value = it.let { it.substring(0, min(20, it.length)) }
                    },
                    decorationBox = {
                        if (value.isEmpty()) {
                            Text(text = placeholder)
                        }
                        it()
                    },
                    modifier = Modifier
                        .weight(1F)
                        .focusRequester(focusRequester)
                )
                Text(
                    text = localeResources.getString(R.string.confirm),
                    modifier = Modifier.pointerInput(Unit) {
                        detectTapGestures {
                            if (value.isNotEmpty()) {
                                onClick(value)
                            }
                        }
                    })
            }
            SideEffect {
                focusRequester.requestFocus()
            }
        }
    }
}

@Composable
fun InputNumberBottomDialog(
    visible: Boolean,
    title: String,
    defaultValue: String = "",
    placeholder: String = "",
    endText: List<String> = listOf(""),
    onClick: (String) -> Unit,
    onCancel: () -> Unit,
) {
    if (visible) {
        AIHBottomSheet(onDismissRequest = { onCancel() }) {
            var value by remember {
                mutableStateOf(defaultValue)
            }
            val focusRequester = remember { FocusRequester() }
            var expanded by remember { mutableStateOf(false) }
            var selectedOption by remember { mutableIntStateOf(0) }

            Row(
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(horizontal = 20.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = title)
                Spacer(modifier = Modifier.width(10.dp))
                BasicTextField(
                    value = value,
                    onValueChange = { it ->
//                        value = it.filter { it.isDigit() }.let { it.substring(0, min(3,it.length)) }
                        val pattern = "^\\d{0,3}(\\.\\d{0,2})?\$".toRegex()
                        value = it.filter { it.isDigit() || it == '.' }
                        value = pattern.find(value)?.value ?: ""
                        LogUtil.i("firstUpdateData:value $value")
                    },
                    decorationBox = {
                        if (value.isEmpty()) {
                            Text(text = placeholder)
                        }
                        it()
                    },
                    modifier = Modifier.focusRequester(focusRequester),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone)
                )
                Box(
                    modifier = Modifier
                        .padding(start = 2.dp)
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .clickable {
                            expanded = true
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = endText[selectedOption], fontSize = 16.sp, color = Color.Black)
                    DropdownMenu(
                        modifier = Modifier.background(Color.White),
                        expanded = expanded,
                        onDismissRequest = {
                            expanded = false
                        },
                    ) {
                        endText.forEachIndexed { index, s ->
                            DropdownMenuItem(
                                colors = MenuDefaults.itemColors(
                                    textColor = Color.Black,
                                    leadingIconColor = Color.White,
                                    trailingIconColor = Color.White,
                                    disabledTextColor = Color.White,
                                    disabledLeadingIconColor = Color.White,
                                    disabledTrailingIconColor = Color.White,
                                ),
                                text = { Text(text = s) },
                                onClick = {
                                    selectedOption = index
                                    expanded = false
                                }
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.weight(1F))
                Text(
                    text = localeResources.getString(R.string.confirm),
                    modifier = Modifier.pointerInput(Unit) {
                        detectTapGestures {
                            if (value.isNotEmpty()) {
                                onClick("$value+$selectedOption")
                            }
                        }
                    })
            }
            SideEffect {
                focusRequester.requestFocus()
            }
        }
    }
}

/**
 * 是否重新校准弹窗
 */
@Composable
private fun PermissionGrantDialog(
    visible: Boolean,
    toCamera: () -> Unit,
    onDismiss: () -> Unit,
) {
    if (visible) {
        Dialog(onDismissRequest = { onDismiss() }) {
            Column(
                modifier = Modifier
                    .width(300.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White)
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.camera_permission),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 40.dp),
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .heightIn(min = 40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                onDismiss()
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                onDismiss()
                                toCamera()
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun InputWeightBottomDialog(
    visible: Boolean,
    title: String,
    defaultValue: String = "",
    placeholder: String = "",
    selected: Int = 0,
    onClick: (String) -> Unit,
    onCancel: () -> Unit,
) {
    if (visible) {
        AIHBottomSheet(onDismissRequest = { onCancel() }) {
            val focusRequester = remember { FocusRequester() }
            var expanded by remember { mutableStateOf(false) }
            val weightUnit = listOf("kg", "lbs")
            var selectedOption by remember { mutableIntStateOf(selected) }
            var value by remember {
                mutableStateOf(defaultValue)
            }
            LogUtil.i("weight init: $value")
            Row(
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(horizontal = 20.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = title)
                Spacer(modifier = Modifier.width(10.dp))
                BasicTextField(
                    value = value,
                    onValueChange = { it ->
//                        value = it.filter { it.isDigit() }.let { it.substring(0, min(3,it.length)) }
                        val pattern = "^\\d{0,3}(\\.\\d{0,2})?\$".toRegex()
                        value = it.filter { it.isDigit() || it == '.' }
                        value = pattern.find(value)?.value ?: ""
                        LogUtil.i("firstUpdateData:value $value")
                    },
                    decorationBox = {
                        if (value.isEmpty()) {
                            Text(text = "$placeholder  ")
                        }
                        it()
                    },
                    modifier = Modifier.focusRequester(focusRequester),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone)
                )
                Box(
                    modifier = Modifier
                        .padding(start = 2.dp)
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .clickable {
                            expanded = true
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = weightUnit[selectedOption], fontSize = 16.sp, color = Color.Black)
                    DropdownMenu(
                        modifier = Modifier.background(Color.White),
                        expanded = expanded,
                        onDismissRequest = {
                            expanded = false
                        },
                    ) {
                        weightUnit.forEachIndexed { index, s ->
                            DropdownMenuItem(
                                colors = MenuDefaults.itemColors(
                                    textColor = Color.Black,
                                    leadingIconColor = Color.White,
                                    trailingIconColor = Color.White,
                                    disabledTextColor = Color.White,
                                    disabledLeadingIconColor = Color.White,
                                    disabledTrailingIconColor = Color.White,
                                ),
                                text = { Text(text = s) },
                                onClick = {
                                    if (selectedOption != index && weightUnit[index] == "kg" && value.trim()
                                            .isNotEmpty()
                                    ) {
                                        value = convertLbsToKg(value.trim().toDouble()).toString()
                                    }
                                    if (selectedOption != index && weightUnit[index] == "lbs" && value.trim()
                                            .isNotEmpty()
                                    ) {
                                        value = convertKgToLbs(value.trim().toDouble()).toString()
                                    }

                                    LogUtil.i("weight select : $value")
                                    selectedOption = index
                                    expanded = false
                                }
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.weight(1F))
                Text(
                    text = localeResources.getString(R.string.confirm),
                    modifier = Modifier.pointerInput(Unit) {
                        detectTapGestures {
                            if (value.isNotEmpty()) {
                                onClick("$value+$selectedOption")
                            }
                        }
                    })
            }
            SideEffect {
                focusRequester.requestFocus()
            }
        }
    }
}

@Composable
fun InputHeightBottomDialog(
    visible: Boolean,
    title: String,
    defaultValue: String = "",
    placeholder: String = "",
    selected: Int = 0,
    onClick: (String) -> Unit,
    onCancel: () -> Unit,
) {
    if (visible) {
        AIHBottomSheet(onDismissRequest = { onCancel() }) {
            val focusRequester = remember { FocusRequester() }
            var expanded by remember { mutableStateOf(false) }
            val heightUnit = listOf("cm", "inches")
            var selectedOption by remember { mutableIntStateOf(selected) }
            var value by remember {
                mutableStateOf(defaultValue)
            }
            LogUtil.i("Height init: $value")
            Row(
                Modifier
                    .imePadding()
                    .fillMaxWidth()
                    .height(60.dp)
                    .padding(horizontal = 20.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = title)
                Spacer(modifier = Modifier.width(10.dp))
                BasicTextField(
                    value = value,
                    onValueChange = { it ->
//                        value = it.filter { it.isDigit() }.let { it.substring(0, min(3,it.length)) }
                        val pattern = "^\\d{0,3}(\\.\\d{0,2})?\$".toRegex()
                        value = it.filter { it.isDigit() || it == '.' }
                        value = pattern.find(value)?.value ?: ""
                        LogUtil.i("firstUpdateData:value $value")
                    },
                    decorationBox = {
                        if (value.isEmpty()) {
                            Text(text = "$placeholder  ")
                        }
                        it()
                    },
                    modifier = Modifier.focusRequester(focusRequester),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone)
                )
                Box(
                    modifier = Modifier
                        .padding(start = 2.dp)
                        .wrapContentWidth()
                        .wrapContentHeight()
                        .clip(RoundedCornerShape(2.dp))
                        .background(Color.White)
                        .clickable {
                            expanded = true
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = heightUnit[selectedOption], fontSize = 16.sp, color = Color.Black)
                    DropdownMenu(
                        modifier = Modifier.background(Color.White),
                        expanded = expanded,
                        onDismissRequest = {
                            expanded = false
                        },
                    ) {
                        heightUnit.forEachIndexed { index, s ->
                            DropdownMenuItem(
                                colors = MenuDefaults.itemColors(
                                    textColor = Color.Black,
                                    leadingIconColor = Color.White,
                                    trailingIconColor = Color.White,
                                    disabledTextColor = Color.White,
                                    disabledLeadingIconColor = Color.White,
                                    disabledTrailingIconColor = Color.White,
                                ),
                                text = { Text(text = s) },
                                onClick = {
                                    if (selectedOption != index && heightUnit[index] == "cm" && value.trim()
                                            .isNotEmpty()
                                    ) {
                                        value =
                                            convertInchesToCm(value.trim().toDouble()).toString()
                                    }
                                    if (selectedOption != index && heightUnit[index] == "inches" && value.trim()
                                            .isNotEmpty()
                                    ) {
                                        value =
                                            convertCmToInches(value.trim().toDouble()).toString()
                                    }

                                    LogUtil.i("height select : $value")
                                    selectedOption = index
                                    expanded = false
                                }
                            )
                        }
                    }
                }
                Spacer(modifier = Modifier.weight(1F))
                Text(
                    text = localeResources.getString(R.string.confirm),
                    modifier = Modifier.pointerInput(Unit) {
                        detectTapGestures {
                            if (value.isNotEmpty()) {
                                onClick("$value+$selectedOption")
                            }
                        }
                    })
            }
            SideEffect {
                focusRequester.requestFocus()
            }
        }
    }
}

@Preview()
@Composable
fun NameEditDialog(
    visible: Boolean = true,
    initialName: String = "",
    onConfirm: (String) -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    var name by remember { mutableStateOf(initialName) }
    if (visible) {
        Dialog(onDismissRequest = onDismiss) {
            Surface(
                shape = MaterialTheme.shapes.medium,
                tonalElevation = 8.dp,
                modifier = Modifier.padding(16.dp),
                color = Color.White
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                    Text(
                        text = stringResource(R.string.edit_name),
                        style = MaterialTheme.typography.titleLarge
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    OutlinedTextField(
                        value = name,
                        onValueChange = { name = it },
                        label = { Text(stringResource(R.string.name)) },
                        singleLine = true,
                        modifier = Modifier.fillMaxWidth()
                    )

                    Spacer(modifier = Modifier.height(24.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = {
                                onDismiss()
                            }
                        ) {
                            Text(
                                text = stringResource(id = R.string.cancel),
                                color = Color(0xFFDEDEDE)
                            )
                        }

                        Spacer(modifier = Modifier.width(8.dp))
                        TextButton(
                            onClick = {
                                onConfirm(name)
                            }
                        ) {
                            Text(
                                text = stringResource(id = R.string.confirm),
                                color = Color(0XFF73C5FF)
                            )
                        }
                    }
                }
            }
        }
    }
}

// Define country codes (can be moved elsewhere)
// Consider a more comprehensive list and loading from resources
private val countryCodes = listOf(
    Pair("CN", "+86"),
    Pair("US", "+1"),
    Pair("GB", "+44"),
    Pair("HK", "+852"),
    Pair("TW", "+886"),
    // Add more countries as needed
)

// Function to find the best matching code and split the number
private fun splitPhoneNumber(fullNumber: String): Pair<String, String> {
    val bestMatch = countryCodes.filter { fullNumber.startsWith(it.second) }
        .maxByOrNull { it.second.length }
    return if (bestMatch != null) {
        Pair(bestMatch.second, fullNumber.substring(bestMatch.second.length))
    } else {
        // Default or fallback if no code matches
        Pair(
            countryCodes.first().second,
            fullNumber.filter { it.isDigit() }) // Default to first code, take only digits
    }
}

@Preview()
@Composable
fun PhoneNumberEditDialog(
    visible: Boolean = true,
    initialPhoneNumber: String = "",
    onConfirm: (String) -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    // State for selected country code and national number
    var selectedCountryCode by remember { mutableStateOf(countryCodes.first().second) }
    var nationalNumber by remember { mutableStateOf("") }
    var countryCodeDropdownExpanded by remember { mutableStateOf(false) }

    // Effect to parse initial number when dialog becomes visible or initial number changes
    LaunchedEffect(visible, initialPhoneNumber) {
        if (visible) {
            val (code, number) = splitPhoneNumber(initialPhoneNumber)
            selectedCountryCode = code
            nationalNumber = number
        }
    }

    if (visible) {
        Dialog(onDismissRequest = onDismiss) {
            Surface(
                shape = MaterialTheme.shapes.medium,
                tonalElevation = 8.dp,
                modifier = Modifier.padding(16.dp),
                color = Color.White
            ) {
                Column(
                    modifier = Modifier.padding(24.dp)
                ) {
                    Text(
                        text = stringResource(R.string.edit_phone_number),
                        style = MaterialTheme.typography.titleLarge
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // Row for Country Code Dropdown and National Number Input
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        // Country Code Selector Button + Dropdown
                        Box {
                            TextButton(
                                onClick = { countryCodeDropdownExpanded = true },
                                contentPadding = PaddingValues(horizontal = 8.dp, vertical = 4.dp)
                            ) {
                                Text(selectedCountryCode)
                                Icon(
                                    painter = painterResource(id = R.drawable.baseline_arrow_drop_down_24),
                                    contentDescription = "Select Country Code",
                                    modifier = Modifier.size(20.dp)
                                )
                            }

                            DropdownMenu(
                                expanded = countryCodeDropdownExpanded,
                                onDismissRequest = { countryCodeDropdownExpanded = false }
                            ) {
                                countryCodes.forEach { (name, code) ->
                                    DropdownMenuItem(
                                        text = { Text("$name ($code)") },
                                        onClick = {
                                            selectedCountryCode = code
                                            countryCodeDropdownExpanded = false
                                        }
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.width(8.dp))

                        // National Number Input
                        OutlinedTextField(
                            value = nationalNumber,
                            onValueChange = { newValue ->
                                // Allow only digits for the national number
                                nationalNumber = newValue.filter { it.isDigit() }
                                    .take(15) // Adjust max length if needed
                            },
                            label = { Text(stringResource(R.string.phone_number)) }, // Reuse existing string
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                            singleLine = true,
                            modifier = Modifier.weight(1.0f) // Take remaining space
                        )
                    }

                    Spacer(modifier = Modifier.height(24.dp))

                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.End
                    ) {
                        TextButton(
                            onClick = { onDismiss() }
                        ) {
                            Text(
                                text = stringResource(id = R.string.cancel),
                                color = Color(0xFFDEDEDE)
                            )
                        }

                        Spacer(modifier = Modifier.width(8.dp))
                        val context = LocalContext.current
                        TextButton(
                            onClick = {
                                // Check if national number part is not empty
                                if (nationalNumber.isNotBlank()) {
                                    onConfirm("$selectedCountryCode$nationalNumber")
                                } else {
                                    // Optional: Show error toast - "Please enter phone number"
                                    DialogUtil.showToast(context.getString(R.string.please_input_phone_number))
                                }
                            }
                        ) {
                            Text(
                                text = stringResource(id = R.string.confirm),
                                color = Color(0XFF73C5FF)
                            )
                        }
                    }
                }
            }
        }
    }
}
