package org.aihealth.ineck.view.screen.exercise


import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.ActivityInfo
import android.graphics.Bitmap
import android.media.MediaFormat
import android.view.TextureView
import android.view.WindowManager
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.animation.slideOutVertically
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.List
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.drawText
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.LifecycleOwner
import androidx.media3.common.Format
import androidx.media3.common.Player
import androidx.media3.common.Player.Listener
import androidx.media3.common.Player.STATE_ENDED
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.DefaultMediaSourceFactory
import androidx.media3.exoplayer.video.VideoFrameMetadataListener
import androidx.media3.ui.AspectRatioFrameLayout.RESIZE_MODE_FIT
import androidx.media3.ui.PlayerView
import coil.compose.AsyncImage
import com.google.accompanist.systemuicontroller.rememberSystemUiController
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.ExerciseResult
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.formatTime
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.videoUtils.VideoDownloadUtil
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.screen.MuteButton
import org.aihealth.ineck.viewmodel.ImproveDetailViewModel
import org.aihealth.ineck.viewmodel.MainViewModel
import kotlin.math.ceil


@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
@Composable
fun TrainingScreen(
    viewModel: ImproveDetailViewModel,
    mainViewModel: MainViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current
) {
    DisposableEffect(Unit) {
        activity.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        // Clean up by clearing the flag when the Composable leaves the composition
        onDispose {
            activity.window.clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }
    /** 获取屏幕长宽高 */
    val configuration = LocalConfiguration.current
    val screenHeight = configuration.screenHeightDp.dp
    val screenWidth = configuration.screenWidthDp.dp
    val context = LocalContext.current
    val playerView = remember {
        TextureView(context)
    }
    val ioScope = CoroutineScope(Dispatchers.IO)
    val imageCapture = remember {
        ImageCapture.Builder()
            .build()
    }
    val exoPlayer = rememberExoPlayer(
        context = context,
        playerListener = viewModel.listener,
        videoFrameMetadataListener = object : VideoFrameMetadataListener {
            override fun onVideoFrameAboutToBeRendered(
                presentationTimeUs: Long,
                releaseTimeNs: Long,
                format: Format,
                mediaFormat: MediaFormat?
            ) {
                ioScope.launch {
                    if (viewModel.duration.contains(presentationTimeUs)) {
                        LogUtil.i("presentationTimeUs:$presentationTimeUs")
                        if (playerView.bitmap != null) {
                            val bitmap: Bitmap? =
                                playerView.bitmap?.copy(Bitmap.Config.ARGB_8888, false)
                            viewModel.getVideoFrame(
                                bitmap!!,
                                presentationTimeUs
                            )
                        }
                    }
                }
                ioScope.launch {
                    if (viewModel.durationList.contains(presentationTimeUs)) {
                        LogUtil.i("presentationTimeUs:$presentationTimeUs")
                        viewModel.savePeopleFace(imageCapture, presentationTimeUs)
                    }
                }

            }
        },
    )

    val isVisiblePlayerList = viewModel.isVisiblePlayList.collectAsState()
    LogUtil.i("videList the init value:${isVisiblePlayerList.value}")
    activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
    // 为了避免在重新组合时丢失系统 UI 控制器的状态，rememberSystemUiController() 来在组合函数中保留该状态。
    val systemUiController = rememberSystemUiController()
    // 当前跳转的目标界面不是轮播页时，显示状态栏，在本组合被清理时执行
    systemUiController.isStatusBarVisible = true

    LaunchedEffect(exoPlayer) {
        LogUtil.d("load exoplayer mediates")
        viewModel.setMediaList()
//        viewModel.setRetrieverSource(0)
        if (!viewModel.isPlaySub.value) {
            exoPlayer.apply {
                setMediaItems(viewModel.downloadMediaItems)
                LogUtil.i("init exoplayer seekTo:0,0")
                seekTo(0, 0)
                repeatMode = Player.REPEAT_MODE_OFF
                prepare()
                exoPlayer.playWhenReady = true
            }
        } else {
            exoPlayer.apply {
                setMediaItems(viewModel.downloadMediaItems)
                LogUtil.i("init exoplayer seekTo:${viewModel.id.value},0")
                seekTo(viewModel.id.value, 0)
                repeatMode = Player.REPEAT_MODE_OFF
                prepare()

            }
        }
        LogUtil.i("load the mediaItems index:${viewModel.id.value}, isPlaySub:${viewModel.isPlaySub.value}")
    }
    LaunchedEffect(key1 = exoPlayer) {
        LogUtil.d("change exoplayer,currentTime:${viewModel.currentTime}")
        exoPlayer.setVideoTextureView(playerView)
        exoPlayer.prepare()
        if (viewModel.currentTime != 0L) {
            if (!viewModel.isPlaySub.value) {
                exoPlayer.seekTo(viewModel.currentTime)
            } else {
                exoPlayer.seekTo(viewModel.id.value, viewModel.currentTime)
            }
        } else {
            if (!viewModel.isPlaySub.value) {
                exoPlayer.seekTo(0)
            } else {
                LogUtil.i("seek to id:${viewModel.id.value}")
                exoPlayer.seekTo(viewModel.id.value, 0)
            }
        }

        while (true) {
            if (exoPlayer.isPlaying) {
                viewModel.currentTime = exoPlayer.currentPosition
                viewModel.totalDuration = exoPlayer.duration
                if (!viewModel.isPlaySub.value) {
                    val time = viewModel.videoStartList.firstOrNull {
                        it > exoPlayer.currentPosition / 1000
                    } ?: viewModel.videoStartList.first()
                    val index = viewModel.videoStartList.indexOf(time)
                    viewModel.id.update { if (index > 0) index - 1 else 0 }
                }
            }
            delay(500)
        }
    }

    DisposableEffect(Unit) {
//        viewModel.extractFrames()
        viewModel.finishedDialogVisible = false
        LogUtil.i("DisposableEffect finishedDialogVisible ${viewModel.finishedDialogVisible}")
        onDispose {
            DialogUtil.hideLoading()
            viewModel.finishedDialogVisible = false
            LogUtil.i("DisposableEffect onDispose finishedDialogVisible ${viewModel.finishedDialogVisible}")
            viewModel.hasFinishedScore.value = false
            viewModel.videoEulerDic.clear()
            viewModel.valueEulerDic.clear()
            viewModel.count.value = 0
        }
    }
    BasePageView(
        title = stringResource(id = R.string.improve_cervical_spine),
        showBackIcon = true,
        headerContent = {
            val isMuteState = MutableStateFlow(false)
            MuteButton(
                isMuteState = isMuteState.collectAsState(),
                changeMuteState = {
                    isMuteState.value = !isMuteState.value
                    if (exoPlayer.volume != 0f) {
                        viewModel.volume.value = exoPlayer.volume
                        exoPlayer.volume = 0f
                    } else {
                        exoPlayer.volume = viewModel.volume.value
                    }
                },
                modifier = Modifier
                    .padding(16.dp)
                    .size(30.dp)
                    .align(Alignment.TopEnd)
            )
        },
    ) {
        Scaffold(
            containerColor = Color.Transparent,
            contentColor = Color.Transparent,
            floatingActionButton = {
                FloatingActionButton(
                    onClick = {
                        viewModel.isVisiblePlayList.update { !it }
                        LogUtil.i("videList onlick the btn :${viewModel.isVisiblePlayList.value}")
                    },
                    modifier = Modifier
                        .padding(16.dp)
                        .size(30.dp)
                ) {
                    Icon(
                        imageVector = Icons.AutoMirrored.Filled.List,
                        contentDescription = "More",
                        tint = Color.White
                    )
                }
            },
        ) { contentPadding ->
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(contentPadding)
                    .background(Color.White)
            ) {
                if (LocalConfiguration.current.orientation == android.content.res.Configuration.ORIENTATION_PORTRAIT) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        /**
                         * 视频播放器
                         */
                        VerticalVideoPlayer(
                            modifier = Modifier
                                .fillMaxWidth(0.9f),
                            exoPlayer = exoPlayer,
                            textureView = playerView,
                            imageUrl = viewModel.improveProgramDetail.cover,
                            viewModel = viewModel,
                        )

                        /**
                         * 视屏监测
                         */
                        Box(
                            modifier = Modifier.fillMaxWidth(0.9f),
                        ) {
                            CameraView(
                                viewModel = viewModel,
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .padding(vertical = 30.dp)
                                    .aspectRatio(screenWidth / screenHeight),
                                lifecycleOwner = lifecycleOwner,
                                imageCapture = imageCapture,
                                content = {}
                            )

                        }
                    }
                } else {
                    Box(
                        modifier = Modifier.fillMaxSize()
                    ) {

                        HorizontalVideoPlayer(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(end = 25.dp)
                                .fillMaxWidth(0.9f)
                                .fillMaxHeight(0.9f),
                            exoPlayer = exoPlayer,
                            textureView = playerView,
                            viewModel = viewModel
                        )
                        /**
                         * 视屏监测
                         */
                        Box(
                            modifier = Modifier
                                .align(Alignment.Center)
                                .padding(end = 25.dp)
                                .fillMaxWidth(0.9f)
                                .fillMaxHeight(0.9f),
                        ) {
                            CameraView(
                                viewModel = viewModel,
                                modifier = Modifier
                                    .align(Alignment.TopEnd)
                                    .fillMaxHeight(0.5f)
                                    .aspectRatio(screenHeight / screenWidth),
                                lifecycleOwner = lifecycleOwner,
                                imageCapture = imageCapture,
                                content = {}
                            )
                        }
                    }
                }

                /**
                 * 视频播放列表
                 */
                VideoList(
                    modifier = Modifier
                        .fillMaxHeight()
                        .align(Alignment.CenterEnd)
                        .background(Color.Transparent),
                    isVisible = isVisiblePlayerList.value,
                    onclick = {
                        viewModel.isVisiblePlayList.update { !it }
                        LogUtil.i("videList onlick the list :${viewModel.isVisiblePlayList.value}")
                    }
                ) {
                    val model = viewModel.sectionData.value
                    val videoStartList = viewModel.videoStartList
                    val isPlaySub = viewModel.isPlaySub.collectAsState()
                    if (model.sections.isNotEmpty()) {
                        if (!isPlaySub.value) {
                            model.sections.forEachIndexed { index, it ->
                                if (index == viewModel.id.value) {
                                    Icon(
                                        modifier = Modifier.size(12.dp),
                                        painter = painterResource(id = R.drawable.ic_vector),
                                        contentDescription = "vector"
                                    )
                                }
                                Text(
                                    color = if (index == viewModel.id.value) Color.Black else Color.White,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 10.dp)
                                        .clickable {
                                            LogUtil.d("index:${index},play the time:${videoStartList[index] * 1000}")
                                            exoPlayer.seekTo(0, videoStartList[index] * 1000)
                                            exoPlayer.prepare()
                                            exoPlayer.playWhenReady = true
                                            viewModel.id.update { index }
                                            viewModel.isVisiblePlayList.update { false }
                                            LogUtil.i("videList click the list")
                                        },
                                    text = it.title
                                )

                                HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
                            }
                        } else {
                            LogUtil.d("play the Video List")
                            model.sections.forEachIndexed { index, it ->
                                if (index == viewModel.id.value) {
                                    Icon(
                                        modifier = Modifier.size(12.dp),
                                        painter = painterResource(id = R.drawable.ic_vector),
                                        contentDescription = "vector"
                                    )
                                }
                                Text(
                                    color = if (index == viewModel.id.value) Color.Black else Color.White,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(top = 10.dp)
                                        .clickable {
                                            exoPlayer.seekTo(index, 0)
                                            exoPlayer.prepare()
                                            exoPlayer.playWhenReady = true
                                            viewModel.id.update { index }
                                            viewModel.isVisiblePlayList.update { false }
                                        },
                                    text = it.title
                                )

                                HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
                            }
                        }

                    } else {
                        Icon(
                            modifier = Modifier.size(12.dp),
                            painter = painterResource(id = R.drawable.ic_vector),
                            contentDescription = "vector"
                        )
                        Text(
                            color = Color.Black,
                            text = viewModel.improveProgramDetail.title,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 10.dp)

                        )

                        HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
                    }
                }

            }
        }
        FinishedDialog(viewModel, exoPlayer)
    }
}

@Composable
fun FinishedDialog(
    viewModel: ImproveDetailViewModel,
    exoPlayer: ExoPlayer
) {

    val finished = viewModel.hasFinishedScore.collectAsState()
    if (viewModel.finishedDialogVisible) {
        if (finished.value.not()) {
            LogUtil.i("finishedDialogVisible show Loading")
            DialogUtil.showLoading()
            LaunchedEffect(finished.value) {
                viewModel.analyzerVideo()
                viewModel.analyzerPicture()
            }
        } else {
            LaunchedEffect(Unit) {
                viewModel.deleteVideoPhoto()
                viewModel.deleteCameraPhoto()
            }
            DialogUtil.hideLoading()
            val body = ExerciseResult(
                viewModel.improveProgramDetail.materialId,
                viewModel.score.value.toInt(),
                viewModel.totalDuration
            )
            apiService.postImprovementProgramUserExerciseData(body).enqueueBody {
                LogUtil.i("upload")
            }
            Dialog(onDismissRequest = {
//                viewModel.finishedDialogVisible = false
//                LogUtil.i("DialogDismiss finishedDialogVisible:${viewModel.finishedDialogVisible}")
            }) {
                Column(
                    Modifier
                        .width(330.dp)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(horizontal = 16.dp, vertical = 30.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    Text(
                        modifier = Modifier,
//                    text = viewModel.valueEulerDic.toString(),
                        text = stringResource(id = R.string.please_it_up),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFF2B56D7),
                            letterSpacing = 0.49.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    Text(
                        text = stringResource(id = R.string.cervical_comprehensive_evaluation),
                        style = TextStyle(
                            fontSize = 18.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF464646),
                            textAlign = TextAlign.Center,
                            letterSpacing = 0.63.sp,
                        )
                    )
                    Text(
                        text = stringResource(id = R.string.your_score, viewModel.score.value),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF464646),
                            textAlign = TextAlign.Center,
                            letterSpacing = 0.63.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                    Spacer(modifier = Modifier.height(15.dp))
                    Text(
                        modifier = Modifier
                            .clickable {
                                viewModel.count.value = 0
                                exoPlayer.seekTo(0, 0)
                                exoPlayer.playWhenReady = true
                                viewModel.finishedDialogVisible = false
                                LogUtil.i("再来一次 finishedDialogVisible:${viewModel.finishedDialogVisible}")
                            },
                        text = stringResource(id = R.string.training_again),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF2B56D7),
                            letterSpacing = 0.56.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                    Spacer(modifier = Modifier.height(15.dp))
                    Text(
                        modifier = Modifier
                            .clickable {
                                viewModel.finishedDialogVisible = false
                                popScreen(Screen.Main.route)
                            },
                        text = stringResource(id = R.string.next_practice),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF2B56D7),
                            letterSpacing = 0.56.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                    Spacer(modifier = Modifier.height(15.dp))
                    Text(
                        modifier = Modifier
                            .clickable {
                                viewModel.finishedDialogVisible = false
                                startScreen(Screen.TrainingEvaluation.route, true)
                            },
                        text = stringResource(id = R.string.evaluate),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF2B56D7),
                            letterSpacing = 0.56.sp,
                        )
                    )
                    Spacer(modifier = Modifier.height(15.dp))
                    HorizontalDivider(modifier = Modifier.padding(horizontal = 10.dp))
                    Spacer(modifier = Modifier.height(15.dp))
                }
            }
        }
    }

}

@SuppressLint("StateFlowValueCalledInComposition")
@Composable
fun VideoList(
    modifier: Modifier = Modifier,
    isVisible: Boolean,
    onclick: () -> Unit,
    content: @Composable () -> Unit
) {
    AnimatedVisibility(
        modifier = modifier,
        visible = isVisible,
        enter = slideInHorizontally { fullSize -> fullSize },
        exit = slideOutHorizontally(targetOffsetX = { fullSize -> fullSize })
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Transparent)
                .clickable {
                    onclick()
                    LogUtil.i("videList onclick1")
                }
        ) {
            Column(
                Modifier
                    .fillMaxWidth(0.5f)
                    .background(Color.Transparent)
            ) {
                Spacer(modifier = Modifier.fillMaxWidth())
            }
            Column(
                Modifier
                    .fillMaxHeight()
                    .background(Color(0X50000000))
                    .padding(start = 10.dp),
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    color = Color.White,
                    text = stringResource(id = R.string.lessons)
                )
                HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
                content()

            }
        }
    }
}


////@Preview
//@Composable
//fun Progress() {
//    val stepTimeState = 1000L
//
//    val targetTime = 3000L
//
//    Column(
//        modifier = Modifier.fillMaxSize(),
//        horizontalAlignment = Alignment.CenterHorizontally
//    ) {
//        Spacer(modifier = Modifier.height(20.dp))
//        ProgressBarView(stepTimeState, targetTime)
//        Spacer(modifier = Modifier.height(20.dp))
//    }
//}

/**
 * 圆形进度条
 */
@Composable
private fun ProgressBarView(stepTimeState: Long, targetTime: Long) {

    val animAngle = animateFloatAsState(
        targetValue = (1.0f * stepTimeState / targetTime * 360),
        animationSpec = tween(100), label = ""
    )
    val animPercent = animateIntAsState(
        targetValue = ceil(stepTimeState.toFloat() / 1000.0).toInt(),
        animationSpec = tween(100), label = ""
    )
//    LogUtil.i((1.0f * stepTimeState / targetTime * 360).toString())
//    LogUtil.i("stepTime:$stepTimeState targetTime:$targetTime animAngle: ${animAngle.value} animPercent: ${animPercent.value}")
    val textPercent = animPercent.value.formatTime()
    val textPercentLayResult = rememberTextMeasurer().measure(
        text = AnnotatedString(textPercent),
        TextStyle(
            color = Color(96, 98, 172),
            fontWeight = FontWeight.Bold
        )
    )
    val textDesc = localeResources.getString(R.string.trained)
    val textDescLayoutResult = rememberTextMeasurer().measure(
        AnnotatedString(textDesc),
        TextStyle(color = Color(46, 120, 249))
    )
    Canvas(modifier = Modifier.size(70.dp), onDraw = {
        val innerStrokeWidth = 4.dp.toPx()
        val radius = 32.dp.toPx()
        val outStrokeWidth = 4.dp.toPx()
        val canvasWidth = size.width
        val canvasHeight = size.height
        //内部圆
        drawCircle(
            Color(222, 228, 246),
            radius = radius,
            center = Offset(canvasWidth / 2, canvasHeight / 2),
            style = Stroke(innerStrokeWidth)
        )
        //圆弧进度
        drawArc(
            Color(46, 120, 249),
            startAngle = -90f,
            sweepAngle = animAngle.value,
            useCenter = false,
            size = Size(radius * 2, radius * 2),
            style = Stroke(outStrokeWidth, cap = StrokeCap.Round),
            topLeft = Offset(center.x - radius, center.y - radius)
        )
        val textPercentWidth = textPercentLayResult.size.width
        val textPercentHeight = textPercentLayResult.size.height
        drawText(
            textLayoutResult = textPercentLayResult,
            topLeft = Offset(
                canvasWidth / 2 - textPercentWidth / 2,
                canvasHeight / 2 - textPercentHeight
            ),
        )

        val textDescWidth = textDescLayoutResult.size.width

        drawText(
            textLayoutResult = textDescLayoutResult,
            topLeft = Offset(
                canvasWidth / 2 - textDescWidth / 2,
                canvasHeight / 2
            ),
        )
    })

}

/**
 * 视频播放器
 *
 */
@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
@Composable
fun VerticalVideoPlayer(
    modifier: Modifier = Modifier,
    exoPlayer: ExoPlayer,
    textureView: TextureView,
    enable: Boolean = true,
    imageUrl: String = "",
    viewModel: ImproveDetailViewModel,
) {
    var shouldShowControls by remember { mutableStateOf(false) }

    val playbackState by remember { mutableIntStateOf(exoPlayer.playbackState) }
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        if (enable) {
        Column(
            modifier = Modifier
                .height(82.dp)
                .padding(bottom = 10.dp)
                .fillMaxWidth()
                .aspectRatio(16 / 9f)
                .background(Color.White),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            ProgressBarView(viewModel.currentTime, viewModel.totalDuration)
        }
        }
        Box(
            modifier = Modifier
                .clip(shape = RoundedCornerShape(10.dp))
                .fillMaxWidth()
                .aspectRatio(16 / 9f)
                .clickable { shouldShowControls = shouldShowControls.not() },
        ) {
            if (viewModel.currentTime == 0L || viewModel.finishedDialogVisible) {
                AsyncImage(
                    modifier = Modifier.fillMaxSize(),
                    model = imageUrl,
                    contentDescription = "",
                    contentScale = ContentScale.FillBounds
                )
            } else {
                TextureView(
                    modifier = Modifier
                        .fillMaxSize(),
                    textureView = textureView,
                    content = {}

                )
            }

        }
    }
}

/**
 * 视频播放器
 *
 */
@androidx.annotation.OptIn(androidx.media3.common.util.UnstableApi::class)
@Composable
private fun HorizontalVideoPlayer(
    modifier: Modifier = Modifier,
    exoPlayer: ExoPlayer,
    textureView: TextureView,
    viewModel: ImproveDetailViewModel
) {

    /** 上下文 */
    var shouldShowControls by remember { mutableStateOf(false) }

    val playbackState by remember { mutableIntStateOf(exoPlayer.playbackState) }

    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopEnd
    ) {

        Box(
            modifier = Modifier
                .clip(shape = RoundedCornerShape(10.dp))
                .aspectRatio(16 / 9f)
                .background(Color.White)
                .clickable { shouldShowControls = shouldShowControls.not() },
        ) {
            if (viewModel.currentTime == 0L || viewModel.finishedDialogVisible) {
                AsyncImage(
                    modifier = Modifier.fillMaxSize(),
                    model = viewModel.improveProgramDetail.cover,
                    contentDescription = "",
                    contentScale = ContentScale.FillBounds
                )
            } else {
                TextureView(
                    modifier = Modifier
                        .fillMaxSize(),
                    textureView = textureView,
                    content = {}
                )
            }
//            ExoPlayerView(
//                modifier = Modifier.fillMaxSize(),
//                exoPlayer = exoPlayer,
//            ) {
//
//                PlayerControls(
//                    modifier
//                        .fillMaxHeight()
//                        .clickable { shouldShowControls = shouldShowControls.not() },
//                    isVisible = { shouldShowControls },
//                    isPlaying = { viewModel.playerIsPlaying },
//                    onSkipPrevious = {
//                        if (exoPlayer.hasPreviousMediaItem()) {
//                            exoPlayer.seekToPreviousMediaItem()
//                            viewModel.id.update { viewModel.id.value - 1 }
//                        }
//                    },
//                    onSkipClick = {
//                        if (exoPlayer.hasNextMediaItem()) {
//                            exoPlayer.seekToNextMediaItem()
//                            viewModel.id.update { viewModel.id.value + 1 }
//                        }
//
//                    },
//                    onPauseToggle = {
//                        if (exoPlayer.isPlaying) {
//                            exoPlayer.pause()
//                        } else if (exoPlayer.playbackState == STATE_ENDED) {
//                            exoPlayer.seekTo(0)
//                            exoPlayer.playWhenReady = true
//                            exoPlayer.play()
//                        } else {
//                            exoPlayer.playWhenReady = true
//                            exoPlayer.play()
//                        }
//
//                    },
//                    totalDuration = { viewModel.totalDuration },
//                    currentTime = { viewModel.currentTime },
//                    playbackState = { playbackState },
//                    onSeekChanged = { timeMs ->
//                        exoPlayer.seekTo(timeMs.toLong())
//                    }
//                )
//            }
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .padding(start = 16.dp, top = 16.dp)
                    .size(80.dp)
                    .background(Color.Transparent)
            ) {
                if (!shouldShowControls) {
                    ProgressBarView(viewModel.currentTime, viewModel.totalDuration)
                }
            }
        }

    }

}

/**
 * 播放器控制面板
 */
@OptIn(ExperimentalAnimationApi::class)
@Composable
private fun PlayerControls(
    modifier: Modifier = Modifier,
    isVisible: () -> Boolean,
    isPlaying: () -> Boolean,
    onSkipPrevious: () -> Unit,
    onSkipClick: () -> Unit,
    onPauseToggle: () -> Unit,
    totalDuration: () -> Long,
    currentTime: () -> Long,
    playbackState: () -> Int,
    onSeekChanged: (timeMs: Float) -> Unit
) {

    val visible = remember(isVisible()) { isVisible() }

    AnimatedVisibility(
        modifier = modifier,
        visible = visible,
        enter = fadeIn(),
        exit = fadeOut()
    ) {
        Box(modifier = Modifier.fillMaxSize()) {

            CenterControls(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(),
                isPlaying = isPlaying,
                onSkipPrevious = onSkipPrevious,
                onSkipClick = onSkipClick,
                onPauseToggle = onPauseToggle,
                playbackState = playbackState
            )

            BottomControls(
                modifier =
                Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .animateEnterExit(
                        enter =
                        slideInVertically(
                            initialOffsetY = { fullHeight: Int ->
                                fullHeight
                            }
                        ),
                        exit =
                        slideOutVertically(
                            targetOffsetY = { fullHeight: Int ->
                                fullHeight
                            }
                        )
                    ),
                totalDuration = totalDuration,
                currentTime = currentTime,
                onSeekChanged = onSeekChanged
            )
        }
    }
}

/**
 * 控制按钮：后退、播放/暂停、前进
 */
@Composable
private fun CenterControls(
    modifier: Modifier = Modifier,
    isPlaying: () -> Boolean,
    playbackState: () -> Int,
    onSkipPrevious: () -> Unit,
    onPauseToggle: () -> Unit,
    onSkipClick: () -> Unit
) {
    val isVideoPlaying = remember(isPlaying()) { isPlaying() }

    val playerState = remember(playbackState()) { playbackState() }

    Row(modifier = modifier, horizontalArrangement = Arrangement.SpaceEvenly) {
        IconButton(modifier = Modifier.size(40.dp), onClick = onSkipPrevious) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = R.drawable.ic_skip_previous),
                contentDescription = "skip previous"
            )
        }

        IconButton(modifier = Modifier.size(40.dp), onClick = onPauseToggle) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                painter =
                when {
                    isVideoPlaying -> {
                        painterResource(id = R.drawable.ic_pause)
                    }

                    isVideoPlaying.not() && playerState == STATE_ENDED -> {
                        painterResource(id = R.drawable.ic_replay)
                    }

                    else -> {
                        painterResource(id = R.drawable.ic_play)
                    }
                },
                contentDescription = "Play/Pause"
            )
        }

        IconButton(modifier = Modifier.size(40.dp), onClick = onSkipClick) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = R.drawable.ic_skip_next),
                contentDescription = "skip next"
            )
        }
    }
}

/**
 * 播放进度条
 */
@Composable
private fun BottomControls(
    modifier: Modifier = Modifier,
    totalDuration: () -> Long,
    currentTime: () -> Long,
    onSeekChanged: (timeMs: Float) -> Unit
) {

    val duration = remember(totalDuration()) { totalDuration() }

    val videoTime = remember(currentTime()) { currentTime() }


    Column(modifier = modifier.padding(bottom = 32.dp)) {
        Box(modifier = Modifier.fillMaxWidth()) {

            Slider(
                modifier = Modifier.fillMaxWidth(),
                value = videoTime.toFloat(),
                onValueChange = onSeekChanged,
                valueRange = 0f..duration.toFloat()
            )
        }

    }
}

@Composable
fun CameraView(
    viewModel: ImproveDetailViewModel,
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit,
    imageCapture: ImageCapture,
    lifecycleOwner: LifecycleOwner,
) {
    if (viewModel.currentTime == 0L || viewModel.finishedDialogVisible) {
        Image(
            painter = painterResource(id = R.drawable.float_window_background_for_aineck),
            contentDescription = "window of floating background",
            modifier = modifier
        )
    } else {
        Box(
            modifier = modifier,
        ) {
            val ct = LocalContext.current
            val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(ct) }
            AndroidView(
                factory = { context ->
                    val previewView = PreviewView(context)
                    val preview = Preview.Builder().build()
                    /* 摄像头选择 */
                    val selector = CameraSelector.Builder()
                        .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                        .build()

                    preview.setSurfaceProvider(previewView.surfaceProvider)
                    try {
                        /* 接触之前所有对摄像头单例的绑定 */
                        cameraProviderFuture.get().unbindAll()
                        /* 针对当前生命周期的绑定行为 */
                        cameraProviderFuture.get().bindToLifecycle(
                            lifecycleOwner, selector, preview, imageCapture
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    previewView
                },
                modifier = Modifier
                    .clip(RoundedCornerShape(10.dp))
            )

            /** 自定义内容 */
            content()
        }
    }

}

/**
 * 初始化ExoPlayer
 * @param context Context
 */
@androidx.annotation.OptIn(UnstableApi::class)
@Composable
fun rememberExoPlayer(
    context: Context,
    playerListener: Listener,
    videoFrameMetadataListener: VideoFrameMetadataListener,
): ExoPlayer {

    val exoPlayer = remember {
        ExoPlayer
            .Builder(context)
            .setMediaSourceFactory(
                DefaultMediaSourceFactory(VideoDownloadUtil.getReadOnlyDataSourceFactory(activity))
            )
            .build().apply {
                prepare()
                playWhenReady = true
                addListener(playerListener)
                setVideoFrameMetadataListener(videoFrameMetadataListener)
            }
    }

    DisposableEffect(Unit) {
        onDispose {
            exoPlayer.removeListener(playerListener)
            exoPlayer.clearVideoFrameMetadataListener(videoFrameMetadataListener)
            exoPlayer.release()
        }
    }

    return exoPlayer
}


@androidx.annotation.OptIn(UnstableApi::class)
@Composable
fun ExoPlayerView(
    modifier: Modifier,
    exoPlayer: ExoPlayer,
    content: @Composable () -> Unit,
) {
    AndroidView(
        modifier = modifier,
        factory = {
            val playerView = PlayerView(it)
            playerView.apply {
                useController = false
                resizeMode = RESIZE_MODE_FIT
                player = exoPlayer
                setShowBuffering(PlayerView.SHOW_BUFFERING_ALWAYS)
                exoPlayer.playWhenReady = true
                keepScreenOn = true
            }
            playerView
        })
    content()
}

@Composable
fun TextureView(
    modifier: Modifier = Modifier,
    textureView: TextureView,
    content: @Composable () -> Unit,
) {

    AndroidView(
        modifier = modifier,
        factory = {
            textureView
        }
    )
    content()

}