package org.aihealth.ineck.view.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun HealthTipScreen(
    viewModel: MainViewModel
) {
    BasePageView(
        title = stringResource(id = R.string.health_tip),
        showBackIcon = true
    ) {
        Column(
            Modifier
                .fillMaxSize()
                .padding(horizontal = 20.dp)
                .verticalScroll(rememberScrollState())) {
            Text(
                text = stringResource(id = R.string.health_tip_1),
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color.Black,
                modifier = Modifier.padding(vertical = 40.dp)
            )
            Text(
                text = stringResource(id = R.string.health_tip_2),
                fontSize = 16.sp,
                color = Color(0XFF444444),
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(10.dp))
            Box(Modifier.fillMaxWidth()) {
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_1),
                    contentDescription = null,
                    modifier = Modifier.size(196.dp)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_2),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .size(196.dp)
                )
            }
            if (viewModel.homeScreen.currentDeviceType == DeviceType.aiNeck) {
                Text(
                    text = stringResource(id = R.string.health_tip_3),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black,
                    modifier = Modifier.padding(top = 40.dp, bottom = 8.dp)
                )
                Text(
                    text = stringResource(id = R.string.health_tip_4),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Text(
                    text = stringResource(id = R.string.health_tip_5),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF333333)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_3),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(top = 10.dp, bottom = 40.dp)
                        .size(152.dp)
                )
                Text(
                    text = stringResource(id = R.string.health_tip_6),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(id = R.string.health_tip_7),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF333333)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_4),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(vertical = 10.dp)
                        .size(156.dp)
                )
                Text(
                    text = stringResource(id = R.string.health_tip_8),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF333333)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_5),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(vertical = 10.dp)
                        .size(210.dp)
                )
                Text(
                    text = stringResource(id = R.string.health_tip_9),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF333333)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_6),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(top = 10.dp, bottom = 40.dp)
                        .size(170.dp)
                )
            } else {
                Spacer(modifier = Modifier.height(40.dp))
                Text(
                    text = stringResource(id = R.string.health_tip_10),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    text = stringResource(id = R.string.health_tip_11),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Text(
                    text = stringResource(id = R.string.health_tip_12),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF333333)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_7),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(top = 10.dp, bottom = 40.dp)
                        .size(138.dp,201.dp)
                )
                Text(
                    text = stringResource(id = R.string.health_tip_13),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color.Black
                )
                Text(
                    text = stringResource(id = R.string.health_tip_14),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = Color(0XFF333333)
                )
                Image(
                    painter = painterResource(id = R.drawable.img_health_tip_8),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(vertical = 10.dp)
                        .size(236.dp)
                )
            }


        }
    }
}