package org.aihealth.ineck.view.screen.my

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Composable
fun VipMessageNoVipEnSliver(
    vipHelloString: String = "Join the membership!",
    vipDescriptionString: String = "Scientifical && Professional prevention, Correction, exercise",
    nextDescriptionString: String = "Free trial",
    onClick: () -> Unit = {},
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(64.dp)
            .clip(shape = RoundedCornerShape(6.dp))
            .paint(
                painterResource(id = R.drawable.vip_my_bg_no),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .paint(
                    painterResource(id = R.drawable.vip_my_bg_no_sliver),
                    contentScale = ContentScale.Crop,
                )
                .clip(shape = RoundedCornerShape(0.dp, 4.dp, 0.dp, 0.dp))
                .clickable { onClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = fontSize10,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF535353),
                    letterSpacing = 0.46.sp,
                )
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = R.drawable.vip_silver_badge),
                contentDescription = "ic_vip"
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(38.dp)
                    .padding(start = 8.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = vipHelloString,
                    style = TextStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight(600),
                        color = Color(0XFF535353),
                    ),
                    textAlign = TextAlign.Start
                )
                Text(
                    modifier = Modifier,
                    text = vipDescriptionString,
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF7B8092),
                    ),
                    textAlign = TextAlign.Start
                )
            }
        }
    }
}
@Composable
fun VipMessageEn(
    @DrawableRes id: Int,
    vipHelloString: String,
    vipDescriptionString: String,
    nextDescriptionString: String,
    onclick: () -> Unit,
    context: @Composable () -> Unit,
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(160.dp)
            .clip(shape = RoundedCornerShape(12.dp))
            .paint(
                painterResource(id = R.drawable.vip_bg_1),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .paint(
                    painterResource(id = R.drawable.vip_in_bg_2),
                )
                .clickable { onclick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF6B4B14),
                    letterSpacing = 0.46.sp,
                )
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = id),
                contentDescription = "ic_vip"
            )
            Text(
                text = vipHelloString,
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFFFFFFFF),
                    letterSpacing = 0.56.sp,
                )
            )
        }
        Text(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 40.dp, start = 21.dp),
            text = vipDescriptionString,
            style = TextStyle(
                fontSize = 10.sp,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFF1F1F1),
                letterSpacing = 0.42.sp,
            )
        )
        Column(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth()
                .padding(top = 7.dp, bottom = 9.dp, start = 8.dp, end = 8.dp)
                .background(Color.White, shape = RoundedCornerShape(6.dp)),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            context()
        }
    }
}

@Composable
fun VipMessageEnGold(
    vipHelloString: String = "Hi, aiSpine member!",
    vipDescriptionString: String = "4",
    nextDescriptionString: String = "My member",
    onclick: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }

    val dayString = buildAnnotatedString {
        withStyle(
            SpanStyle(
                fontSize = fontSize10,
                fontWeight = FontWeight.Normal,
                color = Color(0XFFE9E9E9),
            )
        ) {
            append(stringResource(id = R.string.last_practice_time))
        }
        withStyle(
            SpanStyle(
                fontSize = fontSize12,
                fontWeight = FontWeight.Normal,
                color = Color(0xFFEEC570)
            )
        ) {
            append(" $vipDescriptionString ")
        }
        withStyle(
            SpanStyle(
                fontSize = fontSize10,
                fontWeight = FontWeight.Normal,
                color = Color(0XFFE9E9E9)
            )
        ) {
            stringResource(id = R.string.day)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(64.dp)
            .clip(shape = RoundedCornerShape(6.dp))
            .paint(
                painterResource(id = R.drawable.vip_my_bg_gold),
                contentScale = ContentScale.Crop,
            )
    ) {
        Row(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .paint(
                    painterResource(id = R.drawable.vip_in_bg_2),
                    contentScale = ContentScale.Crop,
                )
                .clip(shape = RoundedCornerShape(0.dp, 4.dp, 0.dp, 0.dp))
                .clickable { onclick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween,
        ) {
            Text(
                modifier = Modifier.padding(start = 18.dp),
                text = nextDescriptionString,
                style = TextStyle(
                    fontSize = fontSize10,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF6B4B14),
                    letterSpacing = 0.46.sp,
                )
            )
            Image(
                modifier = Modifier.padding(end = 11.dp),
                painter = painterResource(id = R.drawable.ic_next_step_2),
                contentDescription = "next step",
            )
        }
        Row(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = 12.dp, start = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                modifier = Modifier
                    .size(22.dp, 22.dp)
                    .padding(end = 2.dp),
                painter = painterResource(id = R.drawable.vip_gold_badge),
                contentDescription = "ic_vip"
            )
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(38.dp)
                    .padding(start = 8.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = vipHelloString,
                    style = TextStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight(600),
                        color = Color(0xFFFFFFFF)
                    )
                )
                Text(
                    modifier = Modifier,
                    text = dayString,
                )
            }

        }

    }
}