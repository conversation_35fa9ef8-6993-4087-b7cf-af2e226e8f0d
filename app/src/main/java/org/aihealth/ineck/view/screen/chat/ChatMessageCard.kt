package org.aihealth.ineck.view.screen.chat

import android.widget.Toast
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.model.chat.ChatMessageEntity
import org.aihealth.ineck.model.chat.ChatSmsEntity
import org.aihealth.ineck.model.chat.FollowContent
import org.aihealth.ineck.model.chat.MeetContent
import org.aihealth.ineck.model.chat.MessageType
import org.aihealth.ineck.model.chat.ReportContent
import org.aihealth.ineck.model.chat.getCallDurationContent
import org.aihealth.ineck.model.chat.getFollowContent
import org.aihealth.ineck.model.chat.getMeetContent
import org.aihealth.ineck.model.chat.getPhoneRequestContent
import org.aihealth.ineck.model.chat.getReportContent
import org.aihealth.ineck.util.TimeUtil
import org.aihealth.ineck.viewmodel.ChatViewModel


/**
 * v1
 * 文字内容小卡片
 */
@Composable
fun TextRequestCard(
    modifier: Modifier = Modifier,
    message: String = "",
    messageType: MessageType = MessageType.RECEIVE
) {
    Surface(
        modifier = modifier
            .clip(RoundedCornerShape(16.dp)),
    ) {
        Column(
            modifier = Modifier.background(
                color = if (messageType == MessageType.RECEIVE) Color(0xFFF8F9FE) else Color(
                    0xFF1B6BFF
                ),
                shape = RoundedCornerShape(16.dp)
            )
        ) {
            Text(
                modifier = Modifier
                    .padding(start = 16.dp, top = 12.dp, end = 16.dp, bottom = 12.dp),
                text = message,
                style = TextStyle(
                    fontSize = 14.sp,
                    lineHeight = 20.sp,
                    fontWeight = FontWeight(400),
                    color = if (messageType == MessageType.RECEIVE) Color(0xFF1F2024) else Color.White,
                )
            )
        }

    }
}

/**
 * v2
 * 文字内容小卡片
 */
@Composable
fun TextRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    providerAvatar: String,
    patientAvatar: String,
) {

    /*** 对话信息*/
    Row(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = if (message.messageType == MessageType.RECEIVE) Arrangement.End else Arrangement.Start
    ) {
        /*** 他人头像（左边）*/
        if (message.messageType == MessageType.RECEIVE) {
            Box(
                modifier = Modifier.Companion
                    .size(45.dp)
                    .clip(CircleShape)
                    .background(Color.Companion.White)
                    .weight(1f)
            ) {
                AsyncImage(
                    model = providerAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Companion.Crop,
                    modifier = Modifier.Companion
                        .fillMaxSize(),
                    error = painterResource(id = R.drawable.header_3)

                )
            }
        }
        Column(
            modifier = Modifier.Companion
                .weight(6f)
                .wrapContentHeight(),
            horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
        ) {
            /*** 内容 - 根据类型显示不同内容*/
            Surface(
                modifier = modifier
                    .wrapContentHeight()
                    .clip(RoundedCornerShape(16.dp)),
            ) {
                Column(
                    modifier = Modifier.background(
                        color = if (message.messageType == MessageType.RECEIVE) Color(
                            0xFFF8F9FE
                        ) else Color(
                            0xFF1B6BFF
                        ),
                        shape = RoundedCornerShape(16.dp)
                    )
                ) {
                    Text(
                        modifier = Modifier
                            .padding(
                                start = 16.dp,
                                top = 12.dp,
                                end = 16.dp,
                                bottom = 12.dp
                            ),
                        text = message.message,
                        style = TextStyle(
                            fontSize = 14.sp,
                            lineHeight = 20.sp,
                            fontWeight = FontWeight(400),
                            color = if (message.messageType == MessageType.RECEIVE) Color(
                                0xFF1F2024
                            ) else Color.White,
                        )
                    )
                }
            }
        }
        /*** 本人头像（右边）*/
        if (message.messageType == MessageType.SEND) {
            Box(
                modifier = Modifier.Companion
                    .size(45.dp)
                    .clip(CircleShape)
                    .background(Color.Companion.White)
                    .weight(1f)
            ) {
                AsyncImage(
                    model = patientAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Companion.Crop,
                    modifier = Modifier.Companion
                        .fillMaxSize(),
                    error = painterResource(id = R.drawable.header_3)

                )
            }
        }
    }
}

/**
 * v3
 * 文字内容小卡片
 */
@Composable
fun TextRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
) {
    Column(
        modifier = modifier
            .wrapContentHeight(),
        horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
    ) {
        /*** 内容 - 根据类型显示不同内容*/
        Surface(
            modifier = Modifier
                .wrapContentHeight()
                .clip(RoundedCornerShape(16.dp)),
        ) {
            Column(
                modifier = Modifier.background(
                    color = if (message.messageType == MessageType.RECEIVE) Color(
                        0xFFF8F9FE
                    ) else Color(
                        0xFF1B6BFF
                    ),
                    shape = RoundedCornerShape(16.dp)
                )
            ) {
                Text(
                    modifier = Modifier
                        .padding(
                            start = 16.dp,
                            top = 12.dp,
                            end = 16.dp,
                            bottom = 12.dp
                        ),
                    text = message.message,
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 20.sp,
                        fontWeight = FontWeight(400),
                        color = if (message.messageType == MessageType.RECEIVE) Color(
                            0xFF1F2024
                        ) else Color.White,
                    )
                )
            }
        }
    }
}

/**
 * 会议请求卡片组件
 */
@Composable
fun MeetRequestCard(
    meetContent: MeetContent,
    chatSmsEntity: ChatSmsEntity,
    chatViewModel: ChatViewModel
) {
    val isAccepted = remember { mutableIntStateOf(meetContent.isAccepted) }
    val context = LocalContext.current

    Surface(
        modifier = Modifier
            .width(220.dp)
            .background(Color.Transparent)
            .clip(RoundedCornerShape(16.dp)),
    ) {
        Column(
            Modifier
                .wrapContentSize()
                .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                .clip(RoundedCornerShape(16.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 顶部蓝色背景
            Box(
                modifier = Modifier
                    .wrapContentWidth()
                    .height(86.dp)
                    .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            ) {
                Image(
                    painter = painterResource(id = R.drawable.background_meet),
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Column(
                    modifier = Modifier
                        .align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painterResource(R.drawable.ic_meet_member),
                        modifier = Modifier
                            .width(48.dp)
                            .height(38.dp),
                        contentDescription = null
                    )
                    Text(
                        text = stringResource(R.string.meet_invite),
                        modifier = Modifier
                            .padding(top = 4.dp)
                            .padding(horizontal = 16.dp),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(600),
                            color = Color(0xFFFFFFFF),
                            textAlign = TextAlign.Center,
                        )
                    )
                }
            }

            // 会议名称
            Text(
                text = meetContent.title,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .padding(horizontal = 4.dp),
                maxLines = 2,
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF666666),
                    textAlign = TextAlign.Center,
                )

            )

            // 时间
            Text(
                text = TimeUtil.isoStringToFormattedDate(meetContent.time),
                modifier = Modifier
                    .padding(bottom = 16.dp, top = 4.dp)
                    .padding(horizontal = 4.dp),
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF1B6BFF),
                    textAlign = TextAlign.Center,
                )
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 4.dp),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                when (isAccepted.intValue) {
                    0 -> {
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .border(
                                    width = 1.dp,
                                    color = Color(0xFF1B6BFF),
                                    shape = RoundedCornerShape(size = 24.dp)
                                )
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color.White)
                                .clickable {
                                    // 不立即更新UI状态，而是等待API请求成功后通过loadChatMessages更新
                                    // 使用ViewModel处理拒绝会议请求
                                    chatViewModel.modifyMeetRequest(
                                        meetContent = meetContent.copy(isAccepted = 2),
                                        chatSmsEntity = chatSmsEntity,
                                        onSuccess = {},
                                        onFailure = { errorMsg ->
                                            Toast.makeText(
                                                context,
                                                errorMsg,
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    )
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.reject),
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF1B6BFF),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }

                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color(0xFF4F97F3))
                                .clickable {
                                    // 不立即更新UI状态，而是等待API请求成功后通过loadChatMessages更新
                                    // 使用ViewModel处理接受会议请求
                                    chatViewModel.modifyMeetRequest(
                                        meetContent = meetContent.copy(isAccepted = 1),
                                        chatSmsEntity = chatSmsEntity,
                                        onSuccess = {
                                            // 请求成功的回调
                                            Toast.makeText(
                                                context,
                                                context.getString(R.string.meet_accept_success),
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        },
                                        onFailure = { errorMsg ->
                                            Toast.makeText(
                                                context,
                                                errorMsg,
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    )
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.accept),
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }

                    }

                    1 -> {
                        // 已同意
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color(0xFF4F97F3).copy(alpha = 0.7f)),
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = stringResource(R.string.accepted),
                                    color = Color.White,
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "Accepted",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .padding(start = 4.dp)
                                        .size(18.dp)
                                )
                            }
                        }
                    }

                    2 -> {
                        // 已拒绝
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .border(
                                    width = 1.dp,
                                    color = Color(0xFF4F97F3).copy(alpha = 0.7f),
                                    shape = RoundedCornerShape(18.dp)
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = stringResource(R.string.rejected),
                                    color = Color(0xFF1B6BFF),
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "rejected",
                                    tint = Color(0xFF1B6BFF),
                                    modifier = Modifier
                                        .padding(start = 4.dp)
                                        .size(18.dp)
                                )
                            }
                        }
                    }

                }
            }
        }
    }
}


/**
 * v2 会议请求卡片组件
 */
@Composable
fun MeetRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    onAccept: () -> Unit,
    onReject: () -> Unit,
    providerAvatar: String,
    patientAvatar: String
) {
    val meet = message.getMeetContent()
    meet?.let {
        /*** 对话信息*/
        Row(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            horizontalArrangement = if (message.messageType == MessageType.RECEIVE) Arrangement.End else Arrangement.Start
        ) {
            /*** 他人头像（左边）*/
            if (message.messageType == MessageType.RECEIVE) {
                Box(
                    modifier = Modifier.Companion
                        .size(45.dp)
                        .clip(CircleShape)
                        .background(Color.Companion.White)
                        .weight(1f)
                ) {
                    AsyncImage(
                        model = providerAvatar,
                        contentDescription = null,
                        contentScale = ContentScale.Companion.Crop,
                        modifier = Modifier.Companion
                            .fillMaxSize(),
                        error = painterResource(id = R.drawable.header_3)
                    )
                }
            }
            Column(
                modifier = Modifier
                    .weight(6f)
                    .wrapContentHeight(),
                horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
            ) {
                Surface(
                    modifier = modifier
                        .width(220.dp)
                        .background(Color.Transparent)
                        .clip(RoundedCornerShape(16.dp)),
                ) {
                    Column(
                        Modifier
                            .wrapContentSize()
                            .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                            .clip(RoundedCornerShape(16.dp)),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 顶部蓝色背景
                        Box(
                            modifier = Modifier
                                .wrapContentWidth()
                                .height(86.dp)
                                .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.background_meet),
                                modifier = Modifier.fillMaxSize(),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            Column(
                                modifier = Modifier
                                    .align(Alignment.Center),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                Image(
                                    painterResource(R.drawable.ic_meet_member),
                                    modifier = Modifier
                                        .width(48.dp)
                                        .height(38.dp),
                                    contentDescription = null
                                )
                                Text(
                                    text = stringResource(R.string.meet_invite),
                                    modifier = Modifier
                                        .padding(top = 4.dp)
                                        .padding(horizontal = 16.dp),
                                    style = TextStyle(
                                        fontSize = 16.sp,
                                        fontWeight = FontWeight(600),
                                        color = Color(0xFFFFFFFF),
                                        textAlign = TextAlign.Center,
                                    )
                                )
                            }
                        }

                        // 会议名称
                        Text(
                            text = meet.title,
                            modifier = Modifier
                                .padding(top = 16.dp)
                                .padding(horizontal = 4.dp),
                            maxLines = 2,
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF666666),
                                textAlign = TextAlign.Center,
                            )

                        )

                        // 时间
                        Text(
                            text = TimeUtil.isoStringToFormattedDate(meet.time),
                            modifier = Modifier
                                .padding(bottom = 16.dp, top = 4.dp)
                                .padding(horizontal = 4.dp),
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF1B6BFF),
                                textAlign = TextAlign.Center,
                            )
                        )

                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 4.dp),
                            horizontalArrangement = Arrangement.SpaceAround
                        ) {
                            when (meet.isAccepted) {
                                0 -> {
                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .border(
                                                width = 1.dp,
                                                color = Color(0xFF1B6BFF),
                                                shape = RoundedCornerShape(size = 24.dp)
                                            )
                                            .clip(RoundedCornerShape(18.dp))
                                            .background(Color.White)
                                            .clickable {
                                                onReject()
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.reject),
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFF1B6BFF),
                                                textAlign = TextAlign.Center,
                                            )
                                        )
                                    }

                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .clip(RoundedCornerShape(18.dp))
                                            .background(Color(0xFF4F97F3))
                                            .clickable {
                                                onAccept()
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.accept),
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFFFFFFFF),
                                                textAlign = TextAlign.Center,
                                            )
                                        )
                                    }

                                }

                                1 -> {
                                    // 已同意
                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .clip(RoundedCornerShape(18.dp))
                                            .background(Color(0xFF4F97F3).copy(alpha = 0.7f)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.Center
                                        ) {
                                            Text(
                                                text = stringResource(R.string.accepted),
                                                color = Color.White,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Icon(
                                                imageVector = Icons.Default.CheckCircle,
                                                contentDescription = "Accepted",
                                                tint = Color.White,
                                                modifier = Modifier
                                                    .padding(start = 4.dp)
                                                    .size(18.dp)
                                            )
                                        }
                                    }
                                }

                                2 -> {
                                    // 已拒绝
                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .clip(RoundedCornerShape(18.dp))
                                            .border(
                                                width = 1.dp,
                                                color = Color(0xFF4F97F3).copy(alpha = 0.7f),
                                                shape = RoundedCornerShape(18.dp)
                                            ),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.Center
                                        ) {
                                            Text(
                                                text = stringResource(R.string.rejected),
                                                color = Color(0xFF1B6BFF),
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Icon(
                                                imageVector = Icons.Default.Close,
                                                contentDescription = "rejected",
                                                tint = Color(0xFF1B6BFF),
                                                modifier = Modifier
                                                    .padding(start = 4.dp)
                                                    .size(18.dp)
                                            )
                                        }
                                    }
                                }

                            }
                        }
                    }
                }
            }
            /*** 本人头像（右边）*/
            if (message.messageType == MessageType.SEND) {
                Box(
                    modifier = Modifier.Companion
                        .size(45.dp)
                        .clip(CircleShape)
                        .background(Color.Companion.White)
                        .weight(1f)
                ) {
                    AsyncImage(
                        model = patientAvatar,
                        contentDescription = null,
                        contentScale = ContentScale.Companion.Crop,
                        modifier = Modifier.Companion
                            .fillMaxSize(),
                        error = painterResource(id = R.drawable.header_3)

                    )
                }
            }
        }
    }
}

/**
 * v3 会议请求卡片组件
 */
@Composable
fun MeetRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    onAccept: () -> Unit,
    onReject: () -> Unit,
) {
    val meet = message.getMeetContent()
    meet?.let {
        Column(
            modifier = modifier
                .wrapContentHeight(),
            horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
        ) {
            Surface(
                modifier = Modifier
                    .width(220.dp)
                    .background(Color.Transparent)
                    .clip(RoundedCornerShape(16.dp)),
            ) {
                Column(
                    Modifier
                        .wrapContentSize()
                        .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                        .clip(RoundedCornerShape(16.dp)),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 顶部蓝色背景
                    Box(
                        modifier = Modifier
                            .wrapContentWidth()
                            .height(86.dp)
                            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.background_meet),
                            modifier = Modifier.fillMaxSize(),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        Column(
                            modifier = Modifier
                                .align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Image(
                                painterResource(R.drawable.ic_meet_member),
                                modifier = Modifier
                                    .width(48.dp)
                                    .height(38.dp),
                                contentDescription = null
                            )
                            Text(
                                text = stringResource(R.string.meet_invite),
                                modifier = Modifier
                                    .padding(top = 4.dp)
                                    .padding(horizontal = 16.dp),
                                style = TextStyle(
                                    fontSize = 16.sp,
                                    fontWeight = FontWeight(600),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }
                    }

                    // 会议名称
                    Text(
                        text = meet.title,
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .padding(horizontal = 4.dp),
                        maxLines = 2,
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF666666),
                            textAlign = TextAlign.Center,
                        )

                    )

                    // 时间
                    Text(
                        text = TimeUtil.isoStringToFormattedDate(meet.time),
                        modifier = Modifier
                            .padding(bottom = 16.dp, top = 4.dp)
                            .padding(horizontal = 4.dp),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF1B6BFF),
                            textAlign = TextAlign.Center,
                        )
                    )

                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 4.dp),
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {
                        when (meet.isAccepted) {
                            0 -> {
                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .border(
                                            width = 1.dp,
                                            color = Color(0xFF1B6BFF),
                                            shape = RoundedCornerShape(size = 24.dp)
                                        )
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color.White)
                                        .clickable {
                                            onReject()
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = stringResource(R.string.reject),
                                        style = TextStyle(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFF1B6BFF),
                                            textAlign = TextAlign.Center,
                                        )
                                    )
                                }

                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color(0xFF4F97F3))
                                        .clickable {
                                            onAccept()
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = stringResource(R.string.accept),
                                        style = TextStyle(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFFFFFFFF),
                                            textAlign = TextAlign.Center,
                                        )
                                    )
                                }

                            }

                            1 -> {
                                // 已同意
                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color(0xFF4F97F3).copy(alpha = 0.7f)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.accepted),
                                            color = Color.White,
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Icon(
                                            imageVector = Icons.Default.CheckCircle,
                                            contentDescription = "Accepted",
                                            tint = Color.White,
                                            modifier = Modifier
                                                .padding(start = 4.dp)
                                                .size(18.dp)
                                        )
                                    }
                                }
                            }

                            2 -> {
                                // 已拒绝
                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .clip(RoundedCornerShape(18.dp))
                                        .border(
                                            width = 1.dp,
                                            color = Color(0xFF4F97F3).copy(alpha = 0.7f),
                                            shape = RoundedCornerShape(18.dp)
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.rejected),
                                            color = Color(0xFF1B6BFF),
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Icon(
                                            imageVector = Icons.Default.Close,
                                            contentDescription = "rejected",
                                            tint = Color(0xFF1B6BFF),
                                            modifier = Modifier
                                                .padding(start = 4.dp)
                                                .size(18.dp)
                                        )
                                    }
                                }
                            }

                        }
                    }
                }
            }
        }
    }
}


/**
 * v1
 * 关注请求卡片组件
 */
@Composable
fun FollowRequestCard(
    followContent: FollowContent,
    chatSmsEntity: ChatSmsEntity,
    chatViewModel: ChatViewModel
) {
    val isAccepted = remember { mutableIntStateOf(followContent.isAccepted) }
    val context = LocalContext.current
    Surface(
        modifier = Modifier
            .width(220.dp)
            .background(Color.Transparent)
            .clip(RoundedCornerShape(16.dp)),
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                .clip(RoundedCornerShape(16.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // 顶部蓝色背景
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(86.dp)
                    .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            ) {
                Image(
                    painter = painterResource(id = R.drawable.background_follow),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(64.dp),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                // 头像 - 半覆盖在蓝色背景上
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .background(Color.White, CircleShape)
                        .padding(2.dp)
                        .size(56.dp)
                        .clip(CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        model = followContent.avatar,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .wrapContentSize()
                            .clip(CircleShape),
                        error = painterResource(id = R.drawable.header_3)
                    )
                }

            }

            // 用户名
            Text(
                text = followContent.name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                modifier = Modifier
                    .padding(bottom = 4.dp)
            )
            // 请求文本
            Text(
                text = stringResource(R.string.request_follow),
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(bottom = 16.dp)
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 4.dp),
                horizontalArrangement = Arrangement.SpaceAround
            ) {
                when (isAccepted.intValue) {
                    0 -> {
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .border(
                                    width = 1.dp,
                                    color = Color(0xFF1B6BFF),
                                    shape = RoundedCornerShape(size = 24.dp)
                                )
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color.White)
                                .clickable {
                                    // 不立即更新UI状态，而是等待API请求成功后通过loadChatMessages更新
                                    // 使用ViewModel处理拒绝关注请求
                                    chatViewModel.modifyFollowRequest(
                                        followContent = followContent.copy(isAccepted = 2),
                                        chatSmsEntity = chatSmsEntity,
                                        onSuccess = {
                                            // 成功回调为空，因为UI会通过loadChatMessages自动更新
                                        },
                                        onFailure = { errorMsg ->
                                            Toast.makeText(
                                                context,
                                                errorMsg,
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    )
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.reject),
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF1B6BFF),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }

                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color(0xFF4F97F3))
                                .clickable {
                                    // 不立即更新UI状态，而是等待API请求成功后通过loadChatMessages更新
                                    // 使用ViewModel处理接受关注请求
                                    chatViewModel.modifyFollowRequest(
                                        followContent = followContent.copy(isAccepted = 1),
                                        chatSmsEntity = chatSmsEntity,
                                        onSuccess = {},
                                        onFailure = { errorMsg ->
                                            Toast.makeText(
                                                context,
                                                errorMsg,
                                                Toast.LENGTH_SHORT
                                            ).show()
                                        }
                                    )
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.accept),
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }

                    }

                    1 -> {
                        // 已同意
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color(0xFF4F97F3).copy(alpha = 0.7f)),
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = stringResource(R.string.accepted),
                                    color = Color.White,
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                Icon(
                                    imageVector = Icons.Default.CheckCircle,
                                    contentDescription = "Accepted",
                                    tint = Color.White,
                                    modifier = Modifier
                                        .padding(start = 4.dp)
                                        .size(18.dp)
                                )
                            }
                        }
                    }

                    2 -> {
                        // 已拒绝
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .border(
                                    width = 1.dp,
                                    color = Color(0xFF4F97F3).copy(alpha = 0.7f),
                                    shape = RoundedCornerShape(18.dp)
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Row(
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Text(
                                    text = stringResource(R.string.rejected),
                                    color = Color(0xFF1B6BFF),
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                                Icon(
                                    imageVector = Icons.Default.Close,
                                    contentDescription = "rejected",
                                    tint = Color(0xFF1B6BFF),
                                    modifier = Modifier
                                        .padding(start = 4.dp)
                                        .size(18.dp)
                                )
                            }
                        }

                    }
                }

            }

        }
    }
}

/**
 * v2
 * 关注请求卡片组件
 */
@Composable
fun FollowRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    onAccept: () -> Unit,
    onReject: () -> Unit,
    providerAvatar: String,
    patientAvatar: String,
) {
    val follow = message.getFollowContent()
    follow?.let {
        /*** 对话信息*/
        Row(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            horizontalArrangement = if (message.messageType == MessageType.RECEIVE) Arrangement.End else Arrangement.Start
        ) {
            /*** 他人头像（左边）*/
            if (message.messageType == MessageType.RECEIVE) {
                Box(
                    modifier = Modifier.Companion
                        .size(45.dp)
                        .clip(CircleShape)
                        .background(Color.Companion.White)
                        .weight(1f)
                ) {
                    AsyncImage(
                        model = providerAvatar,
                        contentDescription = null,
                        contentScale = ContentScale.Companion.Crop,
                        modifier = Modifier.Companion
                            .fillMaxSize(),
                        error = painterResource(id = R.drawable.header_3)

                    )
                }
            }
            Column(
                modifier = Modifier.Companion
                    .weight(6f)
                    .wrapContentHeight(),
                horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
            ) {
                Surface(
                    modifier = modifier
                        .width(220.dp)
                        .background(Color.Transparent)
                        .clip(RoundedCornerShape(16.dp)),
                ) {
                    Column(
                        Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                            .clip(RoundedCornerShape(16.dp)),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        // 顶部蓝色背景
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(86.dp)
                                .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.background_follow),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(64.dp),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            // 头像 - 半覆盖在蓝色背景上
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .background(Color.White, CircleShape)
                                    .padding(2.dp)
                                    .size(56.dp)
                                    .clip(CircleShape),
                                contentAlignment = Alignment.Center
                            ) {
                                AsyncImage(
                                    model = follow.avatar,
                                    contentDescription = null,
                                    contentScale = ContentScale.Crop,
                                    modifier = Modifier
                                        .wrapContentSize()
                                        .clip(CircleShape),
                                    error = painterResource(id = R.drawable.header_3)
                                )
                            }

                        }

                        // 用户名
                        Text(
                            text = follow.name,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black,
                            modifier = Modifier
                                .padding(bottom = 4.dp)
                        )
                        // 请求文本
                        Text(
                            text = stringResource(R.string.request_follow),
                            fontSize = 14.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(horizontal = 4.dp),
                            horizontalArrangement = Arrangement.SpaceAround
                        ) {
                            when (follow.isAccepted) {
                                0 -> {
                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .border(
                                                width = 1.dp,
                                                color = Color(0xFF1B6BFF),
                                                shape = RoundedCornerShape(size = 24.dp)
                                            )
                                            .clip(RoundedCornerShape(18.dp))
                                            .background(Color.White)
                                            .clickable {
                                                onReject()
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.reject),
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFF1B6BFF),
                                                textAlign = TextAlign.Center,
                                            )
                                        )
                                    }

                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .clip(RoundedCornerShape(18.dp))
                                            .background(Color(0xFF4F97F3))
                                            .clickable {
                                                onAccept()
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.accept),
                                            style = TextStyle(
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight(400),
                                                color = Color(0xFFFFFFFF),
                                                textAlign = TextAlign.Center,
                                            )
                                        )
                                    }

                                }

                                1 -> {
                                    // 已同意
                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .clip(RoundedCornerShape(18.dp))
                                            .background(Color(0xFF4F97F3).copy(alpha = 0.7f)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.Center
                                        ) {
                                            Text(
                                                text = stringResource(R.string.accepted),
                                                color = Color.White,
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Icon(
                                                imageVector = Icons.Default.CheckCircle,
                                                contentDescription = "Accepted",
                                                tint = Color.White,
                                                modifier = Modifier
                                                    .padding(start = 4.dp)
                                                    .size(18.dp)
                                            )
                                        }
                                    }
                                }

                                2 -> {
                                    // 已拒绝
                                    Box(
                                        modifier = Modifier
                                            .padding(bottom = 16.dp)
                                            .width(100.dp)
                                            .height(36.dp)
                                            .clip(RoundedCornerShape(18.dp))
                                            .border(
                                                width = 1.dp,
                                                color = Color(0xFF4F97F3).copy(alpha = 0.7f),
                                                shape = RoundedCornerShape(18.dp)
                                            ),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Row(
                                            verticalAlignment = Alignment.CenterVertically,
                                            horizontalArrangement = Arrangement.Center
                                        ) {
                                            Text(
                                                text = stringResource(R.string.rejected),
                                                color = Color(0xFF1B6BFF),
                                                fontSize = 14.sp,
                                                fontWeight = FontWeight.Medium
                                            )
                                            Icon(
                                                imageVector = Icons.Default.Close,
                                                contentDescription = "rejected",
                                                tint = Color(0xFF1B6BFF),
                                                modifier = Modifier
                                                    .padding(start = 4.dp)
                                                    .size(18.dp)
                                            )
                                        }
                                    }

                                }
                            }

                        }

                    }
                }
            }
            /*** 本人头像（右边）*/
            if (message.messageType == MessageType.SEND) {
                Box(
                    modifier = Modifier.Companion
                        .size(45.dp)
                        .clip(CircleShape)
                        .background(Color.Companion.White)
                        .weight(1f)
                ) {
                    AsyncImage(
                        model = patientAvatar,
                        contentDescription = null,
                        contentScale = ContentScale.Companion.Crop,
                        modifier = Modifier.Companion
                            .fillMaxSize(),
                        error = painterResource(id = R.drawable.header_3)

                    )
                }
            }

        }


    }
}

/**
 * v3
 * 关注请求卡片组件
 */
@Composable
fun FollowRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    onAccept: () -> Unit,
    onReject: () -> Unit,
) {
    val follow = message.getFollowContent()
    follow?.let {
        Column(
            modifier = modifier
                .wrapContentHeight(),
            horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
        ) {
            Surface(
                modifier = Modifier
                    .width(220.dp)
                    .background(Color.Transparent)
                    .clip(RoundedCornerShape(16.dp)),
            ) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                        .clip(RoundedCornerShape(16.dp)),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // 顶部蓝色背景
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(86.dp)
                            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.background_follow),
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        // 头像 - 半覆盖在蓝色背景上
                        Box(
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .background(Color.White, CircleShape)
                                .padding(2.dp)
                                .size(56.dp)
                                .clip(CircleShape),
                            contentAlignment = Alignment.Center
                        ) {
                            AsyncImage(
                                model = follow.avatar,
                                contentDescription = null,
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .wrapContentSize()
                                    .clip(CircleShape),
                                error = painterResource(id = R.drawable.header_3)
                            )
                        }

                    }

                    // 用户名
                    Text(
                        text = follow.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        modifier = Modifier
                            .padding(bottom = 4.dp)
                    )
                    // 请求文本
                    Text(
                        text = stringResource(R.string.request_follow),
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 4.dp),
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {
                        when (follow.isAccepted) {
                            0 -> {
                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .border(
                                            width = 1.dp,
                                            color = Color(0xFF1B6BFF),
                                            shape = RoundedCornerShape(size = 24.dp)
                                        )
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color.White)
                                        .clickable {
                                            onReject()
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = stringResource(R.string.reject),
                                        style = TextStyle(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFF1B6BFF),
                                            textAlign = TextAlign.Center,
                                        )
                                    )
                                }

                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color(0xFF4F97F3))
                                        .clickable {
                                            onAccept()
                                        },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Text(
                                        text = stringResource(R.string.accept),
                                        style = TextStyle(
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight(400),
                                            color = Color(0xFFFFFFFF),
                                            textAlign = TextAlign.Center,
                                        )
                                    )
                                }

                            }

                            1 -> {
                                // 已同意
                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .clip(RoundedCornerShape(18.dp))
                                        .background(Color(0xFF4F97F3).copy(alpha = 0.7f)),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.accepted),
                                            color = Color.White,
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Icon(
                                            imageVector = Icons.Default.CheckCircle,
                                            contentDescription = "Accepted",
                                            tint = Color.White,
                                            modifier = Modifier
                                                .padding(start = 4.dp)
                                                .size(18.dp)
                                        )
                                    }
                                }
                            }

                            2 -> {
                                // 已拒绝
                                Box(
                                    modifier = Modifier
                                        .padding(bottom = 16.dp)
                                        .width(100.dp)
                                        .height(36.dp)
                                        .clip(RoundedCornerShape(18.dp))
                                        .border(
                                            width = 1.dp,
                                            color = Color(0xFF4F97F3).copy(alpha = 0.7f),
                                            shape = RoundedCornerShape(18.dp)
                                        ),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically,
                                        horizontalArrangement = Arrangement.Center
                                    ) {
                                        Text(
                                            text = stringResource(R.string.rejected),
                                            color = Color(0xFF1B6BFF),
                                            fontSize = 14.sp,
                                            fontWeight = FontWeight.Medium
                                        )
                                        Icon(
                                            imageVector = Icons.Default.Close,
                                            contentDescription = "rejected",
                                            tint = Color(0xFF1B6BFF),
                                            modifier = Modifier
                                                .padding(start = 4.dp)
                                                .size(18.dp)
                                        )
                                    }
                                }

                            }
                        }

                    }

                }
            }
        }
    }
}

/**
 * v1
 * 报告请求卡片组件
 */
@Composable
fun ReportRequestCard(reportContent: ReportContent) {
    Surface(
        modifier = Modifier
            .width(220.dp)
            .background(Color.Transparent)
            .clip(RoundedCornerShape(16.dp)),
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                .clip(RoundedCornerShape(16.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(86.dp)
                    .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
            ) {
                Image(
                    painter = painterResource(id = R.drawable.background__chat_report),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(64.dp),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                // 头像 - 半覆盖在蓝色背景上
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .background(Color.White, CircleShape)
                        .padding(2.dp)
                        .size(56.dp)
                        .clip(CircleShape),
                    contentAlignment = Alignment.Center
                ) {
                    AsyncImage(
                        model = reportContent.avatar,
                        contentDescription = null,
                        contentScale = ContentScale.Crop,
                        modifier = Modifier
                            .wrapContentSize()
                            .clip(CircleShape),
                        error = painterResource(id = R.drawable.header_3)
                    )
                }
            }
            // 用户名
            Text(
                text = reportContent.name,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
                color = Color.Black,
                modifier = Modifier
                    .padding(bottom = 4.dp)
            )
            // 请求文本
            Text(
                text = stringResource(R.string.request_report),
                fontSize = 14.sp,
                color = Color.Gray,
                modifier = Modifier.padding(bottom = 16.dp)
            )
        }
    }
}

/**
 * v2
 * 报告请求卡片组件
 */
@Composable
fun ReportRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    providerAvatar: String,
    patientAvatar: String,
) {
    val reportContent = message.getReportContent()
    reportContent?.let {
        /*** 对话信息*/
        Row(
            modifier = modifier
                .fillMaxWidth()
                .wrapContentHeight(),
            horizontalArrangement = if (message.messageType == MessageType.RECEIVE) Arrangement.End else Arrangement.Start
        ) {
            /*** 他人头像（左边）*/
            if (message.messageType == MessageType.RECEIVE) {
                Box(
                    modifier = Modifier.Companion
                        .size(45.dp)
                        .clip(CircleShape)
                        .background(Color.Companion.White)
                        .weight(1f)
                ) {
                    AsyncImage(
                        model = providerAvatar,
                        contentDescription = null,
                        contentScale = ContentScale.Companion.Crop,
                        modifier = Modifier.Companion
                            .fillMaxSize(),
                        error = painterResource(id = R.drawable.header_3)

                    )
                }
            }
            Column(
                modifier = Modifier.Companion
                    .weight(6f)
                    .wrapContentHeight(),
                horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
            ) {
                Surface(
                    modifier = Modifier
                        .width(220.dp)
                        .background(Color.Transparent)
                        .clip(RoundedCornerShape(16.dp)),
                ) {
                    Column(
                        Modifier
                            .fillMaxWidth()
                            .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                            .clip(RoundedCornerShape(16.dp)),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(86.dp)
                                .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.background__chat_report),
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(64.dp),
                                contentScale = ContentScale.FillBounds,
                                contentDescription = null
                            )
                            // 头像 - 半覆盖在蓝色背景上
                            Box(
                                modifier = Modifier
                                    .align(Alignment.BottomCenter)
                                    .background(Color.White, CircleShape)
                                    .padding(2.dp)
                                    .size(56.dp)
                                    .clip(CircleShape),
                                contentAlignment = Alignment.Center
                            ) {
                                AsyncImage(
                                    model = reportContent.avatar,
                                    contentDescription = null,
                                    contentScale = ContentScale.Crop,
                                    modifier = Modifier
                                        .wrapContentSize()
                                        .clip(CircleShape),
                                    error = painterResource(id = R.drawable.header_3)
                                )
                            }
                        }
                        // 用户名
                        Text(
                            text = reportContent.name,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            color = Color.Black,
                            modifier = Modifier
                                .padding(bottom = 4.dp)
                        )
                        // 请求文本
                        Text(
                            text = stringResource(R.string.request_report),
                            fontSize = 14.sp,
                            color = Color.Gray,
                            modifier = Modifier.padding(bottom = 16.dp)
                        )
                    }
                }
            }
            /*** 本人头像（右边）*/
            if (message.messageType == MessageType.SEND) {
                Box(
                    modifier = Modifier.Companion
                        .size(45.dp)
                        .clip(CircleShape)
                        .background(Color.Companion.White)
                        .weight(1f)
                ) {
                    AsyncImage(
                        model = patientAvatar,
                        contentDescription = null,
                        contentScale = ContentScale.Companion.Crop,
                        modifier = Modifier.Companion
                            .fillMaxSize(),
                        error = painterResource(id = R.drawable.header_3)

                    )
                }
            }
        }
    }
}

/**
 * v3
 * 报告请求卡片组件
 */
@Composable
fun ReportRequestCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
) {
    val reportContent = message.getReportContent()
    reportContent?.let {
        Column(
            modifier = modifier
                .wrapContentHeight(),
            horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
        ) {
            Surface(
                modifier = Modifier
                    .width(220.dp)
                    .background(Color.Transparent)
                    .clip(RoundedCornerShape(16.dp)),
            ) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                        .clip(RoundedCornerShape(16.dp)),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(86.dp)
                            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.background__chat_report),
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        // 头像 - 半覆盖在蓝色背景上
                        Box(
                            modifier = Modifier
                                .align(Alignment.BottomCenter)
                                .background(Color.White, CircleShape)
                                .padding(2.dp)
                                .size(56.dp)
                                .clip(CircleShape),
                            contentAlignment = Alignment.Center
                        ) {
                            AsyncImage(
                                model = reportContent.avatar,
                                contentDescription = null,
                                contentScale = ContentScale.Crop,
                                modifier = Modifier
                                    .wrapContentSize()
                                    .clip(CircleShape),
                                error = painterResource(id = R.drawable.header_3)
                            )
                        }
                    }
                    // 用户名
                    Text(
                        text = reportContent.name,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.Black,
                        modifier = Modifier
                            .padding(bottom = 4.dp)
                    )
                    // 请求文本
                    Text(
                        text = stringResource(R.string.request_report),
                        fontSize = 14.sp,
                        color = Color.Gray,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )
                }
            }
        }
    }
}
/**
 * 请求对方号码
 */
@Composable
fun PhoneRequestContent(
    modifier: Modifier = Modifier,
    sendPhoneNumber: () -> Unit,
    message: ChatMessageEntity
) {
    val phoneRequestContent = message.getPhoneRequestContent()
    phoneRequestContent?.let {
        Column(
            modifier = modifier
                .wrapContentHeight(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when (phoneRequestContent.isAccepted) {
                0 -> {
                    // 等待
                    Row (
                        Modifier
                            .height(30.dp)
                            .background(color = Color(0x0F1B6BFF), shape = RoundedCornerShape(size = 24.dp))
                            .padding(start = 12.dp, top = 6.dp, end = 12.dp, bottom = 6.dp)
                            .clickable{
                                sendPhoneNumber()
                            },
                        verticalAlignment = Alignment.CenterVertically
                    ){
                        Image(
                            painter = painterResource(id = R.drawable.ic_chat_send_phone_number),
                            contentDescription = null,
                            modifier = Modifier.padding(end = 2.dp).size(18.dp)
                        )
                        Text(
                            text = stringResource(R.string.chat_wait_send_phone_number),
                            style = TextStyle(
                                fontSize = 10.sp,
                                lineHeight = 16.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF1B6BFF)
                            )
                        )
                    }

                }
                1 ->{
                    // 已同意
                    Text(
                        text = stringResource(R.string.chat_phone_sent),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF999999),
                        )
                    )
                }
                2 ->{
                    // 已拒绝
                    Text(
                        text = stringResource(R.string.chat_phone_refued),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF999999),
                        )
                    )
                }
            }

        }
    }
}

/**
 * 通话时长记录卡片
 */
@Composable
fun CallDurationCard(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    callbackOnClick: () -> Unit = {}
) {
    val callDurationContent = message.getCallDurationContent()
    callDurationContent?.let {
        val context = LocalContext.current
        Column(
            modifier = modifier
                .wrapContentHeight(),
            horizontalAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.Start else Alignment.End
        ) {
            Surface(
                modifier = Modifier
                    .width(220.dp)
                    .background(Color.Transparent)
                    .clip(RoundedCornerShape(16.dp)),
            ) {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .background(Color(0xFFF8F8F8), RoundedCornerShape(16.dp))
                        .clip(RoundedCornerShape(16.dp)),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(64.dp)
                            .clip(RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp))
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.background_chat_call_duration),
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(64.dp),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        Image(
                            painter = painterResource(id = R.drawable.ic_chat_call_duration),
                            modifier = Modifier
                                .align(Alignment.Center).padding(bottom = 10.dp)
                                .width(33.dp)
                                .height(35.dp),
                            contentScale = ContentScale.FillBounds,
                            contentDescription = null
                        )
                        // 通话时长标题
                        Text(
                            text = stringResource(R.string.call_duration),
                            style = TextStyle(
                                fontSize = 14.sp,
                                fontWeight = FontWeight(600),
                                color = Color(0xFFFFFFFF),
                                textAlign = TextAlign.Center,
                            ),
                            modifier = Modifier.align(Alignment.BottomCenter).padding(bottom = 1.dp)
                        )
                    }
                    // 日期
                    Text(
                        text = TimeUtil.convertUtcToLocalTime(callDurationContent.time),
                        style = TextStyle(
                            fontSize = 12.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF666666),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    // 通话时长
                    Text(
                        text = formatDuration(callDurationContent.duration),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF1B6BFF),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier.padding(bottom = 20.dp)
                    )

                    // 回电按钮
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 4.dp),
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .padding(bottom = 16.dp)
                                .width(100.dp)
                                .height(36.dp)
                                .clip(RoundedCornerShape(18.dp))
                                .background(Color(0xFF1B6BFF))
                                .clickable {
                                    callbackOnClick()
                                },
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.call_back),
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFFFFFFFF),
                                    textAlign = TextAlign.Center,
                                )
                            )
                        }
                    }
                }
            }

        }
    }
}

/**
 * 格式化通话时长
 */
private fun formatDuration(seconds: Long): String {
    val hours = seconds / 3600
    val minutes = (seconds % 3600) / 60
    val remainingSeconds = seconds % 60

    return when {
        hours > 0 -> String.format("%02d:%02d:%02d", hours, minutes, remainingSeconds)
        else -> String.format("%02d:%02d", minutes, remainingSeconds)
    }
}


@Composable
fun MessageRow(
    modifier: Modifier = Modifier,
    message: ChatMessageEntity,
    providerAvatar: String,
    patientAvatar: String,
    content: @Composable () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        verticalAlignment = Alignment.Top
    ) {
        Box(
            modifier = Modifier
                .width(50.dp)
        ) {
            if (message.messageType == MessageType.RECEIVE) {
                AsyncImage(
                    model = providerAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Companion.Crop,
                    modifier = Modifier
                        .size(45.dp)
                        .clip(CircleShape),
                    error = painterResource(id = R.drawable.header_3)
                )
            }
        }
        Box(
            modifier = Modifier
                .padding(horizontal = 4.dp)
                .weight(1f),
            contentAlignment = if (message.messageType == MessageType.RECEIVE) Alignment.TopStart else Alignment.TopEnd
        ) {
            content()

        }
        Box(
            modifier = Modifier
                .width(50.dp)
        ) {
            if (message.messageType == MessageType.SEND) {
                AsyncImage(
                    model = patientAvatar,
                    contentDescription = null,
                    contentScale = ContentScale.Crop,
                    modifier = Modifier
                        .size(45.dp)
                        .clip(CircleShape),
                    error = painterResource(id = R.drawable.header_3)
                )
            }
        }
    }
}


@Preview(showBackground = true)
@Composable
private fun FollowRequestCardPreview() {
    val followContent = FollowContent(
        type = "follow",
        userId = "user123",
        followingId = "follow456",
        avatar = "https://myaih-net-profile-images.s3.amazonaws.com/38644bf7-7840-4ace-85ed-3b8e01b20f0c_temp_image.jpg",
        name = "scssal",
        isAccepted = 0
    )
    val chatSmsEntity = ChatSmsEntity(
        message = "asdasdasd",
    )
    val meetContent = MeetContent(
        type = "meeting",
        title = "Test Meeting",
        time = "10:00 AM",
        creatorId = "creator456",
        fromId = "from789",
        isAccepted = 1
    )
    Column(modifier = Modifier.fillMaxSize()) {
        FollowRequestCard(followContent, chatSmsEntity, ChatViewModel())
        MeetRequestCard(
            meetContent = meetContent,
            chatSmsEntity = chatSmsEntity,
            chatViewModel = ChatViewModel()
        )
        Row (
            Modifier
                .height(30.dp)
                .background(color = Color(0x0F1B6BFF), shape = RoundedCornerShape(size = 24.dp))
                .padding(start = 12.dp, top = 6.dp, end = 12.dp, bottom = 6.dp)
                .clickable{
//                    sendPhoneNumber()
                },
            verticalAlignment = Alignment.CenterVertically
        ){
            Image(
                painter = painterResource(id = R.drawable.ic_chat_send_phone_number),
                contentDescription = null,
                modifier = Modifier.padding(end = 2.dp).size(18.dp)
            )
            Text(
                text = "The other party has sent you their number, please check it.",
                style = TextStyle(
                    fontSize = 10.sp,
                    lineHeight = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF1B6BFF)
                )
            )
        }

    }
}
