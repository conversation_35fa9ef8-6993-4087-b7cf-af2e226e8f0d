package org.aihealth.ineck.view.screen.meeting

import android.content.Context
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.ImageFormat
import android.graphics.Paint
import android.graphics.Rect
import android.graphics.YuvImage
import android.os.SystemClock
import android.view.View
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.google.mediapipe.framework.image.BitmapImageBuilder
import com.google.mediapipe.framework.image.MPImage
import com.google.mediapipe.tasks.core.BaseOptions
import com.google.mediapipe.tasks.vision.core.RunningMode
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker.PoseLandmarkerOptions
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarkerResult
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.withContext
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.view.custom.BasePageView
import us.zoom.sdk.IncomingLiveStreamStatus
import us.zoom.sdk.SubSessionKit
import us.zoom.sdk.SubSessionUserHelpRequestHandler
import us.zoom.sdk.UVCCameraStatus
import us.zoom.sdk.ZoomVideoSDK
import us.zoom.sdk.ZoomVideoSDKAnnotationHelper
import us.zoom.sdk.ZoomVideoSDKAudioHelper
import us.zoom.sdk.ZoomVideoSDKAudioRawData
import us.zoom.sdk.ZoomVideoSDKAudioStatus
import us.zoom.sdk.ZoomVideoSDKCRCCallStatus
import us.zoom.sdk.ZoomVideoSDKCameraControlRequestHandler
import us.zoom.sdk.ZoomVideoSDKCameraControlRequestType
import us.zoom.sdk.ZoomVideoSDKChatHelper
import us.zoom.sdk.ZoomVideoSDKChatMessage
import us.zoom.sdk.ZoomVideoSDKChatMessageDeleteType
import us.zoom.sdk.ZoomVideoSDKChatPrivilegeType
import us.zoom.sdk.ZoomVideoSDKDelegate
import us.zoom.sdk.ZoomVideoSDKFileTransferStatus
import us.zoom.sdk.ZoomVideoSDKLiveStreamHelper
import us.zoom.sdk.ZoomVideoSDKLiveStreamStatus
import us.zoom.sdk.ZoomVideoSDKLiveTranscriptionHelper
import us.zoom.sdk.ZoomVideoSDKMultiCameraStreamStatus
import us.zoom.sdk.ZoomVideoSDKNetworkStatus
import us.zoom.sdk.ZoomVideoSDKPasswordHandler
import us.zoom.sdk.ZoomVideoSDKPhoneFailedReason
import us.zoom.sdk.ZoomVideoSDKPhoneStatus
import us.zoom.sdk.ZoomVideoSDKProxySettingHandler
import us.zoom.sdk.ZoomVideoSDKRawDataPipe
import us.zoom.sdk.ZoomVideoSDKRawDataPipeDelegate
import us.zoom.sdk.ZoomVideoSDKReceiveFile
import us.zoom.sdk.ZoomVideoSDKRecordingConsentHandler
import us.zoom.sdk.ZoomVideoSDKRecordingStatus
import us.zoom.sdk.ZoomVideoSDKSSLCertificateInfo
import us.zoom.sdk.ZoomVideoSDKSendFile
import us.zoom.sdk.ZoomVideoSDKSessionLeaveReason
import us.zoom.sdk.ZoomVideoSDKShareAction
import us.zoom.sdk.ZoomVideoSDKShareHelper
import us.zoom.sdk.ZoomVideoSDKShareSetting
import us.zoom.sdk.ZoomVideoSDKShareStatus
import us.zoom.sdk.ZoomVideoSDKSubSessionManager
import us.zoom.sdk.ZoomVideoSDKSubSessionParticipant
import us.zoom.sdk.ZoomVideoSDKSubSessionStatus
import us.zoom.sdk.ZoomVideoSDKTestMicStatus
import us.zoom.sdk.ZoomVideoSDKUser
import us.zoom.sdk.ZoomVideoSDKUserHelpRequestResult
import us.zoom.sdk.ZoomVideoSDKUserHelper
import us.zoom.sdk.ZoomVideoSDKVideoAspect
import us.zoom.sdk.ZoomVideoSDKVideoCanvas
import us.zoom.sdk.ZoomVideoSDKVideoHelper
import us.zoom.sdk.ZoomVideoSDKVideoRawData
import us.zoom.sdk.ZoomVideoSDKVideoResolution
import us.zoom.sdk.ZoomVideoSDKVideoSubscribeFailReason
import us.zoom.sdk.ZoomVideoSDKVideoView
import java.io.ByteArrayOutputStream


@Composable
fun MeetingWithLiveStream() {
    val users = remember { mutableStateListOf<ZoomVideoSDKUser>() }
    val context = LocalContext.current
    val configuration = LocalConfiguration.current
    val displayRotation = when (configuration.orientation) {
        Configuration.ORIENTATION_LANDSCAPE -> 90
        Configuration.ORIENTATION_PORTRAIT -> 0
        else -> 0
    }
    val _rawDataFlow = MutableStateFlow<ZoomVideoSDKVideoRawData?>(null)
    val rawDataFlow: StateFlow<ZoomVideoSDKVideoRawData?> = _rawDataFlow
    DisposableEffect(Unit) {
        val listener = object : ZoomVideoSDKDelegate {
            override fun onSessionJoin() {
                LogUtil.i("zoom session Join")
            }

            override fun onSessionLeave() {
                LogUtil.i("zoom session leave")

            }

            override fun onSessionLeave(reason: ZoomVideoSDKSessionLeaveReason?) {
                LogUtil.i("xoom session leave reason:${reason?.toJson()}")
            }

            override fun onError(errorCode: Int) {
                LogUtil.e("zoom Error:${errorCode}")
            }

            override fun onUserJoin(
                userHelper: ZoomVideoSDKUserHelper,
                userList: MutableList<ZoomVideoSDKUser>,
            ) {
                users.clear()
                users.addAll(UserHelper.getAllUsers())
                LogUtil.i("User joined: ${userList.map { it.userName }}, listsize:${users.size}")
            }

            override fun onUserLeave(
                userHelper: ZoomVideoSDKUserHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {
                users.clear()
                users.addAll(UserHelper.getAllUsers())
                LogUtil.i("User leave: ${userList?.map { it?.userName }}, listsize:${users.size}")

            }

            override fun onUserVideoStatusChanged(
                videoHelper: ZoomVideoSDKVideoHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {
                userList?.forEach { user ->
                    user?.let {
                        LogUtil.i("Video status changed for user: ${it.userName}, isOn: ${it.videoCanvas.videoStatus.isOn}")
                    }
                }
            }

            override fun onShareNetworkStatusChanged(
                shareNetworkStatus: ZoomVideoSDKNetworkStatus?,
                isSendingShare: Boolean
            ) {
                
            }

            override fun onUserAudioStatusChanged(
                audioHelper: ZoomVideoSDKAudioHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onUserShareStatusChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                userInfo: ZoomVideoSDKUser?,
                status: ZoomVideoSDKShareStatus?,
            ) {

            }

            override fun onUserShareStatusChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                userInfo: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onShareContentChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                userInfo: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onLiveStreamStatusChanged(
                liveStreamHelper: ZoomVideoSDKLiveStreamHelper?,
                status: ZoomVideoSDKLiveStreamStatus?,
            ) {

            }

            override fun onChatNewMessageNotify(
                chatHelper: ZoomVideoSDKChatHelper?,
                messageItem: ZoomVideoSDKChatMessage?,
            ) {

            }

            override fun onChatDeleteMessageNotify(
                chatHelper: ZoomVideoSDKChatHelper?,
                msgID: String?,
                deleteBy: ZoomVideoSDKChatMessageDeleteType?,
            ) {

            }

            override fun onChatPrivilegeChanged(
                chatHelper: ZoomVideoSDKChatHelper?,
                currentPrivilege: ZoomVideoSDKChatPrivilegeType?,
            ) {

            }

            override fun onUserHostChanged(
                userHelper: ZoomVideoSDKUserHelper?,
                userInfo: ZoomVideoSDKUser?,
            ) {

            }

            override fun onUserManagerChanged(user: ZoomVideoSDKUser?) {

            }

            override fun onUserNameChanged(user: ZoomVideoSDKUser?) {

            }

            override fun onUserActiveAudioChanged(
                audioHelper: ZoomVideoSDKAudioHelper?,
                list: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onSessionNeedPassword(handler: ZoomVideoSDKPasswordHandler?) {

            }

            override fun onSessionPasswordWrong(handler: ZoomVideoSDKPasswordHandler?) {

            }

            override fun onMixedAudioRawDataReceived(rawData: ZoomVideoSDKAudioRawData?) {

            }

            override fun onOneWayAudioRawDataReceived(
                rawData: ZoomVideoSDKAudioRawData?,
                user: ZoomVideoSDKUser?,
            ) {

            }

            override fun onShareAudioRawDataReceived(rawData: ZoomVideoSDKAudioRawData?) {

            }

            override fun onCommandReceived(
                sender: ZoomVideoSDKUser?,
                strCmd: String?,
            ) {

            }

            override fun onCommandChannelConnectResult(isSuccess: Boolean) {

            }

            override fun onCloudRecordingStatus(
                status: ZoomVideoSDKRecordingStatus?,
                handler: ZoomVideoSDKRecordingConsentHandler?,
            ) {

            }

            override fun onHostAskUnmute() {

            }

            override fun onInviteByPhoneStatus(
                status: ZoomVideoSDKPhoneStatus?,
                reason: ZoomVideoSDKPhoneFailedReason?,
            ) {

            }

            override fun onMultiCameraStreamStatusChanged(
                status: ZoomVideoSDKMultiCameraStreamStatus?,
                user: ZoomVideoSDKUser?,
                videoPipe: ZoomVideoSDKRawDataPipe?,
            ) {

            }

            override fun onMultiCameraStreamStatusChanged(
                status: ZoomVideoSDKMultiCameraStreamStatus?,
                user: ZoomVideoSDKUser?,
                canvas: ZoomVideoSDKVideoCanvas?,
            ) {

            }

            override fun onLiveTranscriptionStatus(status: ZoomVideoSDKLiveTranscriptionHelper.ZoomVideoSDKLiveTranscriptionStatus?) {

            }

            override fun onOriginalLanguageMsgReceived(messageInfo: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionMessageInfo?) {

            }

            override fun onLiveTranscriptionMsgInfoReceived(messageInfo: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionMessageInfo?) {

            }

            override fun onLiveTranscriptionMsgError(
                spokenLanguage: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionLanguage?,
                transcriptLanguage: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionLanguage?,
            ) {

            }

            override fun onSpokenLanguageChanged(spokenLanguage: ZoomVideoSDKLiveTranscriptionHelper.ILiveTranscriptionLanguage?) {
                
            }

            override fun onProxySettingNotification(handler: ZoomVideoSDKProxySettingHandler?) {

            }

            override fun onSSLCertVerifiedFailNotification(info: ZoomVideoSDKSSLCertificateInfo?) {

            }

            override fun onCameraControlRequestResult(
                user: ZoomVideoSDKUser?,
                isApproved: Boolean,
            ) {

            }

            override fun onCameraControlRequestReceived(
                user: ZoomVideoSDKUser?,
                requestType: ZoomVideoSDKCameraControlRequestType?,
                requestHandler: ZoomVideoSDKCameraControlRequestHandler?,
            ) {

            }

            override fun onUserVideoNetworkStatusChanged(
                status: ZoomVideoSDKNetworkStatus?,
                user: ZoomVideoSDKUser?,
            ) {

            }

            override fun onUserRecordingConsent(user: ZoomVideoSDKUser?) {

            }

            override fun onCallCRCDeviceStatusChanged(status: ZoomVideoSDKCRCCallStatus?) {

            }

            override fun onVideoCanvasSubscribeFail(
                fail_reason: ZoomVideoSDKVideoSubscribeFailReason?,
                pUser: ZoomVideoSDKUser?,
                view: ZoomVideoSDKVideoView?,
            ) {

            }

            override fun onShareCanvasSubscribeFail(
                fail_reason: ZoomVideoSDKVideoSubscribeFailReason?,
                pUser: ZoomVideoSDKUser?,
                view: ZoomVideoSDKVideoView?,
            ) {

            }

            override fun onShareCanvasSubscribeFail(
                pUser: ZoomVideoSDKUser?,
                view: ZoomVideoSDKVideoView?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onAnnotationHelperCleanUp(helper: ZoomVideoSDKAnnotationHelper?) {

            }

            override fun onAnnotationPrivilegeChange(
                shareOwner: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onTestMicStatusChanged(status: ZoomVideoSDKTestMicStatus?) {

            }

            override fun onMicSpeakerVolumeChanged(
                micVolume: Int,
                speakerVolume: Int,
            ) {

            }

            override fun onCalloutJoinSuccess(
                user: ZoomVideoSDKUser?,
                phoneNumber: String?,
            ) {

            }

            override fun onSendFileStatus(
                file: ZoomVideoSDKSendFile?,
                status: ZoomVideoSDKFileTransferStatus?,
            ) {

            }

            override fun onReceiveFileStatus(
                file: ZoomVideoSDKReceiveFile?,
                status: ZoomVideoSDKFileTransferStatus?,
            ) {

            }

            override fun onUVCCameraStatusChange(
                cameraId: String?,
                status: UVCCameraStatus?,
            ) {

            }

            override fun onVideoAlphaChannelStatusChanged(isAlphaModeOn: Boolean) {

            }

            override fun onSpotlightVideoChanged(
                videoHelper: ZoomVideoSDKVideoHelper?,
                userList: List<ZoomVideoSDKUser?>?,
            ) {

            }

            override fun onFailedToStartShare(
                shareHelper: ZoomVideoSDKShareHelper?,
                user: ZoomVideoSDKUser?
            ) {
                
            }

            override fun onBindIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onUnbindIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onIncomingLiveStreamStatusResponse(
                bSuccess: Boolean,
                streamsStatusList: List<IncomingLiveStreamStatus?>?
            ) {
                
            }

            override fun onStartIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onStopIncomingLiveStreamResponse(
                bSuccess: Boolean,
                streamKeyID: String?
            ) {
                
            }

            override fun onShareContentSizeChanged(
                shareHelper: ZoomVideoSDKShareHelper?,
                user: ZoomVideoSDKUser?,
                shareAction: ZoomVideoSDKShareAction?
            ) {
                
            }

            override fun onSubSessionStatusChanged(
                status: ZoomVideoSDKSubSessionStatus?,
                subSessionKitList: List<SubSessionKit?>?
            ) {
                
            }

            override fun onSubSessionManagerHandle(manager: ZoomVideoSDKSubSessionManager?) {
                
            }

            override fun onSubSessionParticipantHandle(participant: ZoomVideoSDKSubSessionParticipant?) {
                
            }

            override fun onSubSessionUsersUpdate(subSessionKit: SubSessionKit?) {
                
            }

            override fun onBroadcastMessageFromMainSession(
                message: String?,
                userName: String?
            ) {
                
            }

            override fun onSubSessionUserHelpRequest(handler: SubSessionUserHelpRequestHandler?) {
                
            }

            override fun onSubSessionUserHelpRequestResult(eResult: ZoomVideoSDKUserHelpRequestResult?) {
                
            }

            override fun onShareSettingChanged(setting: ZoomVideoSDKShareSetting?) {
                
            }

            // Implement other necessary callbacks
        }
        val params =
            ZoomVideoSDK.getInstance().addListener(listener)
        ZoomVideoSDK.getInstance().videoHelper.rotateMyVideo(displayRotation)
        onDispose {
            ZoomVideoSDK.getInstance().leaveSession(false)
            ZoomVideoSDK.getInstance().removeListener(listener)
        }
    }
    LaunchedEffect(ZoomVideoSDK.getInstance().isInSession) {
        val mySelf = ZoomVideoSDK.getInstance().session.mySelf
        if (mySelf?.audioStatus?.audioType == ZoomVideoSDKAudioStatus.ZoomVideoSDKAudioType.ZoomVideoSDKAudioType_None) {
            ZoomVideoSDK.getInstance().audioHelper.startAudio();
        } else {
            if (mySelf.audioStatus.isMuted) {
                ZoomVideoSDK.getInstance().audioHelper.unMuteAudio(mySelf);
            }
        }
        LogUtil.i("audioType${mySelf?.audioStatus?.audioType}")
        ZoomVideoSDK.getInstance().audioHelper.setSpeaker(true)
        val dataDelegate = object : ZoomVideoSDKRawDataPipeDelegate {
            override fun onRawDataStatusChanged(status: ZoomVideoSDKRawDataPipeDelegate.RawDataStatus?) {
            }

            override fun onRawDataFrameReceived(rawData: ZoomVideoSDKVideoRawData?) {
                rawData?.let {
                    _rawDataFlow.value = rawData
                }
            }
        }

        val pipe = mySelf.videoPipe
        pipe.subscribe(ZoomVideoSDKVideoResolution.VideoResolution_360P, dataDelegate)

    }
    BasePageView(
        modifier = Modifier.fillMaxSize(),
        title = "Meeting",
        showBackIcon = true
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            val mySelf = ZoomVideoSDK.getInstance().session?.mySelf
            val context = LocalContext.current
            val videoView = remember { ZoomVideoSDKVideoView(context) }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .fillMaxHeight(0.5f)
            ) {
                AndroidView(
                    factory = { videoView },
                    modifier = Modifier.fillMaxSize()
                ) {
                    mySelf?.videoCanvas?.subscribe(
                        videoView,
                        ZoomVideoSDKVideoAspect.ZoomVideoSDKVideoAspect_PanAndScan
                    )
                }
                PoseDetectionScreen(Modifier, context, rawDataFlow)
            }

            LazyColumn(
                modifier = Modifier.fillMaxSize()
            ) {
                items(users.filter { it != ZoomVideoSDK.getInstance().session.mySelf }) { user ->
                    LogUtil.i("user size:${users.size}")
                    val otherUser = remember { user }
                    val viewOther = remember { ZoomVideoSDKVideoView(context) }
                    LogUtil.i("other user:${otherUser.userName}")
                    AndroidView(
                        factory = { viewOther },
                        modifier = Modifier.fillParentMaxSize()
                    ) {
                        otherUser.videoCanvas?.subscribe(
                            viewOther,
                            ZoomVideoSDKVideoAspect.ZoomVideoSDKVideoAspect_PanAndScan
                        )
                    }

                }
            }

        }
    }
}

private class OverlayView(context: Context, private val isMirrored: Boolean = true) : View(context) {
    private val paint = Paint().apply {
        color = Color.RED
        style = Paint.Style.FILL
        strokeWidth = 6f
    }
    var results: PoseLandmarkerResult? = null

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        results?.let { poseLandmarkerResult ->
            val canvasWidth = this.width.toFloat()
            for (landmarkList in poseLandmarkerResult.worldLandmarks()) {
                for (landmark in landmarkList) {
                    val x =
                        if (isMirrored) (1.0f - landmark.x()) * canvasWidth else landmark.x() * canvasWidth
                    val y = landmark.y() * this.height
                    canvas.drawPoint(x, y, paint)
                }

                PoseLandmarker.POSE_LANDMARKS.forEach {
                    val startLandmark = poseLandmarkerResult.worldLandmarks()[0][it.start()]
                    val endLandmark = poseLandmarkerResult.worldLandmarks()[0][it.end()]

                    val startX =
                        if (isMirrored) (1.0f - startLandmark.x()) * canvasWidth else startLandmark.x() * canvasWidth
                    val startY = startLandmark.y() * this.height
                    val endX =
                        if (isMirrored) (1.0f - endLandmark.x()) * canvasWidth else endLandmark.x() * this.width
                    val endY = endLandmark.y() * this.height

                    canvas.drawLine(startX, startY, endX, endY, paint)
                }
            }
        }
    }

    fun updateFrame(newLandmarks: PoseLandmarkerResult?) {
        results = newLandmarks // Update the landmarks
        invalidate() // Redraw the view
    }
}

fun initializePoseLandmarker(
    context: Context,
    onResult: (PoseLandmarkerResult?, MPImage?, Long) -> Unit,
): PoseLandmarker {
    val baseOptions = BaseOptions.builder()
        .setModelAssetPath("pose_landmarker_lite.task") // Ensure the model file exists in assets
        .build()

    val options = PoseLandmarkerOptions.builder()
        .setBaseOptions(baseOptions)
        .setMinTrackingConfidence(0.5f)
        .setMinPoseDetectionConfidence(0.5f)
        .setMinPosePresenceConfidence(0.5f)
        .setRunningMode(RunningMode.LIVE_STREAM) // Use live stream mode
        .setResultListener { result, mpImage ->
            onResult(
                result,
                mpImage,
                SystemClock.uptimeMillis()
            ) // Pass all parameters to the callback
        }
        .build()

    return PoseLandmarker.createFromOptions(context, options)
}

fun processFrameAsync(
    rawData: ZoomVideoSDKVideoRawData,
    poseLandmarker: PoseLandmarker,
    frameTime: Long,
) {
    try {
        // Convert raw data to Bitmap
        val bitmap = convertRawDataToBitmap(rawData)

        // Convert Bitmap to MPImage
        val mpImage = BitmapImageBuilder(bitmap).build()

        // Pass the MPImage to the landmarker for asynchronous processing
        poseLandmarker.detectAsync(mpImage, frameTime)

    }catch (e: Exception){
        LogUtil.e("processFrameAsync:$e")
    }
}
fun processFrameAsync(
    bitmap: Bitmap,
    poseLandmarker: PoseLandmarker,
    frameTime: Long,
) {
    // Convert Bitmap to MPImage
    val mpImage = BitmapImageBuilder(bitmap).build()

    // Pass the MPImage to the landmarker for asynchronous processing
    poseLandmarker.detectAsync(mpImage, frameTime)
}

@Composable
fun PoseDetectionScreen(
    modifier: Modifier = Modifier,
    context: Context,
    rawDataFlow: StateFlow<ZoomVideoSDKVideoRawData?>,
) {
    val overlayView = remember { OverlayView(context) }
    // Initialize PoseLandmarker
    val poseLandmarker = remember {
        initializePoseLandmarker(context) { result, mpImage, _ ->
            if (result != null) {
                overlayView.updateFrame(result)
            }
        }
    }

    // Process frames from the raw data flow
    LaunchedEffect(rawDataFlow) {
        rawDataFlow.collect { rawData ->
            if (rawData != null) {
                withContext(Dispatchers.IO) {
                    processFrameAsync(rawData, poseLandmarker, SystemClock.uptimeMillis())
                }
            }
        }
    }
    Box(modifier = modifier) {
        AndroidView(
            factory = { overlayView },
            modifier = Modifier.fillMaxSize()
        )
    }

}


fun convertRawDataToBitmap(rawData: ZoomVideoSDKVideoRawData): Bitmap {
    val width = rawData.streamWidth
    val height = rawData.streamHeight
    // Convert YUV data to NV21 format
    val nv21 = convertYuvToNv21(rawData)
    // Use YuvImage to create a JPEG
    val yuvImage = YuvImage(nv21, ImageFormat.NV21, width, height, null)
    val out = ByteArrayOutputStream()
    yuvImage.compressToJpeg(Rect(0, 0, width, height), 100, out)
    val jpegBytes = out.toByteArray()
    // Decode JPEG to Bitmap
    return BitmapFactory.decodeByteArray(jpegBytes, 0, jpegBytes.size)
}

fun convertYuvToNv21(rawData: ZoomVideoSDKVideoRawData): ByteArray {
    val width = rawData.streamWidth
    val height = rawData.streamHeight
    val ySize = width * height
    val uvSize = ySize / 4
    val nv21 = ByteArray(ySize + uvSize * 2)
    // Copy Y plane
    rawData.getyBuffer().get(nv21, 0, ySize)
    // Interleave U and V planes
    val uBuffer = ByteArray(uvSize)
    val vBuffer = ByteArray(uvSize)
    rawData.getuBuffer().get(uBuffer)
    rawData.getvBuffer().get(vBuffer)
    for (i in 0 until uvSize) {
        nv21[ySize + i * 2] = vBuffer[i]
        nv21[ySize + i * 2 + 1] = uBuffer[i]
    }
    return nv21
}

fun convertBitmapToMPImage(bitmap: Bitmap): MPImage {
    return BitmapImageBuilder(bitmap).build()
}

fun processPoseLandmarks(mpImage: MPImage, poseLandmarker: PoseLandmarker): PoseLandmarkerResult {
    return poseLandmarker.detect(mpImage)
}