package org.aihealth.ineck.view.screen

import android.view.Gravity
import androidx.annotation.DrawableRes
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import kotlinx.coroutines.launch
import no.nordicsemi.android.dfu.DfuProgressListenerAdapter
import no.nordicsemi.android.dfu.DfuServiceListenerHelper
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.navController
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.impl.ConnectUiState

@Composable
fun ConnectedDeviceRoute(
    viewModel: MainViewModel,
    deviceType: String,
    mac: String
) {
    val context = LocalContext.current
    val uiState by viewModel.deviceScreen.uiState.collectAsState()
    var showConnectedDialog by remember {
        mutableStateOf(false)
    }
    var showConnectFailDialog by remember {
        mutableStateOf(false)
    }
    var isConnecting by remember { mutableStateOf(true) }
    
    val deviceConfig = when (viewModel.homeScreen.currentDeviceType) {
        DeviceType.aiNeck -> viewModel.deviceScreen.aiNeckDeviceConfig
        DeviceType.aiBack -> viewModel.deviceScreen.aiBackDeviceConfig
        DeviceType.KneeJoint -> viewModel.deviceScreen.kneeJointDeviceConfig
        DeviceType.ElbowJoint -> viewModel.deviceScreen.elbowJointDeviceConfig
        DeviceType.ShoulderJoint -> viewModel.deviceScreen.shoulderJointDeviceConfig
        DeviceType.HipJoint -> viewModel.deviceScreen.hipJointDeviceConfig
        else -> viewModel.deviceScreen.aiNeckDeviceConfig
    }

    DeviceConnectScreen(
        deviceType = deviceType,
        isConnecting = isConnecting,
        connect = {
            viewModel.deviceScreen.connectRemoteGatt(mac, deviceType)
        }
    )
    
    UpdateDialog(
        visible = viewModel.deviceScreen.updateDialogVisible,
        deviceType = deviceType,
        updateVersion = {
            viewModel.deviceScreen.onDisconnectDeviceClick(viewModel.deviceScreen.deviceTypeChosen.name)
            LogUtil.d("deviceScreen.mac:${deviceConfig.mac}")
            viewModel.deviceScreen.bluetoothUtil.updateDFU(deviceConfig.mac)
        },
        updateSuccess = {
            DialogUtil.showToast(context.getString(R.string.update_version), Gravity.CENTER)
            viewModel.deviceScreen.updateDialogVisible = false
            navController.navigate(Screen.Main.route) {
                launchSingleTop = true
                popUpTo(Screen.MatchingDevice.route) {
                    this.inclusive = true
                }
            }
        }
    )
    
    LaunchedEffect(uiState) {
        if (uiState is ConnectUiState.Connecting) {
            isConnecting = true
        } else {
            isConnecting = false
        }
        
        if (uiState is ConnectUiState.ConnectingSuccess) {
            showConnectedDialog = true
        }
        
        if (uiState is ConnectUiState.ConnectingError) {
            showConnectFailDialog = true
        }
    }
    
    if (showConnectedDialog) {
        ConnectedDialog(
            onDismiss = {
                viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                showConnectedDialog = false
                MainViewModel.pageIndex = 1
                popScreen(Screen.Main.route)
            },
            onConfirm = {
                viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                showConnectedDialog = false
                startScreen(Screen.FormatDevice.route, true)
            }
        )
    }
    
    if (showConnectFailDialog && !viewModel.deviceScreen.updateDialogVisible) {
        if (uiState is ConnectUiState.ConnectingError) {
            val uiError = uiState as ConnectUiState.ConnectingError
            if (uiError.visibility && uiError.res != null && uiError.address != null && uiError.deviceType != null) {
                ConnectedFailDialog(
                    title = stringResource(id = uiError.res, uiError.deviceType),
                    onDismiss = {
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        showConnectFailDialog = false
                    },
                    onConfirm = {
                        viewModel.deviceScreen.clearConnectedData(viewModel.homeScreen.currentDeviceType.name)
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        showConnectFailDialog = false
                        viewModel.deviceScreen.connectRemoteGatt(
                            uiError.address,
                            uiError.deviceType
                        )
                    }
                )
            }
        }
    }
    
    DisposableEffect(Unit) {
        viewModel.deviceScreen.resetUiState(deviceType)
        viewModel.deviceScreen.clearConnectedData(deviceType)
        viewModel.deviceScreen.connectRemoteGatt(mac, deviceType)
        onDispose {
            DialogUtil.hideLoading()
        }
    }
}

@Preview()
@Composable
fun DeviceConnectScreen(
    deviceType: String = "aiNeck",
    isConnecting: Boolean = true,
    connect: () -> Unit = {},
) {
    val imgRes = when (deviceType) {
        DeviceType.aiNeck.name -> R.drawable.img_aispine
        DeviceType.aiBack.name -> R.drawable.img_aiback
        else -> R.drawable.img_aispine
    }
    val title = when (deviceType) {
        DeviceType.aiNeck.name -> stringResource(R.string.welcome_use_aiNeck)
        DeviceType.aiBack.name -> stringResource(R.string.welcome_use_aiBack)
        else -> stringResource(R.string.welcome_use_aiNeck)
    }
    BasePageView(
        showBackIcon = true
    ) {
        Box(
            modifier = Modifier.fillMaxSize()
        ) {
            Text(
                text = title,
                style = TextStyle(
                    fontSize = 32.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF000000),
                ),
                modifier = Modifier
                    .padding(16.dp)
                    .align(Alignment.TopCenter)
                    .padding(top = 32.dp),
            )

            RotatingImageScreen(
                imgRes = imgRes,
                isRotating = isConnecting,
                modifier = Modifier
                    .size(200.dp)
                    .align(Alignment.Center)
            )
            
            if (!isConnecting) {
                AIHTextButton(
                    onClick = connect,
                    text = stringResource(R.string.connect),
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .fillMaxWidth(0.8f)
                        .padding(bottom = 32.dp),
                    textPaddingValues = PaddingValues(vertical = 12.dp)
                )
            }
        }
    }
}


@Composable
fun RotatingImageScreen(
    modifier: Modifier,
    @DrawableRes imgRes: Int,
    isRotating: Boolean = false
) {
    val density = LocalDensity.current
    val rotation = remember { Animatable(0f) }
    val scope = rememberCoroutineScope()

    LaunchedEffect(isRotating) {
        if (isRotating) {
            while (isRotating) {
                rotation.animateTo(
                    targetValue = rotation.value + 360f,
                    animationSpec = tween(
                        durationMillis = 3000,
                        easing = LinearEasing
                    )
                )
            }
        }
    }
    
    Box(
        modifier = modifier.fillMaxSize()
    ) {
        Image(
            painter = painterResource(id = imgRes),
            contentDescription = "",
            modifier = Modifier
                .size(150.dp)
                .align(Alignment.Center)
                .graphicsLayer(
                    rotationY = rotation.value,
                    cameraDistance = 12 * density.density
                ),
            contentScale = ContentScale.Fit
        )
    }
}

@Composable
private fun UpdateDialog(
    visible: Boolean = false,
    deviceType: String,
    updateVersion: () -> Unit = {},
    updateSuccess: () -> Unit = {},
) {
    if (visible) {
        var isUpdating by remember {
            mutableStateOf(false)
        }
        Dialog(onDismissRequest = { }) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                if (!isUpdating) {
                    Column(
                        modifier = Modifier
                            .width(280.dp)
                            .background(Color.White, RoundedCornerShape(20.dp))
                            .padding(horizontal = 16.dp, vertical = 30.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = stringResource(id = R.string.dfu_update_dialog_title1, "6.0.6"),
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Text(text = stringResource(id = R.string.dfu_update_dialog_content1))
                        Spacer(modifier = Modifier.height(20.dp))
                        AIHButton(
                            text = stringResource(id = R.string.update),
                            onClick = {
                                isUpdating = true
                            },
                            modifier = Modifier.size(100.dp, 40.dp)
                        )
                    }
                } else {
                    Column(
                        modifier = Modifier
                            .width(280.dp)
                            .background(Color.White, RoundedCornerShape(20.dp))
                            .padding(horizontal = 16.dp, vertical = 30.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        var progress by remember {
                            mutableIntStateOf(0)
                        }
                        val dfuProgressListener = object : DfuProgressListenerAdapter() {
                            override fun onProgressChanged(
                                deviceAddress: String,
                                percent: Int,
                                speed: Float,
                                avgSpeed: Float,
                                currentPart: Int,
                                partsTotal: Int
                            ) {
                                super.onProgressChanged(
                                    deviceAddress,
                                    percent,
                                    speed,
                                    avgSpeed,
                                    currentPart,
                                    partsTotal
                                )
                                progress = percent
                            }

                            override fun onDfuCompleted(deviceAddress: String) {
                                super.onDfuCompleted(deviceAddress)
                                LogUtil.d("onDfuCompleted: ")
                                updateSuccess()
                            }

                            override fun onDfuAborted(deviceAddress: String) {
                                super.onDfuAborted(deviceAddress)
                                LogUtil.d("onDfuAborted: ")
                            }

                            override fun onError(
                                deviceAddress: String,
                                error: Int,
                                errorType: Int,
                                message: String?
                            ) {
                                super.onError(deviceAddress, error, errorType, message)
                                LogUtil.d("onError: ")
                            }
                        }
                        DisposableEffect(Unit) {
                            DfuServiceListenerHelper.registerProgressListener(
                                activity,
                                dfuProgressListener
                            )
                            onDispose {
                                DfuServiceListenerHelper.unregisterProgressListener(
                                    activity,
                                    dfuProgressListener
                                )
                            }
                        }
                        LaunchedEffect(Unit) {
                            updateVersion()
                        }

                        Text(
                            text = stringResource(id = R.string.dfu_update_dialog_title2),
                            fontSize = 18.sp,
                            fontWeight = FontWeight.Bold,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(30.dp))
                        Text(
                            text = stringResource(
                                id = R.string.dfu_update_dialog_content2,
                                deviceType
                            )
                        )
                        Spacer(modifier = Modifier.height(30.dp))
                        BoxWithConstraints(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(6.dp)
                                .padding(horizontal = 16.dp)
                                .background(Color(0XFF999999), RoundedCornerShape(2.dp))
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(maxWidth * progress / 100, 6.dp)
                                    .background(
                                        Color(0xFF7893EC)
                                    )
                            )
                        }
                        Spacer(modifier = Modifier.height(20.dp))
                        Text(
                            text = "${progress}%",
                            fontSize = 20.sp,
                            color = Color(0XFF333333),
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.align(Alignment.CenterHorizontally)
                        )
                    }
                }
            }
        }
    }
}
