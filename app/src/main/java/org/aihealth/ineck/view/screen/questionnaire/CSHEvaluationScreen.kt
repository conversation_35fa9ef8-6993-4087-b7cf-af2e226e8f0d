package org.aihealth.ineck.view.screen.questionnaire

import android.annotation.SuppressLint
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.QuestionModel
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.CSHEvaluationViewModel


@Composable
fun CSHEvaluationScreen(
    modifier: Modifier = Modifier,
    type: DeviceType = DeviceType.aiNeck,
    viewModel: CSHEvaluationViewModel = viewModel(),
    onStart: () -> Unit = {},
    onSkip: () -> Unit = {}
) {
    val scrollState = rememberScrollState()
    val enable = remember {
        mutableStateOf(false)
    }
    LaunchedEffect(Unit){
        viewModel.startTime = System.currentTimeMillis()
    }
    val title = when (type) {
        DeviceType.aiNeckCV -> stringResource(id = R.string.neck_health_assessment)
        DeviceType.aiNeck -> stringResource(id = R.string.neck_health_assessment)
        DeviceType.aiBackCV -> stringResource(id = R.string.back_health_assessment)
        DeviceType.aiBack -> stringResource(id = R.string.back_health_assessment)
        else -> ""
    }
    val list = when (type) {
        DeviceType.aiNeck -> {
            viewModel.neckQuestionList

        }

        DeviceType.aiNeckCV -> {
            viewModel.neckQuestionList

        }

        DeviceType.aiBackCV -> {
            viewModel.backQuestionList

        }

        DeviceType.aiBack -> {
            viewModel.backQuestionList
        }

        else -> emptyList()
    }

    BasePageView(
        showBackIcon = true,
        headerContent = {
            Text(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(0.8f),
                text = title,
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF141D26),
                    textAlign = TextAlign.Center
                )
            )
            Text(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {
                        enable.value = true
                    },
                text = stringResource(id = R.string.skip),
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF3888FF),
                )
            )
        }
    ) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .verticalScroll(scrollState)
        ) {
            Column (
                modifier = Modifier.fillMaxWidth(),
                horizontalAlignment = Alignment.CenterHorizontally
            ){
                QuestionnaireTagHeader()
            }
            Column(
                modifier = Modifier.padding(horizontal = 12.dp)
            ) {

                list.forEachIndexed { _, questionModel ->
                    when (questionModel.type) {
                        "SCQ" -> SingleChoice(
                            modifier = Modifier
                                .padding(top = 32.dp),
                            questionModel = questionModel
                        ) { indexAns ->
                            questionModel.answer.forEach { ans ->
                                if (ans.check) {
                                    ans.check = false
                                }
                            }
                            questionModel.answer[indexAns].check = true
                        }

                        "MCQs" -> MultipleChoice(
                            modifier = Modifier
                                .padding(top = 32.dp),
                            questionModel = questionModel,
                            onClick = { indexAns ->
                                questionModel.answer[indexAns].check =
                                    !questionModel.answer[indexAns].check
                                LogUtil.i("it.answer[index].check = ${questionModel.answer[indexAns].check}")
                            }
                        )
                    }
                }
                Button(
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(top = 56.dp, bottom = 40.dp)
                        .fillMaxWidth(0.9f)
                        .background(
                            color = Color(0xFF3888FF),
                            shape = RoundedCornerShape(size = 21.dp)
                        ),
                    onClick = {
                        onStart()
                    },
                    colors = ButtonDefaults.buttonColors(Color(0xFF3888FF)),

                    ) {
                    Text(
                        text = stringResource(id = R.string.enter_part_two),
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFF9F9F9),
                        )
                    )
                }
            }
        }
        SkipDialog(
            enable = enable.value,
            onDismissRequest = {
                enable.value = false
            },
            onConfirm = {
                onSkip()
                enable.value = false
            }
        )
    }
}


//@Preview(showBackground = true)
//@Composable
//fun CSHEvaluationScreenPreview() {
//    CSHEvaluationScreen()
//}
@Preview()
@Composable
fun QuestionnaireTagHeader(
    position: Int = 1,
    type: DeviceType = DeviceType.aiNeck,
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    Column (
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Column(
            modifier = Modifier
                .width(346.dp)
                .padding(top = 10.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Row(
                modifier = Modifier
                    .padding(start = 24.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Column(
                    modifier = Modifier
                        .size(24.dp)
                        .background(Color(0xFF3888FF), CircleShape),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "1",
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                        ),
                        textAlign = TextAlign.Center,
                    )
                }
                HorizontalDivider(
                    modifier = Modifier.width(69.dp),
                    thickness = 2.dp,
                    color = if (position > 1) Color(0xFF3888FF) else Color(0xFFD9D9D9)
                )
                Column(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            if (position >= 2) Color(0xFF3888FF) else Color(0xFFD9D9D9),
                            CircleShape
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "2",
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                        ),
                        textAlign = TextAlign.Center,
                    )
                }
                HorizontalDivider(
                    modifier = Modifier.width(69.dp),
                    thickness = 2.dp,
                    color = if (position > 2) Color(0xFF3888FF) else Color(0xFFD9D9D9)
                )
                Column(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            if (position >= 3) Color(0xFF3888FF) else Color(0xFFD9D9D9),
                            CircleShape
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "3",
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                        ),
                        textAlign = TextAlign.Center,
                    )
                }
                HorizontalDivider(
                    modifier = Modifier.width(69.dp),
                    thickness = 2.dp,
                    color = if (position > 3) Color(0xFF3888FF) else Color(0xFFD9D9D9)
                )
                Column(
                    modifier = Modifier
                        .size(24.dp)
                        .background(
                            if (position >= 4) Color(0xFF3888FF) else Color(0xFFD9D9D9),
                            CircleShape
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "4",
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFFFFFFF),
                        ),
                        textAlign = TextAlign.Center,
                    )
                }
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    modifier = Modifier
                        .width(70.dp),
                    text = stringResource(id = R.string.health_questionnaire),
                    style = TextStyle(
                        fontSize = fontSize12,
                        fontWeight = FontWeight(400),
                        color = if (position >= 1) Color(0xFF666666) else Color(0xFFD9D9D9),
                    ),
                    textAlign = TextAlign.Center
                )
                Text(
                    modifier = Modifier
                        .width(70.dp),
                    text = stringResource(id = R.string.scale_collection),
                    style = TextStyle(
                        fontSize = fontSize12,
                        fontWeight = FontWeight(400),
                        color = if (position >= 2) Color(0xFF666666) else Color(0xFFD9D9D9)
                    ),
                    textAlign = TextAlign.Center
                )
                val text3 = when (type) {
                    DeviceType.aiNeckCV -> stringResource(id = R.string.neck_detection)
                    DeviceType.aiNeck -> stringResource(id = R.string.neck_detection)
                    DeviceType.aiBack -> stringResource(id = R.string.back_detection)
                    DeviceType.aiBackCV -> stringResource(id = R.string.back_detection)
                    else -> ""
                }
                Text(
                    modifier = Modifier
                        .width(70.dp),
                    text = text3,
                    style = TextStyle(
                        fontSize = fontSize12,
                        fontWeight = FontWeight(400),
                        color = if (position >= 3) Color(0xFF666666) else Color(0xFFD9D9D9),
                    ),
                    textAlign = TextAlign.Center
                )
                Text(
                    modifier = Modifier
                        .width(70.dp),
                    text = stringResource(id = R.string.montoring),
                    style = TextStyle(
                        fontSize = fontSize12,
                        fontWeight = FontWeight(400),
                        color = if (position >= 4) Color(0xFF666666) else Color(0xFFD9D9D9),
                    ),
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}

@Composable
fun SingleChoice(
    modifier: Modifier = Modifier,
    questionModel: QuestionModel,
    enable: Boolean = true,
    onClick: (Int) -> Unit,
) {
    Column(modifier = modifier.fillMaxWidth())
    {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
        ) {
            Column(
                Modifier
                    .background(
                        color = Color(0xFF3888FF),
                        shape = RoundedCornerShape(
                            topStart = 4.dp,
                            topEnd = 4.dp,
                            bottomStart = 0.dp,
                            bottomEnd = 4.dp,
                        )
                    ),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = stringResource(id = R.string.single_selection),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
            Text(
                modifier = Modifier
                    .padding(start = 12.dp),
                text = questionModel.text,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                )
            )
        }
        questionModel.answer.forEachIndexed { index, it ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp)
                    .background(
                        color = if (it.check) Color(0x1A3888FF) else Color(0xFFF5F5F5),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .border(
                        width = if (it.check) 1.dp else 0.dp,
                        color = if (it.check) Color(0xFF3888FF) else Color.Transparent,
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .clickable(enabled = enable) {
                        onClick(index)
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                if (it.check) {
                    Image(
                        modifier = Modifier.padding(start = 12.dp, top = 14.dp, bottom = 13.dp),
                        painter = painterResource(id = R.drawable.q_s_c),
                        contentDescription = "image description",
                        contentScale = ContentScale.None
                    )
                } else {
                    Image(
                        modifier = Modifier.padding(start = 11.dp, top = 13.dp, bottom = 12.dp),
                        painter = painterResource(id = R.drawable.q_s_c_no),
                        contentDescription = "image description",
                        contentScale = ContentScale.None
                    )
                }

                Text(
                    modifier = Modifier
                        .padding(start = 11.dp),
                    text = it.text,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                    )
                )
            }

        }
    }
}


@Composable
fun MultipleChoice(
    modifier: Modifier = Modifier,
    questionModel: QuestionModel,
    enable: Boolean = true,
    onClick: (Int) -> Unit,
) {
    Column(modifier = modifier.fillMaxWidth())
    {
        Row(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.Start,
        ) {
            Column(
                Modifier
                    .background(
                        color = Color(0xFF3888FF),
                        shape = RoundedCornerShape(
                            topStart = 4.dp,
                            topEnd = 4.dp,
                            bottomStart = 0.dp,
                            bottomEnd = 4.dp,
                        )
                    ),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                Text(
                    text = stringResource(id = R.string.multiple_selection),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
            Text(
                modifier = Modifier
                    .padding(start = 12.dp),
                text = questionModel.text,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                )
            )
        }
        questionModel.answer.forEachIndexed { index, it ->
            Row(
                Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp)
                    .background(
                        color = if (it.check) Color(0x1A3888FF) else Color(0xFFF5F5F5),
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .border(
                        width = if (it.check) 1.dp else 0.dp,
                        color = if (it.check) Color(0xFF3888FF) else Color.Transparent,
                        shape = RoundedCornerShape(size = 4.dp)
                    )
                    .clickable(enabled = enable) {
                        onClick(index)
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                if (it.check) {
                    Image(
                        modifier = Modifier
                            .padding(vertical = 12.dp)
                            .padding(start = 12.dp),
                        painter = painterResource(id = R.drawable.q_m_c),
                        contentDescription = "image description",
                        contentScale = ContentScale.None
                    )
                } else {
                    Image(
                        modifier = Modifier
                            .padding(vertical = 12.dp)
                            .padding(start = 12.dp),
                        painter = painterResource(id = R.drawable.q_m_c_no),
                        contentDescription = "image description",
                        contentScale = ContentScale.None
                    )
                }
                Text(
                    modifier = Modifier
                        .padding(start = 10.dp),
                    text = it.text,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                    )
                )
            }

        }
    }
}

@Preview
@Composable
fun SkipDialog(
    enable: Boolean = true,
    onDismissRequest: () -> Unit ={},
    onConfirm: () -> Unit={},
) {
    if (enable) {
        Dialog(
            onDismissRequest = { onDismissRequest() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false,
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(0.9f)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center,
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.prompt),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF141D26),
                        letterSpacing = 0.63.sp,
                    )
                )
                Spacer(modifier = Modifier.height(14.dp))
                Text(
                    text = stringResource(id = R.string.prompt_info),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF5B6167),

                        textAlign = TextAlign.Start,
                        letterSpacing = 0.49.sp,
                    ),
                    modifier = Modifier.padding(horizontal = 20.dp),
                )
                Spacer(modifier = Modifier.height(20.dp))
                Row(
                    Modifier
                        .fillMaxWidth()
                        .padding(bottom = 20.dp),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Button(
                        modifier = Modifier
                            .weight(0.4f)
                            .padding(end = 5.dp),
                        colors = ButtonDefaults.buttonColors(Color(0xFFF3F4F4)),
                        onClick = { onDismissRequest() }
                    )
                    {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0xFF141D26),
                            fontSize = 16.sp
                        )
                    }
                    Button(
                        modifier = Modifier
                            .weight(0.4f)
                            .padding(end = 5.dp),
                        colors = ButtonDefaults.buttonColors(Color(0xFF3888FF)),
                        onClick = { onConfirm() })
                    {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0xFFFFFFFF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }

}
