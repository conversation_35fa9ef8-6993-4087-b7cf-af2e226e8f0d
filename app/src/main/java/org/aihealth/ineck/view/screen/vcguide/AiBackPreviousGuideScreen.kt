package org.aihealth.ineck.view.screen.vcguide

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layout
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Preview(showBackground = true, locale = "cn")
@Composable
fun AiBackPreviousGuideScreen(
    modifier: Modifier = Modifier,
    onIgnoreEvent: () -> Unit = {},
    onNextEvent: () -> Unit = {}
) {
    var maxTextWidth by remember { mutableIntStateOf(0) }
    var maxTextHeight by remember { mutableIntStateOf(0) }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
    ) {
        // 文案
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 8.dp)
                .background(color = Color(0xFFF4F4F4)),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            Text(
                modifier = Modifier.padding(horizontal = 4.dp),
                text = stringResource(id = R.string.precautions_of_detect_guide_title),
                fontWeight = FontWeight.W400,
                color = Color(0xFF333333),
                fontSize = 22.sp,
                overflow = TextOverflow.Ellipsis
            )

            Column(
                modifier = Modifier
                    .layout { measurable, constraints ->
                        val placeable = measurable.measure(constraints)
                        if (placeable.width - maxTextWidth > 60) {
                            layout(placeable.width, placeable.height) {
                                placeable.placeRelative((placeable.width - maxTextWidth) / 50, 0)
                            }
                        } else {
                            layout(placeable.width, placeable.height) {
                                placeable.placeRelative(0, 0)
                            }
                        }
                    }
                    .padding(horizontal = 20.dp),
                horizontalAlignment = Alignment.Start,
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                Spacer(modifier = Modifier.height(10.dp))
                /* 第一点 */
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(vertical = 5.dp)
                ) {
                    /* 序号 */
                    Box(
                        modifier = Modifier
                            .size(width = 14.dp, height = 20.dp)
                            .background(color = Color.Transparent)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(width = 13.dp, height = 4.dp)
                                .background(
                                    color = Color(0xFFBCCCFF),
                                    shape = RoundedCornerShape(size = 2.dp)
                                )
                                .align(Alignment.BottomCenter),
                        )
                        Image(
                            painter = painterResource(id = R.drawable.img_serial_number_1),
                            contentDescription = "No.1",
                            modifier = Modifier
                                .size(13.dp)
                                .align(Alignment.Center)
                        )
                    }
                    /* 间隔 */
                    Spacer(modifier = Modifier.width(9.dp))
                    /* 文本内容 */
                    Text(
                        text = stringResource(id = R.string.back_guide_1),
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Start
                    )
                }
                /* 第二点 */
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(vertical = 5.dp)
                ) {
                    /* 序号 */
                    Box(
                        modifier = Modifier
                            .size(width = 14.dp, height = 20.dp)
                            .background(color = Color.Transparent)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(width = 13.dp, height = 4.dp)
                                .background(
                                    color = Color(0xFFBCCCFF),
                                    shape = RoundedCornerShape(size = 2.dp)
                                )
                                .align(Alignment.BottomCenter),
                        )
                        Image(
                            painter = painterResource(id = R.drawable.img_serial_number_2),
                            contentDescription = "No.2",
                            modifier = Modifier
                                .size(13.dp)
                                .align(Alignment.Center)
                        )
                    }
                    /* 间隔 */
                    Spacer(modifier = Modifier.width(9.dp))
                    /* 文本内容 */
                    Text(
                        text = stringResource(id = R.string.back_guide_2),
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Start
                    )
                }
                /* 第三点 */
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .layout { measurable, constraints ->
                            val placeable = measurable.measure(constraints)
                            maxTextWidth = placeable.width
                            maxTextHeight = placeable.height
                            layout(placeable.width, placeable.height) {
                                placeable.placeRelative(0, 0)
                            }
                        }
                        .padding(vertical = 5.dp)
                ) {
                    /* 序号 */
                    Box(
                        modifier = Modifier
                            .size(width = 14.dp, height = 20.dp)
                            .background(color = Color.Transparent)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(width = 13.dp, height = 4.dp)
                                .background(
                                    color = Color(0xFFBCCCFF),
                                    shape = RoundedCornerShape(size = 2.dp)
                                )
                                .align(Alignment.BottomCenter),
                        )
                        Image(
                            painter = painterResource(id = R.drawable.img_serial_number_3),
                            contentDescription = "No.3",
                            modifier = Modifier
                                .size(13.dp)
                                .align(Alignment.Center)
                        )
                    }
                    /* 间隔 */
                    Spacer(modifier = Modifier.width(9.dp))
                    /* 文本内容 */
                    Text(
                        text = stringResource(id = R.string.back_guide_3),
                        color = Color(0xFF666666),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.W400,
                        textAlign = TextAlign.Start
                    )
                }
            }
        }

        /* 示例人像 */
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF4F4F4), RoundedCornerShape(8.dp)),
            painter = painterResource(id = R.drawable.img_detect_guide_example_aiback),
            contentDescription = stringResource(id = R.string.text_portrait_example),
            contentScale = ContentScale.Fit,
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFFF4F4F4)),
            text = stringResource(id = R.string.detection_example_img_title),
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF666666),
                textAlign = TextAlign.Center,
            )
        )
        Row(
            modifier = Modifier
                .background(Color(0xFFF4F4F4))
                .padding(vertical = 16.dp)
                .fillMaxWidth()
                .height(IntrinsicSize.Max),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            OutlinedButton(
                shape = RoundedCornerShape(4.dp),
                onClick = onIgnoreEvent,
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = Color(0xFFEFEFEF),
                    contentColor = Color(0xFFEFEFEF),
                    disabledContainerColor = Color(0xFFEFEFEF),
                    disabledContentColor = Color(0xFFEFEFEF)
                ),
                border = BorderStroke(
                    width = 1.dp,
                    color = Color(0xFFCCCCCC)
                ),
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(start = 4.dp)
            ) {
                Text(
                    text = stringResource(id = R.string.ignore),
                    color = Color(0xFF444444),
                    fontWeight = FontWeight.Medium,
                    fontSize = 14.sp
                )
            }
            Button(
                shape = RoundedCornerShape(4.dp),
                onClick = onNextEvent,
                modifier = Modifier
                    .weight(1f)
                    .fillMaxHeight()
                    .padding(end = 4.dp),
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0XFF1E4BDF),
                    contentColor = Color(0XFF1E4BDF),
                    disabledContainerColor = Color(0XFF1E4BDF),
                    disabledContentColor = Color(0XFF1E4BDF)
                )
            ) {
                Text(
                    text = stringResource(id = R.string.next_continue),
                    style = TextStyle(
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                )
            }
        }
    }
}