package org.aihealth.ineck.view.preview

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHCard

/**
 * 使用引导
 */
@Preview
@Composable
fun TryLeader(){
    AIHCard {
        Row(
            modifier = Modifier.padding(10.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceAround,
        ) {
            Row(
                modifier = Modifier.weight(0.6f),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start,
            ) {
                Icon(
                    modifier = Modifier.padding(end = 5.dp),
                    painter = painterResource(id = R.drawable.vip_tip), contentDescription = "tip")
                Text(
                    text = stringResource(id = R.string.improves_prevention_of_cervical_and_lumbar_pain),
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF666666),
                        textAlign = TextAlign.Center,
                        letterSpacing = 0.42.sp,
                    ),
                    maxLines = 2
                )
            }
            Row(
                modifier = Modifier
                    .weight(0.3f)
                    .clickable {
                        startScreen(Screen.MemberShipCenter.route, false)
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceAround,
            ) {
                Text(
                    text = stringResource(id = R.string._30_days_free_trial),
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF395AD2),
                        textAlign = TextAlign.Center,
                        letterSpacing = 0.35.sp,
                    )
                )
                Image(painter = painterResource(id = R.drawable.ic_blue_next), contentDescription = "tip")
            }

        }
    }
}
