package org.aihealth.ineck.view.screen.vcguide

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectedError
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel

@Composable
fun VCDetectedScreen(
    modifier: Modifier = Modifier,
    viewModel: VCGuideViewModel,
    onDismissEvent: (VCDetectingResult) -> Unit,
    tryAgainEvent: () -> Unit
) {
    val result = viewModel.detectedResult.collectAsState()
    LogUtil.i("VCDetectedScreen: ${result.value}")
    when (result.value) {
        is VCDetectingResult.DetectingError -> {
            VCDetectErrorScreen(
                modifier = modifier.fillMaxWidth(),
                detectedResult = result.value as VCDetectingResult.DetectingError,
                tryAgainEvent = tryAgainEvent,
                onDismissEvent = { onDismissEvent(VCDetectingResult.DetectingError(VCDetectedError.Cancel())) }
            )
        }

        else -> {
            onDismissEvent(result.value)
        }
    }

}

//@Preview(showBackground = true, locale = "en")
@Composable
fun VCDetectErrorScreen(
    modifier: Modifier = Modifier,
    detectedResult: VCDetectingResult.DetectingError = VCDetectingResult.DetectingError(
        VCDetectedError.Timeout()
    ),
    tryAgainEvent: () -> Unit = {},
    onDismissEvent: () -> Unit = {}
) {
    ConstraintLayout(
        modifier = modifier
            .fillMaxWidth()
            .height(170.dp)
    ) {
        val (title, text, buttonGroup) = createRefs()
        val titleGuideline = createGuidelineFromTop(.25f)
        val centerGuideline = createGuidelineFromTop(.5f)
        val bottomGuideline = createGuidelineFromBottom(.3f)
        Text(
            text = stringResource(id = R.string.detect_guide_failure),
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF464646),
                letterSpacing = 0.63.sp,
            ),
            modifier = Modifier.constrainAs(title) {
                start.linkTo(parent.start, 20.dp)
                top.linkTo(titleGuideline)
                end.linkTo(parent.end, 20.dp)
                bottom.linkTo(titleGuideline)
            }
        )
        Text(
            text = when (detectedResult.error) {
                is VCDetectedError.NoObject -> {
                    stringResource(id = R.string.detect_guide_no_face)
                }

                is VCDetectedError.Timeout -> {
                    stringResource(id = R.string.detect_guide_time_out)
                }

                else -> {
                    stringResource(id = R.string.detect_guide_no_face)
                }
            },
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight.W400,
                color = Color(0xFF999999),
            ),
            overflow = TextOverflow.Ellipsis,
            modifier = Modifier
                .constrainAs(text) {
                    start.linkTo(parent.start, 36.dp)
                    top.linkTo(centerGuideline)
                    end.linkTo(parent.end, 36.dp)
                    bottom.linkTo(centerGuideline)
                }
                .padding(start = 8.dp)
        )
        Row(
            modifier = Modifier
                .border(width = 1.dp, color = Color(0xFFD9D9D9))
                .constrainAs(buttonGroup) {
                    start.linkTo(parent.start, 36.dp)
                    top.linkTo(bottomGuideline)
                    end.linkTo(parent.end, 36.dp)
                    bottom.linkTo(parent.bottom)
                    height = Dimension.fillToConstraints
                },
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            TextButton(
                onClick = onDismissEvent,
                modifier = Modifier
                    .fillMaxHeight()
                    .background(color = Color.Transparent)
                    .weight(1f),
            ) {
                Text(
                    text = stringResource(id = R.string.detect_guide_next_time),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFF999999),
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(1.dp)
                    .background(color = Color(0xFFD9D9D9))
            )
            TextButton(
                onClick = tryAgainEvent,
                modifier = Modifier
                    .fillMaxHeight()
                    .background(color = Color.Transparent)
                    .weight(1f),
            ) {
                Text(
                    text = stringResource(id = R.string.detect_guide_try_again),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight.W400,
                        color = Color(0xFF6181E9),
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis,
                    textAlign = TextAlign.Center
                )
            }
        }
    }
}