package org.aihealth.ineck.view.screen.questionnaire.aiback

import android.Manifest
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.LifecycleOwner
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.rememberPagerState
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.dialog.AIHTipButtonDialog
import org.aihealth.ineck.view.screen.vcguide.AiBackPreviousGuideScreen
import org.aihealth.ineck.view.screen.vcguide.AiBackVCDetectingScreen
import org.aihealth.ineck.view.screen.vcguide.AiBackVCFaceDetectingScreen
import org.aihealth.ineck.view.screen.vcguide.CloseDetectingDialog
import org.aihealth.ineck.view.screen.vcguide.VCBackDetectingScreen
import org.aihealth.ineck.view.screen.vcguide.VCDetectedScreen
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectedError
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideProcessState

@Composable
fun BackDetectionScreen(
    modifier: Modifier = Modifier,
    viewModel: org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current,
    onDismissEvent: (VCDetectingResult) -> Unit,
) {
    val pagerState = rememberPagerState(
        initialPage = VCGuideProcessState.PreviousGuidePage.toPageNumber(),
    )

    // scope
    val dialogScope = rememberCoroutineScope()
    val isSpeechEnable = remember {
        TextToSpeech.isSpeakingEnable()
    }
    /* 当前静音状态 */
    val isMuteState = viewModel.isMuteState.collectAsState()

    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Card(
            modifier = modifier,
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFFF4F4F4),
            ),
            shape = RoundedCornerShape(10.dp),
        ) {
            HorizontalPager(
                state = pagerState,
                count = 3,
                modifier = Modifier,
                userScrollEnabled = false
            ) { page ->
                when (page) {
                    VCGuideProcessState.PreviousGuidePage.toPageNumber() -> {
                        AiBackPreviousGuideScreen(
                            modifier = Modifier.fillMaxWidth(),
                            onIgnoreEvent = {
                                onDismissEvent(
                                    VCDetectingResult.DetectingError(
                                        VCDetectedError.Cancel()
                                    )
                                )
                            },
                            onNextEvent = {
                                if (ActivityResultUtils.checkPermissions(arrayOf(Manifest.permission.CAMERA))) {
                                    viewModel.changeGuideDetectProcessState(VCGuideProcessState.DetectingPage)
                                    dialogScope.launch {
                                        pagerState.scrollToPage(VCGuideProcessState.DetectingPage.toPageNumber())
                                    }
                                } else {
                                    viewModel.showPowerDialogVisibleState = true
                                }
                            }
                        )
                    }

                    VCGuideProcessState.DetectingPage.toPageNumber() -> {
                        // 校准过程中的分页
                        val detectingPagerState = rememberPagerState(0)
                        AiBackVCDetectingScreen(
                            modifier = Modifier
                                .fillMaxWidth()
                                .wrapContentHeight(),
                            pagerState = detectingPagerState,
                            pagerCount = 2,
                        ) { currentDetectingPager ->
                            when (currentDetectingPager) {
                                0 -> {
                                    VCBackDetectingScreen(
                                        modifier = Modifier.fillMaxWidth(),
                                        isMuteState = isMuteState.value,
                                        isVoiceEnable = isSpeechEnable,
                                        changeMuteState = {
                                            viewModel.changeCurrentMuteState(!isMuteState.value)
                                        },
                                        nextPage = {
                                            LogUtil.d("VCHorizontalDetectingScreen next Pager")
                                            dialogScope.launch {
                                                detectingPagerState.scrollToPage(1)
                                            }
                                        },
                                        timeOut = {
                                            LogUtil.d("VCHorizontalDetectingScreen time Out")

                                            viewModel.changeDetectedResult(
                                                VCDetectingResult.DetectingError(
                                                    VCDetectedError.Timeout()
                                                )
                                            )
                                            viewModel.changeGuideDetectProcessState(
                                                VCGuideProcessState.DetectedPage
                                            )
                                            dialogScope.launch {
                                                pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                            }
                                        }
                                    )
                                }

                                1 -> {
                                    AiBackVCFaceDetectingScreen(
                                        modifier = Modifier.fillMaxWidth(),
                                        isMuteState = isMuteState.value,
                                        isVoiceEnable = isSpeechEnable,
                                        changeMuteState = {
                                            viewModel.changeCurrentMuteState(!isMuteState.value)
                                        },
                                        nextPage = { it ->
                                            viewModel.changeDetectedResult(it)
                                            viewModel.changeGuideDetectProcessState(
                                                VCGuideProcessState.DetectedPage
                                            )
                                            dialogScope.launch {
                                                pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                detectingPagerState.scrollToPage(0)
                                            }
                                        },
                                        lifecycleOwner = lifecycleOwner,
                                        timeOut = {
                                            viewModel.changeDetectedResult(
                                                VCDetectingResult.DetectingError(
                                                    VCDetectedError.Timeout()
                                                )
                                            )
                                            viewModel.changeGuideDetectProcessState(
                                                VCGuideProcessState.DetectedPage
                                            )
                                            dialogScope.launch {
                                                pagerState.scrollToPage(VCGuideProcessState.DetectedPage.toPageNumber())
                                                detectingPagerState.scrollToPage(0)
                                            }
                                        }
                                    )
                                }
                            }

                        }
                    }

                    VCGuideProcessState.DetectedPage.toPageNumber() -> {
                        LogUtil.i(
                            "VCGuideScreen",
                            "${viewModel.detectedResult.collectAsState().value}"
                        )
                        VCDetectedScreen(
                            modifier = Modifier.fillMaxWidth(),
                            viewModel = viewModel,
                            onDismissEvent = {
                                dialogScope.launch {
                                    pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
                                }
                                onDismissEvent(it)
                            },
                            tryAgainEvent = {
                                viewModel.changeGuideDetectProcessState(VCGuideProcessState.PreviousGuidePage)

                                dialogScope.launch {
                                    pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
                                }
                            }
                        )
                    }
                }
            }


        }
        if (pagerState.currentPage != 0 && pagerState.currentPage != 2) {
            CloseDetectingDialog(
                modifier = Modifier
                    .padding(top = 10.dp),
                onDismissEvent = {
                    dialogScope.launch {
//                                pagerState.scrollToPage(VCGuideProcessState.PreviousGuidePage.toPageNumber())
//                                detectingPagerState.scrollToPage(0)
                    }
                    onDismissEvent(VCDetectingResult.DetectingError(VCDetectedError.Cancel()))
                }
            )
        }
    }


    ShowPowerDialog(viewModel)

}

/**
 * 权限说明弹窗
 */
@Composable
private fun ShowPowerDialog(
    viewModel: org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel
) {
    if (viewModel.showPowerDialogVisibleState) {
        Dialog(onDismissRequest = { viewModel.showPowerDialogVisibleState = false }) {
            Column(
                modifier = Modifier
                    .width(300.dp)
                    .clip(RoundedCornerShape(12.dp))
                    .background(Color.White)
            ) {
                Spacer(modifier = Modifier.height(20.dp))
                Text(
                    text = stringResource(id = R.string.camera_permission),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 40.dp),
                    color = Color(0XFF333333),
                    fontWeight = FontWeight.Medium
                )
                Spacer(modifier = Modifier.height(20.dp))
                AIHDivider()
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                            }, contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                    AIHDivider(isVertical = true)
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F)
                            .clickable {
                                viewModel.showPowerDialogVisibleState = false
                                ActivityResultUtils.requestPermissions(
                                    permissions = arrayOf(Manifest.permission.CAMERA),
                                    onAllGranted = {
                                    },
                                    onProhibited = {
                                        if (SPUtil.getBoolean(
                                                SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION,
                                                true
                                            )
                                        ) {
                                            SPUtil.putBoolean(
                                                SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION,
                                                false
                                            )
                                        } else {
                                            viewModel.showPowerDialogVisibleState =
                                                false
                                            DialogUtil.showToast {
                                                AIHTipButtonDialog(
                                                    text = localeResources.getString(R.string.permission_denied),
                                                    buttonText = localeResources.getString(
                                                        R.string.to_authorization
                                                    ),
                                                    onClick = {
                                                        APPUtil.goAPPDetail()
                                                    }
                                                ) {
                                                    DialogUtil.hideToast()
                                                }
                                            }
                                        }
                                    }
                                )
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.confirm),
                            color = Color(0XFF1E4BDF),
                            fontSize = 16.sp
                        )
                    }
                }
            }
        }
    }
}