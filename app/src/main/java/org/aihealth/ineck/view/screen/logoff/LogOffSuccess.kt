package org.aihealth.ineck.view.screen.logoff

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun LogOffSuccessScreen() {
    BasePageView(
        title = stringResource(id = R.string.logoff_account),
        showBackIcon = true
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(horizontal = 12.dp),
            ) {
                Text(
                    modifier = Modifier.padding(top = 18.dp),
                    text = stringResource(id = R.string.logoff_account_success),
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF444444),
                    )
                )

                Text(
                    modifier = Modifier.padding(top = 12.dp),
                    text = stringResource(id = R.string.logoff_account_success_text),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF444444),
                    )
                )
            }

            Button(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 68.dp)
                    .fillMaxWidth(0.8f),
                shape = RoundedCornerShape(size = 21.dp),
                colors = ButtonDefaults.buttonColors(Color(0xFF1E4BDF)),
                onClick = {
                    startScreen(Screen.Login.route, true)
                    MainViewModel.pageIndex = 0
                }
            ) {
                Text(
                    text = stringResource(id = R.string.done),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }

        }
    }
}