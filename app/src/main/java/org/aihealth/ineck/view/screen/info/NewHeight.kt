package org.aihealth.ineck.view.screen.info

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import kotlinx.coroutines.flow.distinctUntilChanged
import org.aihealth.ineck.R
import org.aihealth.ineck.model.Constants.HEIGHT_CM_MAX_LIMIT
import org.aihealth.ineck.model.Constants.HEIGHT_CM_MIN_LIMIT
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import kotlin.math.abs
import kotlin.math.roundToInt

@Preview()
@Composable
fun HeightVerticalPickerWithInputDialog(
    visible: Boolean = true,
    initialHeight: Double = 0.0,
    selectedUnit: String = "M",
    onHeightSelected: (HeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    AnimatedVisibility(
        visible = visible
    ) {
        Dialog(
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            val context = LocalContext.current
            var selectedOption by remember {
                mutableStateOf(
                    when (selectedUnit) {
                        "I" -> "inch"
                        "M" -> "cm"
                        else -> "inch"
                    }
                )
            }
            val heightData = (0..300).toList()
            val heightDataInch = (0..118).toList()

            var heightInput by remember(initialHeight) {
                mutableDoubleStateOf(
                    initialHeight
                )
            }
            var mTextFieldInput by remember(heightInput) { mutableStateOf(heightInput.toString()) }
            var iTextFieldInput by remember(heightInput) { mutableStateOf((heightInput * 0.393701).toString()) }
            val keyboardController = LocalSoftwareKeyboardController.current
            val focusManager = LocalFocusManager.current
            Column(
                modifier = Modifier
                    .background(Color.White, RoundedCornerShape(16.dp))
                    .fillMaxWidth(0.98f)
                    .padding(horizontal = 12.dp)
            ) {
                // close icon
                Row(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Icon(
                        modifier = Modifier
                            .size(24.dp)
                            .clickable {
                                onDismiss()
                            },
                        painter = painterResource(R.drawable.ic_close),
                        contentDescription = "close",
                        tint = Color.Black,
                    )
                }
                // title
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                        .padding(top = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.height),
                        style = TextStyle(
                            fontSize = 20.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(end = 24.dp)
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "inch",
                        selected = selectedOption == "inch",
                        onClick = {
                            selectedOption = "inch"
                        }
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "cm",
                        selected = selectedOption == "cm",
                        onClick = {
                            selectedOption = "cm"
                        }
                    )
                }
                // data picker
                Column(
                    modifier = Modifier
                        .padding(top = 16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    when (selectedOption) {
                        "cm" -> {
                            // data input
                            var textFieldValue by remember(mTextFieldInput) {
                                mutableStateOf(
                                    TextFieldValue(
                                        text =
                                        if (mTextFieldInput.toDoubleOrNull() == null
                                            || mTextFieldInput.last() == '.'
                                            || mTextFieldInput.toIntOrNull() != null
                                        ) {
                                            mTextFieldInput
                                        } else {
                                            String.format("%.1f", mTextFieldInput.toDouble())
                                        },
                                        selection = TextRange(
                                            if (mTextFieldInput.toDoubleOrNull() == null
                                                || mTextFieldInput.last() == '.'
                                                || mTextFieldInput.toIntOrNull() != null
                                            ) {
                                                mTextFieldInput.length
                                            } else {
                                                String.format("%.1f", mTextFieldInput.toDouble()).length
                                            }
                                        )
                                    )
                                )
                            }
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .onFocusChanged { focusState ->
                                        if (!focusState.isFocused) {
                                            // Hide the keyboard when the TextField loses focus
                                            keyboardController?.hide()
                                        }
                                    },
                                value = textFieldValue,
                                onValueChange = { newText ->
                                    LogUtil.i("newText$newText")
                                    if (newText.text.isEmpty()) {
                                        mTextFieldInput = ""
                                    } else if (newText.text.toDoubleOrNull() != null) {
                                        mTextFieldInput = newText.text
                                    }
                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        heightInput =
                                            if (mTextFieldInput.toDoubleOrNull() != null) mTextFieldInput.toDouble()
                                            else 0.0
                                        focusManager.clearFocus()
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            VerticalDataSelector(
                                modifier = Modifier
                                    .padding(top = 10.dp)
                                    .height(250.dp),
                                items = heightData,
                                initialData =
                                when (heightInput) {
                                    in (heightData.first() * 1.0..heightData.last() * 1.0) -> {
                                        heightInput
                                    }

                                    else -> {
                                        when {
                                            heightInput < heightData.first() * 1.0 -> heightData.first() * 1.0
                                            else -> heightData.last() * 1.0
                                        }
                                    }
                                },
                                unit = "cm"
                            ) { height ->
                                LogUtil.i("select item :${height}")
                                heightInput = height
                            }
                        }

                        "inch" -> {
                            // data input
                            // 管理 TextField 的状态，包括文本和光标位置
                            var textFieldValue by remember(iTextFieldInput) {
                                mutableStateOf(
                                    TextFieldValue(
                                        text =
                                        if (iTextFieldInput.toDoubleOrNull() == null
                                            || iTextFieldInput.last() == '.'
                                            || iTextFieldInput.toIntOrNull() != null
                                        ) {
                                            iTextFieldInput
                                        } else {
                                            String.format("%.1f", iTextFieldInput.toDouble())
                                        },
                                        selection = TextRange(
                                            if (iTextFieldInput.toDoubleOrNull() == null
                                                || iTextFieldInput.last() == '.'
                                                || iTextFieldInput.toIntOrNull() != null
                                            ) {
                                                iTextFieldInput.length
                                            } else {
                                                String.format(
                                                    "%.1f",
                                                    iTextFieldInput.toDouble()
                                                ).length
                                            }
                                        )
                                    )
                                )
                            }
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .onFocusChanged { focusState ->
                                        if (!focusState.isFocused) {
                                            // Hide the keyboard when the TextField loses focus
                                            keyboardController?.hide()
                                        }
                                    },
                                value = textFieldValue,
                                onValueChange = { newText ->
                                    if (newText.text.isEmpty()) {
                                        iTextFieldInput = ""
                                    } else if (newText.text.toDoubleOrNull() != null) {
                                        iTextFieldInput = newText.text
                                    }
                                    LogUtil.i("newText$newText, iTextFieldInput$iTextFieldInput")
                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        heightInput =
                                            if (iTextFieldInput.toDoubleOrNull() != null) iTextFieldInput.toDouble() * 2.54
                                            else 0.0
                                        focusManager.clearFocus()
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            VerticalDataSelector(
                                modifier = Modifier
                                    .padding(top = 10.dp)
                                    .height(250.dp),
                                items = heightDataInch,
                                initialData =
                                when (heightInput * 0.393701) {
                                    in (heightDataInch.first() * 1.0..heightDataInch.last() * 1.0) -> {
                                        heightInput * 0.393701
                                    }

                                    else -> {
                                        when {
                                            heightInput * 0.393701 < heightDataInch.first() * 1.0 -> heightDataInch.first() * 1.0
                                            else -> heightDataInch.last() * 1.0
                                        }
                                    }
                                },
                                unit = "inch"
                            ) { height ->
                                LogUtil.i("select item :${height}")
                                heightInput = (height * 2.54)
                            }
                        }

                        else -> {}
                    }
                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 30.dp),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Box(
                        Modifier
                            .weight(1f)
                            .border(
                                width = 1.dp,
                                color = Color(0xFF3262FF),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .background(
                                shape = RoundedCornerShape(size = 16.dp),
                                color = Color.White
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable { onDismiss() }
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            modifier = Modifier
                                .align(Alignment.Center)
                                .clickable { onDismiss() },
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF3C72FF),
                            )
                        )
                    }
                    Spacer(modifier = Modifier.size(15.dp))
                    Box(
                        Modifier
                            .weight(1f)
                            .background(
                                Brush.linearGradient(
                                    colors = listOf(
                                        Color(0XFF73C5FF),
                                        Color(0XFF3161FF),
                                    )
                                ),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable {
                                keyboardController?.hide()
                                when (selectedOption) {
                                    "cm" -> {
                                        heightInput =
                                            if (mTextFieldInput.toDoubleOrNull() != null) mTextFieldInput.toDouble()
                                            else 0.0
                                        if (heightInput <= HEIGHT_CM_MIN_LIMIT || heightInput >= HEIGHT_CM_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_metric))
                                        } else {
                                            onHeightSelected(
                                                HeightUnit(
                                                    value = heightInput,
                                                    unit = "M"
                                                )
                                            )
                                        }
                                    }

                                    else -> {
                                        heightInput =
                                            if (iTextFieldInput.toDoubleOrNull() != null) iTextFieldInput.toDouble() * 2.54
                                            else 0.0

                                        if (heightInput <= HEIGHT_CM_MIN_LIMIT || heightInput >= HEIGHT_CM_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_height_imperial))
                                        } else {
                                            onHeightSelected(
                                                HeightUnit(
                                                    value = heightInput,
                                                    unit = "I"
                                                )
                                            )
                                        }

                                    }
                                }
                            }
                    ) {
                        Text(
                            modifier = Modifier.align(Alignment.Center),
                            text = stringResource(id = R.string.confirm),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color.White
                            )
                        )
                    }

                }
            }

        }
    }
}

@Preview()
@Composable
fun VerticalDataSelector(
    modifier: Modifier = Modifier,
    items: List<Int> = (50..300).toList(),
    initialData: Double = 50.0,
    unit: String = "",
    zoom: Int = 10,
    onItemSelected: (Double) -> Unit = { _ -> }
) {
    val listState = rememberLazyListState()

    val density = LocalDensity.current

    val componentSize = remember { mutableStateOf(IntSize.Zero) }
    // Screen width in pixels
    val screenHeightPx = with(density) { componentSize.value.height.toDp().toPx() }

    // Item width in pixels
    val itemHeightDp = 15.dp
    val itemHeightPx = with(density) { itemHeightDp.toPx() }

    // Calculate start and end padding to center items
    val startEndPaddingPx = (screenHeightPx - itemHeightPx) / 2
    val startEndPadding = with(density) { startEndPaddingPx.toDp() }

    val lists = (items.first() * zoom..items.last() * zoom).toList()
    var selectedValue by remember(initialData) { mutableStateOf((initialData * zoom).roundToInt()) }
    // Calculate center item index
    val centerItemIndex by remember {
        derivedStateOf {
            val layoutInfo = listState.layoutInfo
            if (layoutInfo.visibleItemsInfo.isNotEmpty()) {
                val viewportCenter =
                    layoutInfo.viewportStartOffset + layoutInfo.viewportSize.height / 2
                val index = layoutInfo.visibleItemsInfo.minByOrNull { item ->
                    val itemCenter = item.offset + item.size / 2
                    abs(itemCenter - viewportCenter)
                }?.index ?: 0
                LogUtil.i("viewportCenter:${viewportCenter},viewportStartOffset:${layoutInfo.viewportStartOffset},index:$index")
                index
            } else {
                0
            }
        }
    }

    // Scroll to initialData when it changes
    LaunchedEffect(initialData) {
        val index = lists.indexOf((initialData * zoom).roundToInt())
        if (index != -1) {
            LogUtil.i("initialData: $initialData,index:${index},value:${lists[index]}")
            // Calculate the offset to center the item
            listState.animateScrollToItem(index)
        }
    }

    // Observe scroll state and call onItemSelected when scroll stops
    LaunchedEffect(listState) {
        snapshotFlow { listState.isScrollInProgress }
            .distinctUntilChanged()
            .collect { isScrolling ->
                if (!isScrolling) {
                    selectedValue = lists[centerItemIndex]
                    onItemSelected(lists[centerItemIndex] / 10.0)
                }
            }
    }

    Box(
        modifier = modifier
            .background(Color.White)
            .onGloballyPositioned { coordinates ->
                // 获取组件的大小
                componentSize.value = coordinates.size
            },
    ) {
        LazyColumn(
            state = listState,
            contentPadding = PaddingValues(vertical = if (startEndPadding > 0.dp) startEndPadding else 0.dp),
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .background(Color.White) // Set height to accommodate items
        ) {
            itemsIndexed(lists) { index, item ->
                HorizontalCustomItem(
                    item = item / zoom.toDouble(),
                    unit = unit
                )
            }
        }
        // Overlay the selected line at the center
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.Center
        ) {
            HorizontalCustomItemSelected(
                item = selectedValue / zoom.toDouble(),
                unit = unit
            )
        }
        // Optionally, overlay the icons
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.Start,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Icon(
                modifier = Modifier
                    .width(16.dp),
                painter = painterResource(id = R.drawable.baseline_arrow_drop_down_24),
                contentDescription = ""
            )
            Icon(
                modifier = Modifier
                    .width(16.dp),
                painter = painterResource(id = R.drawable.baseline_arrow_drop_up_24),
                contentDescription = ""
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HorizontalCustomItem(
    item: Double = 2.0,
    unit: String = "cm"
) {
    Box(
        modifier = Modifier
            .height(15.dp) // 父级宽度为 15.dp
            .wrapContentHeight(),
        contentAlignment = Alignment.TopCenter
    ) {
        Row(
            modifier = Modifier.wrapContentSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (item % 1 == 0.0) {
                val annotatedText = buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = Color(0xFFDEDEDE),
                            fontSize = 20.sp,
                        )
                    ) {
                        append("$item".format(".1f"))
                    }
                }
                Box(
                    modifier = Modifier
                        .size(32.dp, 2.dp)
                        .background(
                            shape = RoundedCornerShape(size = 16.dp),
                            color = Color(0xFFDEDEDE)
                        )
                )

                Text(
                    text = annotatedText,
                    maxLines = 1,
                    overflow = TextOverflow.Visible, // 使用 Clip 以防止多行文本
                    softWrap = false,
                    modifier = Modifier
                        .padding(start = 12.dp)
                        .wrapContentHeight()
                        .offset(
                            y = -(7).dp
                        )
                )
            } else {
                Box(
                    modifier = Modifier
                        .size(24.dp, 2.dp)
                        .background(
                            shape = RoundedCornerShape(size = 16.dp),
                            color = Color(0xFFF2F2F2)
                        )
                )
            }

        }
    }
}


@Preview(showBackground = true)
@Composable
fun HorizontalCustomItemSelected(
    item: Double = 2.0,
    unit: String = "cm"
) {
    Box(
        modifier = Modifier
            .height(15.dp) // 父级宽度为 15.dp
            .wrapContentHeight(),
        contentAlignment = Alignment.TopCenter
    ) {
        Row(
            modifier = Modifier.wrapContentSize(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            val annotatedText = buildAnnotatedString {
                withStyle(
                    SpanStyle(
                        color = Color(0xFF333333),
                        fontSize = 30.sp,
                    )
                ) {
                    append("$item".format(".1f"))
                }
                withStyle(
                    SpanStyle(
                        color = Color(0xFF333333),
                        fontSize = 24.sp
                    )
                ) {
                    append(" $unit")
                }
            }
            Box(
                modifier = Modifier
                    .size(100.dp, 2.dp)
                    .background(
                        shape = RoundedCornerShape(size = 16.dp),
                        color = Color(0xFF444444)
                    )
            )
            Text(
                text = annotatedText,
                maxLines = 1,
                overflow = TextOverflow.Visible, // 使用 Clip 以防止多行文本
                softWrap = false,
                modifier = Modifier
                    .padding(start = 30.dp)
                    .wrapContentHeight()
                    .offset(
                        y = -(7).dp
                    )
            )

        }
    }
}