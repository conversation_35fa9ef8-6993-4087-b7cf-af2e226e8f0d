package org.aihealth.ineck.view.preview

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.screen.membershipcenter.OrderData
import org.aihealth.ineck.view.screen.membershipcenter.OrderItem
import java.util.Locale


@Preview(locale = "en")
@Composable
fun MemberShipCenterScreenPreview() {
    Column {
//        MemberShipCenterHeaderNormal(
//            "",
//            "小马蜂",
//            "新人特惠，限时限量低至8折"
//        ) {
//            VipBenefitsInMemberShipCenter(
//                vipBenefitsList = vipBenefitsList,
//                onClick = { (LogUtil.i("it")) },
//            )
//        }
//        Spacer(modifier = Modifier.size(10.dp))
        MemberShipCenterHeaderSpecial(
            "",
            "小马蜂",
            "新人特惠，限时限量低至8折",
            {
                VipBenefitsInMemberShipCenter(
                    vipBenefitsList = vipBenefitsList,
                    onClick = { (LogUtil.i("it")) },
                )
            },
            {}
        )
        val chooseTag = remember {
            mutableStateOf(
                OrderListType.Wearable
            )
        }
        MemberShipCenterContent(
            chooseTag.value,
            orderDataList,
            {
                LogUtil.i(it.name)
                chooseTag.value = it
            },
            {},
            false,
            {},
            {}
        )
    }
}

/**
 * 非会员 和 正常会员（剩余时间>3天）
 */
@Composable
fun MemberShipCenterHeaderNormal(
    picture: String,
    username: String,
    descriptionString: String,
    content: @Composable () -> Unit,
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .clip(RoundedCornerShape(7.dp)),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // 头像 用户名 vip介绍
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painter = painterResource(id = R.drawable.vip_bg_2),
                    contentScale = ContentScale.Crop,
                )
        ) {

            if (picture.isNotBlank()) {
                AsyncImage(
                    model = picture,
                    contentDescription = null,
                    Modifier
                        .padding(start = 16.dp, top = 16.dp)
                        .size(40.dp)
                        .clip(CircleShape),
                    // 圆形头像，设置图片的缩放属性
                    contentScale = ContentScale.Crop
                )
            } else {
                Image(
                    painter = painterResource(id = R.drawable.img_avatar),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 16.dp, top = 16.dp)
                        .size(40.dp)
                )
            }
            Spacer(modifier = Modifier.size(10.dp))
            Column(
                modifier = Modifier
                    .padding(top = 16.dp),
            ) {
                Text(
                    text = username,
                    style = TextStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF6B4B14),
                    )
                )
                Text(
                    text = descriptionString,
                    style = TextStyle(
                        fontSize = fontSize14,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF3C2200),
                    )
                )
            }
        }
        // 权益介绍
        content()
    }
}

@Composable
fun MemberShipCenterHeaderSpecial(
    picture: String,
    username: String,
    descriptionString: String,
    content: @Composable () -> Unit,
    onClick: () -> Unit,
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White)
            .clip(RoundedCornerShape(7.dp)),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        // 头像 用户名 vip介绍
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp)
                .paint(
                    painter = painterResource(id = R.drawable.vip_bg_2),
                    contentScale = ContentScale.Crop,
                )
        ) {
            Row {
                if (picture.isNotBlank()) {
                    AsyncImage(
                        model = picture,
                        contentDescription = null,
                        Modifier
                            .padding(start = 16.dp, top = 16.dp)
                            .size(40.dp)
                            .clip(CircleShape),
                        // 圆形头像，设置图片的缩放属性
                        contentScale = ContentScale.Crop
                    )
                } else {
                    Image(
                        painter = painterResource(id = R.drawable.img_avatar),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(start = 16.dp, top = 16.dp)
                            .size(40.dp)
                    )
                }
                Spacer(modifier = Modifier.size(10.dp))
                Column(
                    modifier = Modifier
                        .padding(top = 16.dp),
                ) {
                    Text(
                        text = username,
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF6B4B14),
                        )
                    )
                    Text(
                        text = descriptionString,
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF3C2200),
                        )
                    )
                }
            }
            Row(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .paint(
                        painterResource(id = R.drawable.vip_in_bg_2),
                    )
                    .clickable { onClick() },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
            ) {
                Text(
                    modifier = Modifier.padding(start = 18.dp),
                    text = stringResource(id = R.string.renew_now),
                    style = TextStyle(
                        fontSize = fontSize10,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF6B4B14),
                    )
                )
                Image(
                    modifier = Modifier.padding(end = 11.dp),
                    painter = painterResource(id = R.drawable.ic_next_step),
                    contentDescription = "next step",
                )
            }
        }
        // 权益介绍
        content()
    }
}

/**
 * 用于会员中心的权益展示
 * @param vipBenefitsList 为权益列表
 */
@Composable
fun VipBenefitsInMemberShipCenter(
    vipBenefitsList: List<VipBenefitObject>,
    onClick: (Int) -> Unit
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize12 = with(density) { 12.sp / fontScale }
    Row(
        modifier = Modifier.padding(top = 10.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Image(
            painter = painterResource(id = R.drawable.vip_horizontal),
            contentDescription = "img"
        )
        val descriptionStr = buildAnnotatedString {
            withStyle(
                SpanStyle(
                    fontSize = fontSize10,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF666666),
                )
            ) {
                append(stringResource(id = R.string.members_enjoy))
            }
            withStyle(
                SpanStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFFEEC570),
                    fontStyle = FontStyle.Italic,

                    )
            ) {
                append(" ${vipBenefitsList.size} ")
            }
            withStyle(
                SpanStyle(
                    fontSize = fontSize10,
                    fontWeight = FontWeight.Normal,
                    color = Color(0xFF666666),

                    )
            ) {
                append(stringResource(id = R.string.benefits_exclusively))
            }
        }

        Text(
            modifier = Modifier.padding(horizontal = 10.dp),
            text = descriptionStr,
        )

        Image(
            painter = painterResource(id = R.drawable.vip_horizontal),
            contentDescription = "img"
        )
    }

    LazyRow {
        items(vipBenefitsList.size) { index ->
            VipBenefitItem(
                modifier = Modifier.padding(horizontal = 10.dp, vertical = 10.dp),
                id = vipBenefitsList[index].id,
                title = vipBenefitsList[index].title,
                onClick = { onClick(index) }
            )
        }
    }
}

/**
 *  会员中心的商品模块
 *  @param tagChoose 高级智能视觉版 和 高级智能穿戴版的 tag
 *  @param orderDataList 商品的列表，注意商品的选择信息也在里面
 *  @param onClick 用于传递 tag 页的切换事件
 *  @param orderClick 用于传递购买时间
 */
@Composable
fun MemberShipCenterContent(
    tagChoose: OrderListType,
    orderDataList: List<OrderData>,
    onClick: (OrderListType) -> Unit,
    orderClick: () -> Unit,
    check: Boolean = false,
    onCheckedChanged: () -> Unit,
    agreementDialog: @Composable () -> Unit
) {
    val density = LocalDensity.current
    val fontSize10 = with(density) { 10.sp / fontScale }
    val fontSize18 = with(density) { 18.sp / fontScale }
    val fontSize20 = with(density) { 20.sp / fontScale }
    val fontSize24 = with(density) { 24.sp / fontScale }
    val fontSize36 = with(density) { 36.sp / fontScale }

    LogUtil.i(tagChoose.name)
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            text = stringResource(id = R.string.select_membership_package),
            style = TextStyle(
                fontSize = fontSize18,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF333333),
            ),
            modifier = Modifier.padding(bottom = 16.dp)
        )
        Column(
            modifier = Modifier
                .padding(vertical = 12.dp)
                .paint(
                    painter = painterResource(
                        id = if (tagChoose == OrderListType.Vision)
                            R.drawable.vip_order_bg_silver
                        else R.drawable.vip_order_bg_gold
                    ),
                    contentScale = ContentScale.FillWidth,
                )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(start = 18.dp, top = 10.dp, end = 23.dp),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    modifier = Modifier
                        .pointerInput(Unit) {
                            detectTapGestures {
                                onClick(OrderListType.Vision)
                            }
                        },
                    text = stringResource(id = R.string.Vision),
                    style = TextStyle(
                        fontSize = fontSize18,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    )
                )
                Text(
                    modifier = Modifier
                        .pointerInput(Unit) {
                            detectTapGestures {
                                onClick(OrderListType.Wearable)
                            }
                        },
                    text = stringResource(id = R.string.Wearable),
                    style = TextStyle(
                        fontSize = fontSize18,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
            if (tagChoose == OrderListType.Vision) {
                Row(
                    modifier = Modifier.padding(12.dp),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    LazyRow(
                        modifier = Modifier.padding(top = 10.dp),
                        verticalAlignment = Alignment.CenterVertically,
                    ) {
                        items(orderDataList.size) { index ->
                            if (index < orderDataList.size - 1) {
                                OrderItem(
                                    if (currentLocale == Locale.CHINESE && index == 0) R.drawable.vip_recommended else 0,
                                    orderDataList[index].isChoose,
                                    orderDataList[index],
                                    onClick = {
                                        orderDataList.forEach {
                                            it.isChoose = false
                                        }
                                        orderDataList[index].isChoose = true
                                    }
                                )
                            }
                        }
                    }

                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 30.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center,
                ) {
                    Text(
                        text = orderDataList.last().orderName,
                        style = TextStyle(
                            fontSize = fontSize20,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFBDA366),
                            textAlign = TextAlign.Center,
                        )
                    )
                    val salePriceString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                fontSize = fontSize24,
                                fontWeight = FontWeight.Normal,
                                color = Color(0xFFBDA366),
                            )
                        ) {
                            append("¥")
                        }
                        withStyle(
                            SpanStyle(
                                fontSize = fontSize36,
                                fontWeight = FontWeight.Normal,
                                color = Color(0xFFBDA366),
                            )
                        ) {
                            append(orderDataList.last().salePrice)
                        }
                    }
                    val originPriceString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                fontSize = fontSize18,
                                fontWeight = FontWeight(400),
                                color = Color(0xFFD4BE8A),
                            )
                        ) {
                            append("¥")
                        }
                        withStyle(
                            SpanStyle(
                                fontSize = fontSize18,
                                fontWeight = FontWeight(400),
                                color = Color(0xFFD4BE8A),
                            )
                        ) {
                            append(orderDataList.last().originPrice)
                        }
                    }
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 5.dp),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                    ) {
                        Text(text = salePriceString)
                        Text(
                            modifier = Modifier.padding(start = 10.dp, top = 21.dp),
                            text = originPriceString,
                            textDecoration = TextDecoration.LineThrough,
                        )
                    }


                }
            }

        }

        // 协议
        Column(
            modifier = Modifier.padding(vertical = 10.dp)
        ) {
            OrderAgreementView(
                check,
                onCheckedChanged
            )
        }

        // 购买按钮
        AIHButton(
            modifier = Modifier
                .fillMaxWidth()
                .height(47.dp),
            backgroundColor = Color(0xFF5B93E4),
            text = stringResource(id = R.string.open_immediately),
            onClick = {
                orderClick()
            })

        if (isInChina) {
            Text(
                modifier = Modifier.padding(vertical = 10.dp),
                text = stringResource(id = R.string.chat_phone),
                style = TextStyle(
                    fontSize = fontSize10,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                )
            )
        }


        agreementDialog()
    }
}

@Composable
fun ColumnScope.OrderAgreementView(
    checked: Boolean,
    onCheckedChanged: () -> Unit
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) { 14.sp / fontScale }

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier.align(Alignment.CenterHorizontally)
    ) {
        Image(
            painter = painterResource(id = if (checked) R.drawable.img_check else R.drawable.img_uncheck),
            contentDescription = null,
            modifier = Modifier
                .size(16.dp)
                .pointerInput(Unit) {
                    detectTapGestures {
                        onCheckedChanged()
                    }
                },
        )
        Spacer(modifier = Modifier.width(10.dp))
        val annotatedText = buildAnnotatedString {
            withStyle(style = SpanStyle(fontSize = fontSize14, color = Color(0XFF838383))) {
                append(stringResource(id = R.string.I_have_read_and_agreed_to_))
            }
            pushStringAnnotation("memberShip", stringResource(id = R.string.AIH_Member_Agreement))
            withStyle(style = SpanStyle(fontSize = fontSize14, color = Color(0XFF1E4BDF))) {
                append(stringResource(id = R.string.AIH_Member_Agreement))
            }
            pop()


        }
        ClickableText(
            text = annotatedText,
            onClick = { offset ->
                annotatedText.getStringAnnotations(
                    tag = "memberShip", start = offset,
                    end = offset
                ).firstOrNull()?.let { _ ->
                    startScreen(Screen.MemberAgreement.route)
                }
            }
        )

    }
}

val orderDataList = mutableStateListOf<OrderData>().apply {
    add(
        OrderData("首月试用", "0", "19")
            .apply {
                this.index = 0
            })
    add(
        OrderData("月卡会员", "18", "39")
            .apply {
                this.index = 1
            })
    add(
        OrderData("年卡会员", "88", "468")
            .apply {
                this.index = 2
            })
    add(
        OrderData("2年会员", "168", "936")
            .apply {
                this.index = 3
            })
    add(
        OrderData("3年会员", "238", "1288")
            .apply {
                this.index = 4
            })
    add(
        OrderData("穿戴设备1套+3年金卡会员", "599", "2088")
            .apply {
                this.index = 5
            })
}

@Composable
fun MemberShipAgreementDialog(
    visible: Boolean,
    onConfirm: () -> Unit,
    onCancel: () -> Unit
) {
    val density = LocalDensity.current
    val fontSize18 = with(density) { 18.sp / fontScale }

    if (visible) {
        Dialog(onDismissRequest = {
        }) {
            Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
                Column(
                    Modifier
                        .width(300.dp)
                        .background(Color.White, RoundedCornerShape(10.dp))
                        .padding(horizontal = 16.dp, vertical = 30.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.user_agreement_and_privacy_statement),
                        fontSize = fontSize18,
                        fontWeight = FontWeight.Bold,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    val annotatedText = buildAnnotatedString {
                        withStyle(style = SpanStyle()) {
                            append(stringResource(id = R.string.in_order_to_better_protect_your_legitimate))
                        }
                        pushStringAnnotation(
                            "ystk",
                            "《${stringResource(id = R.string.privacy_statement)}》"
                        )
                        withStyle(style = SpanStyle(color = Color(0XFF1E4BDF))) {
                            append("《${stringResource(id = R.string.privacy_statement)}》")
                        }
                        pop()
                        withStyle(style = SpanStyle()) {
                            append("、")
                        }
                        pushStringAnnotation(
                            "mzsm",
                            "《${stringResource(id = R.string.user_agreement)}》"
                        )
                        withStyle(style = SpanStyle(color = Color(0XFF1E4BDF))) {
                            append("《${stringResource(id = R.string.user_agreement)}》")
                        }
                        pop()

                    }
                    ClickableText(
                        text = annotatedText,
                        onClick = { offset ->
                            annotatedText.getStringAnnotations(
                                tag = "ystk", start = offset,
                                end = offset
                            ).firstOrNull()?.let {
                                onCancel()
                                startScreen(Screen.PrivateTerm.route)

                            }
                            annotatedText.getStringAnnotations(
                                tag = "mzsm", start = offset,
                                end = offset
                            ).firstOrNull()?.let {
                                onCancel()
                                startScreen(Screen.UserAgreement.route)
                            }
                        }
                    )
                    Spacer(modifier = Modifier.height(20.dp))
                    Column(modifier = Modifier) {
                        AIHOutlinedButton(
                            text = stringResource(id = R.string.disagree),
                            onClick = {
                                onCancel()
                            },
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                        Spacer(modifier = Modifier.width(10.dp))
                        AIHButton(
                            text = stringResource(id = R.string.agree),
                            onClick = {
                                onConfirm()
                            },
                            modifier = Modifier.fillMaxWidth(0.8f)
                        )
                    }
                }
            }
        }
    }
}

enum class OrderListType {
    Vision, Wearable
}
