package org.aihealth.ineck.view.dialog

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Velocity
import org.aihealth.ineck.R
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.monthLastDay
import org.aihealth.ineck.util.toStringList
import org.aihealth.ineck.util.year
import org.aihealth.ineck.view.custom.AIHWheel
import org.aihealth.ineck.view.custom.AIHWheelState
import org.aihealth.ineck.view.dialog.picker.BasePicker
import java.util.Calendar
import java.util.GregorianCalendar

@Composable
fun DatePickerDialog(
    modifier: Modifier = Modifier,
    initialCalendar: Calendar = Calendar.getInstance(),
    minCalendar: Calendar = GregorianCalendar(1900,1,1),
    maxCalendar: Calendar = GregorianCalendar(2100,11,31),
    onConfirmClick: (calendar: Calendar) -> Unit,
    onCancelClick: () -> Unit = {}
) {
    val yearState = remember {
        AIHWheelState(initialIndex = initialCalendar.year - minCalendar.year)
    }
    val monthState = remember {
        AIHWheelState(initialIndex = initialCalendar.month)
    }
    val dayState = remember {
        AIHWheelState(initialIndex = initialCalendar.date - 1)
    }
    BasePicker(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth(),
        title = stringResource(id = R.string.select_date),
        onConfirmClick = {
            onConfirmClick(GregorianCalendar(yearState.selectedIndex + minCalendar.year,monthState.selectedIndex , dayState.selectedIndex + 1))
        },
        onCancelClick = onCancelClick
    ) {
        Row(Modifier
            .fillMaxWidth()
            .nestedScroll(
                object : NestedScrollConnection {
                    override fun onPostScroll(
                        consumed: Offset,
                        available: Offset,
                        source: NestedScrollSource
                    ): Offset {
                        return available
                    }

                    override suspend fun onPostFling(
                        consumed: Velocity,
                        available: Velocity
                    ): Velocity {
                        return available
                    }
                }

            )) {
            val dayCount by remember {
                derivedStateOf{ getLastDay(yearState.selectedIndex + minCalendar.year, monthState.selectedIndex + 1) }
            }
            AIHWheel(state = yearState, list = (minCalendar.year .. maxCalendar.year).toStringList(), modifier = Modifier.weight(1F))
            AIHWheel(state = monthState, list = (1..12).toStringList(), modifier = Modifier
                .weight(1F))
            AIHWheel(state = dayState, list = (1..dayCount).toStringList(), modifier = Modifier
                .weight(1F))
        }
    }
}



private fun getLastDay(year: Int, month: Int): Int {
    return GregorianCalendar(year,month - 1,1).monthLastDay
}
