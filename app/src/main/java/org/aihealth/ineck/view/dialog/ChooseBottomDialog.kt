package org.aihealth.ineck.view.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.AIHDivider

@Composable
fun ChooseBottomDialog(
    visible: Boolean,
    array: Array<String>,
    onClick: (index: Int) -> Unit,
    onCancel: () -> Unit
) {
    if (visible) {
        AIHBottomSheet(
            onDismissRequest = { onCancel() }
        ) {
            Column(modifier = Modifier
                .fillMaxWidth()
                .background(Color.White, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))) {
                array.forEachIndexed { index,str ->
                    Box(modifier = Modifier
                        .height(50.dp)
                        .fillMaxWidth()
                        .clickable { onClick(index) }, contentAlignment = Alignment.Center) {
                        Text(text = str, fontSize = 18.sp, color = Color(0XFF333333))
                        AIHDivider(modifier = Modifier.align(Alignment.BottomCenter))
                    }
                }
                AIHDivider(thickness = 5.dp)
                Box(modifier = Modifier
                    .height(56.dp)
                    .fillMaxWidth()
                    .clickable { onCancel() }, contentAlignment = Alignment.Center) {
                    Text(text = stringResource(id = R.string.cancel), fontSize = 18.sp, color = Color(0XFF333333))
                }
            }
        }
    }
}
