package org.aihealth.ineck.view.screen.exercise

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.ActivityInfo
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.media3.common.util.UnstableApi
import coil.compose.AsyncImage
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.model.improvement.Section
import org.aihealth.ineck.notification.FloatingCameraService
import org.aihealth.ineck.util.APPUtil
import org.aihealth.ineck.util.ActivityResultUtils
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.TimeUtil.convertSecondsToAnnotatedString
import org.aihealth.ineck.util.TimeUtil.convertSecondsToAnnotatedString14
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toJson
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.dialog.AIHTipButtonDialog
import org.aihealth.ineck.view.dialog.CameraPermissionDialog
import org.aihealth.ineck.view.screen.vcguide.AiNeckVCGuideScreen
import org.aihealth.ineck.viewmodel.ImproveDetailViewModel
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.user
import java.util.Locale

/**
 *  改善项目方案详情页
 *  @param  viewModel   改善项目方案详情页视图模型
 */
@androidx.annotation.OptIn(UnstableApi::class)
@Composable
fun ImproveDetailScreen(
    mainViewModel: MainViewModel,
    viewModel: ImproveDetailViewModel,
) {

    /** 上下文 */
    val context = LocalContext.current

    /** 当前改善练习项目详情数据状态 */
    val improveProgramDetail = viewModel.improveProgramDetail
    val sectionData = viewModel.sectionData.value
    activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED

    val isBootVCGuideScreen = remember {
        mutableStateOf(false)
    }
    var showCameraPermissionDialog by remember {
        mutableStateOf(false)
    }
    LaunchedEffect(Unit) {
        viewModel.deleteVideoPhoto()
        viewModel.deleteCameraPhoto()
        // 停止 FloatingCameraService
        if (FloatingCameraService.isServiceRunning.value == true) {
            context.stopService(Intent(context, FloatingCameraService::class.java))
            mainViewModel.homeScreen.onChangeCameraScanModel(false)
        }
    }
    LogUtil.i("improveDetailScreen:${improveProgramDetail.toJson()}")
    BasePageView(
        title = stringResource(id = R.string.improve_cervical_spine),
        modifier = Modifier.fillMaxSize(),
        showBackIcon = true,
    ) {
        LazyColumn(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .fillMaxSize(),
            verticalArrangement = Arrangement.Top,
        ) {
            item {
                // 封面图
                Surface(
                    modifier = Modifier
                        .padding(top = 10.dp)
                        .height(200.dp)
                        .fillMaxWidth(),
                ) {
                    AsyncImage(
                        model = improveProgramDetail.cover,
                        contentDescription = improveProgramDetail.title,
                        placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                        contentScale = ContentScale.Fit,
                        modifier = Modifier
                            .clip(RoundedCornerShape(17.dp)),
                        alignment = Alignment.Center
                    )

                }

                // 课程标题
                Text(
                    text = improveProgramDetail.title,
                    fontSize = 24.sp,
                    color = Color.Black,
                    textAlign = TextAlign.Start,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(
                            top = 20.dp
                        )
                )

                /* 改善项目方案关键属性， 时长、已练习人数等 */
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                ) {
                    val durationString = convertSecondsToAnnotatedString14(
                        improveProgramDetail.duration,
                        LocalContext.current
                    )

                    val numberOfPeopleString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF6181E9),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(improveProgramDetail.frequency.toString())
                        }
                    }
                    val deviceString = buildAnnotatedString {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF6181E9),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(stringResource(id = R.string.no))
                        }
                    }
                    AttributeCard(
                        modifier = Modifier.weight(1f),
                        icons = R.drawable.img_duration_time,
                        number = durationString,
                        unit = "",
                        description = stringResource(id = R.string.duration)
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                    AttributeCard(
                        modifier = Modifier.weight(1f),
                        icons = R.drawable.participated,
                        number = numberOfPeopleString,
                        unit = stringResource(id = R.string.people_text),
                        description = stringResource(id = R.string.participated)
                    )
                    Spacer(modifier = Modifier.width(5.dp))
                    AttributeCard(
                        modifier = Modifier.weight(1f),
                        icons = R.drawable.img_device,
                        number = deviceString,
                        unit = "",
                        description = stringResource(id = R.string.need_device)
                    )
                }

                /* 课程介绍 */
                Text(
                    text = improveProgramDetail.description,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),
                    ),
                    textAlign = TextAlign.Start,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 20.dp)
                )
                /* 分割线 */
                HorizontalDivider(
                    thickness = 1.dp,
                    color = Color(0xFFEFEFEF)
                )
            }
            if (sectionData.sections.isNotEmpty()) {
                item {
                    Text(
                        text = stringResource(id = R.string.lessons),
                        fontSize = 20.sp,
                        color = Color.Black,
                        textAlign = TextAlign.Start,
                        maxLines = 3,
                        overflow = TextOverflow.Ellipsis,
                        modifier = Modifier
                            .padding(
                                top = 20.dp
                            )
                    )
                }

                /* 子课程 */
                items(sectionData.sections.size) { index ->
                    LessonCard(
                        section = sectionData.sections[index],
                        onClick = {
                            // 会员相关
                            if (MainViewModel.onlineTime <= MainViewModel.trailTimeLimit || !viewModel.improveProgramDetail.isMembershipRequired || user.vipStatus) {
                                if (viewModel.checkCameraPermission()) {
                                    isBootVCGuideScreen.value = true
                                } else {
                                    showCameraPermissionDialog = true
                                }
                                viewModel.playVideo(true, index)
//                                LogUtil.i("playVideo isPlaySub:${viewModel.isPlaySub.value},index:$index")
                            } else {
                                startScreen(Screen.MemberShipCenter.route, false)
                            }
                        }
                    )
                    HorizontalDivider(modifier = Modifier.padding(vertical = 2.dp))
                }
            }

            item {

                /* 开始练习按钮 */
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .fillMaxHeight()
                        .padding(
                            vertical = 20.dp
                        ),
                    verticalArrangement = Arrangement.Bottom,
                    horizontalAlignment = Alignment.CenterHorizontally,
                ) {
                    AIHButton(
                        text = stringResource(id = R.string.start),
                        onClick = {
//                                startScreen(Screen.Training.route, false)
                            if (viewModel.checkCameraPermission()) {
                                isBootVCGuideScreen.value = true
                            } else {
                                showCameraPermissionDialog = true
                            }
                            viewModel.playVideo(false, 0)
                        },
                        modifier = Modifier
                            .fillMaxWidth(0.8f)
                            .padding(top = 12.dp)
                    )
                    /*
//                // 会员相关
                    if (MainViewModel.onlineTime <= MainViewModel.trailTimeLimit || !improveProgramDetail.isMembershipRequired || user.vipStatus) {
                        AIHButton(
                            text = stringResource(id = R.string.start),
                            onClick = {
//                                startScreen(Screen.Training.route, false)
                                if (viewModel.checkCameraPermission()) {
                                    isBootVCGuideScreen.value = true
                                } else {
                                    showCameraPermissionDialog = true
                                }
                                viewModel.playVideo(false, 0)
                            },
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 12.dp)
                        )
                    } else if (!user.memberTrial) {
                        AIHButton(
                            text = stringResource(id = R.string.try_free),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 12.dp)
                        )
                    } else {
                        AIHButton(
                            text = stringResource(id = R.string.go_to_subscription_membership),
                            onClick = {
                                startScreen(Screen.MemberShipCenter.route, false)
                            },
                            modifier = Modifier
                                .fillMaxWidth(0.8f)
                                .padding(top = 12.dp)
                        )
                    }

                     */
                }
            }
        }

    }
    AiNeckVCGuideScreen(
        modifier = Modifier.fillMaxWidth(0.9f),
        visible = isBootVCGuideScreen.value,
        viewModel = viewModel<org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel>(
            factory = object : ViewModelProvider.Factory {
                override fun <T : ViewModel> create(modelClass: Class<T>): T {
                    if (modelClass.isAssignableFrom(org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel::class.java))
                        return modelClass.getConstructor(Context::class.java)
                            .newInstance(context)
                    throw IllegalArgumentException("")
                }
            }
        ),
        onDismissEvent = { result ->
            when (result) {
                is VCDetectingResult.DetectingSuccess -> {
                    isBootVCGuideScreen.value = false
                    viewModel.finishedDialogVisible = false
                    val angle = (result.result as Triple<*, *, *>).first
                    viewModel.detectResultValueForFitted = angle as Float
                    startScreen(
                        route = Screen.Training.route, finish = false
                    )
                }

                is VCDetectingResult.DetectingError -> {
                    isBootVCGuideScreen.value = false

                }

                else -> {}
            }
        }

    )
    CameraPermissionDialog(
        modifier = Modifier.fillMaxWidth(0.8f),
        visible = showCameraPermissionDialog,
        onDismiss = {
            showCameraPermissionDialog = false
        },
        onConfirm = {
            ActivityResultUtils.requestPermissions(
                permissions = arrayOf(Manifest.permission.CAMERA),
                onAllGranted = {
                    showCameraPermissionDialog = false
                    isBootVCGuideScreen.value = true
                },
                onProhibited = {
                    if (SPUtil.getBoolean(
                            SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION,
                            true
                        )
                    ) {
                        SPUtil.putBoolean(
                            SPConstant.IS_FIRST_REQUEST_CAMERA_PERMISSION,
                            false
                        )
                    } else {
                        showCameraPermissionDialog = false
                        DialogUtil.showToast {
                            AIHTipButtonDialog(
                                text = localeResources.getString(R.string.permission_denied),
                                buttonText = localeResources.getString(
                                    R.string.to_authorization
                                ),
                                onClick = {
                                    APPUtil.goAPPDetail()
                                }
                            ) {
                                DialogUtil.hideToast()
                            }
                        }
                    }
                }
            )
        }
    )

    /*
    TailMembershipDialog(
        mainViewModel,
        onClick = {
            mainViewModel.isShowTailMembershipDialog = false
            userSP.edit().putBoolean("isShowTailMembershipDialog", false).apply()
        }
    )


    TrialMembershipFinishDialog(mainViewModel)
    if (!userSP.getBoolean(
            "showTrailMembershipFinishedDialog",
            false
        ) && MainViewModel.onlineTime > MainViewModel.trailTimeLimit
    ) {
        mainViewModel.isShowTailMembershipFinishedDialog = true
    }

     */


}

/**
 * 锻炼项目方案关键属性卡片
 */
@Composable
fun AttributeCard(
    modifier: Modifier = Modifier,
    icons: Int, number: AnnotatedString, unit: String, description: String
) {
    Card(
        modifier = modifier
            .height(56.dp),
        colors = CardDefaults.cardColors(Color(0xFFEDEDED)),
        shape = RoundedCornerShape(8.dp),
        elevation = CardDefaults.cardElevation(0.dp),
    ) {
        Column(
            Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    painter = painterResource(id = icons),
                    contentDescription = "description",
                    tint = Color(0xFF444444),
                    modifier = Modifier
                        .size(10.dp)
                        .padding(end = 1.dp)
                )
                Text(
                    text = description,
                    style = TextStyle(
                        fontSize = 10.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF666666),
                    ),
                )
            }
            Row(
                Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {

                Text(
                    text = number,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF333333),
                    )
                )
                Text(
                    text = " $unit",
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal,
                        color = Color(0xFF666666),
                    ),
                )
            }
        }

    }
}

/**
 * 子课程卡片
 * @param section 课程数据
 * @param onClick 子课程点击事件
 */
@androidx.annotation.OptIn(UnstableApi::class)
@Composable
fun LessonCard(
    section: Section,
    onClick: () -> Unit
) {

    val descriptionList = section.description.split("+")
    Card(
        elevation = CardDefaults.cardElevation(0.dp),
        colors = CardDefaults.cardColors(Color.White),
        shape = RoundedCornerShape(16.dp),
        // 点击进入具体跟练页
        onClick = {
            onClick()
        }) {
        // 相对布局
        ConstraintLayout(modifier = Modifier.fillMaxWidth()) {
            val (programCover, programTitle, showDetail, tag) = createRefs()/* 改善项目方案封面 */
            Surface(
                modifier = Modifier
                    .size(64.dp)
                    .constrainAs(programCover) {
                        start.linkTo(parent.start)
                        top.linkTo(parent.top, 5.dp)
                        bottom.linkTo(parent.bottom, 5.dp)
                    }, shape = RoundedCornerShape(6.dp)
            ) {
                AsyncImage(
                    model = section.cover,
                    contentDescription = section.title,
                    placeholder = painterResource(id = R.drawable.img_cover_placeholder),
                    modifier = Modifier.fillMaxSize()
                )
            }/* 改善项目方案标题 */
            Text(
                text = section.title,
                fontSize = 15.sp,
                fontWeight = FontWeight.Medium,
                color = Color(0xFF666666),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier.constrainAs(programTitle) {
                    start.linkTo(programCover.end, 12.dp)
                    top.linkTo(parent.top, 10.dp)
                    end.linkTo(parent.end, 0.dp)
                    width = Dimension.fillToConstraints
                })/* 改善项目方案关键属性， 时长、已练习人数等 */
            Row(
                modifier = Modifier.constrainAs(showDetail) {
                    start.linkTo(programCover.end, 12.dp)
                    top.linkTo(programTitle.bottom, 8.dp)
                    end.linkTo(parent.end, 10.dp)
                    width = Dimension.fillToConstraints
                },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                val durationString =
                    convertSecondsToAnnotatedString(section.duration, LocalContext.current)
                Image(
                    painter = painterResource(id = R.drawable.img_duration_time),
                    contentDescription = "Duration Time",
                    modifier = Modifier
                        .size(14.dp)
                )/* 请求体响应结构中提供的"持续时间"参数是以秒为单位，这里需要再显示上做转换计算 */
                Text(
                    text = durationString,
                    maxLines = 1,
                    modifier = Modifier
                        .padding(horizontal = 4.dp),
                )
            }
            Row(
                modifier = Modifier.constrainAs(tag) {
                    start.linkTo(programCover.end, 12.dp)
                    top.linkTo(showDetail.bottom, 8.dp)
                    end.linkTo(parent.end, 10.dp)
                    width = Dimension.fillToConstraints
                },
                verticalAlignment = Alignment.CenterVertically,
            ) {
                if (currentLocale == Locale.CHINESE) {
                    descriptionList.forEach {
                        Card(
                            colors = CardDefaults.cardColors(Color(0xFFECF1FF)),
                            shape = RoundedCornerShape(6.dp),
                            modifier = Modifier.padding(end = 4.dp)
                        ) {
                            Text(
                                text = it,
                                color = Color(0xFF999999),
                                fontSize = 12.sp,
                            )
                        }
                    }
                }

            }

        }
    }
}