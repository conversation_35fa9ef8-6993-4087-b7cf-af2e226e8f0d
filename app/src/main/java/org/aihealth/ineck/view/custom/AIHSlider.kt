package org.aihealth.ineck.view.custom

import android.annotation.SuppressLint
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.DraggableState
import androidx.compose.foundation.gestures.Orientation
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.gestures.draggable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp

@Composable
fun AIHSlider(
    state: AIHSliderState,
    progression: IntProgression = 0..10 step 1,
    enabled: Boolean = true,
    @SuppressLint("ModifierParameter") modifier: Modifier = Modifier,
    trackColor: Color = Color(0XFF85817C),
    thumbColor: Color = Color(0XFFEE652A)
) {
    val density = LocalDensity.current
    BoxWithConstraints(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .height(20.dp),
        contentAlignment = Alignment.CenterStart
    ) {
        val mWidth by remember {
            mutableFloatStateOf((constraints.maxWidth - with(density) {10.dp.toPx()}) * progression.step / (progression.last - progression.first))
        }
        val offsetDp by remember {
            derivedStateOf {
                with(density) { ((state.value - progression.first) * mWidth / progression.step).toDp() }
            }
        }
        var offset by remember {
            mutableFloatStateOf(0F)
        }
        Box(
            modifier = Modifier
                .padding(horizontal = 5.dp)
                .fillMaxWidth()
                .height(4.dp)
                .background(Color(0XFFCECECE))
                .pointerInput(Unit) {
                    detectTapGestures {
                        if (enabled) {
                            if (it.x % mWidth > mWidth / 2) {
                                state.value = (it.x / mWidth).toInt() * progression.step + progression.step + progression.first
                            } else {
                                state.value = (it.x / mWidth).toInt() * progression.step + progression.first
                            }
                        }
                    }
                }
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .width(offsetDp)
                    .background(trackColor)
            )
        }
        val draggableState by remember {
            mutableStateOf(DraggableState {
                offset += it
                if (offset > mWidth / 2 && state.value < progression.last) {
                    offset -= mWidth
                    state.value += progression.step
                } else if (offset < -mWidth / 2 && state.value > progression.first) {
                    offset += mWidth
                    state.value -= progression.step
                }
            })
        }
        val draggableModifier = if (enabled) {
            Modifier.draggable(
                state = draggableState,
                orientation = Orientation.Horizontal,
                onDragStopped = {
                    offset = 0F
                }
            )
        } else {
            Modifier
        }
        Box(
            contentAlignment = Alignment.Center,
            modifier = Modifier
                .offset(offsetDp - 5.dp, 0.dp)
                .size(20.dp)
                .then(draggableModifier)
        ) {
            Icon(
                painter = painterResource(id = org.aihealth.ineck.R.drawable.img_slider),
                contentDescription = null,
                modifier = Modifier
                    .size(20.dp, 24.dp),
                tint = thumbColor
            )
        }

    }
}

class AIHSliderState(
    initialValue: Int,

){
    var value by mutableIntStateOf(initialValue)
    var offset = 0F
}

@Preview(showBackground = true)
@Composable
private fun Text() {
    val state by remember {
        mutableStateOf(AIHSliderState(0))
    }
    AIHSlider(state = state, modifier = Modifier.padding(horizontal = 20.dp, vertical = 30.dp))
}