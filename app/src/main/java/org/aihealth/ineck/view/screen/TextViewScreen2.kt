package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.tooling.preview.Preview
import org.aihealth.ineck.util.networkTime
import org.aihealth.ineck.view.dialog.DatePickerDialog
import java.util.GregorianCalendar

@Preview
@Composable
fun TextViewScreen2() {
    var visible by remember {
        mutableStateOf(false)
    }
    val list = (5..100).toList().map { "这是第${it}个文本" }
    var result by remember {
        mutableStateOf("")
    }
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Column {
            Text(text = "选中结果： $result")
            Button(onClick = {
                visible = true
            }) {
                Text(text = "弹出")
            }
        }
        if (visible) {
            Box(modifier = Modifier
                .fillMaxSize()
                .background(color = Color(0X50000000))
                .pointerInput(Unit) { detectTapGestures { visible = false } })
//            PickDialog(list = list,
//                onConfirmClick = {
//                    result = list.get(it)
//                    visible = false
//                },
//                modifier = Modifier.align(
//                    Alignment.BottomCenter
//                ),
//                initialIndex = 10,
//                title = "选择一个",
//                onCancelClick = { visible = false })
                DatePickerDialog(
                    onConfirmClick = {
                        result = it.networkTime
                        visible  = false
                    },
                    modifier = Modifier.fillMaxWidth(),
                    initialCalendar = GregorianCalendar(2089,5,31),
                    onCancelClick = { visible = false }
                )
        }
    }


}