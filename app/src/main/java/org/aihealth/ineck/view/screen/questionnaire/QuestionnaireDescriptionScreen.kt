package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.PathEffect
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.Dimension
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil

/**
 * 问卷模块的步骤描述
 */
@Preview(showBackground = true, locale = "en")
@Composable
fun QuestionnaireDescriptionScreen(
    modifier: Modifier = Modifier,
    target: Int = 1,
    type: DeviceType = DeviceType.aiNeck,
    onStart: () -> Unit = {},
    onSkip: () -> Unit = {},
) {
    val density = LocalDensity.current
    val fontSize14 = with(density) {
        14.sp / fontScale
    }
    val fontSize12 = with(density) {
        12.sp / fontScale
    }
    ConstraintLayout(
        modifier = modifier
            .fillMaxSize()
            .verticalScroll(
                rememberScrollState()
            )
    ) {
        val (s1, s2, s3, s4, d1, d2, d3, desc1, desc2, desc3, desc4, but1) = createRefs()
        val startGuideline = createGuidelineFromStart(0.1f)
        val topGuideline = createGuidelineFromTop(0.1f)
        val endGuideline = createGuidelineFromEnd(0.1f)

        val desText1th = when (type) {
            DeviceType.aiNeck -> stringResource(id = R.string.health_questionnaire_text)
            DeviceType.aiNeckCV -> stringResource(id = R.string.health_questionnaire_text)
            DeviceType.aiBack -> stringResource(id = R.string.back_health_questionnaire_text)
            DeviceType.aiBackCV -> stringResource(id = R.string.back_health_questionnaire_text)
            else -> stringResource(id = R.string.health_questionnaire_text)
        }
        if (target == 0) {
            QuestionnaireTitleOne(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(s1) {
                        top.linkTo(topGuideline)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "1",
                text = stringResource(id = R.string.health_questionnaire),
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d1) {
                        top.linkTo(s1.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFF3888FF)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc1) {
                        top.linkTo(s1.bottom, margin = 10.dp)
                        start.linkTo(d1.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                textAlign = TextAlign.Start,
                text = desText1th,
                style = TextStyle(
                    fontSize = fontSize14,
                    lineHeight = 20.87.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.42.sp,
                )
            )
        } else if (target > 0) {
            QuestionnaireTitleTwo(
                modifier = Modifier
                    .constrainAs(s1) {
                        top.linkTo(topGuideline)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "1",
                text = stringResource(id = R.string.health_questionnaire),
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d1) {
                        top.linkTo(s1.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFF3888FF)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc1) {
                        top.linkTo(s1.bottom, margin = 10.dp)
                        start.linkTo(d1.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = stringResource(id = R.string.health_questionnaire_text),
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                ),
                textAlign = TextAlign.Start

            )
        }

        if (target < 1) {
            QuestionnaireTitleThree(
                modifier = Modifier
                    .constrainAs(s2) {
                        top.linkTo(d1.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "2",
                text = stringResource(id = R.string.scale_collection)
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d2) {
                        top.linkTo(s2.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFFD9D9D9)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc2) {
                        top.linkTo(s2.bottom, margin = 10.dp)
                        start.linkTo(d2.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = stringResource(id = R.string.scal_collection_text),
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                ),
                textAlign = TextAlign.Start
            )
        } else if (target == 1) {
            QuestionnaireTitleOne(
                modifier = Modifier
                    .constrainAs(s2) {
                        top.linkTo(d1.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "2",
                text = stringResource(id = R.string.scale_collection)
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d2) {
                        top.linkTo(s2.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFF3888FF)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc2) {
                        top.linkTo(s2.bottom, margin = 10.dp)
                        start.linkTo(d2.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = stringResource(id = R.string.scal_collection_text),
                style = TextStyle(
                    fontSize = fontSize14,
                    lineHeight = 20.87.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.42.sp,
                ),
                textAlign = TextAlign.Start
            )
        } else {
            QuestionnaireTitleTwo(
                modifier = Modifier
                    .constrainAs(s2) {
                        top.linkTo(d1.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "2",
                text = stringResource(id = R.string.scale_collection)
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d2) {
                        top.linkTo(s2.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFF3888FF)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc2) {
                        top.linkTo(s2.bottom, margin = 10.dp)
                        start.linkTo(d2.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = stringResource(id = R.string.scal_collection_text),
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                ),
                textAlign = TextAlign.Start
            )
        }
        val des = when (type) {
            DeviceType.aiNeck -> stringResource(id = R.string.neck_detection)
            DeviceType.aiNeckCV -> stringResource(id = R.string.neck_detection)
            DeviceType.aiBack -> stringResource(id = R.string.back_detection)
            DeviceType.aiBackCV -> stringResource(id = R.string.back_detection)
            else -> stringResource(id = R.string.neck_detection_text)
        }
        val desText = when (type) {
            DeviceType.aiNeck -> stringResource(id = R.string.neck_detection_text)
            DeviceType.aiNeckCV -> stringResource(id = R.string.neck_detection_text)
            DeviceType.aiBack -> stringResource(id = R.string.back_detection_text)
            DeviceType.aiBackCV -> stringResource(id = R.string.back_detection_text)
            else -> stringResource(id = R.string.neck_detection_text)
        }

        if (target < 2) {
            QuestionnaireTitleThree(
                modifier = Modifier
                    .constrainAs(s3) {
                        top.linkTo(d2.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "3",
                text = des,
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d3) {
                        top.linkTo(s3.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFFD9D9D9)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc3) {
                        top.linkTo(s3.bottom, margin = 10.dp)
                        start.linkTo(d3.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = desText,
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                ),
                textAlign = TextAlign.Start
            )
        } else if (target == 2) {
            QuestionnaireTitleOne(
                modifier = Modifier
                    .constrainAs(s3) {
                        top.linkTo(d2.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "3",
                text = des,
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d3) {
                        top.linkTo(s3.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFF3888FF)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc3) {
                        top.linkTo(s3.bottom, margin = 10.dp)
                        start.linkTo(d3.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = desText,
                style = TextStyle(
                    fontSize = fontSize14,
                    lineHeight = 20.87.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.42.sp,
                ),
                textAlign = TextAlign.Start
            )
        } else {

            QuestionnaireTitleTwo(
                modifier = Modifier
                    .constrainAs(s3) {
                        top.linkTo(d2.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "3",
                text = des,
            )
            VerticalDottedLine(
                modifier = Modifier
                    .constrainAs(d3) {
                        top.linkTo(s3.bottom)
                        start.linkTo(startGuideline, 13.dp)
                    },
                Color(0xFF3888FF)
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc3) {
                        top.linkTo(s3.bottom, margin = 10.dp)
                        start.linkTo(d3.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = desText,
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                ),
                textAlign = TextAlign.Start
            )
        }

        if (target < 3) {
            QuestionnaireTitleThree(
                modifier = Modifier
                    .constrainAs(s4) {
                        top.linkTo(d3.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "4",
                text = stringResource(id = R.string.montoring),
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc4) {
                        top.linkTo(s4.bottom, margin = 10.dp)
                        start.linkTo(d3.end, margin = 30.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = stringResource(id = R.string.montoring_text),
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                ),
                textAlign = TextAlign.Start
            )
        } else if (target == 3) {
            QuestionnaireTitleOne(
                modifier = Modifier
                    .constrainAs(s4) {
                        top.linkTo(d3.bottom)
                        start.linkTo(startGuideline)
                    },
                serialNumber = "4",
                text = stringResource(id = R.string.montoring),
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .constrainAs(desc4) {
                        top.linkTo(s4.bottom, margin = 10.dp)
                        start.linkTo(d3.end, margin = 32.dp)
                        end.linkTo(endGuideline)
                        width = Dimension.preferredWrapContent
                    },
                text = stringResource(id = R.string.montoring_text),
                style = TextStyle(
                    fontSize = fontSize14,
                    lineHeight = 20.87.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.42.sp,
                ),
                textAlign = TextAlign.Start
            )

        }

        Column(
            modifier = Modifier
                .constrainAs(but1) {
                    top.linkTo(desc4.bottom, 70.dp)
                }
                .fillMaxWidth()
                .padding(bottom = 30.dp),
            verticalArrangement = Arrangement.Bottom,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Button(
                modifier = Modifier.fillMaxWidth(0.8f),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3888FF)),
                shape = RoundedCornerShape(size = 21.5.dp),
                onClick = {
                    LogUtil.i("onClick btn")
                    onStart()
                }
            ) {
                Text(
                    text = stringResource(id = R.string.welcome_start),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
            Text(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {

                        LogUtil.i("onClick skip")
                        onSkip()
                    },
                text = stringResource(id = R.string.skip),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                )
            )

        }

    }

}

@Composable
fun QuestionnaireTitleOne(
    modifier: Modifier = Modifier,
    serialNumber: String,
    text: String,
) {
    val density = LocalDensity.current
    val fontSize24 = with(density) {
        24.sp / fontScale
    }
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        SerialNumber(
            modifier = Modifier
                .size(26.dp),
            text = serialNumber,
            color = Color(0xFF3888FF),
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 18.dp),
            text = text,
            style = TextStyle(
                fontSize = fontSize24,
                fontWeight = FontWeight(600),
                color = Color(0xFF3888FF),
                letterSpacing = 0.24.sp,
            ),
            textAlign = TextAlign.Start
        )
    }
}


@Composable
fun QuestionnaireTitleTwo(
    modifier: Modifier = Modifier,
    serialNumber: String,
    text: String
) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        SerialNumber(
            modifier = Modifier
                .padding(horizontal = 2.dp)
                .size(22.dp),
            text = serialNumber,
            color = Color(0xFF3888FF),
        )
        Text(
            modifier = Modifier.padding(start = 20.dp),
            text = text,
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF3888FF),
            ),
            textAlign = TextAlign.Start
        )
    }
}

@Composable
fun QuestionnaireTitleThree(
    modifier: Modifier = Modifier,
    serialNumber: String,
    text: String
) {
    val density = LocalDensity.current
    val fontsize16 = with(density) {
        16.sp / fontScale
    }
    Row(
        modifier = modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Start
    ) {
        SerialNumber(
            modifier = Modifier
                .padding(start = 2.dp)
                .size(22.dp),
            text = serialNumber,
            color = Color(0xFFD9D9D9),
        )
        Text(
            modifier = Modifier.padding(start = 20.dp),
            text = text,
            style = TextStyle(
                fontSize = fontsize16,
                fontWeight = FontWeight(500),
                color = Color(0xFF666666),
            ),
            textAlign = TextAlign.Start
        )
    }
}

@Composable
private fun SerialNumber(
    modifier: Modifier = Modifier,
    text: String,
    color: Color,
) {
    val density = LocalDensity.current
    // 忽略系统字体缩放
    val fixedFontSize18 = with(density) {
        18.sp / fontScale
    }
    Box(
        modifier = modifier
            .clip(CircleShape)
            .background(color),
    ) {
        Text(
            modifier = Modifier.align(Alignment.Center),
            text = text,
            style = TextStyle(
                fontSize = fixedFontSize18,
                fontWeight = FontWeight(400),
                color = Color(0xFFFFFFFF),
            )
        )
    }
}

@Composable
fun VerticalDottedLine(
    modifier: Modifier,
    color: Color
) {
    Canvas(modifier = modifier.size(1.dp, 100.dp)) {
        drawLine(
            color = color,
            start = Offset(0f, 0f),
            end = Offset(0f, size.height),
            strokeWidth = 2f,
            pathEffect = PathEffect.dashPathEffect(floatArrayOf(10f, 10f), 0f)
        )
    }
}