package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.viewmodel.PersonalDataViewModel
import org.aihealth.ineck.viewmodel.user

@Composable
fun GenderEditRoute(
    viewModel: PersonalDataViewModel = viewModel()
) {
    // 获取当前用户的性别
    val currentGender = when (user.gender) {
        "M" -> 0
        "F" -> 1
        else -> 2 // Other
    }

    GenderEditScreen(
        initialGender = currentGender,
        onSave = { selectedGender ->
            val genderCode = when (selectedGender) {
                0 -> "M"
                1 -> "F"
                else -> "U"
            }
            viewModel.setGender(genderCode)
            finish()
        },
        onCancel = {
            finish()
        }
    )
}

@Composable
fun GenderEditScreen(
    initialGender: Int = 0,
    onSave: (Int) -> Unit = { },
    onCancel: () -> Unit = {}
) {
    var selectedGender by remember { mutableStateOf(initialGender) }

    BaseEditScreen(
        onSave = {
            onSave(selectedGender)
        },
        onCancel = onCancel
    ) {
        // Gender Options Container
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(16.dp)
        ) {
            GenderOption(
                modifier = Modifier.height(20.dp),
                label = stringResource(R.string.male),
                isSelected = selectedGender == 0,
                onSelect = { selectedGender = 0 }
            )

            AIHDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp)
            )

            GenderOption(
                modifier = Modifier.height(20.dp),
                label = stringResource(R.string.female),
                isSelected = selectedGender == 1,
                onSelect = { selectedGender = 1 }
            )

            AIHDivider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 10.dp)
            )

            GenderOption(
                modifier = Modifier.height(20.dp),
                label = stringResource(R.string.unknown),
                isSelected = selectedGender == 2,
                onSelect = { selectedGender = 2 }
            )
        }
    }
}

@Composable
private fun GenderOption(
    modifier: Modifier = Modifier,
    label: String,
    isSelected: Boolean,
    onSelect: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(
                indication = null,
                interactionSource = remember { MutableInteractionSource() }
            ) {
                onSelect()
            },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = label,
            fontSize = 16.sp,
            fontWeight = FontWeight.Normal,
            color = Color(0xFF666666)
        )

        if (isSelected) {
            Text(
                text = "✓",
                fontSize = 20.sp,
                color = MaterialTheme.colorScheme.primary,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun PreviewGenderEditScreen() {
    AIH_UserTheme {
        GenderEditScreen()
    }
}