package org.aihealth.ineck.view.button

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R


/**
 * 开启视觉校准按钮
 * @param modifier
 * @param onClick
 */
@Composable
fun DetectionButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    OutlinedButton(
        enabled = true,
        onClick = {
            onClick()
        },
        shape = RoundedCornerShape(8.dp),
        contentPadding = PaddingValues(0.dp),
        modifier = modifier
            .width(56.dp)
            .height(24.dp),
        colors = ButtonDefaults.outlinedButtonColors(
            containerColor = Color.White,
        )
    ) {
        Text(
            text = stringResource(id = R.string.enable_detection),
            style = TextStyle(
                fontSize = 10.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF666666),
            )
        )
    }
}

/**
 * 设备重新校准按钮
 * @param modifier
 * @param onClick
 */
@Preview()
@Composable
fun RecalibrateButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        horizontalArrangement = Arrangement.End
    ) {
        Box(modifier = Modifier
            .shadow(
                elevation = 4.dp,
                shape = RoundedCornerShape(
                    topStartPercent = 50,
                    bottomStartPercent = 50
                ),
                spotColor = Color(0XFF1E4BDF),
                ambientColor = Color.Transparent
            )
            .padding(start = 5.dp, top = 5.dp, bottom = 5.dp)
            .clip(RoundedCornerShape(topStart = 20.dp, bottomStart = 20.dp))
            .clickable {
                onClick()
            }
            .background(Color.White),
            contentAlignment = Center
        ) {
            Text(
                modifier = Modifier.padding(vertical = 8.dp, horizontal = 8.dp),
                text = stringResource(id = R.string.device_recalibrate),
                fontSize = 14.sp,
                color = Color(0XFF444444),
                fontWeight = FontWeight.Normal
            )
        }
    }
}

/**
 * 干预的选择按钮
 * @param selectedIndex
 * @param array
 * @param onClick
 * @param modifier
 * @param backgroundColor
 * @param selectedColor
 * @param padding
 */
@Preview()
@Composable
fun TypeSelectButton(
    selectedIndex: Int = 1,
    array: Array<String> = stringArrayResource(id = R.array.device_mode),
    onClick: (index: Int) -> Unit = {},
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0XFFE6E6E6),
    selectedColor: Color = Color.White,
    padding: PaddingValues = PaddingValues(3.dp)
) {
    var _selectedIndex by remember {
        mutableIntStateOf(selectedIndex)
    }
    _selectedIndex = selectedIndex
    BoxWithConstraints(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .height(38.dp)
            .background(backgroundColor, CircleShape)
            .padding(padding)
    ) {
        val offset by animateDpAsState(
            targetValue = (maxWidth * _selectedIndex / array.size), label = ""
        )
        Box(
            modifier = Modifier
                .offset(offset, 0.dp)
                .size(maxWidth / array.size, maxHeight)
                .background(selectedColor, CircleShape)
        )
        Row(verticalAlignment = Alignment.CenterVertically) {
            array.forEachIndexed { index, str ->
                Box(modifier = Modifier
                    .pointerInput(Unit) {
                        detectTapGestures {
                            onClick(index)
                        }
                    }
                    .fillMaxHeight()
                    .weight(1F),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = str,
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(400),
                            color = Color.Black
                        )
                    )
                }
            }
        }
    }

}

@Composable
fun QuestionnaireEnterButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit,
) {
    val gradient = Brush.linearGradient(
        colors = listOf(
            Color(0xFF3E68E7),
            Color(0xFF5ACFF4)
        )
    )
    Text(
        modifier = modifier
            .background(gradient, shape = RoundedCornerShape(24.dp))
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 4.dp),
        text = stringResource(id = R.string.enter_questionnaire),
        style = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight(500),
            color = Color(0xFFFFFFFF),
            textAlign = TextAlign.Center,
        )
    )


}

@Preview(showBackground = true, locale = "zh")
@Composable
private fun PreviewButton() {
//    RecalibrateButton(onClick = {})
//    TypeSelectButton(
//        selectedIndex = 0,
//        array = stringArrayResource(id = R.array.device_mode),
//        onClick = {
//        },
//        modifier = Modifier
//            .padding(top = 20.dp)
//            .size(220.dp,38.dp)
//    )
    QuestionnaireEnterButton(
        onClick = {}
    )
}