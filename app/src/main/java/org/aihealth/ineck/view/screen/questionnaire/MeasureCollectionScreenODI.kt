package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.angles.Odi
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHSliderState
import org.aihealth.ineck.view.screen.ODISelectedView
import org.aihealth.ineck.view.screen.Section
import java.util.Locale

@Preview(showBackground = true)
@Composable
fun MeasureCollectionScreenODI(
    modifier: Modifier = Modifier,
    onStart: () -> Unit = {},
    onSkip: () -> Unit = {},
    enable: Boolean = false,
    onDismissRequest: () -> Unit = {}
) {

    val odiStateList = remember {
        mutableStateListOf<AIHSliderState>()
    }
    repeat(10) {
        odiStateList.add(AIHSliderState(0))
    }

    val odiScrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            text = "ODI",
            style = TextStyle(
                fontSize = 20.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF444444),
                textAlign = TextAlign.Center,
            )
        )
        ODIView(odiScrollState, odiStateList, true) {
            onStart()
        }

    }
    SkipDialog(
        enable = enable,
        onDismissRequest = { onDismissRequest() },
        onConfirm = { onSkip() }
    )

}

@Composable
private fun ODIView(
    scrollState: ScrollState,
    valueList: SnapshotStateList<AIHSliderState>,
    enabled: Boolean = true,
    onSuccess: () -> Unit = {}
) {
    Column(
        Modifier
            .fillMaxSize()
            .padding(horizontal = 8.dp)
            .verticalScroll(scrollState)
    ) {
        Text(
            text = stringResource(id = R.string.odi_record_title),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF64CECD),
                textAlign = TextAlign.Justify,
            ),
            modifier = Modifier.padding(top = 10.dp)
        )
        Section(id = R.string.odi_record_section1)
        ODISelectedView(
            state = valueList[0],
            array = stringArrayResource(R.array.odi_record_option1),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section2)
        ODISelectedView(
            state = valueList[1],
            array = stringArrayResource(R.array.odi_record_option2),
            isBeyondLine = currentLocale != Locale.CHINESE,
            enabled = enabled
        )

        Section(id = R.string.odi_record_section3)
        ODISelectedView(
            state = valueList[2],
            array = stringArrayResource(R.array.odi_record_option3),
            isBeyondLine = true,
            enabled = enabled
        )

        Section(id = R.string.odi_record_section4)
        ODISelectedView(
            state = valueList[3],
            array = stringArrayResource(R.array.odi_record_option4),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section5)
        ODISelectedView(
            state = valueList[4],
            array = stringArrayResource(R.array.odi_record_option5),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section6)
        ODISelectedView(
            state = valueList[5],
            array = stringArrayResource(R.array.odi_record_option6),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section7)
        ODISelectedView(
            state = valueList[6],
            array = stringArrayResource(R.array.odi_record_option7),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section8)
        ODISelectedView(
            state = valueList[7],
            isBeyondLine = currentLocale != Locale.CHINESE,
            array = stringArrayResource(R.array.odi_record_option8),
            enabled = enabled
        )

        Section(id = R.string.odi_record_section9)
        ODISelectedView(
            state = valueList[8],
            array = stringArrayResource(R.array.odi_record_option9),
            enabled = enabled
        )
        Spacer(modifier = Modifier.height(36.dp))
        if (enabled) {
            AIHButton(
                text = stringResource(id = R.string.submit),
                onClick = {
                    apiService.postOdi(
                        odi = Odi(
                            pain = valueList[0].value,
                            care = valueList[1].value,
                            lifting = valueList[2].value,
                            walking = valueList[3].value,
                            sitting = valueList[4].value,
                            standing = valueList[5].value,
                            sleeping = valueList[6].value,
                            social = valueList[7].value,
                            travelling = valueList[8].value
                        )
                    ).enqueueBody {
                        onSuccess()
                    }
                },
                fontSize = 20.sp,
                fontColor = Color.White,
                modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
        }
    }
}
