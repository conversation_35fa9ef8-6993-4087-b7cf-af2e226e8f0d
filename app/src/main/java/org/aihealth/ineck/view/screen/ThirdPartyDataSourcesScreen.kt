package org.aihealth.ineck.view.screen

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.Switch
import androidx.compose.material3.SwitchDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
// 移除StepService导入，现在使用HybridStepTracker
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.StepModuleManager
import org.aihealth.ineck.util.userSP
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun ThirdPartyDataSourcesScreen(
    viewModel: MainViewModel
) {

    BasePageView(
        modifier = Modifier.fillMaxWidth(),
        showBackIcon = true,
        headerContent = {
            Row(
                modifier = Modifier
                    .fillMaxWidth(0.8f)
                    .align(Alignment.Center),
                horizontalArrangement = Arrangement.Center,
            ) {
                Text(
                    modifier = Modifier,
                    text = stringResource(id = R.string.third_party_data_sources),
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
            }
        },
    ) {
        // 功能列表
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
                .padding(horizontal = 8.dp)
                .background(Color.White),
            verticalArrangement = Arrangement.Top,

            ) {
            SwitchItem(
                title = stringResource(id = R.string.local_data),
                stringId = R.array.switch_button_simple_state,
                selectedIndex = viewModel.useLocalData,
                onClick = {
                    LogUtil.i("🎚️ ThirdPartyDataSourcesScreen: ========== 用户点击步数开关 ==========")
                    LogUtil.i("🎚️ ThirdPartyDataSourcesScreen: 当前状态 - useLocalData: ${viewModel.useLocalData} (${if(viewModel.useLocalData == 0) "开启" else "关闭"})")
                    
                    // 使用StepModuleManager统一管理步数开关状态
                    if (viewModel.useLocalData == 1) {
                        LogUtil.i("🎚️ ThirdPartyDataSourcesScreen: 用户尝试启用步数模块，开始权限检查")
                        // 用户尝试启用本地数据源，需要先获取权限
                        activity.requestStepPermission(
                            onPermissionGranted = {
                                LogUtil.i("✅ ThirdPartyDataSourcesScreen: 权限获取成功，用户主动启用步数模块")
                                // 权限获取成功，标记为用户主动启用
                                StepModuleManager.enableByUser()
                                viewModel.useLocalData = 0
                                LogUtil.i("✅ ThirdPartyDataSourcesScreen: UI状态已更新为开启")
                            },
                            onPermissionDenied = {
                                LogUtil.i("❌ ThirdPartyDataSourcesScreen: 权限被拒绝，保持禁用状态")
                                // 权限被拒绝，保持禁用状态
                                viewModel.useLocalData = 1
                                LogUtil.i("❌ ThirdPartyDataSourcesScreen: UI状态保持关闭")
                            }
                        )
                    } else {
                        LogUtil.i("🎚️ ThirdPartyDataSourcesScreen: 用户手动关闭步数模块")
                        // 用户手动禁用本地数据源
                        StepModuleManager.disableByUser()
                        viewModel.useLocalData = 1
                        LogUtil.i("❌ ThirdPartyDataSourcesScreen: UI状态已更新为关闭")
                    }
                    
                    LogUtil.i("🎚️ ThirdPartyDataSourcesScreen: ========== 步数开关处理完成 ==========")
                }
            )

        }
    }

}

@Preview()
@Composable
fun SwitchItem(
    title: String = "谷歌健身",
    stringId: Int = R.array.switch_button_simple_state,
    selectedIndex: Int = 0,
    shape: Shape = RectangleShape,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .height(50.dp)
            .fillMaxWidth()
            .background(Color.White, shape),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(10.dp))
        Text(text = title, fontSize = 18.sp, color = Color(0XFF444444))
        Spacer(modifier = Modifier.weight(1F))
        Switch(
            checked = selectedIndex == 0,
            onCheckedChange = {
                onClick()
            },
            colors = SwitchDefaults.colors(
                checkedThumbColor = Color.White,
                checkedTrackColor = Color(0xFF34C759), // iOS风格的绿色
                uncheckedThumbColor = Color.White,
                uncheckedTrackColor = Color(0xFFE5E5E7), // 浅灰色
                checkedBorderColor = Color(0xFF34C759),
                uncheckedBorderColor = Color(0xFFE5E5E7)
            )
        )
        Spacer(modifier = Modifier.width(10.dp))
    }
    AIHDivider(modifier = Modifier.padding(horizontal = 10.dp))
}