package org.aihealth.ineck.view.screen.device

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import android.content.Intent
import android.os.Build
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.delay
import no.nordicsemi.android.dfu.DfuProgressListenerAdapter
import no.nordicsemi.android.dfu.DfuServiceListenerHelper
import org.aihealth.ineck.R
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.bluetooth.BleService
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.toSp
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.dialog.LoadingDialog
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.device.ConnectionError
import org.aihealth.ineck.viewmodel.device.DeviceUIState
import org.aihealth.ineck.viewmodel.device.DeviceViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MatchingDeviceSheet(
    viewModel: DeviceViewModel,
    deviceType: String,
    onDismiss: () -> Unit
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)

    // 获取deviceViewModel的状态
    val deviceUIState = when (deviceType) {
        DeviceType.aiNeck.name ->  viewModel.neckDeviceUIState.collectAsStateWithLifecycle().value
        else -> viewModel.backDeviceUIState.collectAsStateWithLifecycle().value
    }
    val connectionError by viewModel.connectionError.collectAsStateWithLifecycle()
    val scanUiState by viewModel.scanUiState.collectAsStateWithLifecycle()
    // 获取对应配置文件
    val deviceConfig = when (deviceType) {
        DeviceType.aiNeck.name -> viewModel.neckDeviceConfig.collectAsStateWithLifecycle().value
        DeviceType.aiBack.name -> viewModel.backDeviceConfig.collectAsStateWithLifecycle().value
        else -> DeviceConfig()
    }
    val neckDeviceConnected by viewModel.neckDeviceConnected.collectAsStateWithLifecycle()
    val backDeviceConnected by viewModel.backDeviceConnected.collectAsStateWithLifecycle()
    LaunchedEffect(neckDeviceConnected,backDeviceConnected) {
        if (neckDeviceConnected||backDeviceConnected) {
            // 连接成功，跳转到主页
            onDismiss()
            MainViewModel.pageIndex = 1
        }
    }

    ModalBottomSheet(
        onDismissRequest = {
            onDismiss()
        },
        sheetState = sheetState,
        shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp),
    ) {
        MatchingDeviceSheetContent(
            deviceConfig = deviceConfig,
            devices = scanUiState.foundDevices,
            deviceUIState = deviceUIState,
            connectionError = connectionError,
            connectDevice = { mac ->
//                // 连接设备前停止扫描
                if (scanUiState.isScanning) {
                    viewModel.stopScan()
                }
                val intent = Intent(baseApplication, BleService::class.java)
                if(Build.VERSION.SDK_INT >= Build.VERSION_CODES.O){
                    baseApplication.startForegroundService(intent)
                }else{
                    baseApplication.startService(intent)
                }
                viewModel.connect(mac,deviceConfig.deviceType)
            },
            onRetryScan = {
                viewModel.stopScan()
                viewModel.startScan(deviceConfig.deviceType)
            },
            updateDFU = { mac, type ->
                val t = when (type) {
                    "aiNeck" -> DeviceType.aiNeck
                    "aiBack" -> DeviceType.aiBack
                    else -> DeviceType.aiNeck
                }
                viewModel.startUpgrade(mac, t)
            },
            onDfuCompleted = {
                viewModel.onUpgradeCompleted()
            },
            onDfuError = {
                viewModel.onUpgradeFailed()
            },
            onDismiss = {
                onDismiss()
            }
        )
    }
}

/**
 * MatchingDeviceSheet 的主要内容，抽离出来，用于预览
 */
@Composable
fun MatchingDeviceSheetContent(
    deviceConfig: DeviceConfig,
    devices: List<BluetoothDevice>,
    deviceUIState: DeviceUIState,
    connectionError: ConnectionError?,
    connectDevice: (String) -> Unit = {},
    onRetryScan: () -> Unit = {},
    updateDFU: (mac: String, deviceType: String) -> Unit = { _, _ -> },
    onDfuCompleted: () -> Unit = {},
    onDfuError: () -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    var onClickTime by remember {
        mutableLongStateOf(0L)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 24.dp)
    ) {
        Text(
            text = stringResource(id = R.string.matching_device),
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                color = Color(0xFF333333)
            ),
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(top = 8.dp),
            textAlign = TextAlign.Center
        )

        AIHDivider()

        // 显示连接状态
        when (deviceUIState) {
            is DeviceUIState.Scanning -> {
                if (deviceConfig.mac.isNotEmpty()) {
                    ConnectedDevices(
                        modifier = Modifier
                            .padding(horizontal = 16.dp)
                            .fillMaxWidth()
                            .verticalScroll(rememberScrollState()),
                        devices = listOf(deviceConfig),
                        onClick = {
                            if (System.currentTimeMillis() - onClickTime > 5000L) {
                                LogUtil.i("onClick mac:$it")
                                onClickTime = System.currentTimeMillis()
                                connectDevice(it)
                            }
                        }
                    )
                }
                // 搜索状态内容
                Column(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 4.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (devices.isNotEmpty()) {
                        // 显示已搜索到的设备
                        FindDeviceInSheet(
                            connectDevice = connectDevice,
                            devices = devices,
                            savedDeviceMac = deviceConfig.mac
                        )
                    } else {
                        // 正在寻找设备
                        LookForDeviceInSheet()
                    }
                }
            }
            is DeviceUIState.ScanError -> {
                Column(
                    Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ){
                    ScanOverTimeView(
                        onRetryScan = onRetryScan
                    )
                }
            }
            is DeviceUIState.Connecting -> {
                ConnectionStatusView(
                    isConnecting = true,
                    message = stringResource(id = R.string.connecting)
                )
            }

            is DeviceUIState.Connected -> {
                ConnectionStatusView(
                    isSuccess = true,
                    message = stringResource(id = R.string.connected)
                )
            }

            is DeviceUIState.Error -> {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 24.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_error),
                        contentDescription = null,
                        modifier = Modifier.size(48.dp),
                    )
                    Spacer(modifier = Modifier.height(16.dp))

                    when (connectionError) {
                        is ConnectionError.UnknownError -> {
                            val errorMessage = stringResource(
                                R.string.error_unknown,
                                (connectionError as ConnectionError.UnknownError).message
                            )
                            Text(
                                text = errorMessage,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center,
                                    color = Color(0xFF333333)
                                )
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AIHOutlinedButton(
                                modifier = Modifier.fillMaxWidth(0.8f),
                                text = stringResource(id = R.string.rescan),
                                onClick = onRetryScan,
                                fontSize = 16.sp,
                                fontColor = Color(0XFF333333)
                            )
                        }

                        is ConnectionError.InvalidAddress -> {
                            val errorMessage= stringResource(R.string.error_invalid_address)
                            Text(
                                text = errorMessage,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center,
                                    color = Color(0xFF333333)
                                )
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AIHOutlinedButton(
                                modifier = Modifier.fillMaxWidth(0.8f),
                                text = stringResource(id = R.string.rescan),
                                onClick = onRetryScan,
                                fontSize = 16.sp,
                                fontColor = Color(0XFF333333)
                            )
                        }

                        is ConnectionError.BluetoothNotAvailable -> {
                            val errorMessage =
                                stringResource(R.string.error_bluetooth_not_available)
                            Text(
                                text = errorMessage,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center,
                                    color = Color(0xFF333333)
                                )
                            )
                            Text(
                                text = stringResource(R.string.please_open_bluetooth),
                                fontSize = 18.sp,
                                fontWeight = FontWeight.Medium,
                                textAlign = TextAlign.Center,
                                color = Color(0xFF333333)
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AIHOutlinedButton(
                                modifier = Modifier.fillMaxWidth(0.8f),
                                text = stringResource(id = R.string.rescan),
                                onClick = onRetryScan,
                                fontSize = 16.sp,
                                fontColor = Color(0XFF333333)
                            )
                        }

//                        is ConnectionError.ServiceNotInitialized -> {
//                            val errorMessage= stringResource(R.string.error_service_not_initialized)
//                        }

                        is ConnectionError.DeviceNotFound -> {
                            val errorMessage= stringResource(R.string.error_device_not_found)
                            Text(
                                text = errorMessage,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center,
                                    color = Color(0xFF333333)
                                )
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AIHOutlinedButton(
                                modifier = Modifier.fillMaxWidth(0.8f),
                                text = stringResource(id = R.string.rescan),
                                onClick = onRetryScan,
                                fontSize = 16.sp,
                                fontColor = Color(0XFF333333)
                            )
                        }

                        is ConnectionError.ConnectionFailed -> {
                            val errorMessage= stringResource(R.string.error_connection_failed)
                            Text(
                                text = errorMessage,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center,
                                    color = Color(0xFF333333)
                                )
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AIHOutlinedButton(
                                modifier = Modifier.fillMaxWidth(0.8f),
                                text = stringResource(id = R.string.rescan),
                                onClick = onRetryScan,
                                fontSize = 16.sp,
                                fontColor = Color(0XFF333333)
                            )
                        }

                        else  -> {
                            val errorMessage= stringResource(R.string.connect_device_error)
                            Text(
                                text = errorMessage,
                                style = TextStyle(
                                    fontSize = 18.sp,
                                    fontWeight = FontWeight.Medium,
                                    textAlign = TextAlign.Center,
                                    color = Color(0xFF333333)
                                )
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AIHOutlinedButton(
                                modifier = Modifier.fillMaxWidth(0.8f),
                                text = stringResource(id = R.string.rescan),
                                onClick = onRetryScan,
                                fontSize = 16.sp,
                                fontColor = Color(0XFF333333)
                            )
                        }
                    }
                }
            }

            is DeviceUIState.NeedUpgrade -> {
                FirmwareUpgradeView(
                    updateDFU = {
                        updateDFU(deviceConfig.mac, deviceConfig.deviceType.name)
                    }
                )
            }

            is DeviceUIState.Upgrading -> {
                FirmwareUpgradingView(
                    deviceType = deviceConfig.deviceType.name,
                    onDfuCompleted = {
                        onDfuCompleted()
                    },
                    onError = {
                        onDfuError()
                    }
                )
            }
            is DeviceUIState.UpgradeSuccess -> {
                FirmwareUpgradedSuccessView(
                    onConfirm = {
                        onDismiss()
                    }
                )
            }
            is DeviceUIState.UpgradeFailed -> {
                FirmwareUpgradedErrorView(
                    onRetry = {
                        updateDFU(deviceConfig.mac, deviceConfig.deviceType.name)
                    }
                )
            }
            else -> {}
        }
    }
}

@Composable
private fun ConnectionStatusView(
    isConnecting: Boolean = false,
    isSuccess: Boolean = false,
    message: String
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (isConnecting) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                color = Color(0xFF1E4BDF)
            )
        } else if (isSuccess) {
            Image(
                painter = painterResource(id = R.drawable.icon_ok),
                contentDescription = null,
                modifier = Modifier.size(48.dp)
            )
        }

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = message,
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                color = Color(0xFF333333)
            )
        )
    }
}

@Composable
private fun ConnectionErrorView(
    errorMessage: String,
    onReconnect: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            painter = painterResource(id = R.drawable.icon_error),
            contentDescription = null,
            modifier = Modifier.size(48.dp),
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = errorMessage,
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                color = Color(0xFF333333)
            )
        )

        Spacer(modifier = Modifier.height(24.dp))

        AIHOutlinedButton(
            modifier = Modifier.fillMaxWidth(0.8f),
            text = stringResource(id = R.string.reconnect),
            onClick = onReconnect,
            fontSize = 16.sp,
            fontColor = Color(0XFF333333)
        )
    }
}

@Composable
private fun LookForDeviceInSheet(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.looking_for_your_device),
            style = TextStyle(
                fontSize = 22.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center
            ),
            modifier = Modifier
        )
        Text(
            text = stringResource(id = R.string.keep_aiSpine_device_close_to),
            fontSize = 16.sp,
            style = TextStyle(
                fontWeight = FontWeight.Medium,
                color = Color(0XFF666666),
                textAlign = TextAlign.Center
            ),
            modifier = Modifier.padding(top = 8.dp, bottom = 16.dp)
        )
        LoadingDialog(modifier = Modifier)
        Text(
            text = stringResource(id = R.string.looking_for_your_device_now),
            fontSize = 16.sp,
            style = TextStyle(
                fontWeight = FontWeight.Medium
            ),
            modifier = Modifier.padding(top = 8.dp)
        )
    }
}

@Composable
private fun ScanOverTimeView(
    onRetryScan: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = stringResource(id = R.string.lookfor_timeover_tip),
            fontSize = 18.sp,
            color = Color(0XFF666666),
            textAlign = TextAlign.Center
        )

        AIHOutlinedButton(
            modifier = Modifier
                .fillMaxWidth(0.9f),
            text = stringResource(id = R.string.rescan),
            onClick = onRetryScan,
            fontSize = 18.sp,
            fontColor = Color(0XFF333333)
        )
    }
}

@Composable
private fun FindDeviceInSheet(
    connectDevice: (String) -> Unit = {},
    devices: List<BluetoothDevice> = listOf(),
    savedDeviceMac: String = ""
) {
    Column(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.choose_device),
            fontSize = 22.sp,
            color = Color(0XFF444444),
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(vertical = 16.dp)
        )

        var selectedIndex by remember {
            mutableIntStateOf(0)
        }

        // 寻找已保存的设备索引
        var savedDeviceIndex by remember {
            mutableIntStateOf(-1)
        }

        // 倒计时状态
        var countdown by remember { mutableIntStateOf(0) }
        var isCountingDown by remember { mutableStateOf(false) }

        // 检查是否有保存过的设备在列表中
        LaunchedEffect(devices, savedDeviceMac) {
            if (savedDeviceMac.isNotEmpty()) {
                val index = devices.indexOfFirst { it.address == savedDeviceMac }
                if (index >= 0) {
                    savedDeviceIndex = index
                    selectedIndex = index
                    countdown = 3
                    isCountingDown = true
                }
            }
        }

        // 倒计时逻辑
        LaunchedEffect(isCountingDown, selectedIndex, savedDeviceIndex) {
            if (isCountingDown && selectedIndex == savedDeviceIndex) {
                while (countdown > 0 && selectedIndex == savedDeviceIndex) {
                    delay(1000)
                    countdown--
                }

                if (countdown <= 0 && selectedIndex == savedDeviceIndex) {
                    // 倒计时结束且用户未选择其他设备，自动连接
                    connectDevice(devices[savedDeviceIndex].address)
                }

                isCountingDown = false
            } else if (selectedIndex != savedDeviceIndex) {
                // 用户选择了其他设备，停止倒计时
                isCountingDown = false
            }
        }

        LazyColumn(
            Modifier
                .fillMaxWidth()
                .height(120.dp)
        ) {
            itemsIndexed(devices) { index, device ->
                FindDeviceItem(
                    selectedIndex = selectedIndex,
                    index = index,
                    device = device,
                    isSavedDevice = device.address == savedDeviceMac,
                    countdownSeconds = if (isCountingDown && index == savedDeviceIndex) countdown else null,
                    onClick = {
                        selectedIndex = index
                        // 如果用户选择了非保存设备，停止倒计时
                        if (index != savedDeviceIndex) {
                            isCountingDown = false
                        }
                    }
                )
                AIHDivider()
            }
        }

        var lastClickTime by remember {
            mutableLongStateOf(0L)
        }

        AIHOutlinedButton(
            text = if (isCountingDown && selectedIndex == savedDeviceIndex)
                "${stringResource(id = R.string.next_continue)} ($countdown)"
            else stringResource(id = R.string.next_continue),
            onClick = {
                System.currentTimeMillis().let {
                    if (it - lastClickTime > 1000) {
                        lastClickTime = it
                        isCountingDown = false // 手动点击停止倒计时
                        connectDevice(devices[selectedIndex].address)
                    }
                }
            },
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth(),
            fontSize = 18.sp,
            fontColor = Color(0XFF333333)
        )
    }
}

@SuppressLint("MissingPermission")
@Composable
private fun FindDeviceItem(
    selectedIndex: Int,
    index: Int,
    device: BluetoothDevice,
    isSavedDevice: Boolean = false,
    countdownSeconds: Int? = null,
    onClick: () -> Unit = {}
) {
    val fontSize by animateDpAsState(
        targetValue = if (selectedIndex == index) 20.dp else 16.dp,
        label = ""
    )
    AIHDivider()
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(if (selectedIndex == index) 44.dp else 40.dp)
            .background(if (selectedIndex == index) Color(0x661E4BDF) else Color.White)
            .pointerInput(Unit) {
                detectTapGestures {
                    onClick()
                }
            },
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text(
            text = device.address + if (isSavedDevice) " (${stringResource(id = R.string.save)})" else "",
            fontSize = fontSize.toSp(),
            color = if (selectedIndex == index) Color.White else Color.Black,
            modifier = Modifier.padding(start = 16.dp)
        )

        if (countdownSeconds != null) {
            Text(
                text = "$countdownSeconds",
                fontSize = 14.sp,
                color = if (selectedIndex == index) Color.White else Color(0xFF1E4BDF),
                modifier = Modifier.padding(end = 16.dp)
            )
        }
    }
}

@Composable
fun ConnectedDevices(
    modifier: Modifier = Modifier,
    devices: List<DeviceConfig>,
    onClick: (String) -> Unit = {}
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        devices.forEach {
            ConnectedDeviceItem(
                modifier = Modifier.fillMaxWidth(),
                deviceConfig = it,
                onClick = onClick
            )
        }
    }
}

/**
 * 已链接的设备
 */
@Composable
fun ConnectedDeviceItem(
    modifier: Modifier = Modifier,
    deviceConfig: DeviceConfig,
    onClick: (String) -> Unit = {}
) {
    Column(
        modifier = modifier
            .padding(vertical = 8.dp)
            .fillMaxWidth()
            .clickable { onClick(deviceConfig.mac) }
    ) {
        val textStyle = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    shape = RoundedCornerShape(12.dp),
                    color = Color(0xFF1E4BDF)
                )
                .padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            Text(
                text = "name:" + deviceConfig.name.ifEmpty { deviceConfig.deviceType.name },
                style = textStyle
            )
            Text(
                text = "mac:" + deviceConfig.mac,
                style = textStyle
            )
        }
    }
}

@Composable
private fun FirmwareUpgradeView(updateDFU: () -> Unit) {
    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.dfu_update_dialog_title1, "6.0.6"),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Start
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = stringResource(id = R.string.dfu_update_dialog_content1),
                textAlign = TextAlign.Start
            )
            Spacer(modifier = Modifier.height(20.dp))
            AIHOutlinedButton(
                text = stringResource(id = R.string.update),
                onClick = {
                    updateDFU()
                },
                modifier = Modifier.fillMaxWidth(0.7f)
            )
        }
    }
}


@Composable
private fun FirmwareUpgradingView(
    deviceType: String,
    onDfuCompleted:() -> Unit,
    onError:() -> Unit
) {
    var progress by remember {
        mutableIntStateOf(0)
    }

    val dfuProgressListener = object : DfuProgressListenerAdapter() {
        override fun onProgressChanged(
            deviceAddress: String,
            percent: Int,
            speed: Float,
            avgSpeed: Float,
            currentPart: Int,
            partsTotal: Int
        ) {
            super.onProgressChanged(
                deviceAddress,
                percent,
                speed,
                avgSpeed,
                currentPart,
                partsTotal
            )
            progress = percent
        }

        override fun onDfuCompleted(deviceAddress: String) {
            super.onDfuCompleted(deviceAddress)
            LogUtil.d("onDfuCompleted: ")
            onDfuCompleted()
        }

        override fun onDfuAborted(deviceAddress: String) {
            super.onDfuAborted(deviceAddress)
            LogUtil.d("onDfuAborted: ")
        }

        override fun onError(
            deviceAddress: String,
            error: Int,
            errorType: Int,
            message: String?
        ) {
            super.onError(deviceAddress, error, errorType, message)
            LogUtil.d("onError: ")
            onError()
        }
    }
    DisposableEffect(Unit) {
        DfuServiceListenerHelper.registerProgressListener(
            baseApplication,
            dfuProgressListener
        )
        onDispose {
            DfuServiceListenerHelper.unregisterProgressListener(
                baseApplication,
                dfuProgressListener
            )
        }
    }

    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 32.dp, vertical = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = stringResource(id = R.string.dfu_update_dialog_title2),
                fontSize = 18.sp,
                fontWeight = FontWeight.Bold,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(20.dp))
            Text(
                text = stringResource(
                    id = R.string.dfu_update_dialog_content2,
                    deviceType
                ),
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(20.dp))
            LinearProgressIndicator(
                progress = progress / 100f,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(6.dp)
                    .padding(horizontal = 16.dp),
                color = Color(0xFF7893EC),      // 进度条颜色
                trackColor = Color(0xFF999999), // 背景轨道颜色
            )
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                text = "${progress}%",
                fontSize = 20.sp,
                color = Color(0XFF333333),
                fontWeight = FontWeight.Medium,
                modifier = Modifier.align(Alignment.CenterHorizontally)
            )
        }
    }
}

@Composable
private fun FirmwareUpgradedSuccessView(
    onConfirm: () -> Unit,
    onReconnect: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            painter = painterResource(id = R.drawable.icon_ok),
            contentDescription = null,
            modifier = Modifier.size(48.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(id = R.string.dfu_update_success),
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                color = Color(0xFF333333)
            )
        )

        Spacer(modifier = Modifier.height(8.dp))

        Text(
            text = stringResource(id = R.string.dfu_update_success_tip),
            style = TextStyle(
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                color = Color(0xFF666666)
            )
        )
        
        Spacer(modifier = Modifier.height(8.dp))
        
        Text(
            text = stringResource(id = R.string.dfu_update_need_reconnect),
            style = TextStyle(
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                color = Color(0xFF1E4BDF),
                fontWeight = FontWeight.Bold
            )
        )

        Spacer(modifier = Modifier.height(24.dp))
        // 确认并关闭按钮
        AIHOutlinedButton(
            modifier = Modifier.fillMaxWidth(0.8f),
            text = stringResource(id = R.string.confirm),
            onClick = onConfirm,
            fontSize = 16.sp,
            fontColor = Color(0XFF333333)
        )
    }
}

@Composable
private fun FirmwareUpgradedErrorView(
    onRetry: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            painter = painterResource(id = R.drawable.icon_error),
            contentDescription = null,
            modifier = Modifier.size(48.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(id = R.string.dfu_update_failed),
            style = TextStyle(
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Center,
                color = Color(0xFF333333)
            )
        )

        Spacer(modifier = Modifier.height(24.dp))

        AIHOutlinedButton(
            modifier = Modifier.fillMaxWidth(0.8f),
            text = stringResource(id = R.string.retry_update),
            onClick = onRetry,
            fontSize = 16.sp,
            fontColor = Color(0XFF333333)
        )
    }
}