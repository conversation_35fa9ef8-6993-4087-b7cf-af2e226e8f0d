package org.aihealth.ineck.view.directions

import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen

class HistoryIndexStatusDirections {
    data class HistoryIndexStatusArgs(
        val model: HistoryIndexStatusModel
    )
    @Parcelize
    data class  HistoryIndexStatusModel(
        val startTime: String = "",
        val endTime: String = "",
        val isPromis: Boolean = false
    ): Parcelable

    companion object{
        val route = "${Screen.HistoryIndexStatus.route}?model={model}"
        val gson = Gson()
        val argumentsList:MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model"){
                    type = object : NavType<HistoryIndexStatusModel>(false){
                        override val name: String
                            get() = "HistoryIndexStatusModel"

                        override fun get(bundle: Bundle, key: String): HistoryIndexStatusModel? {
                            return bundle.getParcelable(key)
                        }

                        override fun parseValue(value: String): HistoryIndexStatusModel {
                            return gson.fromJson(value, object : TypeToken<HistoryIndexStatusModel>(){}.type)
                        }

                        override fun put(
                            bundle: Bundle,
                            key: String,
                            value: HistoryIndexStatusModel
                        ) {
                            bundle.putParcelable(key,value)
                        }

                    }
                }
            )
        fun parseArguments(backStackEntry: androidx.navigation.NavBackStackEntry): HistoryIndexStatusArgs {
            return HistoryIndexStatusArgs(
                model = backStackEntry.arguments?.getParcelable<HistoryIndexStatusModel>("model")!!
            )
        }
        fun actionToHistoryIndexStatus(model: HistoryIndexStatusModel):String{
            return Screen.HistoryIndexStatus.route+"?model=${android.net.Uri.encode(gson.toJson(model))}"
        }
    }
}