package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.improvement.ImprovementProgramsLoadState
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.ImprovementDetailDirections
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardFailureContent
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardSuccessContentWithVipEn
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardSuccessContentWithVipZh
import org.aihealth.ineck.view.screen.exercise.UserExerciseCardLoadingContent
import org.aihealth.ineck.viewmodel.MainViewModel
import java.util.Locale

@Composable
fun QuestionnaireReportScreen(
    type: DeviceType = DeviceType.aiNeck,
    mainViewModel: MainViewModel,
    onBack: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize16 = with(density) { 16.sp / fontScale }
    LaunchedEffect(Unit) {
        when (type) {
            DeviceType.aiNeck -> mainViewModel.questionnaireReportScreen.getCervicalVertebraeResult()
            DeviceType.aiNeckCV -> mainViewModel.questionnaireReportScreen.getCervicalVertebraeResult()
            DeviceType.aiBack -> mainViewModel.questionnaireReportScreen.getLumbarVertebraeResult()
            DeviceType.aiBackCV -> mainViewModel.questionnaireReportScreen.getLumbarVertebraeResult()
            else -> {}
        }
    }
    val improvementRecommendProgramDataState =
        mainViewModel.improvementScreen.improvementDataState.collectAsState()

    // 根据 type 设置 title
    val title = when (type) {
        DeviceType.aiNeck -> stringResource(id = R.string.neck_health_assessment)
        DeviceType.aiNeckCV -> stringResource(id = R.string.neck_health_assessment)
        DeviceType.aiBack -> stringResource(id = R.string.back_health_assessment)
        DeviceType.aiBackCV -> stringResource(id = R.string.back_health_assessment)
        else -> {
            "None"
        }
    }

    BasePageView(
        showBackIcon = true,
        headerContent = {
            Text(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth(0.8f),
                text = title,
                style = TextStyle(
                    fontSize = 18.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF141D26),
                    textAlign = TextAlign.Center
                )
            )
            Text(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 5.dp)
                    .clickable {
                        onBack()
                    },
                text = stringResource(id = R.string.re_evaluation),
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFFFFFFF),
                )
            )
        },
        background = {
            Column {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(250.dp)
                        .background(
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color(0XFF3654CD),
                                    Color(0XFF3F6CE3),
                                    Color(0XFF789AF7)
                                ),
                                start = Offset(0f, 0f),
                                end = Offset(0f, Float.POSITIVE_INFINITY)
                            )
                        )
                )
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color(0xFFF8F8F8))
                )
            }

        }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
        ) {
            Row(
                modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = R.string.evaluation_completed),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    )
                )
                Text(
                    text = mainViewModel.questionnaireReportScreen.nowDate,
                    style = TextStyle(
                        fontSize = 12.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
            Spacer(modifier = Modifier.height(12.dp))
            QuestionnaireReportCard(
                modifier = Modifier.padding(horizontal = 12.dp),
                mainViewModel.questionnaireReportScreen.cervicalVertebraeResult.frequency.toString(),
                mainViewModel.questionnaireReportScreen.cervicalVertebraeResult.duration,
                mainViewModel.questionnaireReportScreen.cervicalVertebraeResult.healthStatus,
            )
            Spacer(modifier = Modifier.height(12.dp))
//            QuestionnaireNeckDetectionReport(
//                modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
//            )
//            QuestionnaireReportPlan(
//                modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
//                planTitle = stringResource(id = R.string.evaluation_recommendation_plan),
//                planText = stringResource(id = R.string.evaluation_recommendation_plan_txt),
//            ) {
//                RecommendPlan(
//                    modifier = Modifier,
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0xFF37D6C7),
//                            Color(0xFF37BBAE)
//                        )
//                    ),
//                    planText = stringResource(id = R.string.seven_day_improvement_plan),
//                )
//
//                RecommendPlan(
//                    modifier = Modifier,
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0xFF7290F1),
//                            Color(0xFF4066E5)
//                        )
//                    ),
//                    planText = stringResource(id = R.string.thirty_day_improvement_plan),
//                )
//
//                RecommendPlan(
//                    modifier = Modifier,
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0xFF7290F1),
//                            Color(0xFF4066E5)
//                        )
//                    ),
//                    planText = stringResource(id = R.string.ninety_day_improvement_plan),
//                )
//            }
//            QuestionnaireReportPlan(
//                modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp),
//                planTitle = stringResource(id = R.string.custom_plan),
//                planText = stringResource(id = R.string.custom_plan_txt),
//            ) {
//                SelfPlan(
//                    modifier = Modifier,
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0xFFF4F4F4),
//                            Color(0xFFFCFCFC)
//                        )
//                    ),
//                    planTitle = stringResource(id = R.string.sp_day, 1),
//                    planText = stringResource(id = R.string.wake_up_neck_rotation),
//                )
//
//                SelfPlan(
//                    modifier = Modifier,
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0xFFF4F4F4),
//                            Color(0xFFFCFCFC)
//                        )
//                    ),
//                    planTitle = stringResource(id = R.string.sp_day, 2),
//                    planText = stringResource(id = R.string.enhance_neck_rotation),
//                )
//
//                SelfPlan(
//                    modifier = Modifier,
//                    brush = Brush.linearGradient(
//                        colors = listOf(
//                            Color(0xFFF4F4F4),
//                            Color(0xFFFCFCFC)
//                        )
//                    ),
//                    planTitle = stringResource(id = R.string.sp_day, 3),
//                    planText = stringResource(id = R.string.fire_neck_rotation),
//                )
//            }

            LaunchedEffect(Unit) {
                mainViewModel.improvementScreen.loadImprovementProgramsData(
                    loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                )
            }
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 8.dp)
                    .background(
                        Color.White,
                        RoundedCornerShape(12.dp)
                    ),
            ) {

                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 12.dp, top = 4.dp),
                    text = stringResource(id = R.string.improvement_plans),
                    style = TextStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF333333),
                    ),
                    textAlign = TextAlign.Start
                )
                when (improvementRecommendProgramDataState.value) {
                    is ImprovementProgramsLoadState.InitLoading -> {
                        /* 初次加载，先从本地获取数据， 再请求服务端数据进行更新 */
                        repeat(3) { count ->
                            UserExerciseCardLoadingContent(count = count)
                        }
                    }

                    is ImprovementProgramsLoadState.Loading -> {
                        /* 非初次加载，直接从服务器发送请求 */
                        repeat(3) { count ->
                            UserExerciseCardLoadingContent(count = count)
                        }
                    }

                    is ImprovementProgramsLoadState.Success -> {
                        val data =
                            (improvementRecommendProgramDataState.value as ImprovementProgramsLoadState.Success).improvementProgramsData
                        if (currentLocale == Locale.CHINESE) {
                            ImprovementProgramCardSuccessContentWithVipZh(
                                programsData = data,
                                onclick = { it ->
                                    startScreen(
                                        route = ImprovementDetailDirections.actionToImprovementDetailCompose(
                                            model = it
                                        ),
                                        finish = false
                                    )
                                }
                            )
                        } else {
                            ImprovementProgramCardSuccessContentWithVipEn(
                                programsData = data,
                                onclick = { it ->
                                    startScreen(
                                        route = ImprovementDetailDirections.actionToImprovementDetailCompose(
                                            model = it
                                        ),
                                        finish = false
                                    )
                                }
                            )
                        }

                    }

                    is ImprovementProgramsLoadState.Failure -> {
                        LogUtil.d("ImprovementProgramsLoadState Failure")
                        ImprovementProgramCardFailureContent {
                            mainViewModel.improvementScreen.loadImprovementProgramsData(
                                loadType = mainViewModel.improvementScreen.currentImprovementCardRecommendType.value
                            )
                        }
                    }
                }
            }


        }
    }
}

/**
 * 本次测评总揽
 */
@Preview()
@Composable
fun QuestionnaireReportCard(
    modifier: Modifier = Modifier,
    evaluationCount: String = "1",
    durationTime: String = "1",
    healthStatus: String = "健康",
) {
    val density = LocalDensity.current
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    val fontSize10 = with(density) { 10.sp / fontScale }
    val status: Int = when (healthStatus) {
        "健康" -> {
            0
        }

        "亚健康" -> {
            1
        }

        "需要警惕" -> {
            2
        }

        "轻度受损" -> {
            3
        }

        "严重受损" -> {
            4
        }

        else -> {
            -1
        }
    }
    val health = when (status) {
        0 -> {
            R.string.health
        }

        1 -> {
            R.string.subclinical
        }

        2 -> {
            R.string.needs_caution
        }

        3 -> {
            R.string.light_loss
        }

        4 -> {
            R.string.serious_loss
        }

        else -> {
            -1
        }
    }
    Card(
        modifier = modifier
            .fillMaxSize()
            .background(
                color = Color(0xFFFFFFFF),
                shape = RoundedCornerShape(size = 12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFFFFF),
            contentColor = Color(0xFF333333),
        ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Row(
                modifier = Modifier
                    .padding(top = 4.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround,
            ) {
                Column(
                    modifier = Modifier
                        .width(90.dp)
                        .height(56.dp)
                        .background(
                            color = Color(0xFFF4F4F4),
                            shape = RoundedCornerShape(size = 8.dp)
                        ),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.evaluation_count),
                        style = TextStyle(
                            fontSize = fontSize12,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Spacer(
                        modifier = Modifier
                            .height(4.dp)
                            .fillMaxWidth()
                    )
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = evaluationCount,
                        style = TextStyle(
                            fontSize = fontSize12,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF2B56D7),
                        ),
                        textAlign = TextAlign.Center
                    )

                }
                Column(
                    modifier = Modifier
                        .width(90.dp)
                        .height(56.dp)
                        .background(
                            color = Color(0xFFF4F4F4),
                            shape = RoundedCornerShape(size = 8.dp)
                        ),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.time),
                        style = TextStyle(
                            fontSize = fontSize12,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Spacer(
                        modifier = Modifier
                            .height(4.dp)
                            .fillMaxWidth()
                    )
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = durationTime,
                        style = TextStyle(
                            fontSize = fontSize12,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF2B56D7),
                        ),
                        textAlign = TextAlign.Center
                    )

                }
                Column(
                    modifier = Modifier
                        .width(90.dp)
                        .height(56.dp)
                        .background(
                            color = Color(0xFFF4F4F4),
                            shape = RoundedCornerShape(size = 8.dp)
                        ),
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = stringResource(id = R.string.health_status),
                        style = TextStyle(
                            fontSize = fontSize12,
                            fontWeight = FontWeight(400),
                            color = Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Spacer(
                        modifier = Modifier
                            .height(4.dp)
                            .fillMaxWidth()
                    )
                    Text(
                        modifier = Modifier.fillMaxWidth(),
                        text = if (health == -1) "None" else stringResource(id = health),
                        style = TextStyle(
                            fontSize = fontSize14,
                            fontWeight = FontWeight(400),
                            color = Color(0xFFE5B716),
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }
            Text(
                modifier = Modifier
                    .padding(top = 8.dp)
                    .fillMaxWidth(),
                text = stringResource(id = R.string.current_health_status),
                style = TextStyle(
                    fontSize = fontSize12,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF3B58CE),
                ),
                textAlign = TextAlign.Center,
            )
            Column(
                modifier = Modifier
                    .padding(top = 10.dp)
                    .width(265.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                var parentSize by remember { mutableStateOf(IntSize.Zero) }
                val childSize = parentSize.width.toFloat() / 5
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .onGloballyPositioned { coordinates ->
                            parentSize = coordinates.size
                        },
                    horizontalArrangement = Arrangement.SpaceAround,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = if (status == 0) R.drawable.icon_health_selected else R.drawable.icon_health),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 0) 18.dp else 20.dp)
                    )
                    Image(
                        painter = painterResource(id = if (status == 1) R.drawable.icon_poor_health_selected else R.drawable.icon_poor_health),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 1) 18.dp else 20.dp)

                    )
                    Image(
                        painter = painterResource(id = if (status == 2) R.drawable.icon_vigilant_health_selected else R.drawable.icon_vigilant_health),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 2) 18.dp else 20.dp)

                    )
                    Image(
                        painter = painterResource(id = if (status == 3) R.drawable.icon_poor_lose_selected else R.drawable.icon_poor_lose),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 3) 18.dp else 20.dp)

                    )
                    Image(
                        painter = painterResource(id = if (status == 4) R.drawable.icon_serious_lose_selected else R.drawable.icon_serious_lose),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit,
                        modifier = Modifier.size(if (status == 4) 18.dp else 20.dp)
                    )
                }
                Spacer(
                    modifier = Modifier
                        .height(8.dp)
                        .fillMaxWidth()
                )

                Box(
                    modifier = Modifier
                        .padding(top = 5.dp)
                        .fillMaxWidth(),
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_health_localtion),
                        contentDescription = "location",
                        modifier = Modifier
                            .padding(start = with(density) { (status * childSize + childSize / 2).toDp() })
                    )

                }

                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(7.dp)
                        .background(
                            shape = RoundedCornerShape(4.dp),
                            brush = Brush.linearGradient(
                                colors = listOf(
                                    Color(0xFF6EA944),
                                    Color(0xFFA0E14F),
                                    Color(0xFFD5ED28),
                                    Color(0xFFF1C114),
                                    Color(0xFFF17216),
                                    Color(0xFFFB593F)
                                ),
                            )
                        )
                )

                Canvas(
                    modifier = Modifier
                        .padding(top = 5.dp)
                        .fillMaxWidth()
                        .height(10.dp)
                ) {
                    val path1 = Path().apply {
                        moveTo(childSize, 0.664062f)
                        lineTo(childSize, 6.16406f)
                        lineTo(1f, 6.16406f)
                        lineTo(1f, 0.164062f)
                    }
                    drawPath(path1, Color(0xFF93CA65), style = Stroke(width = 2f))
                    val path2 = Path().apply {
                        moveTo(2 * childSize, 0.664062f)
                        lineTo(2 * childSize, 6.16406f)
                        lineTo(childSize, 6.16406f)
                    }
                    drawPath(path2, Color(0xFFBFE951), style = Stroke(width = 2f))
                    val path3 = Path().apply {
                        moveTo(3 * childSize, 0.664062f)
                        lineTo(3 * childSize, 6.16406f)
                        lineTo(2 * childSize, 6.16406f)
                    }
                    drawPath(path3, Color(0xFFDEE73A), style = Stroke(width = 2f))
                    val path4 = Path().apply {
                        moveTo(4 * childSize, 0.664062f)
                        lineTo(4 * childSize, 6.16406f)
                        lineTo(3 * childSize, 6.16406f)
                    }
                    drawPath(path4, Color(0xFFF2C72C), style = Stroke(width = 2f))
                    val path5 = Path().apply {
                        moveTo(5 * childSize - 2, 0.664062f)
                        lineTo(5 * childSize - 2, 6.16406f)
                        lineTo(4 * childSize, 6.16406f)
                    }
                    drawPath(path5, Color(0xFFF37E30), style = Stroke(width = 2f))

                }

                Row(
                    modifier = Modifier
                        .padding(bottom = 5.dp)
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceAround,
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Text(
                        modifier = Modifier
                            .width(with(density) { childSize.toDp() }),
                        text = stringResource(id = R.string.health),
                        style = TextStyle(
                            fontSize = fontSize10,
                            fontWeight = FontWeight(400),
                            color = if (status == 0) Color(0xFF93CA65) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(with(density) { childSize.toDp() }),
                        text = stringResource(id = R.string.subclinical),
                        style = TextStyle(
                            fontSize = fontSize10,
                            fontWeight = FontWeight(400),
                            color = if (status == 1) Color(0xFFBFE951) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(with(density) { childSize.toDp() }),
                        text = stringResource(id = R.string.needs_caution),
                        style = TextStyle(
                            fontSize = fontSize12,
                            fontWeight = FontWeight(400),
                            color = if (status == 2) Color(0xFFDEE73A) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(with(density) { childSize.toDp() }),
                        text = stringResource(id = R.string.light_loss),
                        style = TextStyle(
                            fontSize = fontSize10,
                            fontWeight = FontWeight(400),
                            color = if (status == 3) Color(0xFFF2C72C) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                    Text(
                        modifier = Modifier
                            .width(with(density) { childSize.toDp() }),
                        text = stringResource(id = R.string.serious_loss),
                        style = TextStyle(
                            fontSize = fontSize10,
                            fontWeight = FontWeight(400),
                            color = if (status == 4) Color(0xFFF37E30) else Color(0xFF666666),
                        ),
                        textAlign = TextAlign.Center
                    )
                }
            }


        }

    }

}

@Preview()
@Composable
fun QuestionnaireNeckDetectionReport(
    modifier: Modifier = Modifier,
    type: DeviceType = DeviceType.aiNeck,
    onClick: () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize16 = with(density) { 16.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    Card(
        modifier = modifier
            .fillMaxSize()
            .background(
                color = Color(0xFFFFFFFF),
                shape = RoundedCornerShape(size = 12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFFFFF),
            contentColor = Color(0xFF333333),
        )
    ) {
        Column(
            modifier = Modifier
                .padding(start = 12.dp, top = 22.dp, end = 9.dp)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth(),
                text = stringResource(id = R.string.questionnaire_auto),
                style = TextStyle(
                    fontSize = fontSize16,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF333333),
                ),
                textAlign = TextAlign.Start
            )
            val text = when (type) {
                DeviceType.aiNeck -> stringResource(id = R.string.questionnaire_neck_report_txt)
                DeviceType.aiNeckCV -> stringResource(id = R.string.questionnaire_neck_report_txt)
                DeviceType.aiBack -> stringResource(id = R.string.questionnaire_back_report_txt)
                DeviceType.aiBackCV -> stringResource(id = R.string.questionnaire_back_report_txt)
                else -> ""
            }
            Text(
                modifier = Modifier
                    .padding(start = 3.dp, top = 4.dp)
                    .fillMaxWidth(),
                text = text,
                style = TextStyle(
                    fontSize = fontSize14,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF444444),
                    textAlign = TextAlign.Justify,
                ),
                textAlign = TextAlign.Start
            )
            Button(
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFEDF1FF)
                ),
                modifier = Modifier.fillMaxWidth(0.8f),
                shape = RoundedCornerShape(size = 16.dp),
                onClick = {
                    onClick()
                }
            ) {
                Text(
                    text = stringResource(id = R.string.view_monitoring_data),
                    style = TextStyle(
                        fontSize = fontSize16,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF3C59CE),
                    ),
                )
            }

        }

    }
}

@Preview()
@Composable
fun QuestionnaireReportPlan(
    modifier: Modifier = Modifier,
    planTitle: String = "改善计划",
    planText: String = "根据您的检测结果，推荐您按照以下内容进行改善",
    content: @Composable () -> Unit = {}
) {
    val density = LocalDensity.current
    val fontSize16 = with(density) { 16.sp / fontScale }
    val fontSize14 = with(density) { 14.sp / fontScale }
    Card(
        modifier = modifier
            .fillMaxWidth()
            .background(
                color = Color(0xFFFFFFFF),
                shape = RoundedCornerShape(size = 12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFFFFFFF),
            contentColor = Color(0xFF333333),
        )
    ) {
        Column(
            modifier = Modifier
                .padding(start = 12.dp, top = 22.dp, end = 9.dp)
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth(),
                text = planTitle,
                style = TextStyle(
                    fontSize = fontSize16,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF333333),
                )
            )
            Text(
                modifier = Modifier
                    .padding(start = 3.dp, top = 4.dp)
                    .fillMaxWidth(),
                text = planText,
                style = TextStyle(
                    fontSize = fontSize14,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                )
            )
            Row(
                modifier = Modifier
                    .padding(top = 11.dp, bottom = 11.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceAround,
            ) {
                content()
            }
        }

    }
}

@Composable
fun RecommendPlan(
    modifier: Modifier = Modifier,
    brush: Brush,
    planText: String,
) {
    val density = LocalDensity.current
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize8 = with(density) { 8.sp / fontScale }
    Column(
        modifier = modifier
            .background(
                shape = RoundedCornerShape(size = 4.dp),
                brush = brush,
            )
            .width(102.dp),
        verticalArrangement = Arrangement.Center,
    ) {
        Text(
            text = planText,
            style = TextStyle(
                fontSize = fontSize12,
                fontWeight = FontWeight(400),
                color = Color(0xFFFFFFFF),
            ),
            textAlign = TextAlign.Start,
            modifier = Modifier
                .padding(horizontal = 7.dp)
                .fillMaxWidth()
        )
        Row(
            modifier = Modifier
                .padding(start = 7.dp, top = 8.dp, bottom = 5.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Text(
                text = stringResource(id = R.string.add),
                style = TextStyle(
                    fontSize = fontSize8,
                    fontWeight = FontWeight(400),
                    color = Color(0xFFFFFFFF),
                ),
                textAlign = TextAlign.Start
            )
            Image(
                modifier = Modifier.padding(start = 5.dp, top = 1.dp),
                painter = painterResource(id = R.drawable.icon_next),
                contentDescription = "next",
            )
        }
    }
}


@Composable
fun SelfPlan(
    modifier: Modifier = Modifier,
    brush: Brush,
    planTitle: String,
    planText: String
) {
    val density = LocalDensity.current
    val fontSize12 = with(density) { 12.sp / fontScale }
    val fontSize8 = with(density) { 8.sp / fontScale }

    Column(
        modifier = modifier
            .background(
                shape = RoundedCornerShape(size = 4.dp),
                brush = brush,
            )
            .width(102.dp)
            .height(64.dp),
        verticalArrangement = Arrangement.Center,
    ) {

        Text(
            text = planTitle,
            style = TextStyle(
                fontSize = fontSize12,
                fontWeight = FontWeight(400),
                color = Color(0xFF333333),
            ),
            textAlign = TextAlign.Start,
            modifier = Modifier
                .padding(horizontal = 7.dp)
                .fillMaxWidth()
        )
        Text(
            text = planText,
            style = TextStyle(
                fontSize = fontSize8,
                fontWeight = FontWeight(400),
                color = Color(0xFF666666),
            ),
            textAlign = TextAlign.Start,
            modifier = Modifier
                .padding(horizontal = 7.dp)
                .fillMaxWidth()

        )
    }
}