package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.angles.Promis
import org.aihealth.ineck.network.NetWork.enqueueBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHSliderState
import org.aihealth.ineck.view.screen.PromisSelectedView
import org.aihealth.ineck.view.screen.Section

@Preview(showBackground = true)
@Composable
fun MeasureCollectionScreenPromis(
    modifier: Modifier = Modifier,
    onStart: () -> Unit = {},
    onSkip: () -> Unit = {},
    enable: Boolean = false,
    onDismissRequest: () -> Unit = {}
) {
    val promisValueList = remember {
        mutableStateListOf<AIHSliderState>()
    }
    repeat(10) {
        promisValueList.add(AIHSliderState(1))
    }

    val promisScrollState = rememberScrollState()

    Column(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp),
            text = "PROMIS",
            style = TextStyle(
                fontSize = 20.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF444444),
                textAlign = TextAlign.Center,
            )
        )
        PromisView(promisScrollState, promisValueList, true) {
            onStart()
        }


    }
    SkipDialog(
        enable = enable,
        onDismissRequest = {
            onDismissRequest()
        },
        onConfirm = {
            onSkip()
        }
    )
}

@Composable
private fun PromisView(
    scrollState: ScrollState,
    valueList: SnapshotStateList<AIHSliderState>,
    enabled: Boolean = true,
    onClick: () -> Unit = {}
) {
    Column(
        Modifier
            .fillMaxSize()
            .padding(horizontal = 8.dp)
            .verticalScroll(scrollState)
    ) {
        Text(
            text = stringResource(id = R.string.promis_record_title),
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF64CECD),
                textAlign = TextAlign.Justify,
            ),
            modifier = Modifier.padding(top = 10.dp)
        )
        val statusArray = stringArrayResource(R.array.promis_record_option2)
        Section(id = R.string.promis_record_section1)
        PromisSelectedView(
            state = valueList[0],
            array = stringArrayResource(R.array.promis_record_option1),
            enabled = enabled
        )

        Section(id = R.string.promis_record_section2)
        PromisSelectedView(
            state = valueList[1],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section3)
        PromisSelectedView(
            state = valueList[2],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section4)
        PromisSelectedView(
            state = valueList[3],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section5)
        PromisSelectedView(
            state = valueList[4],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section6)
        PromisSelectedView(
            state = valueList[5],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section7)
        PromisSelectedView(
            state = valueList[6],
            array = statusArray,
            enabled = enabled
        )

        Section(id = R.string.promis_record_section8)
        PromisSelectedView(
            state = valueList[7],
            array = stringArrayResource(R.array.promis_record_option3),
            enabled = enabled
        )

        Section(id = R.string.promis_record_section9)
        PromisSelectedView(
            state = valueList[8],
            array = stringArrayResource(R.array.promis_record_option4),
            enabled = enabled
        )

        Section(id = R.string.promis_record_section10)
        PromisSelectedView(
            state = valueList[9],
            array = stringArrayResource(R.array.promis_record_option5),
            enabled = enabled
        )
        Spacer(modifier = Modifier.height(36.dp))
        if (enabled) {
            AIHButton(
                text = stringResource(id = R.string.submit),
                onClick = {
                    apiService.postPromis(
                        promis = Promis(
                            average_pain = valueList[0].value,
                            health = valueList[1].value,
                            life = valueList[2].value,
                            physical = valueList[3].value,
                            mental = valueList[4].value,
                            social = valueList[5].value,
                            behave = valueList[6].value,
                            sport = valueList[7].value,
                            sentiment = valueList[8].value,
                            fatigue = valueList[9].value
                        )
                    ).enqueueBody {
                        onClick()
                    }
                },
                fontSize = 20.sp,
                fontColor = Color.White,
                modifier = Modifier
                    .padding(horizontal = 12.dp)
                    .fillMaxWidth()
            )
            Spacer(modifier = Modifier.height(30.dp))
        }
    }
}



