package org.aihealth.ineck.view.screen.aijoint

import androidx.annotation.DrawableRes
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.OutlinedButton
import androidx.compose.material3.Text
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.directions.MatchingDeviceDirections
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun AiJointDetailScreen(
    viewModel: MainViewModel
) {
    val kneeState by viewModel.deviceScreen.kneeDeviceState.collectAsState()
    val elbowState by viewModel.deviceScreen.elbowDeviceState.collectAsState()
    val hipState by viewModel.deviceScreen.hipDeviceState.collectAsState()
    val shoulderState by viewModel.deviceScreen.shoulderDeviceState.collectAsState()
    BasePageView(
        showBackIcon = true,
        title = stringResource(id = R.string.joint_connect),
        modifier = Modifier.fillMaxSize()
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
                .verticalScroll(rememberScrollState()),
        ) {
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp),
                text = stringResource(id = R.string.joint_connect_chosen_1),
                style = TextStyle(
                    fontSize = 24.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF444444),
                    textAlign = TextAlign.Center,
                )
            )
            Text(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp),
                text = stringResource(id = R.string.joint_connect_chosen_2),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    textAlign = TextAlign.Center,
                )
            )
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 30.dp)
            ) {
                Column(
                    modifier = Modifier.padding(top = 50.dp)
                ) {
                    AiJointItem(
                        modifier = Modifier
                            .fillMaxWidth(0.3f),
                        text = stringResource(id = R.string.joint_elbow),
                        isConnected = elbowState.isDeviceConnected ?: false,
                        onClick = {
                            viewModel.homeScreen.currentDeviceType = DeviceType.ElbowJoint
                            val model =
                                MatchingDeviceDirections.MatchingDeviceModel(deviceType = DeviceType.ElbowJoint.name)
                            startScreen(
                                route = MatchingDeviceDirections.actionToMatchingDevice(model = model),
                                finish = true
                            )
                        },
                        cancel = {
                            viewModel.deviceScreen.onDisconnectDeviceClick(DeviceType.ElbowJoint.name)
                        },
                        icon = R.drawable.elbow_joint
                    )
                    AiJointItem(
                        modifier = Modifier
                            .fillMaxWidth(0.3f),
                        text = stringResource(id = R.string.joint_knee),
                        isConnected = kneeState.isDeviceConnected ?: false,
                        onClick = {
                            viewModel.homeScreen.currentDeviceType = DeviceType.KneeJoint
                            val model =
                                MatchingDeviceDirections.MatchingDeviceModel(deviceType = DeviceType.KneeJoint.name)
                            startScreen(
                                route = MatchingDeviceDirections.actionToMatchingDevice(model = model),
                                finish = true
                            )
                        },
                        cancel = {
                            viewModel.deviceScreen.onDisconnectDeviceClick(DeviceType.KneeJoint.name)
                        },
                        icon = R.drawable.knee_joint
                    )
                }

                Image(
                    modifier = Modifier.align(Alignment.Center),
                    painter = painterResource(id = R.drawable.joint_body),
                    contentDescription = ""
                )

                Column(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                ) {
                    AiJointItem(
                        modifier = Modifier
                            .fillMaxWidth(0.3f),
                        text = stringResource(id = R.string.joint_shoulder),
                        isConnected = shoulderState.isDeviceConnected ?: false,
                        icon = R.drawable.shoulder_joint,
                        onClick = {
                            viewModel.homeScreen.currentDeviceType = DeviceType.ShoulderJoint
                            val model =
                                MatchingDeviceDirections.MatchingDeviceModel(deviceType = DeviceType.ShoulderJoint.name)
                            startScreen(
                                route = MatchingDeviceDirections.actionToMatchingDevice(model = model),
                                finish = true
                            )
                        },
                        cancel = {
                            viewModel.deviceScreen.onDisconnectDeviceClick(DeviceType.ShoulderJoint.name)
                        }

                    )
                    AiJointItem(
                        modifier = Modifier
                            .fillMaxWidth(0.3f),
                        text = stringResource(id = R.string.joint_hip),
                        isConnected = hipState.isDeviceConnected ?: false,
                        icon = R.drawable.hip_joint,
                        onClick = {
                            viewModel.homeScreen.currentDeviceType = DeviceType.HipJoint
                            val model =
                                MatchingDeviceDirections.MatchingDeviceModel(deviceType = DeviceType.HipJoint.name)
                            startScreen(
                                route = MatchingDeviceDirections.actionToMatchingDevice(model = model),
                                finish = true
                            )
                        },
                        cancel = {
                            viewModel.deviceScreen.onDisconnectDeviceClick(DeviceType.HipJoint.name)
                        }
                    )
                }
            }

        }
    }
}

@Composable
private fun AiJointItem(
    modifier: Modifier = Modifier,
    text: String = "已连接",
    @DrawableRes icon: Int = R.drawable.elbow_joint,
    isConnected: Boolean = true,
    onClick: () -> Unit = {},
    cancel: () -> Unit = {}
) {
    var size by remember { mutableStateOf(Size.Zero) }
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        if (!isConnected) {
            OutlinedButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .onGloballyPositioned { coordinates ->
                        size = Size(
                            coordinates.size.width.toFloat(),
                            coordinates.size.height.toFloat()
                        ) // Step 3: Update the state
                    },
                contentPadding = PaddingValues(0.dp),
                elevation = null,
                border = BorderStroke(
                    width = 1.dp,
                    color = Color(0xFF1E4BDF)
                ),
                onClick = onClick
            ) {
                Text(
                    text = text,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF666666),
                    )
                )
            }
        } else {
            Button(
                modifier = Modifier
                    .fillMaxWidth()
                    .onGloballyPositioned { coordinates ->
                        size = Size(
                            coordinates.size.width.toFloat(),
                            coordinates.size.height.toFloat()
                        ) // Step 3: Update the state
                    },
                colors = ButtonColors(
                    contentColor = Color.Red,
                    containerColor = Color.Red,
                    disabledContentColor = Color.Transparent,
                    disabledContainerColor = Color.Transparent,
                ),
                contentPadding = PaddingValues(0.dp),
                elevation = null,
                onClick = cancel
            ) {
                Text(
                    text = stringResource(id = R.string.disconnect),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color.White,
                    )
                )
            }
        }

        VerticalDivider(
            modifier = Modifier
                .height(convertPixelsToDp(size.width / 4)),
            color = Color(0xFF82B7CE),
            thickness = 1.dp
        )

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1f)
                .clip(CircleShape)
                .border(
                    width = 1.dp,
                    color = Color(0xFF1E4BDF),
                    shape = CircleShape
                )
        ) {
            Image(
                modifier = Modifier
                    .align(Alignment.Center)
                    .fillMaxWidth()
                    .background(Color.Transparent),
                painter = painterResource(id = icon),
                contentDescription = "",
                contentScale = ContentScale.Fit
            )
        }
    }
}

@Composable
fun convertPixelsToDp(pixels: Float): Dp {
    val density = LocalDensity.current
    return with(density) {
        pixels.toDp()
    }
}
//
//@Preview(showBackground = true)
//@Composable
//private fun AiJointPreview() {
//    AiJointDetailScreen()
//}