package org.aihealth.ineck.view.directions

import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen

class HistoryPainDirections {
    data class HistoryPainArgs(
        val model: HistoryPainModel
    )
    @Parcelize
    data class HistoryPainModel(
        val painTypeNumberState : Int = 1
    ):Parcelable

    companion object{
        val route = "${Screen.HistoryPain.route}?model={model}"
        val gson = Gson()
        val argumentsList:MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model"){
                    type = object : NavType<HistoryPainModel>(false){
                        override val name: String
                            get() = "HistoryPainModel"
                        override fun get(bundle: Bundle, key: String): HistoryPainModel? {
                            return bundle.getParcelable(key)
                        }

                        override fun parseValue(value: String): HistoryPainModel {
                            return gson.fromJson(value, object : TypeToken<HistoryPainModel>(){}.type)
                        }

                        override fun put(bundle: Bundle, key: String, value: HistoryPainModel) {
                            bundle.putParcelable(key,value)
                        }

                    }
                }
            )
        fun parseArguments(backStackEntry: androidx.navigation.NavBackStackEntry): HistoryPainArgs {
            return HistoryPainArgs(
                model = backStackEntry.arguments?.getParcelable<HistoryPainModel>("model")!!
            )
        }
        fun actionToHistoryPain(model: HistoryPainModel):String{
            return Screen.HistoryPain.route+"?model=${android.net.Uri.encode(gson.toJson(model))}"
        }
    }

}