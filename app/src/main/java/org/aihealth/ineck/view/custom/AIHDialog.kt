package org.aihealth.ineck.view.custom

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Outline
import android.view.*
import androidx.activity.ComponentDialog
import androidx.activity.addCallback
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.R
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.AbstractComposeView
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import androidx.lifecycle.*
import androidx.savedstate.findViewTreeSavedStateRegistryOwner
import androidx.savedstate.setViewTreeSavedStateRegistryOwner
import java.util.*

@Composable
fun AIHDialog(
    onDismissRequest: () -> Unit,
    alignment: Alignment = Alignment.Center,
    dismissOnBackPress: Boolean = true,
    dismissOnClickOutside: Boolean = true,
    scrimColor: Color = Color(0X50000000),
    content: @Composable () -> Unit
) {
    val view = LocalView.current
    val composition = rememberCompositionContext()
    val currentContent by rememberUpdatedState(content)
    val dialogId = rememberSaveable { UUID.randomUUID() }

    val dialog = remember(view) {
        AIHDialogWrapper(
            onDismissRequest,
            dismissOnBackPress,
            view,
            dialogId
        ).apply {
            setContent(composition) {
                val onClickOutsideModifier = if (dismissOnClickOutside) {
                    Modifier.pointerInput(Unit) {
                        detectTapGestures {
                            onDismissRequest()
                        }
                    }
                } else {
                    Modifier
                }
                Box(modifier = Modifier
                    .fillMaxSize()
                    .background(scrimColor)
                    .then(onClickOutsideModifier), contentAlignment = alignment) {
                    Box(modifier = Modifier
                        .pointerInput(Unit) { detectTapGestures { } }) {
                        currentContent()
                    }
                }
            }
        }
    }

    DisposableEffect(dialog) {
        dialog.show()

        onDispose {
            dialog.dismiss()
            dialog.disposeComposition()
        }
    }
}


@Suppress("ViewConstructor")
private class AIHDialogLayout(
    context: Context,
    val window: Window
) : AbstractComposeView(context) {

    private var content: @Composable () -> Unit by mutableStateOf({})

    override var shouldCreateCompositionOnAttachedToWindow: Boolean = false
        private set

    fun setContent(parent: CompositionContext, content: @Composable () -> Unit) {
        setParentCompositionContext(parent)
        this.content = content
        shouldCreateCompositionOnAttachedToWindow = true
        createComposition()
    }

    @Composable
    override fun Content() {
        content()
    }
}

@SuppressLint("PrivateResource")
private class AIHDialogWrapper(
    private var onDismissRequest: () -> Unit,
    dismissOnBackPress: Boolean,
    private val composeView: View,
    bottomDialogId: UUID
) : ComponentDialog(
    ContextThemeWrapper(composeView.context, R.style.FloatingDialogWindowTheme)
) {
    private val dialogLayout: AIHDialogLayout
    private val defaultSoftInputMode: Int

    init {
        val window = window ?: error("Dialog has no window")
        defaultSoftInputMode =
            window.attributes.softInputMode and WindowManager.LayoutParams.SOFT_INPUT_MASK_ADJUST
        window.requestFeature(Window.FEATURE_NO_TITLE)
        window.setBackgroundDrawableResource(android.R.color.transparent)
        WindowCompat.setDecorFitsSystemWindows(window, false)
        dialogLayout = AIHDialogLayout(context, window).apply {
            setTag(R.id.compose_view_saveable_id_tag, "Dialog:$bottomDialogId")
            clipChildren = false
            outlineProvider = object : ViewOutlineProvider() {
                override fun getOutline(view: View, result: Outline) {
                    result.setRect(0, 0, view.width, view.height)
                    result.alpha = 0f
                }
            }
        }

        fun ViewGroup.disableClipping() {
            clipChildren = false
            if (this is AIHDialogLayout) return
            for (i in 0 until childCount) {
                (getChildAt(i) as? ViewGroup)?.disableClipping()
            }
        }

        // Turn of all clipping so shadows can be drawn outside the window
        (window.decorView as? ViewGroup)?.disableClipping()
        setContentView(dialogLayout)
        //https://androidx.tech/artifacts/compose.ui/ui/1.4.0-source/androidx/compose/ui/window/AndroidDialog.android.kt.html
        //https://androidx.tech/artifacts/compose.ui/ui/1.3.0-rc01-source/androidx/compose/ui/window/AndroidDialog.android.kt.html
        dialogLayout.setViewTreeSavedStateRegistryOwner(composeView.findViewTreeSavedStateRegistryOwner())
//        ViewTreeLifecycleOwner.set(dialogLayout, ViewTreeLifecycleOwner.get(composeView))
        dialogLayout.setViewTreeViewModelStoreOwner(composeView.findViewTreeViewModelStoreOwner())
        dialogLayout.setViewTreeSavedStateRegistryOwner(
            composeView.findViewTreeSavedStateRegistryOwner()
        )
//        ViewTreeViewModelStoreOwner.set(
//            dialogLayout,
//            ViewTreeViewModelStoreOwner.get(composeView)
//        )
//        dialogLayout.setViewTreeLifecycleOwner(composeView.findViewTreeLifecycleOwner())
//        dialogLayout.setViewTreeViewModelStoreOwner(composeView.findViewTreeViewModelStoreOwner())
        dialogLayout.setViewTreeSavedStateRegistryOwner(
            composeView.findViewTreeSavedStateRegistryOwner()
        )
        onBackPressedDispatcher.addCallback(this) {
            if (dismissOnBackPress) {
                onDismissRequest()
            }
        }
    }

    // TODO(b/159900354): Make the Android Dialog full screen and the scrim fully transparent

    fun setContent(parentComposition: CompositionContext, children: @Composable () -> Unit) {
        dialogLayout.setContent(parentComposition, children)
    }

    fun disposeComposition() {
        dialogLayout.disposeComposition()
    }

    override fun cancel() {
        // Prevents the dialog from dismissing itself
        return
    }
}