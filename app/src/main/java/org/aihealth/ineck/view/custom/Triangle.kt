package org.aihealth.ineck.view.custom

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.unit.dp

@Composable
fun Triangle(
    modifier: Modifier = Modifier,
    color: Color = Color.White,

) {
    Canvas(modifier = Modifier.then(modifier).size(14.dp,8.dp)) {
        val path = Path()
        path.moveTo(0F,size.height)
        path.lineTo(size.width,size.height)
        path.lineTo(size.width / 2 , 0F)
        drawPath(
            color = color,
            path = path
        )
        path.close()
    }
}
