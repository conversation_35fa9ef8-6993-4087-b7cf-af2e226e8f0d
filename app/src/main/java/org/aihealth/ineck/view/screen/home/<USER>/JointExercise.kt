package org.aihealth.ineck.view.screen.home.aijoint

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.screen.home.HomeStatusCard

@Composable
fun KneeExercise(
    modifier: Modifier = Modifier,
    maxValueOne: Int = 10,
    valueOne: Int = 0,
    maxValueTwo: Int = 10,
    valueTwo: Int = 0,
    toStart: () -> Unit = {},
    toReport: () -> Unit = {},
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(200.dp),
    ) {
        HomeStatusCard(
            modifier = Modifier
                .align(Alignment.TopStart)
                .fillMaxWidth(0.65f),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.Start
            ) {
                Text(
                    text = stringResource(id = R.string.joint_exercise),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(600),
                        color = Color(0xFF2D2F33),
                    )
                )
            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
            ) {
                Image(
                    modifier = Modifier.align(Alignment.CenterStart),
                    painter = painterResource(id = R.drawable.knee_exercise),
                    contentDescription = ""
                )
                Column(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.End
                ) {
                    Column {
                        Text(
                            text = stringResource(id = R.string.action_one),
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF999999),
                            )
                        )
                        Row(
                            modifier = Modifier,
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.End
                        ) {
                            CustomSlider(
                                modifier = Modifier
                                    .width(100.dp)
                                    .height(11.dp),
                                value = valueOne,
                                maxValue = maxValueOne,
                            )
                            Text(
                                modifier = Modifier.padding(start = 8.dp),
                                text = "${valueOne}/${maxValueOne}",
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF999999),
                                )
                            )
                        }
                    }
                    Column {

                        Text(
                            text = stringResource(id = R.string.action_two),
                            style = TextStyle(
                                fontSize = 12.sp,
                                fontWeight = FontWeight(400),
                                color = Color(0xFF999999),
                            )
                        )
                        Row(
                            modifier = Modifier,
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.End
                        ) {
                            CustomSlider(
                                modifier = Modifier
                                    .width(100.dp)
                                    .height(11.dp),
                                color = Color(0xFFDF9754),
                                value = valueTwo,
                                maxValue = maxValueTwo,
                            )
                            Text(
                                modifier = Modifier.padding(start = 8.dp),
                                text = "${valueTwo}/${maxValueTwo}",
                                style = TextStyle(
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight(400),
                                    color = Color(0xFF999999),
                                )
                            )
                        }
                    }
                }
            }
        }
        HomeStatusCard(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .fillMaxWidth(0.34f)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize(),
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                AIHOutlinedButton(
                    modifier = Modifier
                        .fillMaxWidth(0.8f)
                        .height(30.dp),
                    text = stringResource(id = R.string.start_exercise), onClick = { toStart() }
                )
                AIHButton(
                    modifier = Modifier
                        .padding(top = 24.dp)
                        .fillMaxWidth(0.8f)
                        .height(30.dp),
                    text = stringResource(id = R.string.show_report),
                    onClick = { toReport() }
                )
            }
        }
    }

}

@Composable
fun CustomSlider(
    modifier: Modifier = Modifier,
    value: Int = 5,
    maxValue: Int = 10,
    color: Color = Color(0xFF7AAF55)
) {
    Row(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier
                .fillMaxSize()
                .background(color = Color(0xFFEFEFEF), shape = RoundedCornerShape(size = 7.dp))
        ) {
            Box(
                modifier = Modifier
                    .fillMaxHeight()
                    .fillMaxWidth((value.toFloat() / maxValue.toFloat()))
                    .background(color = color, shape = RoundedCornerShape(size = 7.dp))
            )
        }
    }
}


@Preview
@Composable
private fun Preview() {
    KneeExercise(
        modifier = Modifier.fillMaxWidth()
    )
}