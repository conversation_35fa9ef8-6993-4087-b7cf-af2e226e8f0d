package org.aihealth.ineck.view.directions

import android.net.Uri
import android.os.Bundle
import android.os.Parcelable
import androidx.navigation.NamedNavArgument
import androidx.navigation.NavBackStackEntry
import androidx.navigation.NavType
import androidx.navigation.navArgument
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import org.aihealth.ineck.Screen



class HistoryVitalSignsDirections {


    data class HistoryVitalSignsArgs(
        val model: HistoryEventModel
    )
    @Parcelize
    data class HistoryEventModel(
        val vitalSign: Int,
        val isCelsius: Boolean,
        val isMmolL: Boolean
    ):Parcelable
    companion object {
        val route = Screen.HistoryVitalSigns.route+"?model={model}"
        val gson = Gson()
        val argumentsList:MutableList<NamedNavArgument>
            get() = mutableListOf(
                navArgument("model"){
                    type = object : NavType<HistoryEventModel>(false){
                        override val name: String
                            get() = "HistoryEventModel"
                        override fun get(bundle: Bundle, key: String): HistoryEventModel? {
                            return bundle.getParcelable(key)
                        }

                        override fun parseValue(value: String): HistoryEventModel {
                           return gson.fromJson(value, object : TypeToken<HistoryEventModel>(){}.type)
                        }

                        override fun put(bundle: Bundle, key: String, value: HistoryEventModel) {
                            bundle.putParcelable(key,value)
                        }

                    }
                }
            )
        fun parseArguments(backStackEntry: NavBackStackEntry): HistoryVitalSignsArgs {
          return HistoryVitalSignsArgs(
              model = backStackEntry.arguments?.getParcelable<HistoryEventModel>("model")!!
          )
        }
        fun actionToHistoryVitalCompose(model: HistoryEventModel):String{
            return Screen.HistoryVitalSigns.route+"?model=${Uri.encode(gson.toJson(model))}"
        }
    }

}