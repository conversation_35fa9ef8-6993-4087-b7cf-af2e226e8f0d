package org.aihealth.ineck.view.screen.info

import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.DatePicker
import androidx.compose.material3.DatePickerDefaults
import androidx.compose.material3.DatePickerDialog
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberDatePickerState
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHTextButton
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone


/**
 * 编辑出生日期
 */
@Preview(showBackground = true)
@Composable
fun EditBirthdate(
    modifier: Modifier = Modifier,
    birthdate: String = "",
    onBirthdateChange: (Long) -> Unit = { _ -> }
) {
    val context = LocalContext.current
    var showDialog by remember { mutableStateOf(false) }
    val interactionSource = remember { MutableInteractionSource() }
    var birthdateString = birthdate
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Text(
                text = stringResource(id = R.string.birthday),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF444444)
                ),
                modifier = Modifier.padding(end = 4.dp)
            )
        }
        TextField(
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth()
                .clickable {
                    showDialog = !showDialog
                },
            shape = RoundedCornerShape(24.dp),
            placeholder = {
                Text(
                    text = stringResource(id = R.string.input_birthdate),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFDEDEDE)
                    )
                )
            },
            trailingIcon = {
                Icon(
                    modifier = Modifier,
                    imageVector = if (showDialog) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    tint = Color(0xffDCDCDC),
                    contentDescription = ""
                )
            },
            colors = TextFieldDefaults.colors().copy(
                focusedTextColor = Color.Black,
                focusedContainerColor = Color(0x1FC7CBD6),
                disabledTextColor = Color.Black,
                unfocusedContainerColor = Color(0x1FC7CBD6),
                disabledIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledContainerColor = Color(0x1FC7CBD6),
            ),
            enabled = false,
            interactionSource = interactionSource, // 将interactionSource传递给TextField
            value = birthdateString,
            onValueChange = {},
        )
    }
    AIHDatePickerDialog(
        visible = showDialog,
        onDateSelected = {
            showDialog = false
            onBirthdateChange(it ?: 0)
            birthdateString = convertUtcMillisToDateString(it ?: 0,context.getString(R.string.dateFormat))
        },
        onDismiss = {
            showDialog = false
        }
    )
//    if (showDialog) {
//        DatePickerModal(
//            onDateSelected = {
//                showDialog = false
//                onBirthdateChange(it ?: 0)
//            },
//            onDismiss = {
//                showDialog = false
//            }
//        )
//        DatePickerModal(
//            initialCalendar = Calendar.getInstance(),
//            onConfirmClick = {
//                showDialog = false
//            },
//            onCancelClick = {
//                showDialog = false
//            },
//            maxCalendar = Calendar.getInstance()
//        )
//    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun DatePickerModal(
    onDateSelected: (Long?) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val maxYear = Calendar.getInstance().get(Calendar.YEAR)-3
    val minYear = maxYear - 90

    // Create a calendar with the max year to ensure initial date is within range
    val initialCalendar = Calendar.getInstance().apply {
        set(Calendar.YEAR, maxYear)
        set(Calendar.MONTH, Calendar.JANUARY)
        set(Calendar.DAY_OF_MONTH, 1)
    }

    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialCalendar.timeInMillis,
        yearRange = minYear..maxYear,
    )
    val sheetState = rememberModalBottomSheetState()
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = sheetState,
        containerColor = Color.White
    ) {
        DatePicker(
            state = datePickerState,
            colors = DatePickerDefaults.colors().copy(
                selectedDayContainerColor = Color(0XFF73C5FF),
                todayDateBorderColor = Color(0XFF73C5FF),
                containerColor = Color(0xFFDEDEDE),
                selectedYearContainerColor = Color(0XFF73C5FF),
            )
        )
        AIHTextButton(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .fillMaxWidth(0.9f),
            text = stringResource(id = R.string.next_step),
            onClick = {
                onDateSelected(datePickerState.selectedDateMillis)
                onDismiss()
            },
        )
    }
}

@Preview()
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AIHDatePickerDialog(
    visible: Boolean = true,
    onDateSelected: (Long?) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val maxYear = Calendar.getInstance().get(Calendar.YEAR)-3
    val minYear = maxYear - 90

    // Create a calendar with the max year to ensure initial date is within range
    val initialCalendar = Calendar.getInstance().apply {
        set(Calendar.YEAR, maxYear)
        set(Calendar.MONTH, Calendar.JANUARY)
        set(Calendar.DAY_OF_MONTH, 1)
    }

    val datePickerState = rememberDatePickerState(
        initialSelectedDateMillis = initialCalendar.timeInMillis,
        yearRange = minYear..maxYear,
    )
    if (visible) {
        DatePickerDialog(
            onDismissRequest = onDismiss,
            confirmButton = {
                TextButton(
                    onClick = {
                        onDateSelected(datePickerState.selectedDateMillis)
                    }
                ) {
                    Text(
                        text = stringResource(id = R.string.confirm),
                        color = Color(0XFF73C5FF)
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        onDismiss()
                    }
                ) {
                    Text(
                        text = stringResource(id = R.string.cancel),
                        color = Color(0xFFDEDEDE)
                    )
                }
            },
            colors = DatePickerDefaults.colors().copy(
                selectedDayContainerColor = Color(0XFF73C5FF),
                todayDateBorderColor = Color(0XFF73C5FF),
                containerColor = Color.White,
                selectedYearContainerColor = Color(0XFF73C5FF),
            )
        ) {
            DatePicker(
                state = datePickerState,
                colors = DatePickerDefaults.colors().copy(
                    selectedDayContainerColor = Color(0XFF73C5FF),
                    todayDateBorderColor = Color(0XFF73C5FF),
                    containerColor = Color.White,
                    selectedYearContainerColor = Color(0XFF73C5FF),
                )
            )
        }
    }
}

fun convertUtcMillisToDateString(utcMillis: Long, format: String = "MM/dd/YYYY"): String {
    val sdf = SimpleDateFormat(format, Locale.getDefault())
    sdf.timeZone = TimeZone.getTimeZone("UTC")
    val date = Date(utcMillis)
    return sdf.format(date)
}
