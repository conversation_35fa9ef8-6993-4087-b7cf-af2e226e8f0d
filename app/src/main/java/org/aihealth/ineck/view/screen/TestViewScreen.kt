package org.aihealth.ineck.view.screen

import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.AIHSwitch
import org.aihealth.ineck.view.custom.AIHTextField


@Preview
@Composable
fun TestScreen() {
    MaterialTheme {
        val offset by remember {
            mutableFloatStateOf(0F)
        }
        var refreshing by remember {
            mutableStateOf(false)
        }
        val coroutineScope = rememberCoroutineScope()
        Column(
            Modifier
                .fillMaxSize()
                .offset { IntOffset(0, offset.toInt()) }
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

        Text(text = "AIHOutlinedTextField")
            var text1 by remember {
                mutableStateOf("")
            }

            MySpacer()
            Text(text = "AIHTextField")
            var text2 by remember {
                mutableStateOf("")
            }
            AIHTextField(
                value = text2,
                onValueChange = {text2 = it},
                placeholder = "请输入真实姓名"
            )

            MySpacer()
            Text(text = "AIHTextField")
            var text3 by remember {
                mutableStateOf("")
            }
//            AIHTextField(
//                value = text3,
//                onValueChange = {text3 = it},
//                placeholder = "请输入密码",
//                visualTransformation = PasswordVisualTransformation(),
//                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Password)
//            )

            MySpacer()
            Text(text = "AIHButton")
            AIHButton(text = "登录", onClick = {  })

            MySpacer()
            Text(text = "AIHOutlinedButton")
            AIHOutlinedButton("注册", onClick = {})

            var text4 by remember {
                mutableStateOf("")
            }
            TextField(
                value = text4,
                onValueChange = {text4 = it},

            )
//            var isChecked by remember {
//                mutableStateOf(false)
//            }
//            AIHTextSwitch(
//                isLeft = isChecked,
//                onCheckedChange = {isChecked = it},
//                leftText = "监测",
//                rightText = "自动"
//            )
            var checked by remember {
                mutableStateOf(false)
            }
            AIHSwitch(
                checked = checked,
                onCheckedChange = {checked = it},
                checkedText = "ON",
                unCheckedText = "OFF",
                modifier = Modifier
                    .padding(top = 20.dp)
                    .size(120.dp, 50.dp),
                padding = PaddingValues(5.dp)
            )
            var isClick by remember {
                mutableStateOf(false)
            }
            val length by animateDpAsState(
                targetValue = if (isClick) 200.dp else 100.dp,
                animationSpec = infiniteRepeatable(
                    animation = tween(durationMillis = 300),
                    repeatMode = RepeatMode.Reverse
                ), label = ""
            )
            LaunchedEffect(Unit) {
                isClick = true
                LogUtil.d("LaunchedEffect")
            }
            Box(modifier = Modifier
                .size(length)
                .background(Color.Yellow), contentAlignment = Alignment.Center) {
                Button(onClick = {
                    isClick = !isClick
                }) {
                    Text(text = "按钮")
                }
            }

        }
    }
}

@Composable
private fun MySpacer() {
    Spacer(modifier = Modifier.height(20.dp))
}