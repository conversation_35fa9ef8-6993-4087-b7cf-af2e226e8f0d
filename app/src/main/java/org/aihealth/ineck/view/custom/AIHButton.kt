package org.aihealth.ineck.view.custom

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Composable
fun AIHButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    backgroundColor: Color = Color(0XFF1E4BDF),
    fontColor: Color = Color.White,
    fontSize: TextUnit = 14.sp,
    fontWeight: FontWeight = FontWeight.Medium,
    shape: Shape = CircleShape
){
    val modif = if (enabled) {
        Modifier
            .background(backgroundColor)
            .clickable {
                onClick()
            }
    } else{
        Modifier.background(Color(0XFFAAAAAA))
    }
    Box(
        modifier = Modifier
            .then(modifier)
            .clip(shape)
            .then(modif)
    ){
        Text(
            text = text,
            fontSize = fontSize,
            color = fontColor,
            fontWeight = fontWeight,
            modifier = Modifier
                .padding(horizontal = 8.dp, vertical = 4.dp)
                .align(Alignment.Center),
        )
    }

}

@Composable
fun AIHOutlinedButton(
    text: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color.Transparent,
    borderColor: Color = Color(0XFF1E4BDF),
    fontColor: Color = Color(0XFF1E4BDF),
    fontWeight: FontWeight = FontWeight.Medium,
    fontSize: TextUnit = 14.sp,
    shape: Shape = CircleShape
){
    Box(modifier = Modifier
        .then(modifier)
        .clip(shape)
        .background(backgroundColor)
        .border(1.dp, borderColor, shape)
        .clickable {
            onClick()
        }
    ) {
        Text(
            text = text,
            fontSize = fontSize,
            color = fontColor,
            fontWeight = fontWeight,
            modifier = Modifier
                .align(Alignment.Center)
                .padding(vertical = 4.dp)
        )
    }
}

@Composable
fun AIHTextButton(
    text: String,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    backgroundBrush: Brush = Brush.linearGradient(
        colors = listOf(
            Color(0XFF73C5FF),
            Color(0XFF3161FF),
        )
    ),
    shape: Shape = CircleShape,
    textPaddingValues: PaddingValues = PaddingValues(top = 8.dp, bottom = 8.dp),
    style: TextStyle = LocalTextStyle.current
) {
    Box(
        modifier = Modifier
            .then(modifier)
            .clip(shape)
            .background(backgroundBrush)
            .clickable {
                onClick()
            }
    ) {
        Text(
            modifier = Modifier
                .padding(textPaddingValues)
                .align(Alignment.Center),
            text = text,
            style = style,
        )
    }
}

@Preview()
@Composable
fun AIHSelectButton(
    selectedIndex: Int = 0,
    array: Array<String> = stringArrayResource(id = R.array.pain_record_numbness_status),
    onClick: (index: Int) -> Unit = {},
    modifier: Modifier = Modifier,
    backgroundColor: Color = Color(0XFFE6E6E6),
    selectedColor: Color = Color.White,
    textColor: Color = Color(0xFF444444),
    padding: PaddingValues = PaddingValues(3.dp)
) {
    var _selectedIndex by remember {
        mutableIntStateOf(selectedIndex)
    }
    _selectedIndex = selectedIndex
    BoxWithConstraints(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .background(backgroundColor, CircleShape)
            .padding(padding)
    ) {
        val offset by animateDpAsState(
            targetValue = (maxWidth * _selectedIndex / array.size), label = ""
        )
        val topBoxWidth = maxWidth
        Box(modifier = Modifier
            .matchParentSize()
            .padding(vertical = 1.dp, horizontal = 2.dp)
        ) {
            Box(
                modifier = Modifier
                    .offset(offset, 0.dp)
                    .background(selectedColor, CircleShape)
                    .fillMaxHeight()
                    .width(topBoxWidth / array.size)
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(IntrinsicSize.Max),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            array.forEachIndexed { index, str ->
                Box(modifier = Modifier
                    .pointerInput(Unit) {
                        detectTapGestures {
                            onClick(index)
                        }
                    }
                    .fillMaxHeight()
                    .weight(1F),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        modifier = Modifier
                            .align(Alignment.Center)
                            .padding(horizontal = 4.dp, vertical = 2.dp),
                        text = str,
                        fontSize = 14.sp,
                        color = textColor,
                    )
                }
            }
        }
    }
}