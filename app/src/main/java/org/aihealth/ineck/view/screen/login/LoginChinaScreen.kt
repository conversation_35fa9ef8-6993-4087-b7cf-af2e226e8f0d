package org.aihealth.ineck.view.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Card
import androidx.compose.material3.CardColors
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import kotlinx.coroutines.delay
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.SPUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.AIHTextButton
import org.aihealth.ineck.view.custom.AIHTextField
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.LoginChinaViewModel

/**
 * 国内服务器登录界面UI
 */
@Composable
fun LoginChinaRoute() {
    val viewModel = viewModel<LoginChinaViewModel>()
    val isReadChecked by viewModel.isReadChecked.collectAsState()
    val isDialogVisible by viewModel.dialogVisible.collectAsState()

    LaunchedEffect(true) {
        if (SPUtil.getBoolean(SPConstant.IS_FIRST_READ_AGREEMENT, true)) {
            LogUtil.i("first private term")
            SPUtil.putBoolean(SPConstant.IS_FIRST_READ_AGREEMENT, false)
            delay(800) // 延迟800ms让页面先完全加载
            viewModel.setDialogVisible(true)
        }
    }
    LoginChinaScreen(
        modifier = Modifier.fillMaxSize(),
        loginWay = if (viewModel.isPhoneLogin) "phone" else "email",
        phone = viewModel.phoneLogin,
        changePhone = { viewModel.phoneLogin = it },
        countDown = viewModel.timeLogin,
        code = viewModel.codeLogin,
        sendCode = viewModel::sendCodeClick,
        changeCode = { viewModel.codeLogin = it },
        loginWithPhone = viewModel::phoneLoginClick,
        email = viewModel.emailLogin,
        changeEmail = { viewModel.emailLogin = it },
        password = viewModel.passwordLogin,
        changePassword = { viewModel.passwordLogin = it },
        loginWithEmail = viewModel::authingLoginByEmail,
        loginWithWechat = viewModel::wechatLoginClick,
        isReadChecked = isReadChecked,
        changeIsReadChecked = { viewModel.changeIsReadChecked() },
        isDialogVisible = isDialogVisible,
        changeIsDialogVisible = {
            viewModel.setDialogVisible(it)
        },
        toEmailLogin = { viewModel.isPhoneLogin = false },
        toPhoneLogin = { viewModel.isPhoneLogin = true },
        toForgetPassword = {
            startScreen(Screen.ForgetPassword.route + "?email=$it")
        }
    )
}

@Composable
fun LoginChinaScreen(
    modifier: Modifier = Modifier,
    loginWay: String,
    phone: String,
    changePhone: (String) -> Unit,
    countDown: Int,
    code: String,
    sendCode: () -> Unit,
    changeCode: (String) -> Unit,
    loginWithPhone: () -> Unit,
    email: String,
    changeEmail: (String) -> Unit,
    password: String,
    changePassword: (String) -> Unit,
    loginWithEmail: () -> Unit,
    loginWithWechat: () -> Unit,
    isReadChecked: Boolean,
    changeIsReadChecked: () -> Unit,
    isDialogVisible: Boolean,
    changeIsDialogVisible: (Boolean) -> Unit,
    toEmailLogin: () -> Unit,
    toPhoneLogin: () -> Unit,
    toForgetPassword: (String) -> Unit
) {
    BasePageView(
        background = {
            Image(
                painter = painterResource(id = R.drawable.bg_login_new),
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds
            )
        }
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .align(Alignment.Center)
                .verticalScroll(rememberScrollState())
        ) {
            Row(
                modifier = Modifier
                    .align(Alignment.End)
                    .padding(top = 40.dp, end = 16.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            startScreen(Screen.ChangeCity.route)
                        }
                    },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(id = R.string.mainland),
                    color = Color(0xFF333333),
                    fontSize = 14.sp
                )
                Image(
                    painter = painterResource(id = R.drawable.img_pulldown),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .size(10.dp)
                )
            }
            Image(
                painter = painterResource(id = R.drawable.img_icon),
                contentDescription = null,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 38.dp)
                    .size(131.dp, 62.dp)
            )
            Card(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = 61.dp),
                shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp),
                colors = CardColors(
                    containerColor = Color.White,
                    disabledContainerColor = Color.Transparent,
                    disabledContentColor = Color.Transparent,
                    contentColor = Color.Transparent
                ),
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                ) {
                    if (loginWay == "phone") {
                        LoginWithPhone(
                            phone = phone,
                            changePhone = changePhone,
                            countDown = countDown,
                            code = code,
                            sendCode = sendCode,
                            changeCode = changeCode,
                            loginWithPhone = loginWithPhone,
                            toEmailLogin = toEmailLogin
                        )

                    } else {
                        LoginWithEmail(
                            email = email,
                            changeEmail = changeEmail,
                            password = password,
                            changePassword = changePassword,
                            loginWithEmail = loginWithEmail,
                            toPhoneLogin = toPhoneLogin,
                            toForgetPassword = toForgetPassword
                        )
                    }

                    Row(
                        Modifier
                            .fillMaxWidth()
                            .padding(top = 42.dp, bottom = 26.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        HorizontalDivider(
                            modifier = Modifier
                                .height(1.dp)
                                .weight(1F), color = Color(0XFFE6E6E6)
                        )
                        Text(
                            text = stringResource(id = R.string.other_login_methods),
                            fontSize = 14.sp,
                            color = Color(0XFF666666),
                            modifier = Modifier.padding(horizontal = 20.dp)
                        )
                        HorizontalDivider(
                            modifier = Modifier
                                .height(1.dp)
                                .weight(1F), color = Color(0XFFE6E6E6)
                        )
                    }
                    Row(Modifier.fillMaxWidth()) {
                        Spacer(modifier = Modifier.weight(1F))
                        Image(
                            painter = painterResource(id = R.drawable.img_wechat),
                            contentDescription = null,
                            modifier = Modifier
                                .size(40.dp)
                                .shadow(elevation = 1.dp, CircleShape)
                                .pointerInput(Unit) {
                                    detectTapGestures {
                                        loginWithWechat()
                                    }
                                }
                        )
                        Spacer(modifier = Modifier.weight(1F))

                    }

                    Spacer(modifier = Modifier.height(40.dp))

                    LoginAgreementView(checked = isReadChecked) {
                        changeIsReadChecked()
                    }
                    Spacer(modifier = Modifier.height(42.dp))
                }
            }
        }
    }

    AgreementDialog(
        visible = isDialogVisible,
        onConfirm = {
            changeIsReadChecked()
            changeIsDialogVisible(false)
            // 国内用户同意协议后初始化 Umeng
            baseApplication.initUmeng()
        },
        onCancel = {
            changeIsDialogVisible(false)
        }
    )

}

@Composable
fun LoginWithPhone(
    modifier: Modifier = Modifier,
    phone: String,
    changePhone: (String) -> Unit,
    countDown: Int,
    code: String,
    sendCode: () -> Unit,
    changeCode: (String) -> Unit,
    loginWithPhone: () -> Unit,
    toEmailLogin: () -> Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.phone),
            color = Color(0XFF444444),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 10.dp, vertical = 16.dp)
        )
        AIHTextField(
            value = phone,
            onValueChange = {
                changePhone(it)
            },
            keyboardType = KeyboardType.Phone,
            placeholder = stringResource(id = R.string.enter_your_phone_number)
        )
        Text(
            text = stringResource(id = R.string.code),
            color = Color(0XFF444444),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp, vertical = 16.dp),

            )
        Box {
            AIHTextField(
                value = code,
                onValueChange = {
                    changeCode(it)
                },
                keyboardType = KeyboardType.Number,
                maxLength = 4,
                placeholder = stringResource(id = R.string.enter_verification_code)
            )
            Text(text = if (countDown > 0) stringResource(
                id = R.string.get_code_after_time,
                countDown
            ) else stringResource(
                id = R.string.get_code
            ),
                color = Color(0xFF1E4BDF),
                fontSize = 14.sp,
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            sendCode()
                        }
                    }
            )
        }
        Spacer(modifier = Modifier.height(10.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp),
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = stringResource(id = R.string.email_login),
                color = Color(0xFF1E4BDF),
                modifier = Modifier.clickable {
                    toEmailLogin()
                }
            )
        }
        AIHTextButton(
            text = stringResource(id = R.string.login),
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .padding(top = 30.dp),
            style = TextStyle(
                fontWeight = FontWeight(400),
                color = Color(0xFFF7F7F7),
                fontSize = 20.sp,
                textAlign = TextAlign.Center
            ),
            onClick = {
                loginWithPhone()
            }
        )
    }
}

@Composable
fun LoginWithEmail(
    modifier: Modifier = Modifier,
    email: String,
    changeEmail: (String) -> Unit,
    password: String,
    changePassword: (String) -> Unit,
    loginWithEmail: () -> Unit,
    toPhoneLogin: () -> Unit,
    toForgetPassword:(String)->Unit
) {
    Column(
        modifier = modifier
            .fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.email),
            color = Color(0XFF444444),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp, vertical = 16.dp)
        )
        AIHTextField(
            value = email,
            onValueChange = {
                changeEmail(it)
            },
            keyboardType = KeyboardType.Email,
            placeholder = stringResource(id = R.string.please_enter_email_account)
        )
        Text(
            text = stringResource(id = R.string.password),
            color = Color(0XFF444444),
            fontSize = 18.sp,
            fontWeight = FontWeight.Bold,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp, vertical = 16.dp)
        )
        AIHTextField(
            value = password,
            onValueChange = {
                changePassword(it)
            },
            keyboardType = KeyboardType.Password,
            placeholder = stringResource(id = R.string.please_enter_password),
            morePlaceholder = {
                if (password.isEmpty()) {
                    Text(
                        text = stringResource(id = R.string.forget_password),
                        fontSize = 14.sp, color = Color(0xFF1E4BDF),
                        modifier = Modifier
                            .align(Alignment.CenterEnd)
                            .padding(end = 4.dp)
                            .pointerInput(Unit) {
                                detectTapGestures {
                                    toForgetPassword(email)
                                }
                            },
                    )
                }
            }
        )

        Spacer(modifier = Modifier.height(10.dp))

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 10.dp),
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = stringResource(id = R.string.phone_login),
                color = Color(0XFF1E4BDF),
                modifier = Modifier.clickable {
                    toPhoneLogin()
                }
            )
        }
        AIHTextButton(
            text = stringResource(id = R.string.login),
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .padding(top = 30.dp),
            style = TextStyle(
                fontWeight = FontWeight(400),
                color = Color(0xFFF7F7F7),
                fontSize = 20.sp,
                textAlign = TextAlign.Center
            ),
            onClick = {
                loginWithEmail()
            }
        )

        AIHOutlinedButton(
            text = stringResource(id = R.string.sign_up),
            onClick = {
                startScreen(
                    route = Screen.LoginSignUpChina.route,
                    finish = false
                )
            },
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .padding(vertical = 10.dp),
            fontSize = 20.sp,
            fontColor = Color(0XFF1E4BDF)
        )
    }
}

@Preview
@Composable
private fun Preview() {
    LoginChinaScreen(
        loginWay = "email",
        phone = "",
        changePhone = {},
        countDown = 0,
        code = "",
        sendCode = {},
        changeCode = {},
        loginWithPhone = {},
        email = "",
        changeEmail = {},
        password = "",
        changePassword = {},
        loginWithEmail = {},
        loginWithWechat = {},
        isReadChecked = false,
        changeIsReadChecked = {},
        isDialogVisible = false,
        changeIsDialogVisible = {},
        toEmailLogin = {},
        toPhoneLogin = {},
        toForgetPassword = {}
    )
}