package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.google.accompanist.web.WebView
import com.google.accompanist.web.rememberWebViewState
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.view.custom.BasePageView
import java.util.Locale

@SuppressLint("SetJavaScriptEnabled")
@Composable
fun MemberAgreementScreen() {
    BasePageView(
        title = stringResource(id = R.string.membership_agreement),
        showBackIcon = true
    ) {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            val state = rememberWebViewState(
                url = if (currentLocale == Locale.CHINESE)
                    "https://health.aihnet.cn/?page_id=6747#/terms"
                else "https://medical.aihnet.com/terms-of-us/"
            )
            WebView(state = state, modifier = Modifier,
                onCreated = { webView ->
                    webView.settings.javaScriptEnabled = true
                }
            )
        }
    }
}