package org.aihealth.ineck.view.screen.info

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowUp
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.MenuDefaults
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableDoubleStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import org.aihealth.ineck.R
import org.aihealth.ineck.model.Constants.WEIGHT_KG_MAX_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_KG_MIN_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_LB_MAX_LIMIT
import org.aihealth.ineck.model.Constants.WEIGHT_LB_MIN_LIMIT
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.custom.AIHTextButton
import kotlin.math.roundToInt


/**
 * 编辑体重
 * Weight 仅保持公制，在显示的时候根据"M","I"来判断是否转换为英制。
 */
@Composable
fun EditWeight(
    modifier: Modifier = Modifier,
    weight: Double,
    selectedUnit: String,
    onWeightChange: (WeightUnit) -> Unit
) {
    val context = LocalContext.current
    val interactionSource = remember { MutableInteractionSource() }
    var showDialog by remember { mutableStateOf(false) }
    val locale = context.resources.configuration.locales[0]
    val weightString = when (selectedUnit) {
        "M" -> {
            if (weight >= WEIGHT_KG_MIN_LIMIT)
                String.format(locale, "%.1f %s", weight, stringResource(R.string.weight_unit_kg))
            else ""
        }

        "I" -> {
            if (weight >= WEIGHT_KG_MIN_LIMIT)
                String.format(
                    locale,
                    "%.1f %s",
                    weight * 2.**********,
                    stringResource(R.string.weight_unit_lb)
                )
            else ""
        }

        else -> ""

    }
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Text(
                text = stringResource(id = R.string.weight),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(600),
                    color = Color(0xFF444444)
                ),
                modifier = Modifier.padding(end = 4.dp)
            )
        }
        TextField(
            modifier = Modifier
                .padding(top = 12.dp)
                .fillMaxWidth()
                .clickable {
                    showDialog = !showDialog
                },
            shape = RoundedCornerShape(24.dp),
            placeholder = {
                Text(
                    text = stringResource(id = R.string.input_weight),
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFFDEDEDE)
                    )
                )
            },
            trailingIcon = {
                Icon(
                    modifier = Modifier,
                    imageVector = if (showDialog) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                    tint = Color(0xffDCDCDC),
                    contentDescription = ""
                )
            },
            colors = TextFieldDefaults.colors().copy(
                focusedTextColor = Color.Black,
                focusedContainerColor = Color(0x1FC7CBD6),
                disabledTextColor = Color.Black,
                unfocusedContainerColor = Color(0x1FC7CBD6),
                disabledIndicatorColor = Color.Transparent,
                focusedIndicatorColor = Color.Transparent,
                unfocusedIndicatorColor = Color.Transparent,
                disabledContainerColor = Color(0x1FC7CBD6),
            ),
            enabled = false,
            interactionSource = interactionSource, // 将interactionSource传递给TextField
            value = weightString,
            onValueChange = {},
        )
    }

    WeightVerticalPickerWithInputDialog(
        visible = showDialog,
        initialWeight = weight,
        selectedUnit = selectedUnit,
        onWeightSelected = {
            LogUtil.i("onWeightSelected: $it")
            onWeightChange(it)
            showDialog = false
        },
        onDismiss = { showDialog = false }
    )

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WeightPickerSheet(
    initialWeight: Double = 10.0,
    selectedUnit: String,
    onWeightSelected: (WeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val sheetState = rememberModalBottomSheetState()
    ModalBottomSheet(
        onDismissRequest = onDismiss,
        sheetState = sheetState,
        containerColor = Color.White,
    ) {
        InputWeight(
            modifier = Modifier.fillMaxWidth(),
            initialWeight = initialWeight,
            selectedUnit = selectedUnit,
            onWeightSelected = onWeightSelected
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WeightInputDialog(
    visible: Boolean = true,
    initialWeight: Double = 10.0,
    selectedUnit: String,
    onWeightSelected: (WeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val context = LocalContext.current
    val locale = context.resources.configuration.locales[0]
    var selected by remember(selectedUnit) {
        mutableStateOf(selectedUnit)
    }
    var expanded by remember { mutableStateOf(false) }

    var weightInput by remember(initialWeight) {
        mutableStateOf(
            String.format(locale, "%.2f", initialWeight)
        )
    }

    var selectedWeightLbs by remember(initialWeight) {
        mutableStateOf(
            String.format(locale, "%.2f", (initialWeight * 2.**********))
        )
    }
    if (visible) {
        AlertDialog(
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false,
            ),
            modifier = Modifier.fillMaxWidth(0.9f),
            containerColor = Color.White,
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = stringResource(R.string.weight),
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
            },
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ExposedDropdownMenuBox(
                        modifier = Modifier
                            .align(Alignment.End)
                            .background(Color.Transparent),
                        expanded = expanded,
                        onExpandedChange = { expanded = !it }
                    ) {
                        Card(
                            modifier = Modifier
                                .wrapContentHeight()
                                .menuAnchor(),
                            shape = RoundedCornerShape(size = 12.dp),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = 1.dp,
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .heightIn(min = 32.dp)
                                    .clickable { expanded = !expanded }
                                    .align(Alignment.CenterHorizontally),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    modifier = Modifier.padding(start = 16.dp),
                                    text = when (selected) {
                                        "M" -> stringResource(R.string.kilograms)
                                        else -> stringResource(R.string.pounds)
                                    },
                                    color = Color.Black,
                                    fontWeight = FontWeight.Light,
                                    fontSize = 12.sp,
                                    textAlign = TextAlign.Center
                                )
                                Icon(
                                    imageVector = Icons.Default.KeyboardArrowDown,
                                    contentDescription = null,
                                    tint = Color.Black,
                                    modifier = Modifier
                                        .padding(end = 16.dp)
                                        .size(16.dp)
                                )
                            }
                        }
                        // 下拉菜单
                        DropdownMenu(
                            modifier = Modifier,
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = {
                                    Text(
                                        modifier = Modifier.padding(vertical = 2.dp),
                                        text = stringResource(R.string.kilograms),
                                        color = Color.Black,
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    selected = "M"
                                },
                                colors = MenuDefaults.itemColors().copy(
                                    textColor = Color.Black,

                                    )
                            )
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = {
                                    Text(
                                        modifier = Modifier.padding(vertical = 2.dp),
                                        text = stringResource(R.string.pounds),
                                        color = Color.Black,
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    selected = "I"
                                }
                            )
                        }
                    }
                    when (selected) {
                        "M" -> {
                            OutlinedTextField(
                                value = weightInput,
                                onValueChange = { input ->
                                    if (input == "") {
                                        weightInput = input
                                    }
                                    // 验证输入是否合法
                                    if (input.all { it.isDigit() || it == '.' } &&
                                        (input.count { it == '.' } <= 1) &&  // 只允许一个小数点
                                        (input == "." || input.toDoubleOrNull() != null))  // 确保小数点不是第一个字符，且字符串可以转换为有效的小数
                                    {
                                        // 如果包含小数点，限制小数点后最多两位
                                        val decimalIndex = input.indexOf('.')
                                        if (decimalIndex == -1 || input.substring(decimalIndex).length <= 3) {
                                            weightInput = input
                                        }
                                    }
                                },
                                label = { Text(text = stringResource(R.string.weight_unit_kg)) },
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp),
                                colors = OutlinedTextFieldDefaults.colors(
                                    focusedTextColor = Color.Black,
                                    disabledTextColor = Color.Transparent,
                                    focusedContainerColor = Color.Transparent,
                                    focusedBorderColor = Color(0xFF244CD2),
                                    unfocusedBorderColor = Color(0xFF999999),
                                    disabledBorderColor = Color.Transparent,
                                ),
                                singleLine = true,
                                keyboardOptions = KeyboardOptions(
                                    keyboardType = KeyboardType.Number,
                                    imeAction = ImeAction.Done
                                ),
                            )
                        }

                        "I" -> {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 8.dp)
                            ) {
                                OutlinedTextField(
                                    value = selectedWeightLbs,
                                    onValueChange = { input ->
                                        if (input == "") {
                                            selectedWeightLbs = input
                                        }
                                        // 验证输入是否合法
                                        if (input.all { it.isDigit() || it == '.' } &&
                                            (input.count { it == '.' } <= 1) &&  // 只允许一个小数点
                                            (input == "." || input.toDoubleOrNull() != null))  // 确保小数点不是第一个字符，且字符串可以转换为有效的小数
                                        {
                                            // 如果包含小数点，限制小数点后最多两位
                                            val decimalIndex = input.indexOf('.')
                                            if (decimalIndex == -1 || input.substring(decimalIndex).length <= 3) {
                                                selectedWeightLbs = input
                                            }
                                        }

                                    },
                                    label = { Text(text = stringResource(R.string.weight_unit_lb)) },
                                    modifier = Modifier
                                        .fillMaxWidth(),
                                    colors = OutlinedTextFieldDefaults.colors(
                                        focusedTextColor = Color.Black,
                                        disabledTextColor = Color.Transparent,
                                        focusedContainerColor = Color.Transparent,
                                        focusedBorderColor = Color(0xFF244CD2),
                                        unfocusedBorderColor = Color(0xFF999999),
                                        disabledBorderColor = Color.Transparent,
                                    ),
                                    singleLine = true,
                                    keyboardOptions = KeyboardOptions(
                                        keyboardType = KeyboardType.Number,
                                        imeAction = ImeAction.Done
                                    ),
                                )
                            }

                        }

                        else -> {}
                    }
                }
            },
            confirmButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AIHTextButton(
                        text = stringResource(id = R.string.confirm),
                        onClick = {
                            when (selected) {
                                "M" -> {
                                    if (weightInput.isEmpty()) {
                                        DialogUtil.showToast(context.getString(R.string.weight_cannot_be_0))
                                    } else {
                                        val value = weightInput.toDoubleOrNull() ?: 0.0
                                        if (value < WEIGHT_KG_MIN_LIMIT || value > WEIGHT_KG_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_metric))
                                        } else {
                                            onWeightSelected(
                                                WeightUnit(
                                                    value = value,
                                                    unit = "M"
                                                )
                                            )
                                        }
                                    }
                                }

                                else -> {
                                    if (selectedWeightLbs.isEmpty()) {
                                        DialogUtil.showToast(context.getString(R.string.weight_cannot_be_0))
                                    } else {
                                        val value =
                                            selectedWeightLbs.toDoubleOrNull()?.times(0.45359237)
                                                ?: 0.0

                                        LogUtil.i("weight: ${value}, selectedWeightLb:${selectedWeightLbs}")
                                        if (value < WEIGHT_KG_MIN_LIMIT || value > WEIGHT_KG_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_imperial))
                                        } else {
                                            onWeightSelected(
                                                WeightUnit(
                                                    value = value,
                                                    unit = "I"
                                                )
                                            )
                                        }
                                    }
                                }
                            }
                        },
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color.White
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                    )
                }
            },
            dismissButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.cancel),
                        color = Color(0x70CECECE),
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .clickable { onDismiss() }
                    )
                }
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WeightPickerDialog(
    visible: Boolean = true,
    initialWeight: Double = 10.0,
    selectedUnit: String = "I",
    onWeightSelected: (WeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val context = LocalContext.current
    val locale = context.resources.configuration.locales[0]
    var selected by remember(selectedUnit) {
        mutableStateOf(selectedUnit)
    }
    var expanded by remember { mutableStateOf(false) }
    val weightDot = (0..9).toList()
    val weightKg = (10..300).toList()
    val weightLb = (22..660).toList()
    var selectedWeightKg by remember(initialWeight) { mutableIntStateOf(initialWeight.toInt()) }
    var selectedWeightDotKg by remember(initialWeight) {
        mutableIntStateOf(
            (initialWeight % 1 * 10).roundToInt()
        )
    }
    var selectedWeightLb by remember(initialWeight) {
        mutableIntStateOf(
            (initialWeight * 2.**********).toInt()
        )
    }
    var selectedWeightDotLb by remember(initialWeight) {
        mutableIntStateOf(
            ((initialWeight * 2.********** % 1) * 10).roundToInt() % 10
        )
    }

    var weightInput by remember(initialWeight) {
        mutableStateOf(
            String.format(locale, "%.2f", initialWeight)
        )
    }

    var selectedWeightLbs by remember(initialWeight) {
        mutableStateOf(
            String.format(locale, "%.2f", (initialWeight * 2.**********))
        )
    }
    if (visible) {
        AlertDialog(
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = true,
                usePlatformDefaultWidth = false,
            ),
            modifier = Modifier.fillMaxWidth(0.9f),
            containerColor = Color.White,
            onDismissRequest = onDismiss,
            title = {
                Text(
                    text = stringResource(R.string.weight),
                    style = TextStyle(
                        fontSize = 24.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    )
                )
            },
            text = {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ExposedDropdownMenuBox(
                        modifier = Modifier
                            .align(Alignment.Start)
                            .background(Color.Transparent),
                        expanded = expanded,
                        onExpandedChange = { expanded = !it }
                    ) {
                        Card(
                            modifier = Modifier
                                .wrapContentHeight()
                                .menuAnchor(),
                            shape = RoundedCornerShape(size = 12.dp),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = 1.dp,
                            )
                        ) {
                            Row(
                                modifier = Modifier
                                    .heightIn(min = 32.dp)
                                    .clickable { expanded = !expanded }
                                    .align(Alignment.CenterHorizontally),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    modifier = Modifier.padding(start = 16.dp),
                                    text = when (selected) {
                                        "M" -> stringResource(R.string.kilograms)
                                        else -> stringResource(R.string.pounds)
                                    },
                                    color = Color.Black,
                                    fontWeight = FontWeight.Light,
                                    fontSize = 12.sp,
                                    textAlign = TextAlign.Center
                                )
                                Icon(
                                    imageVector = Icons.Default.KeyboardArrowDown,
                                    contentDescription = null,
                                    tint = Color.Black,
                                    modifier = Modifier
                                        .padding(end = 16.dp)
                                        .size(16.dp)
                                )
                            }
                        }
                        // 下拉菜单
                        DropdownMenu(
                            modifier = Modifier,
                            expanded = expanded,
                            onDismissRequest = { expanded = false }
                        ) {
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = {
                                    Text(
                                        modifier = Modifier.padding(vertical = 2.dp),
                                        text = stringResource(R.string.kilograms),
                                        color = Color.Black,
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    selected = "M"
                                },
                                colors = MenuDefaults.itemColors().copy(
                                    textColor = Color.Black,

                                    )
                            )
                            DropdownMenuItem(
                                modifier = Modifier,
                                text = {
                                    Text(
                                        modifier = Modifier.padding(vertical = 2.dp),
                                        text = stringResource(R.string.pounds),
                                        color = Color.Black,
                                    )
                                },
                                onClick = {
                                    expanded = false
                                    selected = "I"
                                }
                            )
                        }
                    }
                    when (selected) {
                        "M" -> {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Row(
                                    modifier = Modifier.weight(1f),
                                ) {
                                    ListNumberPicker(
                                        data = weightKg,
                                        selectIndex = weightKg.indexOf(selectedWeightKg)
                                            .takeIf { it != -1 } ?: 0,
                                        visibleCount = 3,
                                        modifier = Modifier
                                            .height(150.dp),
                                        onSelect = { _, item ->
                                            selectedWeightKg = item
                                        },
                                        align = Alignment.CenterEnd
                                    ) {
                                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                        if (it == selectedWeightKg) {
                                            Column(
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 30.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                )
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                            }
                                        } else {
                                            Column(
                                                modifier = Modifier
                                                    .width(89.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 26.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                                Column {
                                    Text(
                                        text = ".",
                                        style = TextStyle(
                                            fontSize = 30.sp,
                                            fontWeight = FontWeight(500),
                                            color = Color(0xFF333333)
                                        )
                                    )
                                }
                                Row(
                                    modifier = Modifier.weight(1f),
                                ) {
                                    ListNumberPicker(
                                        data = weightDot,
                                        selectIndex = weightDot.indexOf(selectedWeightDotKg)
                                            .takeIf { it != -1 } ?: 0,
                                        visibleCount = 3,
                                        modifier = Modifier
                                            .height(150.dp),
                                        onSelect = { _, item ->
                                            selectedWeightDotKg = item
                                        },
                                        align = Alignment.CenterStart
                                    ) {
                                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                        if (it == selectedWeightDotKg) {
                                            Column(
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 30.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                )
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                            }
                                        } else {
                                            Column(
                                                modifier = Modifier.width(89.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 26.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                                Column(
                                    modifier = Modifier.weight(0.5f)
                                ) {
                                    Text(
                                        text = stringResource(R.string.weight_unit_kg),
                                        style = TextStyle(
                                            fontSize = 30.sp,
                                            fontWeight = FontWeight(500),
                                            color = Color(0xFF333333)
                                        )
                                    )
                                }

                            }

                        }

                        "I" -> {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Row(
                                    modifier = Modifier.weight(1f),
                                ) {
                                    ListNumberPicker(
                                        data = weightLb,
                                        selectIndex = weightLb.indexOf(selectedWeightLb)
                                            .takeIf { it != -1 } ?: 0,
                                        visibleCount = 3,
                                        modifier = Modifier
                                            .height(150.dp),
                                        onSelect = { _, item ->
                                            selectedWeightLb = item
                                        },
                                        align = Alignment.CenterEnd
                                    ) {
                                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                        if (it == selectedWeightLb) {
                                            Column(
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 30.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                )
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                            }
                                        } else {
                                            Column(
                                                modifier = Modifier
                                                    .width(89.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 26.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                                Column {
                                    Text(
                                        text = ".",
                                        style = TextStyle(
                                            fontSize = 30.sp,
                                            fontWeight = FontWeight(500),
                                            color = Color(0xFF333333)
                                        )
                                    )
                                }
                                Row(
                                    modifier = Modifier.weight(1f),
                                ) {
                                    ListNumberPicker(
                                        data = weightDot,
                                        selectIndex = weightDot.indexOf(selectedWeightDotLb)
                                            .takeIf { it != -1 } ?: 0,
                                        visibleCount = 3,
                                        modifier = Modifier
                                            .height(150.dp),
                                        onSelect = { _, item ->
                                            selectedWeightDotLb = item
                                        },
                                        align = Alignment.CenterStart
                                    ) {
                                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                                        if (it == selectedWeightDotLb) {
                                            Column(
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 30.sp,
                                                        fontWeight = FontWeight(500),
                                                        color = Color(0xFF333333)
                                                    )
                                                )
                                                HorizontalDivider(
                                                    modifier = Modifier
                                                        .width(89.dp),
                                                    color = Color.Gray
                                                )
                                            }
                                        } else {
                                            Column(
                                                modifier = Modifier.width(89.dp),
                                                horizontalAlignment = Alignment.CenterHorizontally,
                                            ) {
                                                Text(
                                                    text = "$it",
                                                    style = TextStyle(
                                                        fontSize = 26.sp,
                                                        fontWeight = FontWeight(400),
                                                        color = Color(0xFF999999)
                                                    )
                                                )
                                            }
                                        }
                                    }
                                }
                                Column(
                                    modifier = Modifier.weight(0.5f)
                                ) {
                                    Text(
                                        text = stringResource(R.string.weight_unit_lb),
                                        style = TextStyle(
                                            fontSize = 30.sp,
                                            fontWeight = FontWeight(500),
                                            color = Color(0xFF333333)
                                        )
                                    )
                                }

                            }

                        }

                        else -> {}
                    }
                }
            },
            confirmButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    AIHTextButton(
                        text = stringResource(id = R.string.confirm),
                        onClick = {
                            when (selected) {
                                "M" -> {
                                    val value =
                                        selectedWeightKg.toDouble() + selectedWeightDotKg.toDouble() / 10.0
                                    if (value < WEIGHT_KG_MIN_LIMIT || value > WEIGHT_KG_MAX_LIMIT) {
                                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_metric))
                                    } else {
                                        onWeightSelected(
                                            WeightUnit(
                                                value = value,
                                                unit = "M"
                                            )
                                        )
                                    }

                                }

                                else -> {
                                    val value =
                                        (selectedWeightLb + selectedWeightDotLb / 10.0).times(
                                            0.45359237
                                        )

                                    LogUtil.i("weight: ${value}, selectedWeightLb:${selectedWeightLbs}")
                                    if (value < WEIGHT_KG_MIN_LIMIT || value > WEIGHT_KG_MAX_LIMIT) {
                                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_imperial))
                                    } else {
                                        onWeightSelected(
                                            WeightUnit(
                                                value = value,
                                                unit = "I"
                                            )
                                        )
                                    }

                                }
                            }
                        },
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight(500),
                            color = Color.White
                        ),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                    )
                }
            },
            dismissButton = {
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = stringResource(id = R.string.cancel),
                        color = Color(0x70CECECE),
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .clickable { onDismiss() }
                    )
                }
            }
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun InputWeight(
    modifier: Modifier = Modifier,
    initialWeight: Double = 170.00,
    selectedUnit: String = "M",
    onWeightSelected: (WeightUnit) -> Unit = { _ -> }
) {
    val context = LocalContext.current
    val weightDot = (0..9).toList()
    val weightKg = (10..300).toList()
    val weightLb = (22..660).toList()
    var selectedWeightKg by remember(initialWeight) { mutableIntStateOf(initialWeight.toInt()) }
    var selectedWeightDotKg by remember(initialWeight) {
        mutableIntStateOf(
            (initialWeight % 1 * 10).roundToInt()
        )
    }
    var selectedWeightLb by remember(initialWeight) {
        mutableIntStateOf(
            (initialWeight * 2.**********).toInt()
        )
    }
    var selectedWeightDotLb by remember(initialWeight) {
        mutableIntStateOf(
            ((initialWeight * 2.********** % 1) * 10).roundToInt() % 10
        )
    }
    var expanded by remember { mutableStateOf(false) }
    var selected by remember(selectedUnit) {
        mutableStateOf(
            when (selectedUnit) {
                "M" -> context.getString(R.string.kilograms)
                "I" -> context.getString(R.string.pounds)
                else -> ""
            }
        )
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.weight),
            style = TextStyle(
                fontSize = 24.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
                textAlign = TextAlign.Center,
            )
        )

        ExposedDropdownMenuBox(
            modifier = Modifier
                .align(Alignment.End)
                .background(Color.White),
            expanded = expanded,
            onExpandedChange = { expanded = !it }
        ) {
            Card(
                modifier = Modifier
                    .height(32.dp)
                    .menuAnchor(),
                shape = RoundedCornerShape(size = 12.dp),
                elevation = CardDefaults.cardElevation(
                    defaultElevation = 1.dp,
                )
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxHeight()
                        .clickable { expanded = !expanded }
                        .align(Alignment.CenterHorizontally),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        modifier = Modifier.padding(start = 16.dp),
                        text = selected,
                        color = Color.Black,
                        fontWeight = FontWeight.Light,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center
                    )
                    Icon(
                        imageVector = Icons.Default.KeyboardArrowDown,
                        contentDescription = null,
                        tint = Color.Black,
                        modifier = Modifier
                            .padding(end = 16.dp)
                            .size(16.dp)
                    )
                }
            }
            // 下拉菜单
            DropdownMenu(
                modifier = Modifier,
                expanded = expanded,
                onDismissRequest = { expanded = false }
            ) {
                DropdownMenuItem(
                    modifier = Modifier,
                    text = {
                        Text(
                            text = stringResource(R.string.kilograms),
                            color = Color.Black,
                        )
                    },
                    onClick = {
                        expanded = false
                        selected = context.getString(R.string.kilograms)
                    },
                    colors = MenuDefaults.itemColors().copy(
                        textColor = Color.Black,

                        )
                )
                DropdownMenuItem(
                    modifier = Modifier,
                    text = { Text(stringResource(R.string.pounds)) },
                    onClick = {
                        expanded = false
                        selected = context.getString(R.string.pounds)
                    }
                )
            }
        }

        if (selected == stringResource(R.string.kilograms)) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                ) {
                    ListNumberPicker(
                        data = weightKg,
                        selectIndex = weightKg.indexOf(selectedWeightKg),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedWeightKg = item
                        },
                        align = Alignment.CenterEnd
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedWeightKg) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier
                                    .width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                )
                            }
                        }
                    }
                }
                Column {
                    Text(
                        text = ".",
                        style = TextStyle(
                            fontSize = 30.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333)
                        )
                    )
                }
                Row(
                    modifier = Modifier.weight(1f),
                ) {
                    ListNumberPicker(
                        data = weightDot,
                        selectIndex = weightDot.indexOf(selectedWeightDotKg),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedWeightDotKg = item
                        },
                        align = Alignment.CenterStart
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedWeightDotKg) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier.width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                )
                            }
                        }
                    }
                }
                Column(
                    modifier = Modifier.weight(0.5f)
                ) {
                    Text(
                        text = stringResource(R.string.weight_unit_kg),
                        style = TextStyle(
                            fontSize = 30.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333)
                        )
                    )
                }

            }
            AIHTextButton(
                text = stringResource(id = R.string.next_step),
                onClick = {
                    val value = selectedWeightKg.toDouble() + selectedWeightDotKg.toDouble() / 10.0
                    if (value < WEIGHT_KG_MIN_LIMIT || value > WEIGHT_KG_MAX_LIMIT) {
                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_metric))
                    } else {
                        onWeightSelected(
                            WeightUnit(
                                value = selectedWeightKg.toDouble() + selectedWeightDotKg.toDouble() / 10.0,
                                unit = "M"
                            )
                        )
                    }

                },
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 32.dp)
                    .fillMaxWidth(0.9f)
            )
        } else {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Row(
                    modifier = Modifier.weight(1f),
                ) {
                    ListNumberPicker(
                        data = weightLb,
                        selectIndex = weightLb.indexOf(selectedWeightLb),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedWeightLb = item
                        },
                        align = Alignment.CenterEnd
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedWeightLb) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier
                                    .width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                )
                            }
                        }
                    }
                }
                Column {
                    Text(
                        text = ".",
                        style = TextStyle(
                            fontSize = 30.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333)
                        )
                    )
                }
                Row(
                    modifier = Modifier.weight(1f),
                ) {
                    ListNumberPicker(
                        data = weightDot,
                        selectIndex = weightDot.indexOf(selectedWeightDotLb),
                        visibleCount = 3,
                        modifier = Modifier
                            .height(150.dp),
                        onSelect = { _, item ->
                            selectedWeightDotLb = item
                        },
                        align = Alignment.CenterStart
                    ) {
                        //判断是否是选中的状态，选中要展示的样式和非选中的样式
                        if (it == selectedWeightDotLb) {
                            Column(
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 30.sp,
                                        fontWeight = FontWeight(500),
                                        color = Color(0xFF333333)
                                    )
                                )
                                HorizontalDivider(
                                    modifier = Modifier
                                        .width(89.dp),
                                    color = Color.Gray
                                )
                            }
                        } else {
                            Column(
                                modifier = Modifier.width(89.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                            ) {
                                Text(
                                    text = "$it",
                                    style = TextStyle(
                                        fontSize = 26.sp,
                                        fontWeight = FontWeight(400),
                                        color = Color(0xFF999999)
                                    )
                                )
                            }
                        }
                    }
                }
                Column(
                    modifier = Modifier.weight(0.5f)
                ) {
                    Text(
                        text = stringResource(R.string.weight_unit_lb),
                        style = TextStyle(
                            fontSize = 30.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333)
                        )
                    )
                }

            }
            AIHTextButton(
                text = stringResource(id = R.string.next_step),
                onClick = {
                    val value = (selectedWeightLb + selectedWeightDotLb / 10.0) * 0.45359237
                    LogUtil.i("weight, value: ${value} selectedWeightLb: $selectedWeightLb, selectedWeightDotLb: $selectedWeightDotLb")

                    if (value < WEIGHT_KG_MIN_LIMIT || value > WEIGHT_KG_MAX_LIMIT) {
                        DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_imperial))
                    } else {
                        onWeightSelected(
                            WeightUnit(
                                value = value,
                                unit = "I"
                            )
                        )
                    }

                },
                modifier = Modifier
                    .padding(top = 10.dp, bottom = 32.dp)
                    .fillMaxWidth(0.9f)
            )
        }
    }
}

@Composable
fun WeightPickerWithInputDialog(
    visible: Boolean = true,
    initialWeight: Double = 10.0,
    selectedUnit: String = "I",
    onWeightSelected: (WeightUnit) -> Unit = { _ -> },
    onDismiss: () -> Unit = {}
) {
    val context = LocalContext.current
    val locale = context.resources.configuration.locales[0]

    var selectedOption by remember {
        mutableStateOf(
            when (selectedUnit) {
                "I" -> "lb"
                "M" -> "kg"
                else -> "lb"
            }
        )
    }
    LogUtil.i("selectedOption:$selectedOption")
    val weightKg = (10..300).toList()
    val weightLb = (22..661).toList()

    var weightInput by remember(initialWeight) {
        mutableDoubleStateOf(initialWeight)
    }
    var mTextFieldInput by remember(weightInput) { mutableStateOf(weightInput.toString()) }
    var iTextFieldInput by remember(weightInput) { mutableStateOf((weightInput * 2.**********).toString()) }

    if (visible) {
        Dialog(
            onDismissRequest = { onDismiss() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            val keyboardController = LocalSoftwareKeyboardController.current
            Column(
                modifier = Modifier
                    .background(Color.White, RoundedCornerShape(16.dp))
                    .fillMaxWidth(0.98f)
                    .padding(horizontal = 12.dp)
            ) {
                // close icon
                Row(
                    modifier = Modifier
                        .padding(top = 12.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.End
                ) {
                    Icon(
                        modifier = Modifier
                            .size(24.dp)
                            .clickable {
                                onDismiss()
                            },
                        painter = painterResource(R.drawable.ic_close),
                        contentDescription = "close",
                        tint = Color.Black,
                    )
                }
                // title
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(Color.White)
                        .padding(top = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.weight),
                        style = TextStyle(
                            fontSize = 20.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(end = 24.dp)
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "lb",
                        selected = selectedOption == "lb",
                        onClick = {
                            selectedOption = "lb"
                        }
                    )
                    CustomChip(
                        modifier = Modifier.padding(start = 8.dp, end = 4.dp),
                        text = "kg",
                        selected = selectedOption == "kg",
                        onClick = {
                            selectedOption = "kg"
                        }
                    )
                }
                Column(
                    modifier = Modifier,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    when (selectedOption) {
                        "kg" -> {
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 16.dp),
                                value = if (mTextFieldInput.toDoubleOrNull() == null
                                    || mTextFieldInput.last() == '.'
                                    || mTextFieldInput.toIntOrNull() != null
                                ) {
                                    mTextFieldInput
                                } else {
                                    String.format("%.1f", mTextFieldInput.toDouble())
                                },
                                onValueChange = { newText ->
                                    // 仅保留数字字符
                                    if (newText.isEmpty()) {
                                        mTextFieldInput = ""
                                    } else if (newText.toDoubleOrNull() != null) {
                                        mTextFieldInput = newText
                                    }
                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        keyboardController?.hide()
                                        weightInput =
                                            if (mTextFieldInput.toDoubleOrNull() != null) mTextFieldInput.toDouble()
                                            else 0.0
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            HorizontalDataSelector(
                                modifier = Modifier.padding(top = 68.dp),
                                items = weightKg,
                                initialData = when (weightInput) {
                                    in (weightKg.first() * 1.0..weightKg.last() * 1.0) -> {
                                        weightInput
                                    }

                                    else -> {
                                        when {
                                            weightInput < weightKg.first() * 1.0 -> weightKg.first() * 1.0
                                            else -> weightKg.last() * 1.0
                                        }
                                    }
                                },
                                unit = "kg"
                            ) { weight ->
                                LogUtil.i("select item :${weight}")
                                weightInput = weight
                            }
                        }

                        "lb" -> {
                            // data input
                            TextField(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 16.dp),
                                value = if (iTextFieldInput.toDoubleOrNull() == null
                                    || iTextFieldInput.last() == '.'
                                    || iTextFieldInput.toIntOrNull() != null
                                ) {
                                    iTextFieldInput
                                } else {
                                    String.format("%.1f", iTextFieldInput.toDouble())
                                },
                                onValueChange = { newText ->
                                    if (newText.isEmpty()) {
                                        iTextFieldInput = ""
                                    } else if (newText.toDoubleOrNull() != null) {
                                        iTextFieldInput = newText
                                    }
                                    LogUtil.i("newText$newText, iTextFieldInput$iTextFieldInput")

                                },
                                shape = RoundedCornerShape(24.dp),
                                maxLines = 1,
                                keyboardOptions = KeyboardOptions().copy(
                                    imeAction = ImeAction.Done,
                                    keyboardType = KeyboardType.Number
                                ),
                                keyboardActions = KeyboardActions(
                                    onDone = {
                                        keyboardController?.hide()
                                        weightInput =
                                            if (iTextFieldInput.toDoubleOrNull() != null) iTextFieldInput.toDouble() * 0.45359237
                                            else 0.0
                                    }
                                ),
                                colors = TextFieldDefaults.colors().copy(
                                    focusedTextColor = Color.Black,
                                    focusedContainerColor = Color(0x1FC7CBD6),
                                    disabledTextColor = Color.Black,
                                    unfocusedContainerColor = Color(0x1FC7CBD6),
                                    disabledIndicatorColor = Color.Transparent,
                                    focusedIndicatorColor = Color.Transparent,
                                    unfocusedIndicatorColor = Color.Transparent,
                                    disabledContainerColor = Color(0x1FC7CBD6),
                                )
                            )
                            HorizontalDataSelector(
                                modifier = Modifier.padding(top = 68.dp),
                                items = weightLb,
                                initialData = when (weightInput * 2.**********) {
                                    in (weightLb.first() * 1.0..weightLb.last() * 1.0) -> {
                                        weightInput * 2.**********
                                    }

                                    else -> {
                                        when {
                                            weightInput * 2.********** < weightLb.first() * 1.0 -> weightLb.first() * 1.0
                                            else -> weightLb.last() * 1.0
                                        }
                                    }
                                },
                                unit = "lb"
                            ) { weight ->
                                LogUtil.i("select item :${weight}")
                                weightInput = (weight * 0.45359237)
                            }

                        }

                        else -> {}
                    }
                }
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 68.dp, bottom = 42.dp),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    Box(
                        Modifier
                            .weight(1f)
                            .border(
                                width = 1.dp,
                                color = Color(0xFF3262FF),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .background(
                                shape = RoundedCornerShape(size = 16.dp),
                                color = Color.White
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable { onDismiss() }
                    ) {
                        Text(
                            text = stringResource(id = R.string.cancel),
                            modifier = Modifier
                                .align(Alignment.Center)
                                .clickable { onDismiss() },
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color(0xFF3C72FF),
                            )
                        )
                    }
                    Spacer(modifier = Modifier.size(15.dp))
                    Box(
                        Modifier
                            .weight(1f)
                            .background(
                                Brush.linearGradient(
                                    colors = listOf(
                                        Color(0XFF73C5FF),
                                        Color(0XFF3161FF),
                                    )
                                ),
                                shape = RoundedCornerShape(size = 16.dp)
                            )
                            .wrapContentHeight()
                            .heightIn(min = 40.dp)
                            .clickable {
                                when (selectedOption) {
                                    "kg" -> {
                                        if (weightInput < WEIGHT_KG_MIN_LIMIT || weightInput > WEIGHT_KG_MAX_LIMIT) {
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_metric))
                                        } else {
                                            onWeightSelected(
                                                WeightUnit(
                                                    value = weightInput,
                                                    unit = "M"
                                                )
                                            )
                                        }
                                    }

                                    else -> {
                                        if (weightInput * 2.********** < WEIGHT_LB_MIN_LIMIT || weightInput * 2.********** > WEIGHT_LB_MAX_LIMIT) {
                                            LogUtil.i("weightInput:$weightInput,${weightInput * 2.**********}")
                                            DialogUtil.showToast(context.getString(R.string.personal_data_limit_of_weight_imperial))
                                        } else {
                                            onWeightSelected(
                                                WeightUnit(
                                                    value = weightInput,
                                                    unit = "I"
                                                )
                                            )
                                        }

                                    }
                                }
                            }
                    ) {
                        Text(
                            modifier = Modifier.align(Alignment.Center),
                            text = stringResource(id = R.string.confirm),
                            style = TextStyle(
                                fontSize = 20.sp,
                                fontWeight = FontWeight(500),
                                color = Color.White
                            )
                        )
                    }

                }
            }
        }
    }
}
