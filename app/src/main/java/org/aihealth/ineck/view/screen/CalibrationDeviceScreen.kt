package org.aihealth.ineck.view.screen

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.navController
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.util.popScreen
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.impl.ConnectUiState

/**
 *  校准页
 */
@Composable
fun CalibrationDeviceScreen(
    viewModel: MainViewModel,
) {
    if (viewModel.homeScreen.currentDeviceType != DeviceType.KneeJoint) {
        CalibrationDevice(viewModel)
    } else {
        CalibrationKnee(viewModel)
    }
}

@Composable
fun CalibrationDevice(viewModel: MainViewModel) {
    val uiState by viewModel.deviceScreen.uiState.collectAsState()
    var showConnectedDialog by remember {
        mutableStateOf(false)
    }
    var showConnectFailDialog by remember {
        mutableStateOf(false)
    }

    var isStart by remember {
        mutableStateOf(false)
    }
    var tipIndex by remember {
        mutableIntStateOf(0)
    }
    var calibrationType by remember {
        mutableStateOf(CalibrationType.HeadUp)
    }
    var isCalibrationSuccess by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(uiState) {
        if (uiState is ConnectUiState.Connecting) {
            DialogUtil.showLoading()
        } else {
            DialogUtil.hideLoading()
        }
        if (uiState is ConnectUiState.ConnectingSuccess) {
            showConnectedDialog = true
        }
        if (uiState is ConnectUiState.ConnectingError) {
            showConnectFailDialog = true
        }
    }

    if (showConnectedDialog) {
        ConnectedDialog(
            onDismiss = {
                viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                showConnectedDialog = false
                startScreen(Screen.FormatDevice.route, true)
            },
            onConfirm = {
                viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                showConnectedDialog = false
                startScreen(Screen.CalibrationDevice.route, true)
            }
        )
    }
    if (showConnectFailDialog) {
        if (uiState is ConnectUiState.ConnectingError) {
            val uiError = uiState as ConnectUiState.ConnectingError
            LogUtil.d("state: $uiError")
            if (uiError.visibility && uiError.res != null && uiError.address != null && uiError.deviceType != null) {
                ConnectedFailDialog(
                    title = stringResource(id = uiError.res, uiError.deviceType),
                    onDismiss = {
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        showConnectFailDialog = false
                        popScreen(Screen.Main.route)
                    },
                    onConfirm = {
                        showConnectFailDialog = false
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        viewModel.deviceScreen.clearConnectedData(viewModel.homeScreen.currentDeviceType.name)
                        viewModel.deviceScreen.connectRemoteGatt(
                            uiError.address,
                            uiError.deviceType
                        )
                    }
                )
            }
        }
    }
    val imageUpRes = when (viewModel.homeScreen.currentDeviceType) {
        DeviceType.aiNeck -> R.drawable.img_calibration_headup_aineck
        DeviceType.aiBack -> R.drawable.img_calibration_headup_aiback
        DeviceType.KneeJoint -> R.drawable.knee_0
        DeviceType.ShoulderJoint -> R.drawable.shoulder_0
        DeviceType.ElbowJoint -> R.drawable.elbow_0
        DeviceType.HipJoint -> R.drawable.hip_0
        else -> R.drawable.img_calibration_headup_aineck
    }
    val imageDownRes = when (viewModel.homeScreen.currentDeviceType) {
        DeviceType.aiNeck -> calibrationType.aiNeckDrawable
        DeviceType.aiBack -> calibrationType.aiBackDrawable
        DeviceType.KneeJoint -> calibrationType.aiKneeDrawable
        DeviceType.ShoulderJoint -> calibrationType.aiShoulderDrawable
        DeviceType.ElbowJoint -> calibrationType.aiElbowDrawable
        DeviceType.HipJoint -> calibrationType.aiHipDrawable
        else -> calibrationType.aiNeckDrawable
    }
    BasePageView(
        title = stringResource(id = R.string.device_calibration),
        showBackIcon = true
    ) {
        LaunchedEffect(isStart, calibrationType) {
            if (isStart) {
                if (calibrationType == CalibrationType.HeadUp) {
                    if (viewModel.deviceScreen.onHeadUpCalibrating(viewModel.homeScreen.currentDeviceType.name)) {
                        tipIndex = 0
                        calibrationType = CalibrationType.BendNeck
                    } else {
                        calibrationType = CalibrationType.HeadUpFailed
                    }
                } else if (calibrationType == CalibrationType.BendNeck) {
                    launch(Dispatchers.Default) {
                        if (viewModel.deviceScreen.onBendNeckCalibrating(viewModel.homeScreen.currentDeviceType.name)) {
                            isCalibrationSuccess = true
                        } else {
                            calibrationType = CalibrationType.BendNeckFailed
                        }
                    }
                }
            }
        }
        LazyColumn(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
            if (!isStart) {
                item {
                    val speechText =
                        stringResource(id = R.string.device_calibration_unstart_tip)
                    DisposableEffect(Unit) {
                        TextToSpeech.ttsSpeaking(speechText)
                        onDispose {
                            TextToSpeech.ttsStop()
                        }
                    }
                    Text(
                        text = stringResource(id = R.string.device_calibration_unstart),
                        fontSize = 24.sp,
                        color = Color.Black,
                        modifier = Modifier
                            .padding(vertical = 28.dp, horizontal = 20.dp)
                    )
                    Image(
                        painter = painterResource(id = imageUpRes),
                        contentDescription = null,
                        modifier = Modifier.size(318.dp)
                    )
                    Spacer(modifier = Modifier.height(23.dp))
                    Text(
                        text = stringResource(id = R.string.device_calibration_unstart_tip),
                        fontSize = 16.sp,
                        color = Color(0XFF444444),
                        modifier = Modifier
                            .fillMaxWidth(0.9f)
                    )
                }
            } else {
                item {
                    val firstTipList = when (viewModel.homeScreen.currentDeviceType) {
                        DeviceType.aiNeck -> stringArrayResource(id = R.array.calibration_first_tip_aineck)
                        DeviceType.aiBack -> stringArrayResource(id = R.array.calibration_first_tip_aiback)
                        DeviceType.ElbowJoint -> stringArrayResource(id = R.array.calibration_first_tip_elbow_joint)
                        DeviceType.KneeJoint -> stringArrayResource(id = R.array.calibration_first_tip_knee_joint)
                        DeviceType.HipJoint -> stringArrayResource(id = R.array.calibration_first_tip_hip_joint)
                        DeviceType.ShoulderJoint -> stringArrayResource(id = R.array.calibration_first_tip_shoulder_joint)
                        else -> stringArrayResource(id = R.array.calibration_first_tip_aineck)
                    }
                    val secondTipList = when (viewModel.homeScreen.currentDeviceType) {
                        DeviceType.aiNeck -> stringArrayResource(id = R.array.calibration_second_tip_aineck)
                        DeviceType.aiBack -> stringArrayResource(id = R.array.calibration_second_tip_aiback)
                        DeviceType.ElbowJoint -> stringArrayResource(id = R.array.calibration_second_tip_elbow_joint)
                        DeviceType.KneeJoint -> stringArrayResource(id = R.array.calibration_second_tip_knee_joint)
                        DeviceType.HipJoint -> stringArrayResource(id = R.array.calibration_second_tip_hip_joint)
                        DeviceType.ShoulderJoint -> stringArrayResource(id = R.array.calibration_second_tip_shoulder_joint)
                        else -> stringArrayResource(id = R.array.calibration_second_tip_aineck)
                    }
                    val tip by remember {
                        derivedStateOf {
                            when (calibrationType) {
                                CalibrationType.HeadUp -> firstTipList[tipIndex % 3]
                                CalibrationType.BendNeck -> if (!viewModel.deviceScreen.isSecondCalibratedSuccess) {
                                    secondTipList[tipIndex % 3]
                                } else {
                                    tipIndex = 0
                                    secondTipList[tipIndex]
                                }

                                else -> ""
                            }
                        }
                    }
                    LaunchedEffect(Unit) {
                        while (true) {
                            delay(3000)
                            tipIndex++
                        }
                    }
                    var firstSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    var secondSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    LaunchedEffect(tip) {
                        if (tip == firstTipList[0] && firstSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            firstSpeechEnabled = false
                        }
                        if (tip == secondTipList[0] && secondSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            secondSpeechEnabled = false
                        }
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 43.dp)
                            .padding(horizontal = 16.dp),
                    ) {
                        Text(
                            text = tip,
                            fontSize = 24.sp,
                            color = if (calibrationType == CalibrationType.HeadUp || calibrationType == CalibrationType.BendNeck) Color.Black else Color(
                                0XFFCC4141
                            ),
                            textAlign = TextAlign.Start,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    Image(
                        painter = painterResource(id = imageDownRes),
                        contentDescription = null,
                        modifier = Modifier.size(318.dp)
                    )
                }
            }
            item {
                Spacer(modifier = Modifier.height(50.dp))
                AIHButton(
                    text = stringResource(
                        id = if (!isStart) {
                            R.string.device_start_calibration
                        } else if (calibrationType == CalibrationType.HeadUp || calibrationType == CalibrationType.BendNeck) {
                            R.string.device_calibrating
                        } else {
                            R.string.device_recalibrate
                        }
                    ),
                    onClick = {
                        if (!isStart) {
                            isStart = true
                        } else if (calibrationType == CalibrationType.HeadUpFailed) {
                            calibrationType = CalibrationType.HeadUp
                        } else if (calibrationType == CalibrationType.BendNeckFailed) {
                            calibrationType = CalibrationType.BendNeck
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth(0.9f)
                )
                Spacer(modifier = Modifier.height(50.dp))
            }

        }

    }
    if (isCalibrationSuccess) {
        FinishedCalibration(
            onClick = {
                MainViewModel.pageIndex = 1
                isCalibrationSuccess = false
                navController.popBackStack(Screen.Main.route, false)
            }
        )
    }

    DisposableEffect(Unit) {
        LogUtil.d("init state ${uiState}")
        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
        onDispose {
            viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
            TextToSpeech.ttsStop()
        }
    }
}

@Preview()
@Composable
fun FinishedCalibration(
    onClick: () -> Unit = {}
) {
    Dialog(onDismissRequest = {
        onClick()
    }) {
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                Modifier
                    .align(Alignment.Center),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Box(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.img_calibration_dialog),
                        contentDescription = null,
                        modifier = Modifier.fillMaxWidth(),
                        contentScale = ContentScale.FillWidth
                    )
                    Text(
                        text = stringResource(id = R.string.device_calibration_success),
                        fontSize = 20.sp,
                        color = Color.Black,
                        textAlign = TextAlign.Start,
                        modifier = Modifier
                            .padding(top = 73.dp)
                            .padding(horizontal = 16.dp)
                            .align(Alignment.Center)
                    )
                }
                Spacer(modifier = Modifier.height(30.dp))
                AIHButton(
                    text = stringResource(id = R.string.finish),
                    onClick = { onClick() },
                    modifier = Modifier
                        .width(256.dp),
                    fontSize = 20.sp,
                    fontColor = Color.White
                )
            }

        }
    }
}

@Composable
fun CalibrationKnee(viewModel: MainViewModel) {
    val context = LocalContext.current
    val uiState by viewModel.deviceScreen.uiState.collectAsState()
    var showConnectedDialog by remember {
        mutableStateOf(false)
    }
    var showConnectFailDialog by remember {
        mutableStateOf(false)
    }

    var isStart by remember {
        mutableStateOf(false)
    }
    var tipIndex by remember {
        mutableIntStateOf(0)
    }
    var calibrationType by remember {
        mutableStateOf(CalibrationKneeType.InitialState)
    }
    var isCalibrationSuccess by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(uiState) {
        if (uiState is ConnectUiState.Connecting) {
            DialogUtil.showLoading()
        } else {
            DialogUtil.hideLoading()
        }
        if (uiState is ConnectUiState.ConnectingSuccess) {
            showConnectedDialog = true
        }
        if (uiState is ConnectUiState.ConnectingError) {
            showConnectFailDialog = true
        }
    }

    if (showConnectedDialog) {
        ConnectedDialog(
            onDismiss = {
                viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                showConnectedDialog = false
                startScreen(Screen.FormatDevice.route, true)
            },
            onConfirm = {
                viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                showConnectedDialog = false
                startScreen(Screen.CalibrationDevice.route, true)
            }
        )
    }
    if (showConnectFailDialog) {
        if (uiState is ConnectUiState.ConnectingError) {
            val uiError = uiState as ConnectUiState.ConnectingError
            LogUtil.d("state: $uiError")
            if (uiError.visibility && uiError.res != null && uiError.address != null && uiError.deviceType != null) {
                ConnectedFailDialog(
                    title = stringResource(id = uiError.res, uiError.deviceType),
                    onDismiss = {
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        showConnectFailDialog = false
                        popScreen(Screen.Main.route)
                    },
                    onConfirm = {
                        showConnectFailDialog = false
                        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
                        viewModel.deviceScreen.clearConnectedData(viewModel.homeScreen.currentDeviceType.name)
                        viewModel.deviceScreen.connectRemoteGatt(
                            uiError.address,
                            uiError.deviceType
                        )
                    }
                )
            }
        }
    }
    val imageOriginRes = R.drawable.img_calibration_headup_knee
    val imageDownRes = calibrationType.kneeDrawable

    BasePageView(
        title = stringResource(id = R.string.device_calibration),
        showBackIcon = true
    ) {
        LaunchedEffect(isStart, calibrationType) {
            if (isStart) {
                if (calibrationType == CalibrationKneeType.InitialState) {
                    if (viewModel.deviceScreen.onHeadUpCalibrating(viewModel.homeScreen.currentDeviceType.name)) {
                        tipIndex = 0
                        calibrationType = CalibrationKneeType.StretchState
                    } else {
                        calibrationType = CalibrationKneeType.FailedState
                    }
                } else if (calibrationType == CalibrationKneeType.StretchState) {
                    tipIndex = 0
                    if (viewModel.deviceScreen.onBendNeckCalibrating(viewModel.homeScreen.currentDeviceType.name)) {
                        calibrationType = CalibrationKneeType.BucklingState
                        viewModel.deviceScreen.recordKneeMaxAngle()
                    } else {
                        calibrationType = CalibrationKneeType.FailedState
                    }
                } else if (calibrationType == CalibrationKneeType.BucklingState) {
                    tipIndex = 0
                    launch {
                        delay(4000)
                        viewModel.deviceScreen.recordKneeMinAngle()
                        isCalibrationSuccess = true
                    }
                }
            }
        }
        LazyColumn(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
            if (!isStart) {
                item {
                    val speechText =
                        stringResource(id = R.string.device_calibration_unstart_tip)
                    DisposableEffect(Unit) {
                        TextToSpeech.ttsSpeaking(speechText)
                        onDispose {
                            TextToSpeech.ttsStop()
                        }
                    }
                    Text(
                        text = stringResource(id = R.string.device_calibration_unstart),
                        fontSize = 24.sp,
                        color = Color.Black,
                        modifier = Modifier
                            .padding(vertical = 28.dp, horizontal = 20.dp)
                    )
                    Image(
                        painter = painterResource(id = imageOriginRes),
                        contentDescription = null,
                        modifier = Modifier.size(318.dp)
                    )
                    Spacer(modifier = Modifier.height(23.dp))
                    Text(
                        text = stringResource(id = R.string.device_calibration_unstart_tip),
                        fontSize = 16.sp,
                        color = Color(0XFF444444),
                        modifier = Modifier
                            .width(252.dp)
                    )
                }
            } else {
                item {
                    val firstTipList =
                        stringArrayResource(id = R.array.calibration_first_tip_knee_joint)
                    val secondTipList =
                        stringArrayResource(id = R.array.calibration_second_tip_knee_joint)
                    val thirdTipList =
                        stringArrayResource(id = R.array.calibration_third_tip_knee_joint)

                    val tip by remember {
                        derivedStateOf {
                            when (calibrationType) {
                                CalibrationKneeType.InitialState -> firstTipList[tipIndex % 3]
                                CalibrationKneeType.StretchState -> if (!viewModel.deviceScreen.isSecondCalibratedSuccess) {
                                    secondTipList[tipIndex % 3]
                                } else {
                                    tipIndex = 0
                                    secondTipList[tipIndex]
                                }

                                CalibrationKneeType.BucklingState -> thirdTipList[tipIndex % 3]
                                else -> context.getString(R.string.failed)
                            }
                        }
                    }
                    LaunchedEffect(Unit) {
                        while (true) {
                            delay(500)
                            tipIndex++
                        }
                    }
                    var firstSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    var secondSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    var thirdSpeechEnabled by remember {
                        mutableStateOf(true)
                    }
                    LaunchedEffect(tip) {
                        if (tip == firstTipList[0] && firstSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            firstSpeechEnabled = false
                        }
                        if (tip == secondTipList[0] && secondSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            secondSpeechEnabled = false
                        }
                        if (tip == thirdTipList[0] && thirdSpeechEnabled) {
                            TextToSpeech.ttsSpeaking(tip)
                            thirdSpeechEnabled = false
                        }
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 43.dp)
                            .padding(horizontal = 16.dp)
                            .height(86.dp),
                    ) {
                        Text(
                            text = tip,
                            fontSize = 24.sp,
                            color = if (calibrationType != CalibrationKneeType.FailedState) Color.Black else Color(
                                0XFFCC4141
                            ),
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxWidth()
                        )
                    }

                    Image(
                        painter = painterResource(id = imageDownRes),
                        contentDescription = null,
                        modifier = Modifier.size(318.dp)
                    )
                }
            }
            item {
                Spacer(modifier = Modifier.height(50.dp))
                AIHButton(
                    text = stringResource(
                        id = if (!isStart) {
                            R.string.device_start_calibration
                        } else if (calibrationType != CalibrationKneeType.FailedState) {
                            R.string.device_calibrating
                        } else {
                            R.string.device_recalibrate
                        }
                    ),
                    onClick = {
                        if (!isStart) {
                            isStart = true
                            calibrationType = CalibrationKneeType.InitialState
                        }
                    },
                    modifier = Modifier
                        .size(278.dp, 42.dp)
                )
                Spacer(modifier = Modifier.height(50.dp))
            }

        }

    }
    if (isCalibrationSuccess) {
        Dialog(onDismissRequest = { }) {
            Box(modifier = Modifier.fillMaxSize()) {
                Column(
                    Modifier
                        .align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Box(
                        modifier = Modifier
                            .size(256.dp, 290.dp)
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.img_calibration_dialog),
                            contentDescription = null,
                            modifier = Modifier.size(256.dp, 290.dp)
                        )
                        Text(
                            text = stringResource(id = R.string.device_calibration_success),
                            fontSize = 20.sp,
                            color = Color.Black,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .padding(top = 73.dp)
                                .width(200.dp)
                                .align(Alignment.Center)
                        )
                    }
                    Spacer(modifier = Modifier.height(30.dp))
                    AIHButton(
                        text = stringResource(id = R.string.finish),
                        onClick = {
//                            userSP.edit().putBoolean(viewModel.deviceScreen.deviceConfig.sn, true)
//                                .apply()
                            MainViewModel.pageIndex = 1
                            isCalibrationSuccess = false
                            navController.popBackStack(Screen.Main.route, false)

                        },
                        modifier = Modifier
                            .size(256.dp, 40.dp),
                        fontSize = 20.sp,
                        fontColor = Color.White
                    )
                }

            }
        }
    }

    DisposableEffect(Unit) {
        LogUtil.d("init state ${uiState}")
        viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
        onDispose {
            viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
            TextToSpeech.ttsStop()
        }
    }
}

/**
 * 设备校准过程中的类别
 */
private enum class CalibrationType(
    @DrawableRes val aiNeckDrawable: Int = 0,
    @DrawableRes val aiBackDrawable: Int = 0,
    @DrawableRes val aiKneeDrawable: Int = 0,
    @DrawableRes val aiShoulderDrawable: Int = 0,
    @DrawableRes val aiElbowDrawable: Int = 0,
    @DrawableRes val aiHipDrawable: Int = 0,
) {
    /**
     * 抬头状态
     */
    HeadUp(
        R.drawable.img_calibration_headup_aineck,
        R.drawable.img_calibration_headup_aiback,
        R.drawable.knee_0,
        R.drawable.shoulder_0,
        R.drawable.elbow_0,
        R.drawable.hip_0
    ),

    /**
     * 弯曲颈部状态
     */
    BendNeck(
        R.drawable.img_calibration_bendneck,
        R.drawable.img_calibration_bendback,
        R.drawable.knee_80,
        R.drawable.shoulder_90,
        R.drawable.elbow_80,
        R.drawable.hip_90
    ),

    /**
     * 抬头校准失败
     */
    HeadUpFailed(
        R.drawable.img_calibration_headup_aineck,
        R.drawable.img_calibration_headup_aiback,
        R.drawable.knee_unconnect,
        R.drawable.shoulder_unconnect,
        R.drawable.elbow_unconnect,
        R.drawable.hip_unconnect

    ),

    /**
     * 弯曲颈部校准失败
     */
    BendNeckFailed(
        R.drawable.img_calibration_headup_aineck,
        R.drawable.img_calibration_headup_aiback,
        R.drawable.knee_unconnect,
        R.drawable.shoulder_unconnect,
        R.drawable.elbow_unconnect,
        R.drawable.hip_unconnect
    )

}

/**
 * Knee 设备校准状态
 */
private enum class CalibrationKneeType(
    @DrawableRes val kneeDrawable: Int = 0,
) {
    InitialState(
        R.drawable.knee_0
    ),
    StretchState(
        R.drawable.knee_80
    ),
    BucklingState(
        R.drawable.knee_p15
    ),
    FailedState(
        R.drawable.knee_unconnect
    )
}