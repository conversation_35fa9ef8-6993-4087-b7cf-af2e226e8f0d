package org.aihealth.ineck.view.screen.meeting

import us.zoom.sdk.ZoomVideoSDK
import us.zoom.sdk.ZoomVideoSDKUser

class UserHelper {
    companion object {
        fun getAllUsers(): List<ZoomVideoSDKUser> {
            val userList = mutableListOf<ZoomVideoSDKUser>()
            val sdkSession = ZoomVideoSDK.getInstance().session
            if (sdkSession == null) {
                return userList
            }
            sdkSession.mySelf?.let {
                userList.add(it)
            }
            userList.addAll(sdkSession.remoteUsers)
            return userList
        }
    }
}