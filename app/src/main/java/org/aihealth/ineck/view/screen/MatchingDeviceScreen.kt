package org.aihealth.ineck.view.screen

import android.annotation.SuppressLint
import android.bluetooth.BluetoothDevice
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.delay
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.util.toSp
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHOutlinedButton
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.dialog.LoadingDialog
import org.aihealth.ineck.view.directions.MatchingDeviceDirections
import org.aihealth.ineck.view.screen.device.ConnectedDeviceItem
import org.aihealth.ineck.view.screen.device.ConnectedDevices
import org.aihealth.ineck.viewmodel.MainViewModel

@Composable
fun MatchingRoute(
    viewModel: MainViewModel,
    deviceType: String
) {
    val scanUiState by viewModel.deviceScreen.scanUiState.collectAsStateWithLifecycle()

    val deviceConfig = when (viewModel.homeScreen.currentDeviceType) {
        DeviceType.aiNeck -> viewModel.deviceScreen.aiNeckDeviceConfig
        DeviceType.aiBack -> viewModel.deviceScreen.aiBackDeviceConfig
        DeviceType.KneeJoint -> viewModel.deviceScreen.kneeJointDeviceConfig
        DeviceType.ElbowJoint -> viewModel.deviceScreen.elbowJointDeviceConfig
        DeviceType.ShoulderJoint -> viewModel.deviceScreen.shoulderJointDeviceConfig
        DeviceType.HipJoint -> viewModel.deviceScreen.hipJointDeviceConfig
        else -> viewModel.deviceScreen.aiNeckDeviceConfig
    }

    var isTimeOut by remember {
        mutableStateOf(false)
    }

    LaunchedEffect(Unit) {
        delay(60000)
        if (scanUiState.foundDevices.isEmpty()) {
            if (scanUiState.isScanning) {
                viewModel.deviceScreen.stopScanDevice()
            }
            isTimeOut = true
        }
    }

    MatchingDeviceScreen(
        deviceConfig = deviceConfig,
        deviceType = deviceType,
        devices = scanUiState.foundDevices,
        isTimeOut = isTimeOut,
        connectDevice = { mac ->
            viewModel.deviceScreen.stopScanDevice()
            viewModel.deviceScreen.clearConnectedData(viewModel.homeScreen.currentDeviceType.name)
            viewModel.deviceScreen.resetUiState(viewModel.homeScreen.currentDeviceType.name)
            startScreen(Screen.DeviceConnect.route + "?mac=$mac&deviceType=$deviceType", true)
//            viewModel.deviceScreen.connectRemoteGatt(mac, deviceType)
        }
    )


    DisposableEffect(Unit) {
        viewModel.deviceScreen.onScanDevice()
        onDispose {
            if (scanUiState.isScanning) {
                viewModel.deviceScreen.stopScanDevice()
            }
        }
    }
}

@Composable
fun MatchingDeviceScreen(
    deviceConfig: DeviceConfig,
    deviceType: String,
    devices: List<BluetoothDevice>,
    isTimeOut: Boolean,
    connectDevice: (String) -> Unit = {},
) {
    var onClickTime by remember {
        mutableLongStateOf(0L)
    }
    BasePageView(
        title = stringResource(id = R.string.matching_device),
        showBackIcon = true
    ) {
        Column(Modifier.fillMaxSize()) {
            if (deviceConfig.mac.isNotEmpty()) {
                ConnectedDevices(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .fillMaxWidth()
                        .verticalScroll(rememberScrollState()),
                    devices = listOf(deviceConfig),
                    onClick = {
                        if (System.currentTimeMillis() - onClickTime > 5000L) {
                            LogUtil.i("onClick mac:$it")
                            onClickTime = System.currentTimeMillis()
                            connectDevice(it)
                        }
                    }
                )
            }
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                if (devices.isNotEmpty()) {
                    FindDevice(
                        connectDevice = connectDevice,
                        devices = devices
                    )
                } else {
                    if (isTimeOut) {
                        LookForDeviceOvertime(deviceType)
                    } else {
                        LookForDevice()
                    }
                }
            }
        }
    }
}

@Preview()
@Composable
fun ConnectedDialog(
    onDismiss: () -> Unit = {},
    onConfirm: () -> Unit = {}
) {
    Dialog(
        onDismissRequest = { onDismiss() },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 17.dp))
                .padding(bottom = 15.dp, top = 16.dp)
                .padding(horizontal = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                modifier = Modifier.size(40.dp),
                painter = painterResource(id = R.drawable.icon_ok),
                contentDescription = "ok"
            )
            Text(
                modifier = Modifier
                    .padding(top = 16.dp),
                text = stringResource(id = R.string.device_connect_ok_desc),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                )
            )

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(IntrinsicSize.Max)
                    .padding(vertical = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {

            AIHButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .fillMaxHeight(),
                    text = stringResource(id = R.string.next_continue),
                    onClick = {
                        onConfirm()
                    }
                )
            }
        }
    }
}

@Composable
fun ConnectedFailDialog(
    title: String = "aineck",
    onDismiss: () -> Unit = {},
    onConfirm: () -> Unit = {}
) {
    Dialog(
        onDismissRequest = { onDismiss() },
        properties = DialogProperties(
            dismissOnBackPress = true,
            dismissOnClickOutside = false,
            usePlatformDefaultWidth = false,
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(0.9f)
                .background(color = Color(0xFFFFFFFF), shape = RoundedCornerShape(size = 17.dp))
                .padding(start = 16.dp, end = 15.dp, top = 16.dp, bottom = 16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Image(
                modifier = Modifier.size(40.dp),
                painter = painterResource(id = R.drawable.icon_error),
                contentDescription = "ok"
            )
            Text(
                modifier = Modifier
                    .padding(top = 16.dp),
                text = title,
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF333333),
                    textAlign = TextAlign.Start
                )
            )

            Row(
                modifier = Modifier
                    .height(IntrinsicSize.Max)
                    .padding(vertical = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                AIHOutlinedButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .fillMaxHeight(),
                    text = stringResource(id = R.string.cancel),
                    onClick = {
                        onDismiss()
                    }
                )
                AIHButton(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .fillMaxHeight(),
                    text = stringResource(id = R.string.reconnect),
                    onClick = {
                        onConfirm()
                    }
                )
            }
        }
    }
}

@Composable
fun ConnectedDevices(
    modifier: Modifier = Modifier,
    devices: List<DeviceConfig>,
    onClick: (String) -> Unit = {}
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        devices.forEach {
            ConnectedDeviceItem(
                modifier = Modifier.fillMaxWidth(),
                deviceConfig = it,
                onClick = onClick
            )
        }
    }
}

/**
 * 已链接的设备
 */
@Composable
fun ConnectedDeviceItem(
    modifier: Modifier = Modifier,
    deviceConfig: DeviceConfig = DeviceConfig().let {
        it.name = "aiNeck"
        it.mac = "00:00:00:00:00:00"
        it.version = "1.0.0"
        it.sn = "1234567890"
        it.deviceType = DeviceType.aiNeck
        it
    },
    onClick: (String) -> Unit = {}
) {
    Column(
        modifier = modifier
            .padding(vertical = 8.dp)
            .fillMaxWidth()
            .clickable { onClick(deviceConfig.mac) }
    ) {
        val textStyle = TextStyle(
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            color = Color.White
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(shape = RoundedCornerShape(12.dp), color = Color(0xFF1E4BDF))
                .padding(horizontal = 16.dp, vertical = 8.dp)
        ) {
            Text(
                text = "name:" + deviceConfig.name.ifEmpty { deviceConfig.deviceType.name },
                style = textStyle
            )
            Text(
                text = "mac:" + deviceConfig.mac,
                style = textStyle
            )
        }
    }
}


@Composable
private fun LookForDevice(
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp)
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.looking_for_your_device),
            style = TextStyle(
                fontSize = 30.sp,
                fontWeight = FontWeight.Medium,
                textAlign = TextAlign.Start
            ),
            modifier = Modifier
        )
        Text(
            text = stringResource(id = R.string.keep_aiSpine_device_close_to),
            fontSize = 20.sp,
            style = TextStyle(
                fontWeight = FontWeight.Medium,
                color = Color(0XFF666666),
                textAlign = TextAlign.Start
            ),
            modifier = Modifier.padding(top = 10.dp, bottom = 30.dp)
        )
        LoadingDialog()
        Text(
            text = stringResource(id = R.string.looking_for_your_device_now),
            fontSize = 18.sp,
            style = TextStyle(
                fontWeight = FontWeight.Medium
            ),
            modifier = Modifier.padding(top = 10.dp)
        )
    }
}

@Composable
private fun LookForDeviceOvertime(
    deviceType: String = ""
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
    ) {

        Text(
            text = stringResource(id = R.string.lookfor_timeover_tip),
            fontSize = 20.sp,
            color = Color(0XFF666666),
            modifier = Modifier.align(Alignment.Center)
        )

        AIHOutlinedButton(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .fillMaxWidth(0.9f)
                .padding(bottom = 32.dp),
            text = stringResource(id = R.string.reconnect),
            onClick = {
                val model =
                    MatchingDeviceDirections.MatchingDeviceModel(deviceType = deviceType)
                startScreen(
                    route = MatchingDeviceDirections.actionToMatchingDevice(model = model),
                    finish = true
                )
            },
            fontSize = 20.sp,
            fontColor = Color(0XFF333333)
        )
    }
}

@Composable
private fun ColumnScope.FindDevice(
    connectDevice: (String) -> Unit = {},
    devices: List<BluetoothDevice> = listOf()
) {
    Text(
        text = stringResource(id = R.string.choose_device),
        fontSize = 30.sp,
        color = Color(0XFF444444),
        fontWeight = FontWeight.Medium,
        modifier = Modifier.padding(vertical = 55.dp)
    )
    var selectedIndex by remember {
        mutableIntStateOf(0)
    }
    Box(
        Modifier
            .fillMaxWidth()
            .weight(1F), contentAlignment = Alignment.Center
    ) {
        Column(
            Modifier
                .fillMaxWidth()
                .verticalScroll(rememberScrollState())
        ) {
            devices.forEachIndexed { index, device ->
                FindDeviceItem(
                    selectedIndex = selectedIndex,
                    index = index,
                    device = device,
                    onClick = { selectedIndex = index })
            }
            AIHDivider()
        }
    }
    var lastClickTime by remember {
        mutableLongStateOf(0L)
    }
    AIHOutlinedButton(
        text = stringResource(id = R.string.next_continue),
        onClick = {
            System.currentTimeMillis().let {
                if (it - lastClickTime > 1000) {
                    lastClickTime = it
                    connectDevice(devices[selectedIndex].address)
                }
            }
        },
        modifier = Modifier
            .padding(55.dp)
            .fillMaxWidth(),
        fontSize = 20.sp,
        fontColor = Color(0XFF333333)
    )
}

@SuppressLint("MissingPermission")
@Composable
private fun FindDeviceItem(
    selectedIndex: Int,
    index: Int,
    device: BluetoothDevice,
    onClick: () -> Unit = {}
) {
    val fontSize by animateDpAsState(
        targetValue = if (selectedIndex == index) 24.dp else 20.dp,
        label = ""
    )
    AIHDivider()
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(if (selectedIndex == index) 44.dp else 40.dp)
            .background(if (selectedIndex == index) Color(0x661E4BDF) else Color.White)
            .pointerInput(Unit) {
                detectTapGestures {
                    onClick()
                }
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = device.address,
            fontSize = fontSize.toSp(),
            color = if (selectedIndex == index) Color.White else Color.Black
        )
    }

}