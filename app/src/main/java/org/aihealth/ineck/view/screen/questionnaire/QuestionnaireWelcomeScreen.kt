package org.aihealth.ineck.view.screen.questionnaire

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.startScreen

@Preview
@Composable
fun QuestionnaireWelcomeScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White)
            .verticalScroll(rememberScrollState()),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Text(
            modifier = Modifier.fillMaxWidth(),
            text = stringResource(id = R.string.welcome_to_aiSpine),
            style = TextStyle(
                fontSize = 32.sp,
                fontWeight = FontWeight(600),
                color = Color(0xFF3B61D9),
            ),
            textAlign = TextAlign.Center,
        )
        Text(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 24.dp),
            text = stringResource(id = R.string.welcome_to_aiSpine_text),
            style = TextStyle(
                fontSize = 12.sp,
                fontWeight = FontWeight(400),
                color = Color(0xFF666666),
            )
        )
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(top = 24.dp),
            horizontalAlignment = Alignment.Start,
        ) {
            Text(
                text = "1." + stringResource(id = R.string.welcome_to_aiShpine_benefit_1),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 17.89.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.36.sp,
                )
            )
            Text(
                text = "2." + stringResource(id = R.string.welcome_to_aiShpine_benefit_2),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 17.89.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.36.sp,
                )
            )
            Text(
                text = "3." + stringResource(id = R.string.welcome_to_aiShpine_benefit_3),
                style = TextStyle(
                    fontSize = 12.sp,
                    lineHeight = 17.89.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF666666),
                    letterSpacing = 0.36.sp,
                )
            )
        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 100.dp),
            verticalArrangement = Arrangement.Bottom,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Button(
                modifier = Modifier.fillMaxWidth(0.8f),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF3888FF)),
                shape = RoundedCornerShape(size = 21.5.dp),
                onClick = {
                    startScreen(Screen.QuestionnaireGuide.route, true)
                }
            ) {
                Text(
                    text = stringResource(id = R.string.welcome_start),
                    style = TextStyle(
                        fontSize = 18.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFFFFFFFF),
                    )
                )
            }
            Text(
                modifier = Modifier
                    .padding(top = 18.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) {
                        finish()
                    },
                text = stringResource(id = R.string.welcome_skip),
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF999999),
                )
            )
        }
    }
}

@Composable
fun BenefitItem(
    modifier: Modifier = Modifier,
    text: String,
    icon: Int,
    iconBack: Long,
) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Row(
            modifier = Modifier
                .clip(CircleShape)
                .size(34.dp)
                .background(Color(iconBack)),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
        ) {
            Image(painter = painterResource(id = icon), contentDescription = "icon")
        }
        Text(
            modifier = Modifier
                .width(80.dp),
            text = text,
            style = TextStyle(
                fontSize = 10.sp,
                lineHeight = 15.2.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF333333),
                letterSpacing = 0.3.sp,
            ),
            textAlign = TextAlign.Center,
        )
    }
}
