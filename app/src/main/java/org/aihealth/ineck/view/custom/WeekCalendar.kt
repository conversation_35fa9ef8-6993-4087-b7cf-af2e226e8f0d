package org.aihealth.ineck.view.custom

import android.os.Build
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.KeyboardArrowLeft
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.date
import org.aihealth.ineck.util.dayOfWeek
import org.aihealth.ineck.util.month
import org.aihealth.ineck.util.monthLastDay
import org.aihealth.ineck.util.year
import java.util.Calendar

/**
 *  在周报告中展示日历的日期单元
 *  @property   date    当前单元的日期
 *  @property   currentMonth    当前日期所在月历月份
 *  @property   isBelongCurrentMonth    当前日期是否属于当前月历所在月份
 */
data class WeekMonthCalendarUnit(
    var date: Int = 1,
    var currentMonth: Int = 1,
    var currentYear: Int = 1970,
    var isBelongCurrentMonth: Boolean = false
)

/**
 *  一周的周历内容
 *  @property   dates  日期单元列表，容量为7，表示从星期天到星期六
 */
data class SingleWeekMonthCalendar(
    val dates : MutableList<WeekMonthCalendarUnit> = mutableListOf(
        WeekMonthCalendarUnit(),WeekMonthCalendarUnit(),WeekMonthCalendarUnit(),
        WeekMonthCalendarUnit(),WeekMonthCalendarUnit(),WeekMonthCalendarUnit(),
        WeekMonthCalendarUnit()
    )
) {
    /**
     *  将日期单元填充到周历中
     *  @param  date    日期单元
     *  @param  position    该日期单元在本周中的位置，即星期几
     */
    fun fillInWeekCalendar(date: WeekMonthCalendarUnit, position: Int) : Boolean {
        /* 排除无效信息 */
        return if (position < 0 || position > 6) false
        else if (date.date < 1) false
        else {
            /* 对有效信息进行赋值 */
            this.dates[position] = date
            true
        }
    }

    /**
     *  改变本周日期单元的信息
     *  @param  newDate    目标变更后的日期单元
     *  @param  position    该日期单元在本周中的位置，即星期几
     */
    fun changeInWeekCalendar(newDate: WeekMonthCalendarUnit, position: Int) : Boolean {
        /* 排除无效信息 */
        return if (position < 0 || position > 6) false
        else if (newDate.date < 1) false
        else {
            /* 变更有效信息 */
            this.dates[position] = newDate
            true
        }
    }

    /**
     *  返回当前星期内日期的整形数组
     */
    fun getDateNumList(): List<Int>
            = this.dates.map { it.date }
}

/**
 *  用于周报告选择的月历内容数据
 *  @property   weeks   一个月历所包含的所有周信息
 *  @property   currentMonth    当前所在月份
 */
data class WeekMonthCalendar(
    var weeks : MutableList<SingleWeekMonthCalendar> = mutableListOf(),
) {
    /* 用于确认该月历的所有日期已完成初始化渲染 */
    private var isInitRendered by mutableStateOf(false)
    /* 当前月份 */
    private var currentMonth by mutableIntStateOf(0)

    init {
        weeks.clear()
        weeks.add(SingleWeekMonthCalendar())
    }

    /**
     *  渲染月历数据
     *  @param  initDate    首先用于初始化的立足数据，通常为今天的日期
     */
    fun initRenderCalendarData(
        initDate: Calendar,
    ) {
        weeks.clear()
        weeks.add(SingleWeekMonthCalendar())
        val initDateNum = initDate.date
        /* 依据指定日期星期，补充指定所在周数据 */
        val initDayOfWeek: Int = initDate.dayOfWeek
        /* 本月最后一天 */
        val lastDay = initDate.monthLastDay
        /* 当前月份 */
        this.currentMonth = initDate.month + 1
        /* 为立足日期当日填充数据 */
        this.weeks[0].dates[initDayOfWeek].apply {
            <EMAIL> = initDateNum
            <EMAIL> = <EMAIL>
            <EMAIL> = initDate.year
            <EMAIL> = true
        }
        /* 为立足日期所在周且在其之前的日期填充日期信息 */
        for (i in initDayOfWeek downTo 0) {
            if (i == initDayOfWeek) continue
            else {
                /* 若该立足日期所在月历星期为第一行，极有可能包含上个月的日期，这里需要做判断 */
                this.weeks[0].dates[i].apply {
                    if (initDateNum - (initDayOfWeek - i) <= 0) {
                        /* 当前日期为上个月的日期，需要获取上个月的最后一天，然后于立足日期作星期差计算 */
                        <EMAIL> = if ((<EMAIL> - 1) > 0) {
                            /* 若当前月为1月，则上个月就为12月 */
                            <EMAIL> - 1
                        } else 12
                        /* 先获取上个月的总天数，在减去if判断内容中的值 */
                        <EMAIL> = getDaysInMonth(initDate.year, <EMAIL> - 1) + (initDateNum - (initDayOfWeek - i))
                        <EMAIL> = if ((<EMAIL> - 1) > 0) {
                            /* 若当前月为1月，则上个月就为12月, 即上一年 */
                            initDate.year
                        } else initDate.year - 1
                        <EMAIL> = false
                    } else {
                        /* 本周内的日期且在立足日期之前 */
                        <EMAIL> = initDateNum - (initDayOfWeek - i)
                        <EMAIL> = <EMAIL>
                        <EMAIL> = initDate.year
                        <EMAIL> = true
                    }
                }
            }
        }
        /* 为立足日期所在周且在其之后的日期填充日期信息 */
        for(i in initDayOfWeek until 7) {
            if (i == initDayOfWeek) continue
            else {
                /* 若该立足日期所在月历星期最后一行，极有可能包含下个月的日期，这里需要做判断 */
                this.weeks[0].dates[i].apply {
                    /* 若累加日期超过了本月最后一天的日期，则表示该日期为下个月的日期，这里要利用立足日期与当前星期之差合值再与本月最后一天做差得到下个月的日期 */
                    if ((initDateNum + (i - initDayOfWeek)) > lastDay) {
                        <EMAIL> = (initDateNum + (i - initDayOfWeek)) - lastDay
                        <EMAIL> = if ((<EMAIL> + 1) <= 12) {
                            <EMAIL> + 1
                        } else 1
                        <EMAIL> = if ((<EMAIL> + 1) <= 12) {
                            initDate.year
                        } else initDate.year + 1
                        <EMAIL> = false
                    } else {
                        /* 本周内的日期且在立足日期之后 */
                        <EMAIL> = initDateNum + (i - initDayOfWeek)
                        <EMAIL> = <EMAIL>
                        <EMAIL> = initDate.year
                        <EMAIL> = true
                    }
                }
            }
        }
        /* 判断首先填充的星期是否为本月月历的第一个星期或者是最后一个星期 */
        val initWeek = this.weeks[0].copy()

        /* 分别计算向前补全和向后补全需要轮询的次数 */
        val lastDayUnitOfInitLastWeek = initWeek.dates.last()
        val firstDayUnitOfInitFirstWeek = initWeek.dates.first()
        /* 若初始化周的第一天不属于当前月历所表示月份，或者第一天为1日，则无需向前补全 */
        val preLoop: Int = if (!firstDayUnitOfInitFirstWeek.isBelongCurrentMonth || firstDayUnitOfInitFirstWeek.date == 1) 0
        else if((firstDayUnitOfInitFirstWeek.date - 1)%7 == 0) {
            /* 当初始化周的第一天为7的公约数日时，表示初始化周前的天数能够整除不含余，并不需为余数增加额外一行 */
            (firstDayUnitOfInitFirstWeek.date)/7
        } else {
            (firstDayUnitOfInitFirstWeek.date - 1)/7 + 1
        }
        /* 若初始化周的最后一天不属于当前月历所表示月份，或者最后一天为当前月最后一天，则无需向后补全 */
        val nextLoop: Int = if (!lastDayUnitOfInitLastWeek.isBelongCurrentMonth || lastDayUnitOfInitLastWeek.date == lastDay) 0
        else if ((lastDay - lastDayUnitOfInitLastWeek.date)%7 == 0) {
            /* 当初始化周的最后一天与当月天数之差为7的公约数时，表示初始化周后的天数能够整除不含余，并不需为余数增加额外一行 */
            (lastDay - lastDayUnitOfInitLastWeek.date)/7
        } else {
            (lastDay - lastDayUnitOfInitLastWeek.date)/7 + 1
        }
        /* 需要向前补齐 */
        if (preLoop > 0) {
            repeat(preLoop) {
                val tempWeekMonthCalendar = SingleWeekMonthCalendar()
                val firstDayOfFirstWeek: Int = this.weeks.first().getDateNumList().first()
                tempWeekMonthCalendar.dates.forEachIndexed { index, _ ->
                    if (firstDayOfFirstWeek - (7 - index) < 1) {
                        tempWeekMonthCalendar.dates[index].date = getDaysInMonth(initDate.year, currentMonth - 1) + (firstDayOfFirstWeek - (7 - index))
                        tempWeekMonthCalendar.dates[index].currentMonth = if ((<EMAIL> - 1) > 0) {
                            /* 若当前月为1月，则上个月就为12月 */
                            <EMAIL> - 1
                        } else 12
                        tempWeekMonthCalendar.dates[index].currentYear = if ((<EMAIL> - 1) > 0) {
                            /* 若当前月为1月，则上个月就为12月, 即上一年 */
                            initDate.year
                        } else initDate.year - 1
                        tempWeekMonthCalendar.dates[index].isBelongCurrentMonth = false
                    } else {
                        tempWeekMonthCalendar.dates[index].date = firstDayOfFirstWeek - (7 - index)
                        tempWeekMonthCalendar.dates[index].currentMonth = <EMAIL>
                        tempWeekMonthCalendar.dates[index].currentYear = initDate.year
                        tempWeekMonthCalendar.dates[index].isBelongCurrentMonth = true
                    }
                }
                this.weeks.add(0, tempWeekMonthCalendar)
            }
        }

        /* 需要向后补齐 */
        if (nextLoop > 0) {
            repeat(nextLoop) {
                val tempWeekMonthCalendar = SingleWeekMonthCalendar()
                val lastDayOfLastWeek: Int = this.weeks.last().getDateNumList().last()
                tempWeekMonthCalendar.dates.forEachIndexed { index, _ ->
                    if (lastDayOfLastWeek + index + 1 > lastDay) {
                        tempWeekMonthCalendar.dates[index].date = (lastDayOfLastWeek + index + 1) - lastDay
                        tempWeekMonthCalendar.dates[index].currentMonth = if ((<EMAIL> + 1) <= 12) {
                            <EMAIL> + 1
                        } else 1
                        tempWeekMonthCalendar.dates[index].currentYear = if ((<EMAIL> + 1) <= 12) {
                            initDate.year
                        } else initDate.year + 1
                        tempWeekMonthCalendar.dates[index].isBelongCurrentMonth = false
                    } else {
                        tempWeekMonthCalendar.dates[index].date = lastDayOfLastWeek + index + 1
                        tempWeekMonthCalendar.dates[index].currentMonth = <EMAIL>
                        tempWeekMonthCalendar.dates[index].currentYear = initDate.year
                        tempWeekMonthCalendar.dates[index].currentYear = initDate.year
                        tempWeekMonthCalendar.dates[index].isBelongCurrentMonth = true
                    }
                }
                this.weeks.add(tempWeekMonthCalendar)
            }
        }
        this.isInitRendered = true
    }

    /**
     *  获得前一个月的星期列表
     *  @param  initDayOfPreviousCalendar   上个月的初始化日期
     */
    fun turnOnPreviousMonth(
        initDayOfPreviousCalendar: Calendar,
    ) {initRenderCalendarData(initDayOfPreviousCalendar)
    }

    /**
     *  获得后一个月的星期列表
     *  @param  initDayNextCalendar   下个月的初始化日期
     *  @param  today   系统今日日期
     */
    fun turnOnNextMonth(
        initDayNextCalendar: Calendar,
        today: Calendar
    ) {
        if (initDayNextCalendar > today) {
            return
        } else {
            initRenderCalendarData(initDayNextCalendar)
        }
    }

    /**
     *  打印日历
     */
    private fun printWeekMonthCalendar() {
        this.weeks.forEachIndexed { outIndex, it ->
            it.dates.forEachIndexed { index, dateUnit ->
                LogUtil.d(
                    "_chen",
                    "第${outIndex + 1}周, 星期${index}, ${dateUnit.currentYear}-${dateUnit.currentMonth}-${dateUnit.date} "
                )
            }
        }
    }
}

/**
 *  周报告月历中选择星期显示面板
 *  @param  modifier    修饰符参数
 *  @param  isVisible   显示标识
 *  @param  isBackSelected  显示记录当日月历标识
 *  @param  selectDay   被选择日期
 *  @param  today   系统今日日期
 *  @param  onSelectedEvent 日期单元点击触发事件
 *  @param  onBackSelectedEvent 修改记录当日月历标识事件
 *  @param  onDismissEvent  星期选择面板关闭事件
 *  @param  onReCompose     强行重组月历面板
 */
@Composable
fun WeekMonthCalendarShowPanel(
    isVisible: State<Boolean>,
    isBackSelected: State<Boolean>,
    modifier: Modifier = Modifier,
    selectDay: Calendar,
    today: Calendar,
    onSelectedEvent: (WeekMonthCalendarUnit) -> Unit,
    onBackSelectedEvent: (Boolean) -> Unit,
    onDismissEvent: () -> Unit,
    onReCompose: () -> Unit,
) {
    val week = stringArrayResource(id = R.array.week3)

    /* 周报告所在月历数据 */
    val weekMonthCalendar = remember {
        mutableStateOf(
            WeekMonthCalendar().apply {
                initRenderCalendarData(selectDay) }
        )
    }
    /* 标记的日期截至的前七天 */
    val markDays by remember(selectDay) {
        derivedStateOf {
            getPassSevenDay(selectDay)
        }
    }
    AnimatedVisibility(
        visible = isVisible.value
    ) {
        /* 判断本次组件加载是否为 针对显示当前选择记录日期的月历 */
        /* 当月历窗口Dismiss后，再次打开为当前周报告记录日期所在月历 */
        if (isBackSelected.value) {
            weekMonthCalendar.value = WeekMonthCalendar().apply {
                initRenderCalendarData(selectDay) }
        }

        AIHDialog(onDismissRequest = { onDismissEvent() }) {
            Column(
                modifier = modifier,
                verticalArrangement = Arrangement.Center
            ) {
                Box(
                    Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp, bottom = 2.dp)
                ) {
                    /* 前往上个月 */
                    IconButton(
                        modifier = Modifier.align(Alignment.CenterStart),
                        onClick = {
                            onBackSelectedEvent(false)
                            val previousMonth = if (weekMonthCalendar.value.weeks[1].dates.last().currentMonth == 1) 12 else weekMonthCalendar.value.weeks[1].dates.last().currentMonth - 1
                            val previousMonthYear = if (weekMonthCalendar.value.weeks[1].dates.last().currentMonth == 1) weekMonthCalendar.value.weeks[1].dates.last().currentYear - 1 else weekMonthCalendar.value.weeks[1].dates.last().currentYear
                            weekMonthCalendar.value.turnOnPreviousMonth(
                                initDayOfPreviousCalendar = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                    Calendar.Builder()
                                        .setDate(
                                            previousMonthYear, previousMonth - 1, getDaysInMonth(previousMonthYear, previousMonth)
                                        )
                                        .build()
                                } else {
                                    Calendar.getInstance()
                                        .apply {
                                            set(Calendar.YEAR, previousMonthYear)
                                            set(Calendar.MONTH, previousMonth - 1)
                                            set(Calendar.DAY_OF_MONTH, getDaysInMonth(previousMonthYear, previousMonth))
                                        }
                                }
                            )
                            onReCompose()
                        }
                    ) {
                        Icon(imageVector = Icons.Default.KeyboardArrowLeft, contentDescription = "Previous month", tint = Color.White)
                    }
                    Text(
                        text = stringResource(
                            id = R.string.year_month,
                            weekMonthCalendar.value.weeks[1].dates.last().currentYear,
                            weekMonthCalendar.value.weeks[1].dates.last().currentMonth
                        ),
                        fontSize = 20.sp,
                        color = Color.White,
                        textAlign = TextAlign.Center,
                        fontWeight = FontWeight.Medium,
                        modifier = Modifier
                            .wrapContentSize()
                            .padding(vertical = 4.dp)
                            .align(Alignment.Center)
                    )
                    /* 前往下个月  */
                    IconButton(
                        modifier = Modifier.align(Alignment.CenterEnd),
                        onClick = {
                            /* 此时为月历对话框浏览翻页，组件刷新时不回到前周报告记录日期所在月历 */
                            onBackSelectedEvent(false)
                            val nextMonth = if (weekMonthCalendar.value.weeks.last().dates.first().currentMonth == 12) 0 else weekMonthCalendar.value.weeks.last().dates.first().currentMonth
                            val nextMonthYear = if (weekMonthCalendar.value.weeks.last().dates.first().currentMonth == 12) weekMonthCalendar.value.weeks.last().dates.first().currentYear + 1 else weekMonthCalendar.value.weeks.last().dates.first().currentYear
                            val firstDayOfNextMonthDay = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                Calendar.Builder()
                                    .setDate(
                                        nextMonthYear, nextMonth, 1
                                    )
                                    .build()
                            } else {
                                Calendar.getInstance()
                                    .apply {
                                        set(Calendar.YEAR, nextMonthYear)
                                        set(Calendar.MONTH, nextMonth)
                                        set(Calendar.DAY_OF_MONTH, 1)
                                    }
                            }
                            weekMonthCalendar.value.turnOnNextMonth(
                                initDayNextCalendar = firstDayOfNextMonthDay,
                                today = today
                            )
                            onReCompose()
                        }
                    ) {
                          Icon(imageVector = Icons.Default.KeyboardArrowRight, contentDescription = "Next month", tint = Color.White)
                    }
                }
                /* 星期标题 */
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 4.dp),
                    horizontalArrangement = Arrangement.SpaceAround
                ) {
                    repeat(7) {
                        Text(text = week[it], fontSize = 12.sp, color = Color(0XFF999999), textAlign = TextAlign.Center, modifier = Modifier.weight(1F))
                    }
                }
                weekMonthCalendar.value.weeks.forEach { singleWeek ->
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceAround
                    ) {
                        singleWeek.dates.forEach { dateUnit ->
                            Box(
                                modifier = Modifier
                                    .weight(1f)
                                    .clip(
                                        when (dateUnit) {
                                            /* 当markDays的标记区间横跨两个月份时，无需要求两个月份中日期单元的isBelongCurrentMonth必须吻合，只需年月日正确即标记背景高亮或首尾圆角样式 */
                                            markDays
                                                .first()
                                                .copy(isBelongCurrentMonth = false) -> {
                                                RoundedCornerShape(
                                                    topStart = 20.dp,
                                                    bottomStart = 20.dp
                                                )
                                            }

                                            markDays
                                                .first()
                                                .copy(isBelongCurrentMonth = true) -> {
                                                RoundedCornerShape(
                                                    topStart = 20.dp,
                                                    bottomStart = 20.dp
                                                )
                                            }

                                            markDays
                                                .last()
                                                .copy(isBelongCurrentMonth = false) -> {
                                                RoundedCornerShape(
                                                    topEnd = 20.dp,
                                                    bottomEnd = 20.dp
                                                )
                                            }

                                            markDays
                                                .last()
                                                .copy(isBelongCurrentMonth = true) -> {
                                                RoundedCornerShape(
                                                    topEnd = 20.dp,
                                                    bottomEnd = 20.dp
                                                )
                                            }

                                            else -> {
                                                RectangleShape
                                            }
                                        }
                                    )
                                    .background(
                                        if (markDays.contains(dateUnit.copy(isBelongCurrentMonth = false)) || markDays.contains(
                                                dateUnit.copy(isBelongCurrentMonth = true)
                                            )
                                        ) {
                                            Color(0xFF72D0BE)
                                        } else {
                                            Color(0x00000000)
                                        }
                                    )
                                    .clickable {
                                        /* 若选择时间在今日之后，则不继续逻辑 */
                                        val selectedCalendar =
                                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                                Calendar
                                                    .Builder()
                                                    .setDate(
                                                        dateUnit.currentYear,
                                                        dateUnit.currentMonth - 1,
                                                        dateUnit.date
                                                    )
                                                    .build()
                                            } else {
                                                Calendar
                                                    .getInstance()
                                                    .apply {
                                                        set(Calendar.YEAR, dateUnit.currentYear)
                                                        set(
                                                            Calendar.MONTH,
                                                            dateUnit.currentMonth - 1
                                                        )
                                                        set(Calendar.DAY_OF_MONTH, dateUnit.date)
                                                    }
                                            }
                                        if (selectedCalendar <= today) {
                                            /* 触发点击事件 */
                                            onSelectedEvent(dateUnit)
                                            weekMonthCalendar.value.initRenderCalendarData(
                                                selectedCalendar
                                            )
                                        } else {
                                            return@clickable
                                        }
                                    }
                            ) {
                                if (selectDay.date == dateUnit.date && selectDay.month + 1 == dateUnit.currentMonth && selectDay.year == dateUnit.currentYear) {
                                    Box(
                                        modifier = Modifier
                                            .clip(
                                                RoundedCornerShape(20.dp)
                                            )
                                            .background(Color(0xFF59BFA6))
                                            .matchParentSize()
                                    )
                                }
                                Text(
                                    text = dateUnit.date.toString(),
                                    fontSize = 20.sp,
                                    color = if (dateUnit.isBelongCurrentMonth) Color.White else Color(0XFF999999) ,
                                    textAlign = TextAlign.Center,
                                    fontWeight = FontWeight.Medium,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 4.dp)
                                )
                            }
                        }
                    }
                }
            }
        }
    }
//    weekMonthCalendar.printWeekMonthCalendar()
}

/**
 *  依据指定日期获取过去的七天
 *  @param  targetDay   给定目标日期
 */
private fun getPassSevenDay(
    targetDay: Calendar,
): List<WeekMonthCalendarUnit> {
    val selectDay = WeekMonthCalendarUnit(
        date = targetDay.date,
        currentMonth = targetDay.month + 1,
        currentYear = targetDay.year,
        isBelongCurrentMonth = true
    )
    val answerList: MutableList<WeekMonthCalendarUnit> = mutableListOf()
    /* 先将给定日期作为列表最后一个元素插入 */
    answerList.add(selectDay)
    /* 判断过去的6天是否都在当前月份内，分类讨论 */
    for (index in 1..6) {
        if (selectDay.date - index < 1) {
            answerList.add(
                index = 0,
                element = WeekMonthCalendarUnit(
                    date = getDaysInMonth(
                        year = if (selectDay.currentMonth == 1) targetDay.year - 1 else targetDay.year,
                        month = selectDay.currentMonth - 1
                    ) + (selectDay.date - index),
                    currentMonth = if (selectDay.currentMonth == 1) 12 else selectDay.currentMonth - 1,
                    currentYear = if (selectDay.currentMonth == 1) selectDay.currentYear - 1 else selectDay.currentYear,
                    isBelongCurrentMonth = false
                )
            )
        } else {
            answerList.add(
                index = 0,
                element = WeekMonthCalendarUnit(
                    date = selectDay.date - index,
                    currentMonth = selectDay.currentMonth,
                    currentYear = selectDay.currentYear,
                    isBelongCurrentMonth = true
                )
            )
        }
    }
    return answerList
}

/**
 *  通过年份和月份确认该月份的总天数
 *  @param  year    年份
 *  @param  month   月份
 */
private fun getDaysInMonth(year: Int, month: Int): Int {
    // Get the calendar object for the given year and month.
    val calendar = Calendar.getInstance()
    calendar.set(year, month - 1, 1)

    // Get the number of days in the month.
    return calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
}