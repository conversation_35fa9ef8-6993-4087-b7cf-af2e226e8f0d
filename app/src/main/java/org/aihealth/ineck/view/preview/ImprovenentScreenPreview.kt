package org.aihealth.ineck.view.preview

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import com.google.gson.Gson
import org.aihealth.ineck.Screen
import org.aihealth.ineck.mock.MockImprovementDetailString
import org.aihealth.ineck.model.improvement.ImproveProgram
import org.aihealth.ineck.model.improvement.ImprovementProgramsData
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardSuccessContentWithVipZh
import org.aihealth.ineck.view.screen.exercise.ImprovementProgramCardsWithVipZh
import org.aihealth.ineck.view.screen.exercise.VipMessageCardNoVipEn

@Composable
fun PreviewImprovementProgramCardSuccessContent() {
    val gson = Gson()
    val programs = mutableListOf<ImproveProgram>()
    repeat(1) {
        programs.add(gson.fromJson(MockImprovementDetailString, ImproveProgram::class.java))
    }
    val programsData = ImprovementProgramsData(1, programs)
    ImprovementProgramCardSuccessContentWithVipZh(
        programsData,
        onclick = {}
    )
}

@Preview(showBackground = true)
@Composable
fun PreviewImprovementProgramCardsWithVipNewOne() {
    ImprovementProgramCardsWithVipZh(
        vipContent = {
//            VipMessageCardNoVipZh(
//                vipHelloString = "Hi，成为会员",
//                vipDescriptionString = "开启健康生活，随时随地改善预防颈椎病",
//                descriptionString = "新人特惠，海量课程30天免费试用",
//                onclick = {
//                    startScreen(Screen.MemberShipCenter.route, false)
//                }
//            )
            VipMessageCardNoVipEn(
                vipHelloString = "Hi, Join the membership.",
                vipDescriptionString = "Open a healthy life, anytime, anywhere to improve the prevention of cervical spondylosis",
                descriptionString = "New person's special offer, free 30-day trial of massive courses",
                onclick = {
                    startScreen(
                        Screen.MemberShipCenter.route,
                        false
                    )
                }
            )
        },
        lessonContent = { PreviewImprovementProgramCardSuccessContent() }
    )

}
//@Preview(showBackground = true)
//@Composable
//fun PreviewImprovementProgramCardsWithVipOldOne() {
//    ImprovementProgramCardsWithVip(
//        VipContent = {
//            VipMessageCard(
//                vipHelloString = "Hi，成为会员",
//                vipDescriptionString = "开启健康生活，随时随地改善预防颈椎病",
//                descriptionString = "老用户专享，海量课程等您来",
//                onclick = {}
//            )
//        },
//        LessonContent = { PreviewImprovementProgramCardSuccessContent() }
//    )
//}
@Stable
class QureytoImageShapes(var hudu: Float = 100f) : Shape {

    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path()
        path.moveTo(0f, 0f)
        path.lineTo(0f, size.height-hudu)
        path.quadraticBezierTo(size.width/2f, size.height, size.width, size.height-hudu)
        path.lineTo(size.width,0f)
        path.close()
        return Outline.Generic(path)
    }
}

