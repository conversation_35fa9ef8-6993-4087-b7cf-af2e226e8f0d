package org.aihealth.ineck.view.screen

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.toStringList
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.AIHSwitch
import org.aihealth.ineck.view.custom.AIHWheel
import org.aihealth.ineck.view.custom.AIHWheelState
import org.aihealth.ineck.view.custom.BasePageView
import org.aihealth.ineck.view.dialog.picker.BasePicker
import org.aihealth.ineck.view.dialog.picker.PickDialog
import org.aihealth.ineck.viewmodel.device.DeviceViewModel
import java.util.Locale

/**
 *  设备设置页
 */
@Composable
fun DeviceSettingsScreen(
    deviceType: DeviceType = DeviceType.None,
) {
    val deviceViewModel = ViewModelProvider(activity)[DeviceViewModel::class.java]
    BasePageView(
        title = stringResource(id = R.string.device_settings),
        showBackIcon = true
    ) {
        Column(Modifier.fillMaxSize()) {
            List(deviceViewModel,deviceType)
//            AIHOutlinedButton(
//                text = stringResource(id = R.string.save),
//                onClick = {
//
//                },
//                modifier = Modifier
//                    .padding(horizontal = 52.dp, vertical = 17.dp)
//                    .fillMaxWidth()
//                    .height(42.dp),
//                fontSize = 20.sp,
//                fontColor = Color(0XFF333333)
//            )
        }
    }
}

@Composable
private fun ColumnScope.List(
    viewModel: DeviceViewModel,
    deviceType: DeviceType
) {

    val deviceConfig = when (deviceType) {
        DeviceType.aiNeck -> viewModel.neckDeviceConfig.collectAsStateWithLifecycle().value
        DeviceType.aiBack -> viewModel.backDeviceConfig.collectAsStateWithLifecycle().value
        else -> DeviceConfig()
    }
    val vibrationAngleList by remember {
        mutableStateOf((1..90).toStringList())
    }
    var vibrationAngleDialogVisible by remember {
        mutableStateOf(false)
    }
    val vibrationFrequencyList by remember {
        mutableStateOf((1..60).toStringList())
    }
    var vibrationFrequencyDialogVisible by remember {
        mutableStateOf(false)
    }
    val vibrationIntensityList by remember {
        mutableStateOf((0..15).toStringList())
    }
    var vibrationIntensityDialogVisible by remember {
        mutableStateOf(false)
    }
    Column(
        Modifier
            .fillMaxWidth()
            .weight(1F)
            .verticalScroll(rememberScrollState())
    ) {
        HorizontalDivider(modifier = Modifier.height(12.dp))
        Item(title = stringResource(id = R.string.calibration_reminder)) {
            AIHSwitch(
                checked = deviceConfig.isVibration,
                onCheckedChange = {
                    viewModel.toggleVibrationMode(deviceConfig.deviceType, it)
                },
                checkedText = "ON",
                unCheckedText = "OFF"
            )
        }
        Item(title = stringResource(id = R.string.vibration_alert_angle)) {
            Box(
                modifier = Modifier
                    .size(60.dp, 28.dp)
                    .background(Color(0XFF7893EC), CircleShape)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            vibrationAngleDialogVisible = true
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(text = deviceConfig.vibrationAngle.toString(), fontSize = 18.sp, color = Color.White)
                    Spacer(modifier = Modifier.width(3.dp))
                    Image(
                        painter = painterResource(id = R.drawable.img_device_angle),
                        contentDescription = null,
                        modifier = Modifier.size(10.dp)
                    )
                }
            }
        }
        Item(title = stringResource(id = R.string.vibration_frequency)) {
            Box(
                modifier = Modifier
                    .size(60.dp, 28.dp)
                    .background(Color(0XFF7893EC), CircleShape)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            vibrationFrequencyDialogVisible = true
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(id = R.string.second_num, deviceConfig.vibrationFrequency),
                    fontSize = 12.sp,
                    color = Color.White
                )
            }
        }
        Item(title = stringResource(id = R.string.vibration_intensity)) {
            Box(
                modifier = Modifier
                    .size(60.dp, 28.dp)
                    .background(Color(0XFF7893EC), CircleShape)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            vibrationIntensityDialogVisible = true
                        }
                    },
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = deviceConfig.vibrationIntensity.toString(),
                    fontSize = 14.sp,
                    color = Color.White
                )
            }
        }
        Item(title = stringResource(id = R.string.find_device)) {
            AIHSwitch(
                checked = deviceConfig.findDevice,
                onCheckedChange = {
//                    viewModel.deviceScreen.vibrate(deviceConfig.deviceType.name, it)
                    viewModel.findDevice(deviceType, it)
                    deviceConfig.findDevice = it
                },
                checkedText = stringResource(id = R.string.vibration),
                unCheckedText = stringResource(id = R.string.mute),
                modifier = Modifier.width(if (currentLocale == Locale.CHINESE) 60.dp else 84.dp)
            )
        }

        Item(title = stringResource(id = R.string.region)) {
            Text(
                text = if (currentLocale == Locale.CHINESE) stringResource(id = R.string.mainland) else stringResource(
                    id = R.string.united_states
                ),
                fontSize = 14.sp,
                color = Color(0XFF999999),
                fontWeight = FontWeight.Light
            )
        }
        Item(title = stringResource(id = R.string.device_name)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                var name by remember { mutableStateOf(deviceConfig.name) }
                val keyboardController = LocalSoftwareKeyboardController.current
                BasicTextField(
                    value = name,
                    onValueChange = { name = it },
                    modifier = Modifier.width(IntrinsicSize.Min),
                    textStyle = TextStyle(
                        fontSize = 14.sp,
                        color = Color(0XFF666666),
                        fontWeight = FontWeight.Light,
                    ),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done // 设置回车键的行为为完成，你可以根据需要更改它
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            // 隐藏软键盘
                            keyboardController?.hide()
                            // 保存设备名称
                            viewModel.setDeviceName(deviceConfig.deviceType, name)
//                            deviceConfig.saveToLocal()
                        }
                    ),
                    cursorBrush = SolidColor(Color(0XFF666666)),
                    singleLine = true,
                )
                Spacer(modifier = Modifier.width(6.dp))
                Image(
                    painter = painterResource(id = R.drawable.img_update_text),
                    contentDescription = null,
                    modifier = Modifier.size(12.dp)
                )
            }
        }

        /**
        Item(title = stringResource(id = R.string.step_goal)) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                val keyboardController = LocalSoftwareKeyboardController.current
                BasicTextField(
                    value = viewModel.deviceStep,
                    onValueChange = { viewModel.deviceStep = it },
                    modifier = Modifier.width(IntrinsicSize.Min),
                    textStyle = TextStyle(
                        fontSize = 14.sp,
                        color = Color(0XFF666666),
                        fontWeight = FontWeight.Light
                    ),
                    keyboardOptions = KeyboardOptions(
                        imeAction = ImeAction.Done // 设置回车键的行为为完成，你可以根据需要更改它
                    ),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            // 这里定义当用户按下回车键时你想执行的操作
                            deviceConfig.saveToLocal()
                            // 隐藏软键盘
                            keyboardController?.hide()
                        }
                    ),
                    cursorBrush = SolidColor(Color(0XFF666666)),
                )
                Spacer(modifier = Modifier.width(6.dp))
                Image(
                    painter = painterResource(id = R.drawable.img_update_text),
                    contentDescription = null,
                    modifier = Modifier.size(12.dp)
                )
            }
        }
        */
        /**
        Item(title = stringResource(id = R.string.firmware_upgrade)) {
            var firmwareUpgradeChecked by remember {
                mutableStateOf(false)
            }
            AIHSwitch(
                checked = firmwareUpgradeChecked,
                onCheckedChange = { firmwareUpgradeChecked = it },
                checkedText = "ON",
                unCheckedText = "OFF"
            )
        }
        */
        Item(title = stringResource(id = R.string.firmware_version)) {
            Text(
                text = deviceConfig.version,
                fontSize = 14.sp,
                color = Color(0XFF666666),
                fontWeight = FontWeight.Light
            )
        }
        Item(title = stringResource(id = R.string.s_n)) {
            Text(
                text = deviceConfig.sn,
                fontSize = 14.sp,
                color = Color(0XFF666666),
                fontWeight = FontWeight.Light
            )
        }
    }

    if (vibrationAngleDialogVisible) {
        AIHBottomSheet(
            onDismissRequest = {
                vibrationAngleDialogVisible = false
            }
        ) {
            PickDialog(
                title = stringResource(id = R.string.vibration_alert_angle),
                list = vibrationAngleList,
                onConfirmClick = {
//                    viewModel.setVibrationAngle(deviceConfig.deviceType.name, it + 1)
                    viewModel.setVibrationAngle(deviceConfig.deviceType, it + 1)
                    vibrationAngleDialogVisible = false
                },
                initialIndex = deviceConfig.vibrationAngle - 1,
                onCancelClick = {
                    vibrationAngleDialogVisible = false
                }
            )
        }
    }

    if (vibrationFrequencyDialogVisible) {
        AIHBottomSheet(
            onDismissRequest = {
                vibrationFrequencyDialogVisible = false
            }
        ) {
            val state = remember {
                AIHWheelState(initialIndex = deviceConfig.vibrationFrequency - 1)
            }
            BasePicker(
                onConfirmClick = {
//                    viewModel.setVibrationFrequency(
//                        deviceConfig.deviceType.name,
//                        state.selectedIndex + 1
//                    )
                    viewModel.setVibrationFrequency(
                        deviceConfig.deviceType,
                        state.selectedIndex + 1
                    )
                    vibrationFrequencyDialogVisible = false
                },
                onCancelClick = {
                    vibrationFrequencyDialogVisible = false
                }
            ) {
                Row(
                    Modifier
                        .fillMaxWidth()
                        .height(180.dp)
                ) {
                    AIHWheel(
                        state = state,
                        list = vibrationFrequencyList,
                        modifier = Modifier.weight(2F)
                    )
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .weight(1F), contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = stringResource(id = R.string.second),
                            fontSize = 16.sp,
                            color = Color(0XFF333333)
                        )
                    }
                }
            }
        }
    }

    if (vibrationIntensityDialogVisible) {
        AIHBottomSheet(
            onDismissRequest = {
                vibrationIntensityDialogVisible = false
            }
        ) {
            val state = remember {
                AIHWheelState(initialIndex = deviceConfig.vibrationIntensity)
            }
            BasePicker(
                onConfirmClick = {
                    viewModel.setVibrationIntensity(
                        deviceConfig.deviceType,
                        state.selectedIndex
                    )
                    vibrationIntensityDialogVisible = false
                },
                onCancelClick = {
                    vibrationIntensityDialogVisible = false
                }
            ) {
                AIHWheel(
                    state = state,
                    list = vibrationIntensityList,
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
private fun Item(
    title: String,
    content: @Composable () -> Unit
) {
    Box(
        Modifier
            .fillMaxWidth()
            .height(52.dp)
            .padding(horizontal = 35.dp)
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            color = Color(0XFF444444),
            modifier = Modifier.align(
                Alignment.CenterStart
            ),
            fontWeight = FontWeight.Light
        )
        Box(modifier = Modifier.align(Alignment.CenterEnd)) {
            content()
        }

    }
    AIHDivider()
}