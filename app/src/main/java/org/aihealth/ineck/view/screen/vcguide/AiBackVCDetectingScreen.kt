package org.aihealth.ineck.view.screen.vcguide

import android.view.ViewTreeObserver
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.LifecycleOwner
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.rememberLottieComposition
import com.google.accompanist.pager.HorizontalPager
import com.google.accompanist.pager.PagerState
import com.google.mediapipe.tasks.vision.core.RunningMode
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.mediapipe.PoseLandmarkerHelper
import org.aihealth.ineck.model.Queue
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.TextToSpeech
import org.aihealth.ineck.viewmodel.dao.AngleWithTimestamp
import org.aihealth.ineck.viewmodel.dao.compareEnqueue
import org.aihealth.ineck.viewmodel.dao.getTimestampNow
import org.aihealth.ineck.viewmodel.dao.verifyDiff
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import java.util.concurrent.Executors
import kotlin.math.PI
import kotlin.math.acos
import kotlin.math.sqrt


@Composable
fun AiBackVCDetectingScreen(
    modifier: Modifier = Modifier,
    pagerState: PagerState,
    pagerCount: Int,
    content: @Composable (Int) -> Unit

) {
    HorizontalPager(
        state = pagerState,
        count = pagerCount,
        modifier = modifier.fillMaxWidth(),
        userScrollEnabled = false
    ) { page ->
        content(page)
    }
}

@Composable
fun AiBackVCFaceDetectingScreen(
    modifier: Modifier = Modifier,
    lifecycleOwner: LifecycleOwner,
    isMuteState: Boolean = false,
    isVoiceEnable: Boolean = true,
    changeMuteState: () -> Unit,
    nextPage: (VCDetectingResult.DetectingSuccess) -> Unit,
    timeOut: () -> Unit,
) {
    val context = LocalContext.current

    /** 组合的协程域 */
    val localScope = rememberCoroutineScope()

    /** 获取相机实例 */
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(context) }

    /** 是否捕捉到躯干 */
    var isCapturedPose by remember {
        mutableStateOf(false)
    }


    /** 点头的仰角 */
    var bodyTiltAngle by remember {
        mutableFloatStateOf(0f)
    }

    /** 开始监测时间戳 - 从有效数据开始计算 */
    var startTimestampForEffectDetecting by remember {
        mutableLongStateOf(getTimestampNow())
    }

    /** 角度队列 */
    val angleQueue = remember {
        mutableStateOf(Queue<AngleWithTimestamp>())
    }
    var isTimeOut by remember {
        mutableStateOf(false)
    }

    var isNextPager by remember {
        mutableStateOf(false)
    }
    // 最大倾斜角度
    val maxTiltAngle = 20f
    /* 图像分析 */
    val poseLandmarkerListener = object : PoseLandmarkerHelper.LandmarkerListener {
        override fun onError(error: String, errorCode: Int) {
            /* 躯干不存在 */
            isCapturedPose = false
            bodyTiltAngle = 0f
            /* 重置监测开始检测时间  */
            startTimestampForEffectDetecting = getTimestampNow()
        }

        override fun onResults(resultBundle: PoseLandmarkerHelper.ResultBundle) {
            resultBundle.results.first().let { poseLandmarkerResult ->
                for (landmark in poseLandmarkerResult.worldLandmarks()) {
                    if (landmark.get(11).visibility().get() > 0.5f
                        && landmark.get(12).visibility().get() > 0.5f
                        && landmark.get(23).visibility().get() > 0.5f
                        && landmark.get(24).visibility().get() > 0.5f
                    ) {
                        // 人体存在
                        isCapturedPose = true

                        val x1 = landmark.get(11).x() / 2 + landmark.get(12).x() / 2
                        val y1 = landmark.get(11).y() / 2 + landmark.get(12).y() / 2
                        val z1 = landmark.get(11).z() / 2 + landmark.get(12).z() / 2
                        val x2 = landmark.get(23).x() / 2 + landmark.get(24).x() / 2
                        val y2 = landmark.get(23).y() / 2 + landmark.get(24).y() / 2
                        val z2 = landmark.get(23).z() / 2 + landmark.get(24).z() / 2
                        val abX = x2 - x1
                        val abY = y2 - y1
                        val abZ = z2 - z1
                        val cX = 0.0
                        val cY = 1.0
                        val cZ = 0.0
                        // 计算向量AB和向量C的点积
                        val dotProduct = abX * cX + abY * cY + abZ * cZ

                        // 计算向量AB和向量C的模长
                        val abMagnitude = sqrt(abX * abX + abY * abY + abZ * abZ)
                        val cMagnitude = sqrt(cX * cX + cY * cY + cZ * cZ)

                        // 计算夹角的余弦值
                        val cosTheta = dotProduct / (abMagnitude * cMagnitude)

                        val angle = (acos(cosTheta) * (180.0 / PI)).toFloat()
                        bodyTiltAngle = angle

                        /* 检测中 */
                        angleQueue.value.compareEnqueue(
                            AngleWithTimestamp(angle, getTimestampNow())
                        )
                        LogUtil.i("body angle: $angle")

                        /* 若校准时间超过 5sec 以上，在当前拥有足够样本容量的情况下，判断队列差是否小于10度 */
                        if (getTimestampNow() - startTimestampForEffectDetecting > 2000L) {
                            if (angleQueue.value.verifyDiff(maxTiltAngle)) {
                                /* 检测成功 */
                                isTimeOut = false
                                isNextPager = true
                                LogUtil.i("detecting success")
                                localScope.launch {
                                    delay(2000)
                                    val last = angleQueue.value.last()
                                    nextPage(
                                        VCDetectingResult.DetectingSuccess(
                                            result = last.angle
                                        )
                                    )
                                }
                            } else {
                                /* 重置监测开始检测时间  */
                                startTimestampForEffectDetecting = getTimestampNow()
                            }
                        }
                    }

                }
            }
        }


    }
    val backgroundExecutor = remember { Executors.newSingleThreadExecutor() }
    val poseLandmarkerHelper = remember {
        PoseLandmarkerHelper(
            context = context,
            runningMode = RunningMode.LIVE_STREAM,
            poseLandmarkerHelperListener = poseLandmarkerListener
        )
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Top
    ) {
        Box(
            modifier = modifier.fillMaxWidth(),
        ) {
            MuteButton(
                isMuteState = isMuteState,
                isVoiceEnable = isVoiceEnable,
                changeMuteState = { changeMuteState() },
                modifier = Modifier
                    .padding(16.dp)
                    .size(30.dp)
                    .align(Alignment.CenterEnd)
            )
        }
        /** 检测引导完成动画 */
        val finishComposition by rememberLottieComposition(spec = LottieCompositionSpec.RawRes(R.raw.vertical_calibration_finish_animation))
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp)
                .background(color = Color.Transparent),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (isNextPager) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    /* 校准完成 */
                    LottieAnimation(
                        composition = finishComposition,
                        iterations = 1,
                        modifier = Modifier.size(60.dp)
                    )
                    Spacer(modifier = Modifier.height(1.dp))
                    Text(
                        text = stringResource(id = R.string.passed),
                        style = TextStyle(
                            fontSize = 22.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color(0xFF333333),
                        ),
                        textAlign = TextAlign.Center,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

            } else {
                val speechText =
                    if (!isCapturedPose) {
                        stringResource(id = R.string.detect_guide_please_show_your_body)
                    } else {
                        stringResource(id = R.string.precautions_of_detect_guide_line_3)
                    }
                LaunchedEffect(isMuteState, speechText) {
                    if (isMuteState) {
                        TextToSpeech.ttsStop()
                    } else {
                        TextToSpeech.ttsSpeaking(speechText)
                    }
                }
                Text(
                    text = speechText,
                    style = TextStyle(
                        fontSize = 20.sp,
                        fontWeight = FontWeight.W400,
                        color = if (isCapturedPose)
                            Color(0xFF333333)
                        else
                            Color(0xFFFC7349),
                    ),
                    modifier = Modifier
                        .animateContentSize()
                )

            }
        }

        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 10.dp, bottom = 20.dp)
                .height(220.dp)
                .background(color = Color.Transparent)
        ) {
            val frameBlockColor: Color by animateColorAsState(
                targetValue = if (isCapturedPose)
                    Color(0xFFDDE4f1)
                else Color(0xFFFC7349), label = ""
            )
            val circleProgress = remember { Animatable(0f) }
            /* 外围检测框 */
            Icon(
                painter = painterResource(id = R.drawable.img_vc_detect_block),
                contentDescription = "Detection frame",
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center),
                tint = frameBlockColor
            )
            /* 检测框周边圆圈进度条 */
            CircularProgressIndicator(
                progress = { circleProgress.value },
                modifier = Modifier
                    .size(196.dp)
                    .clip(CircleShape)
                    .background(color = Color(0x80CCCCCC))
                    .align(Alignment.Center),
                color = frameBlockColor,
                strokeWidth = 10.dp,
            )
            Surface(
                modifier = Modifier
                    .size(180.dp)
                    .align(Alignment.Center),
                shape = CircleShape
            ) {
                AndroidView(
                    factory = { context ->
                        val previewView = PreviewView(context)
                        val preview = Preview.Builder()
                            .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                            .build()
                        previewView.viewTreeObserver.addOnGlobalLayoutListener(object :
                            ViewTreeObserver.OnGlobalLayoutListener {
                            override fun onGlobalLayout() {
                                preview.setTargetRotation(previewView.display.rotation)
                                previewView.viewTreeObserver.removeOnGlobalLayoutListener(this)
                            }
                        })
                        /* 摄像头选择 */
                        val selector = CameraSelector.Builder()
                            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
                            .build()

                        /* 自定义图像分析对象 */
                        val backAnalyzer =
                            ImageAnalysis.Builder()
                                .setTargetAspectRatio(AspectRatio.RATIO_4_3)
                                .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                                .setOutputImageFormat(ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888)
                                .build().also {
                                    it.setAnalyzer(backgroundExecutor) { image ->
                                        poseLandmarkerHelper.detectLiveStream(
                                            imageProxy = image,
                                            isFrontCamera = true
                                        )
                                    }
                                }

                        try {
                            cameraProviderFuture.get().unbindAll()
                            cameraProviderFuture.get().bindToLifecycle(
                                lifecycleOwner, selector, preview, backAnalyzer
                            )
                            preview.setSurfaceProvider(previewView.surfaceProvider)
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                        previewView
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
            LaunchedEffect(getTimestampNow() - startTimestampForEffectDetecting > 5000L) {
                delay(1000)
                launch(Dispatchers.IO) {
                    (1..4).forEach { i ->
                        circleProgress.animateTo(
                            i * (1f / 4f),
                            animationSpec = tween(500, 0, LinearEasing)
                        )
                    }
                }
            }
        }

    }
    LaunchedEffect(Unit) {
        isTimeOut = true
        delay(60000)
        if (isTimeOut) {
            timeOut()
        }
    }
    DisposableEffect(Unit) {
        onDispose {
            TextToSpeech.ttsStop()
        }
    }
}