package org.aihealth.ineck.view.dialog

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import org.aihealth.ineck.R
import org.aihealth.ineck.view.custom.AIHButton
import org.aihealth.ineck.view.custom.AIHOutlinedButton

/**
 * 扫码成功后确认是否添加关注对话框
 */
@Preview()
@Composable
fun AddAttentionDialog(
    isShowAddAttentionDialog: Boolean = true,
    name: String = "",
    onDismissRequest: () -> Unit = {},
    onConfirm: () -> Unit = {}
) {
    if (isShowAddAttentionDialog) {
        Dialog(
            onDismissRequest = { onDismissRequest() },
            properties = DialogProperties(
                dismissOnBackPress = true,
                dismissOnClickOutside = false,
                usePlatformDefaultWidth = false
            )
        ) {
            Column(
                Modifier
                    .widthIn(min = 250.dp)
                    .background(Color.White, RoundedCornerShape(10.dp))
                    .padding(horizontal = 16.dp, vertical = 30.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.confirm_add_attention, name),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium,
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(25.dp))

                AIHOutlinedButton(
                    text = stringResource(id = R.string.cancel),
                    onClick = {
                        onDismissRequest()
                    },
                    modifier = Modifier.fillMaxWidth(0.8f)
                )
                Spacer(modifier = Modifier.width(4.dp))
                AIHButton(
                    text = stringResource(id = R.string.confirm),
                    onClick = {
                        onConfirm()
                    },
                    modifier = Modifier
                        .fillMaxWidth(0.8f)
                        .padding(top = 8.dp)
                )

            }
        }
    }
}