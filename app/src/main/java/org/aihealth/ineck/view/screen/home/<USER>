package org.aihealth.ineck.view.screen.home

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterStart
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.ui.theme.Typography
import org.aihealth.ineck.view.screen.AiJointHeaderDropdownMenu
import org.aihealth.ineck.view.screen.AiNeckAndAiBackHeaderDropdownMenu
import org.aihealth.ineck.view.screen.AiNeckCVAndAiBackCVHeaderDropdownMenu

@Preview()
@Composable
fun HomeHeader(
    modifier: Modifier = Modifier,
    deviceType: DeviceType = DeviceType.aiNeck,
    currentDeviceType: DeviceType = DeviceType.aiNeck,
    toCamera: () -> Unit = {},
    toggleDeviceType: (DeviceType) -> Unit = {}
) {
    var dropdownVisible by remember { mutableStateOf(false) }
    Box(
        modifier = modifier
            .then(modifier)
            .fillMaxWidth()
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier
                .align(CenterStart)
                .padding(start = 16.dp)
                .pointerInput(Unit) {
                    detectTapGestures {
                        dropdownVisible = !dropdownVisible
                    }
                },
        ) {
            Text(
                text = when (deviceType.netWorkName) {
                    DeviceType.aiNeck.netWorkName -> {
                        stringResource(id = R.string.ai_neck)
                    }

                    DeviceType.aiBack.netWorkName -> {
                        stringResource(id = R.string.ai_back)

                    }

                    DeviceType.aiNeckCV.netWorkName -> {
                        stringResource(id = R.string.ai_neck_cv)
                    }

                    DeviceType.aiBackCV.netWorkName -> {
                        stringResource(id = R.string.ai_back_cv)
                    }

                    DeviceType.KneeJoint.netWorkName -> {
                        stringResource(id = R.string.joint_knee)
                    }

                    DeviceType.KneeJoint.netWorkName -> {
                        stringResource(id = R.string.joint_knee)

                    }

                    DeviceType.ElbowJoint.netWorkName -> {
                        stringResource(id = R.string.joint_elbow)
                    }

                    DeviceType.HipJoint.netWorkName -> {
                        stringResource(id = R.string.joint_hip)
                    }

                    DeviceType.ShoulderJoint.netWorkName -> {
                        stringResource(id = R.string.joint_shoulder)
                    }

                    DeviceType.aiJointCV.netWorkName -> {
                        stringResource(id = R.string.joint_knee)
                    }

                    else -> stringResource(id = R.string.ai_neck_cv)
                },
                style = Typography.displayLarge,
                color = Color(0XFFF2F2F2)
            )
            Spacer(modifier = Modifier.width(18.dp))
            Icon(
                painter = painterResource(id = R.drawable.img_pulldown),
                contentDescription = null,
                tint = Color(0XFFF4F4F4),
                modifier = Modifier.size(16.dp)
            )

            when (currentDeviceType) {
                DeviceType.aiNeck, DeviceType.aiBack -> {
                    AiNeckAndAiBackHeaderDropdownMenu(
                        visible = dropdownVisible,
                        onDismissRequest = { dropdownVisible = false },
                        onClick = {
                            toggleDeviceType(it)
                            dropdownVisible = false
                        }
                    )
                }

                DeviceType.aiNeckCV, DeviceType.aiBackCV -> {
                    AiNeckCVAndAiBackCVHeaderDropdownMenu(
                        visible = dropdownVisible,
                        onDismissRequest = { dropdownVisible = false },
                        onClick = {
                            toggleDeviceType(it)
                            dropdownVisible = false
                        }
                    )
                }

                DeviceType.aiJoint, DeviceType.KneeJoint, DeviceType.HipJoint, DeviceType.ElbowJoint, DeviceType.ShoulderJoint -> {
                    AiJointHeaderDropdownMenu(
                        visible = dropdownVisible,
                        onDismissRequest = { dropdownVisible = false },
                        onClick = {
                            toggleDeviceType(it)
                            dropdownVisible = false
                        }
                    )
                }

                else -> {}
            }

        }
        Row(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .padding(end = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.End
        ) {
            Icon(
                painter = painterResource(id = R.drawable.img_scanner),
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier
                    .size(16.dp)
                    .pointerInput(Unit) {
                        detectTapGestures {
                            toCamera()
                        }
                    }
            )
//            Icon(
//                painter = painterResource(id = R.drawable.ic_message),
//                contentDescription = null,
//                tint = Color.White,
//                modifier = Modifier
//                    .padding(start = 12.dp)
//                    .size(16.dp)
//                    .pointerInput(Unit) {
//                        detectTapGestures {
//                            startScreen(Screen.MessageCenter.route, false)
//                        }
//                    },
//            )
        }
    }
}