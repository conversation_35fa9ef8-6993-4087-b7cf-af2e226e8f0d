package org.aihealth.ineck.view.screen.home

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Alignment.Companion.CenterStart
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.button.RecalibrateButton
import org.aihealth.ineck.view.button.TypeSelectButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiBackSmartVersion
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiNeck
import org.aihealth.ineck.view.screen.TopTipsBlockOfConnectionStatusCard
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.device.DeviceViewModel

@Composable
fun AiBackHomeModule(
    modifier: Modifier = Modifier,
    viewModel: MainViewModel,
    deviceViewModel: DeviceViewModel
) {
    val scope = rememberCoroutineScope()
    // 当前设备是否连接
    val isConnected by deviceViewModel.backDeviceConnected.collectAsStateWithLifecycle()

    // 设备角度
    val deviceAngle = remember { Animatable(0f) }
    var maxAngle by remember { mutableIntStateOf(0) }

    // 设备电量
    var power by remember {
        mutableIntStateOf(0)
    }
    /** 是否显示校准提示对话框 */
    var showCalibrationDialog by rememberSaveable { mutableStateOf(false) }
    var isVibrationModeEnabled = deviceViewModel.backIsVibrationModeEnabled.collectAsStateWithLifecycle()

    val lifecycleOwner = LocalLifecycleOwner.current
    val backDeviceConfig = deviceViewModel.backDeviceConfig.collectAsStateWithLifecycle()

    // 使用LaunchedEffect监听设备连接和配置变化
    LaunchedEffect(isConnected, backDeviceConfig.value) {
        if (isConnected && !backDeviceConfig.value.isCalibrated) {
            LogUtil.d("设备已连接但未校准，显示校准提示")
            showCalibrationDialog = true
        } else if (isConnected && backDeviceConfig.value.isCalibrated) {
            // 设备已连接且已校准，确保对话框不显示
            LogUtil.d("设备已连接且已校准，不显示校准提示")
            showCalibrationDialog = false
        }
    }

    // 校准提示对话框
    if (showCalibrationDialog && isConnected) {
        AlertDialog(
            onDismissRequest = { showCalibrationDialog = false },
            title = { Text(stringResource(id = R.string.calibration_suggestion_title)) },
            text = { Text(stringResource(id = R.string.calibration_suggestion_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        showCalibrationDialog = false
                        startScreen(Screen.CalibrationDeviceRoute.route+"?deviceType=${DeviceType.aiNeck}")
                    }
                ) {
                    Text(stringResource(id = R.string.confirm_calibration))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showCalibrationDialog = false
                    }
                ) {
                    Text(stringResource(id = R.string.maybe_later))
                }
            }
        )
    }
    // 校准提示对话框
    if (showCalibrationDialog && isConnected) {
        AlertDialog(
            onDismissRequest = { showCalibrationDialog = false },
            title = { Text(stringResource(id = R.string.calibration_suggestion_title)) },
            text = { Text(stringResource(id = R.string.calibration_suggestion_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        showCalibrationDialog = false
                        startScreen(Screen.CalibrationDeviceRoute.route+"?deviceType=${DeviceType.aiBack}")
                    }
                ) {
                    Text(stringResource(id = R.string.confirm_calibration))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showCalibrationDialog = false
                    }
                ) {
                    Text(stringResource(id = R.string.maybe_later))
                }
            }
        )
    }

    HomeStatusCard(
        modifier = modifier.fillMaxWidth()
    ) {
        TopTipsBlockOfConnectionStatusCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            if (isConnected) {
                GuideToNeckImproveOfAiNeck()
            } else {
                GuideToNeckImproveOfAiBackSmartVersion()
            }
        }
        // 分割线
        AIHDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )

        Box(
            modifier = Modifier
                .padding(top = 5.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            // 中间部分
            BackAngleStatusModule(
                modifier = Modifier
                    .align(CenterStart)
                    .fillMaxWidth(0.7f),
                isShowAngle = isConnected,
                angle = deviceAngle.value.toInt()
            )

            if (isConnected) {
                AngleStatue(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 16.dp, bottom = 16.dp),
                    angle = deviceAngle.value.toInt()
                )
            }
        }

        if (isConnected) {
            /* 重新校准Button位置 */
            RecalibrateButton(
                modifier = Modifier
                    .padding(vertical = 5.dp)
                    .fillMaxWidth()
                    .wrapContentHeight(),
                onClick = { startScreen(Screen.CalibrationDeviceRoute.route+"?deviceType=${DeviceType.aiBack}") }
            )
            AboutDevice(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 5.dp),
                power = power,
                maxAngle = maxAngle
            )
            TypeSelectButton(
                selectedIndex = if (isVibrationModeEnabled.value) 0 else 1,
                array = stringArrayResource(id = R.array.device_mode),
                onClick = {
                    LogUtil.d("Click TypeSelectButton", "isVibrationModeEnabled ${isVibrationModeEnabled.value}")
                    deviceViewModel.toggleVibrationMode(
                        DeviceType.aiBack,
                        it == 0
                    )
                },
                modifier = Modifier
                    .padding(horizontal = 16.dp, vertical = 5.dp)
                    .align(CenterHorizontally)
            )
        }


    }
    // 获取设备状态
    LaunchedEffect(Unit) {
        launch {
            // 监听角度变化
            deviceViewModel.backAngle.collect { angle ->
                LogUtil.d("HomeScreen","angle: ${angle.toString()}" )
                angle?.let {
                    deviceAngle.animateTo(it.toFloat(), tween(250))
                }
            }
        }

        // 监听电量和最大角度变化
        launch {
            deviceViewModel.backBatteryLevel.collect { batteryLevel ->
                power = batteryLevel ?: 0
            }
        }

        launch {
            deviceViewModel.backMaxAngle.collect { maxAngleValue ->
                maxAngle = maxAngleValue ?: 0
            }
        }
    }

    // 监听连接状态变化，管理电量读取定时任务
    var batteryReadingJob: kotlinx.coroutines.Job? = null


    // 上传数据到服务器并管理电量读取定时任务
    DisposableEffect(isConnected) {
        var currentTime = System.currentTimeMillis()

        // 上传数据到服务器
        scope.launch(Dispatchers.IO) {
            if (currentTime - viewModel.homeScreen.lastUpdateTime >= 1000 * 10) {
                viewModel.homeScreen.lastUpdateTime = currentTime
                // 上传当前数据库中基于设备连接的数据
                viewModel.postAngle(DeviceType.aiBack)
            }
        }

        // 根据连接状态管理电量读取定时任务
        if (isConnected) {
            // 设备连接成功，启动电量读取定时任务
            LogUtil.d("HomeScreen", "设备已连接，启动电量读取定时任务")

            // 立即读取一次初始电量
            deviceViewModel.readBatteryLevel(DeviceType.aiBack)

            // 创建新的定时任务
            batteryReadingJob = scope.launch {
                while (true) {
                    // 等待60秒后读取
                    delay(60000)
                    deviceViewModel.readBatteryLevel(DeviceType.aiBack)
                    LogUtil.d("HomeScreen", "定时读取电量")
                }
            }
        } else if (batteryReadingJob != null) {
            // 设备断开，取消电量读取定时任务
            LogUtil.d("HomeScreen", "设备已断开，取消电量读取定时任务")
            batteryReadingJob?.cancel()
            batteryReadingJob = null
        }

        onDispose {
            // 上传最终数据
            scope.launch(Dispatchers.IO) {
                currentTime = System.currentTimeMillis()
                if (currentTime - viewModel.homeScreen.lastUpdateTime >= 1000 * 10) {
                    viewModel.homeScreen.lastUpdateTime = currentTime
                    viewModel.postAngle(DeviceType.aiBack)
                }
            }

            // 如果定时任务存在，取消它
            if (batteryReadingJob != null) {
                LogUtil.d("HomeScreen", "组件销毁，取消电量读取定时任务")
                batteryReadingJob?.cancel()
                batteryReadingJob = null
            }
        }
    }
}