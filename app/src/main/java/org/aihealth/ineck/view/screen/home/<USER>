package org.aihealth.ineck.view.screen.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Composable
fun AiFits(
    modifier: Modifier = Modifier,
    stepData: Int = 0,
    calorieData: Double = 0.0,
    hasData: Boolean = false,
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.White,
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            // Title
            Row(
                modifier = Modifier
                    .padding(start = 15.dp, end = 16.dp)
                    .padding(vertical = 10.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.Start,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_aifit),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp)
                )
                Text(
                    text = stringResource(id = R.string.aiFits_report),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    ),
                    modifier = Modifier.padding(start = 4.dp)
                )
            }
            // step
            Row(
                modifier = Modifier
                    .padding(vertical = 10.dp)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Image(
                        modifier = Modifier.size(16.dp),
                        painter = painterResource(id = R.drawable.ic_step),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit
                    )
                    Text(
                        text = stringResource(id = R.string.steps),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(start = 4.dp)
                    )
                    Text(
                        text = if (hasData) stepData.toString() else "--",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF6181E9),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }

                Image(
                    modifier = Modifier.size(14.dp),
                    painter = painterResource(id = R.drawable.ic_more),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
            }

            HorizontalDivider(
                Modifier
                    .padding(vertical = 8.dp)
                    .padding(horizontal = 16.dp),
                thickness = 0.5.dp,
                color = Color(0xFFE7EDFD)
            )
            // calorie
            Row(
                modifier = Modifier
                    .padding(vertical = 10.dp)
                    .padding(bottom = 15.dp)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Image(
                        modifier = Modifier.size(16.dp),
                        painter = painterResource(id = R.drawable.ic_calorie),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit
                    )
                    Text(
                        text = stringResource(id = R.string.calories),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(start = 4.dp)
                    )
                    Text(
                        text = if (hasData) calorieData.toInt().toString() else "--",
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF6181E9),
                            textAlign = TextAlign.Center,
                        ),
                        modifier = Modifier.padding(start = 16.dp)
                    )
                }

                Image(
                    modifier = Modifier.size(14.dp),
                    painter = painterResource(id = R.drawable.ic_more),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}

//@Preview
//@Composable
//private fun PreviewAiFits() {
//    Column(
//        modifier = Modifier
//            .fillMaxSize()
//            .background(Color.Black),
//        horizontalAlignment = Alignment.CenterHorizontally,
//    ) {
//        AiFits(
//            modifier = Modifier
//                .fillMaxWidth(0.8f)
//                .wrapContentHeight()
//        )
//    }
//}