package org.aihealth.ineck.view.screen.home

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Snackbar
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import kotlinx.coroutines.cancel
import org.aihealth.ineck.R
import org.aihealth.ineck.model.vitalsigns.VitalSignGroupLoadingState
import org.aihealth.ineck.util.UnitUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.custom.AIHBottomSheet
import org.aihealth.ineck.view.custom.vieweffect.shimmerEffect
import org.aihealth.ineck.view.directions.HistoryVitalSignsDirections
import org.aihealth.ineck.viewmodel.dao.HomeScreenEvent

@Composable
fun VitalSignsModule(
    state: VitalSignsModuleState,
    onDismissEvent: (Boolean) -> Unit,
    viewModel: HomeScreenEvent,
    isCelsiusState: State<Boolean>,
    isMmolL: State<Boolean>
) {
    val snackBarHost = remember { SnackbarHostState() }
    val snackBarBackground = Color.Black.copy(alpha = .9f)
    val snackBarTextColor = Color.White
//    LogUtil.i("VitalSignsModule state:$state")
    if (state == VitalSignsModuleState.VISIBLE) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Transparent)
        ) {
            val coroutineScope = rememberCoroutineScope()
            AIHBottomSheet(
                onDismissRequest = {
                    onDismissEvent(false)
                    coroutineScope.cancel()
                }
            ) {
                SnackbarHost(
                    hostState = snackBarHost,
                    modifier = Modifier
                        .fillMaxWidth(.9f)
                        .align(Alignment.Center)
                        .padding(vertical = 16.dp)
                        .zIndex(10f)
                ) {
                    Snackbar(
                        containerColor = snackBarBackground,
                        contentColor = snackBarTextColor,
                    ) {
                        Text(
                            text = it.visuals.message,
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                }
                VitalSignsItemList(
                    viewModel = viewModel,
                    isCelsiusState = isCelsiusState,
                    isMmolL = isMmolL,
                )

            }
        }
    }
}

/**
 *  生命体征卡片
 *  @param  viewModel   视图模型对象
 *  @param  isCelsiusState  生命体征体温视图是否采用摄氏度作为测量单位状态
 *  @param  isMmolL 生命体征血糖视图是否采用 毫摩尔/升(mmol/L) 作为测量单位状态
 */
@Composable
fun VitalSignsItemList(
    viewModel: HomeScreenEvent,
    isCelsiusState: State<Boolean>,
    isMmolL: State<Boolean>
) {
    val vitalSignsState = viewModel.vitalSignsData.collectAsState()

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Spacer(modifier = Modifier.height(20.dp))
        /* 各个生命体征模块集合 */
        when (vitalSignsState.value) {
            is VitalSignGroupLoadingState.Loading -> {
                /* 加载状态 */
                VitalSignsModuleCardInnerLoadingModel()
                VitalSignsModuleCardInnerLoadingModel()
                VitalSignsModuleCardInnerLoadingModel()
                VitalSignsModuleCardInnerLoadingModel()
                VitalSignsModuleCardInnerLoadingModel()
            }

            is VitalSignGroupLoadingState.Success -> {
                val vitalSigns =
                    (vitalSignsState.value as VitalSignGroupLoadingState.Success).vitalSigns
                /* 生命体征数据请求成功 */

                /* 体温数据 */
                VitalSignsModuleCardInnerModel(
                    icon = R.drawable.ic_vital_sign_temperature,
                    title = stringResource(id = R.string.body_temperature),
                    value = when {
                        vitalSigns.bodyTemperature.temperature <= 0 -> "--"
                        else -> if (isCelsiusState.value) {
                            vitalSigns.bodyTemperature.temperature.toString()
                        } else {
                            String.format(
                                "%.1f",
                                UnitUtil.celsiusToFahrenheit(vitalSigns.bodyTemperature.temperature.toDouble())
                            )
                        }
                    },
                    unit = if (isCelsiusState.value) "℃" else "℉",
                    onCheckHistoryEvent = {
                        val model = HistoryVitalSignsDirections.HistoryEventModel(
                            vitalSign = 1,
                            isCelsius = isCelsiusState.value,
                            isMmolL = isMmolL.value
                        )
                        startScreen(
                            route = HistoryVitalSignsDirections.actionToHistoryVitalCompose(
                                model = model
                            ),
                        )
                    }
                )
                /* 血压数据 */
                VitalSignsModuleCardInnerModel(
                    icon = R.drawable.ic_vital_sign_blood_pressure,
                    title = stringResource(id = R.string.blood_pressure),
                    value = "${vitalSigns.bloodPressure.diastolic.takeIf { it >= 0 } ?: "--"}/${vitalSigns.bloodPressure.systolic.takeIf { it >= 0 } ?: "--"}",
                    unit = "mmHg",
                    onCheckHistoryEvent = {
                        val model = HistoryVitalSignsDirections.HistoryEventModel(
                            vitalSign = 3,
                            isCelsius = isCelsiusState.value,
                            isMmolL = isMmolL.value
                        )
                        startScreen(
                            route = HistoryVitalSignsDirections.actionToHistoryVitalCompose(
                                model = model
                            ),
                        )
                    }
                )
                /* 心率数据 */
                VitalSignsModuleCardInnerModel(
                    icon = R.drawable.ic_vital_sign_heart_rate,
                    title = stringResource(id = R.string.heart_rate),
                    value = "${(vitalSigns.heartRate.value).let { value -> if (value >= 0) value else "--" }}",
                    unit = "bpm",
                    onCheckHistoryEvent = {
                        val model = HistoryVitalSignsDirections.HistoryEventModel(
                            vitalSign = 0,
                            isCelsius = isCelsiusState.value,
                            isMmolL = isMmolL.value
                        )
                        startScreen(
                            route = HistoryVitalSignsDirections.actionToHistoryVitalCompose(
                                model = model
                            ),
                        )

                    }
                )
                /* 血糖数据 */
                VitalSignsModuleCardInnerModel(
                    icon = R.drawable.ic_vital_sign_blood_sugar,
                    title = stringResource(id = R.string.blood_glucose),
                    value = if (isMmolL.value) {
                        "${(vitalSigns.bloodGlucose.level).let { level -> if (level >= 0) level else "--" }}"
                    } else {
                        (UnitUtil.glucoseMmolLToMgDl(vitalSigns.bloodGlucose.level.toDouble())).let { level ->
                            if (level >= 0) "%.1f".format(level) else "--"
                        }
                    },
                    unit = if (isMmolL.value) "mmol/L" else "mg/dL",
                    onCheckHistoryEvent = {
                        val model = HistoryVitalSignsDirections.HistoryEventModel(
                            vitalSign = 4,
                            isCelsius = isCelsiusState.value,
                            isMmolL = isMmolL.value
                        )
                        startScreen(
                            route = HistoryVitalSignsDirections.actionToHistoryVitalCompose(
                                model = model
                            ),
                        )

                    }
                )
                /* 血氧数据 */
                VitalSignsModuleCardInnerModel(
                    icon = R.drawable.ic_vital_sign_blood_oxygen,
                    title = stringResource(id = R.string.blood_oxygen),
                    value = "${
                        (vitalSigns.bloodOxygen.saturation).let { saturation ->
                            if (saturation >= 0) saturation else "--"
                        }
                    }",
                    unit = "%",
                    onCheckHistoryEvent = {
                        val model = HistoryVitalSignsDirections.HistoryEventModel(
                            vitalSign = 2,
                            isCelsius = isCelsiusState.value,
                            isMmolL = isMmolL.value
                        )
                        startScreen(
                            route = HistoryVitalSignsDirections.actionToHistoryVitalCompose(
                                model = model
                            ),
                        )
                    }
                )
            }

            is VitalSignGroupLoadingState.Failure -> {
                /* 生命体征数据请求失败 */
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(220.dp)
                        .padding(start = 12.dp)
                        .clip(RoundedCornerShape(16.dp))
                        .clickable {
                            /* 重新加载向服务器请求生命体征数据 */
                            viewModel.loadVitalSignData()
                        },
                    verticalArrangement = Arrangement.Center,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Icon(
                        painter = painterResource(id = R.drawable.icon_data_error_msg),
                        contentDescription = "something error",
                        tint = Color(0x80999999),
                        modifier = Modifier
                            .size(58.dp)
                            .padding(horizontal = 10.dp, vertical = 10.dp)
                    )
                    Text(
                        text = stringResource(id = R.string.vital_signs_error),
                        fontWeight = FontWeight.Light,
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center,
                        color = Color(0x80999999),
                        modifier = Modifier.padding(horizontal = 4.dp, vertical = 10.dp)
                    )
                }
            }
        }
    }

}

/**
 *  update 2024.02.02
 *  加载态的 生命体征数据卡片内模块
 */
@Composable
internal fun VitalSignsModuleCardInnerLoadingModel() {

    Card(
        modifier = Modifier
            .padding(horizontal = 4.dp, vertical = 6.dp),
        shape = RoundedCornerShape(12.dp),

        ) {
        Column(
            modifier = Modifier
                .background(color = Color(0xFFF7F8FC))
                .padding(horizontal = 10.dp, vertical = 8.dp)
        ) {
            Box(
                modifier = Modifier
                    .height(14.dp)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(18.dp))
                    .shimmerEffect()
            )
            Spacer(modifier = Modifier.height(28.dp))
            Box(
                modifier = Modifier
                    .height(14.dp)
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(18.dp))
                    .shimmerEffect()
                    .align(Alignment.CenterHorizontally)
            )
        }
    }

}

/**
 *  生命体征数据卡片内模块
 *  @param  title   模块标题
 *  @param  content 模块内可组合项内容
 *  @param  onRecordEvent   记录按键点击事件
 *  @param  onCheckHistoryEvent 查看历史记录点击事件
 */
@Preview()
@Composable
internal fun VitalSignsModuleCardInnerModel(
    @DrawableRes icon: Int = R.drawable.ic_vital_sign_temperature,
    title: String = "体温",
    value: String = "36.5",
    unit: String = "℃",
    onCheckHistoryEvent: () -> Unit = {},
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .height(70.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF6F9FE)
        ),
        shape = RoundedCornerShape(12.dp),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = 10.dp)
                .padding(start = 14.dp, end = 12.dp)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() },
                ) { onCheckHistoryEvent() },
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Image(
                        painter = painterResource(id = icon),
                        contentDescription = "icon",
                        contentScale = ContentScale.None,
                        modifier = Modifier
                            .width(16.dp)
                            .height(16.dp),
                    )
                    /* 模块内标题 */
                    Text(
                        text = title,
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        textAlign = TextAlign.Start,
                        modifier = Modifier.padding(start = 4.dp),
                    )
                }
                /* 查看该模块更多历史记录信息入口IconButton */
                Image(
                    painter = painterResource(id = R.drawable.ic_more),
                    contentDescription = "image description",
                    contentScale = ContentScale.None,
                    modifier = Modifier.size(14.dp),
                )
            }
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Text(
                    text = value,
                    style = TextStyle(
                        fontSize = 16.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF6181E9),
                        textAlign = TextAlign.Center,
                    ),
                    modifier = Modifier.padding(start = 18.dp, end = 2.dp)
                )
                Text(
                    text = unit,
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(400),
                        color = Color(0xFF999999),
                        textAlign = TextAlign.Center,
                    )
                )
            }
        }
    }
}

enum class VitalSignsModuleState {
    HIDDEN,
    VISIBLE
}