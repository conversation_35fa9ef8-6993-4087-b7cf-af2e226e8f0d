package org.aihealth.ineck.view.screen.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R


@Composable
fun ScaleModule(
    modifier: Modifier = Modifier,
    onPainClick: () -> Unit = {},
    onMeasurementClick: () -> Unit = {}
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.White,
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
        ) {
            // Pain scale
            Row(
                modifier = Modifier
                    .padding(vertical = 10.dp)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) { onPainClick() },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Image(
                        modifier = Modifier.size(16.dp),
                        painter = painterResource(id = R.drawable.ic_question_promis),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit
                    )
                    Text(
                        text = stringResource(id = R.string.pain_record),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }

                Image(
                    modifier = Modifier.size(14.dp),
                    painter = painterResource(id = R.drawable.ic_more),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
            }
            HorizontalDivider(
                Modifier
                    .padding(vertical = 8.dp)
                    .padding(horizontal = 16.dp),
                thickness = 0.5.dp,
                color = Color(0xFFE7EDFD)
            )
            Row(
                modifier = Modifier
                    .padding(vertical = 10.dp)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .clickable(
                        indication = null,
                        interactionSource = remember { MutableInteractionSource() }
                    ) { onMeasurementClick() },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(
                    modifier = Modifier,
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.Start
                ) {
                    Image(
                        modifier = Modifier.size(16.dp),
                        painter = painterResource(id = R.drawable.ic_questionnair_odi),
                        contentDescription = "image description",
                        contentScale = ContentScale.Fit
                    )
                    Text(
                        text = stringResource(id = R.string.promis_odi),
                        style = TextStyle(
                            fontSize = 14.sp,
                            fontWeight = FontWeight(500),
                            color = Color(0xFF333333),
                        ),
                        modifier = Modifier.padding(start = 4.dp)
                    )
                }

                Image(
                    modifier = Modifier.size(14.dp),
                    painter = painterResource(id = R.drawable.ic_more),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
            }
        }
    }
}

@Composable
fun ODIAndPromise(
    modifier: Modifier = Modifier,
    onMeasurementClick: () -> Unit = {}
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.White,
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White
        )
    ) {
        Row(
            modifier = Modifier
                .padding(vertical = 10.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) { onMeasurementClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    modifier = Modifier.size(16.dp),
                    painter = painterResource(id = R.drawable.ic_questionnair_odi),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
                Text(
                    text = stringResource(id = R.string.promis_odi),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    ),
                    modifier = Modifier.padding(start = 4.dp)
                )
            }

            Image(
                modifier = Modifier.size(14.dp),
                painter = painterResource(id = R.drawable.ic_more),
                contentDescription = "image description",
                contentScale = ContentScale.Fit
            )
        }

    }

}

@Composable
fun PainScale(
    modifier: Modifier = Modifier,
    onPainClick: () -> Unit = {}
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.White,
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White
        )
    ) {
        Row(
            modifier = Modifier
                .padding(vertical = 10.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) { onPainClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    modifier = Modifier.size(16.dp),
                    painter = painterResource(id = R.drawable.ic_question_promis),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
                Text(
                    text = stringResource(id = R.string.pain_record),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    ),
                    modifier = Modifier.padding(start = 4.dp)
                )
            }

            Image(
                modifier = Modifier.size(14.dp),
                painter = painterResource(id = R.drawable.ic_more),
                contentDescription = "image description",
                contentScale = ContentScale.Fit
            )
        }
    }
}

@Composable
fun NeuralScale(
    modifier: Modifier = Modifier,
    onNeuralClick: () -> Unit = {}
) {
    Card(
        modifier = modifier,
        shape = RoundedCornerShape(8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White,
            contentColor = Color.White,
            disabledContainerColor = Color.White,
            disabledContentColor = Color.White
        )
    ) {
        Row(
            modifier = Modifier
                .padding(vertical = 10.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .clickable(
                    indication = null,
                    interactionSource = remember { MutableInteractionSource() }
                ) { onNeuralClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                modifier = Modifier,
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Start
            ) {
                Image(
                    modifier = Modifier.size(16.dp),
                    painter = painterResource(id = R.drawable.ic_neural),
                    contentDescription = "image description",
                    contentScale = ContentScale.Fit
                )
                Text(
                    text = stringResource(id = R.string.neural_record),
                    style = TextStyle(
                        fontSize = 14.sp,
                        fontWeight = FontWeight(500),
                        color = Color(0xFF333333),
                    ),
                    modifier = Modifier.padding(start = 4.dp)
                )
            }

            Image(
                modifier = Modifier.size(14.dp),
                painter = painterResource(id = R.drawable.ic_more),
                contentDescription = "image description",
                contentScale = ContentScale.Fit
            )
        }
    }
}

//@Preview(showBackground = true)
//@Composable
//private fun PreviewScaleModule() {
//    Column(
//        modifier = Modifier
//            .fillMaxSize()
//            .background(Color.Black),
//        horizontalAlignment = Alignment.CenterHorizontally,
//    ) {
//        ScaleModule(
//            modifier = Modifier
//                .fillMaxWidth(0.8f)
//                .wrapContentHeight()
//        )
//    }
//
//}