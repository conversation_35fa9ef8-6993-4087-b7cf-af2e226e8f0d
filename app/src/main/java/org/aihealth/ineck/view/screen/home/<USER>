package org.aihealth.ineck.view.screen.home

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Composable
fun AboutDevice(
    modifier: Modifier = Modifier,
    isConnected: Boolean = true,
    power: Int,
    maxAngle:Int,
) {
    Row(
        modifier = modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceAround,
        verticalAlignment = Alignment.CenterVertically,
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(id = R.drawable.img_bluetooth),
                contentDescription = null,
                modifier = Modifier
                    .size(20.dp),
                tint = Color(0xFF2B56D7),
//                            tint = if (isConneced) Color(0XFF2B56D7) else Color(0xFFCECECE)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Column {
                Text(
                    text = stringResource(id = R.string.device),
                    fontSize = 12.sp,
                    color = Color(0XFF7C7C7C)
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = stringResource(id = if (isConnected) R.string.connected else R.string.unconnected),
                    fontSize = 16.sp,
                    color = Color(0XFF7C7C7C)
                )
            }
        }

        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(id = R.drawable.img_power),
                contentDescription = null,
                modifier = Modifier.size(13.dp, 23.dp),
                tint = if (isConnected) Color(0XFF2B56D7) else Color(0XFFCECECE)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Column {
                Text(
                    text = stringResource(id = R.string.battery),
                    fontSize = 12.sp,
                    color = Color(0XFF7C7C7C)
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = if (isConnected) "${power}%" else "0%",
                    fontSize = 16.sp,
                    color = Color(0XFF7C7C7C),
                    fontWeight = FontWeight.Medium
                )
            }
        }
        Row(
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_max_angle),
                contentDescription = null,
                modifier = Modifier.size(20.dp),
                tint = if (isConnected) Color(0XFF2B56D7) else Color(0XFFCECECE)
            )
            Spacer(modifier = Modifier.width(4.dp))
            Column {
                Text(
                    text = stringResource(id = R.string.max),
                    fontSize = 12.sp,
                    color = Color(0XFF7C7C7C)
                )
                Spacer(modifier = Modifier.height(2.dp))
                Text(
                    text = "${maxAngle}°",
                    fontSize = 16.sp,
                    color = Color(0XFF7C7C7C),
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
private fun DeviceAbout() {
    AboutDevice(isConnected = true, power = 100, maxAngle = 100)
}