package org.aihealth.ineck.view.screen.home

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Alignment.Companion.CenterStart
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.LayoutCoordinates
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.model.DeviceType
import kotlin.math.cos
import kotlin.math.floor
import kotlin.math.sin

@Composable
fun NeckAngleStatusModule(
    modifier: Modifier = Modifier,
    isShowAngle: Boolean = false,
    angle: Int = 0,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Box {
            Image(
                modifier = Modifier
                    .padding(start = 16.dp)
                    .size(100.dp)
                    .align(Alignment.BottomStart),
                contentScale = ContentScale.Fit,
                contentDescription = "",
                painter = painterResource(
                    id = if (isShowAngle) {
                        when (angle) {
                            in -80 until 0 -> DeviceType.aiNeck.connectedDrawable_negative_15
                            in 45..90 -> DeviceType.aiNeck.connectedDrawable_45
                            in 30 until 45 -> DeviceType.aiNeck.connectedDrawable_30
                            in 15 until 30 -> DeviceType.aiNeck.connectedDrawable_15
                            else -> DeviceType.aiNeck.connectedDrawable_0
                        }
                    } else {
                        DeviceType.aiNeck.unConnecteDrawable
                    }
                ),
            )

            var x: Float
            var y: Float
            val path = Path()

            /* 角度弧线图像 */
            Image(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .drawBehind {
                        /*
                        if (isShowAngle) {
                            val angleTag = floor(angle * 86 / 90F).toInt() + 2
                            x =
                                ((195.dp.toPx() * cos(Math.toRadians(90.0 - angleTag))).toFloat() + 19.dp.toPx()) * 0.8f
                            y =
                                (224.dp.toPx() - (189.dp.toPx() * sin(Math.toRadians(90.0 - angle))).toFloat()) * 0.8f
                            rotate(
                                angle.toFloat(),
                                pivot = Offset(x, y)
                            ) {
                                path.moveTo(x - 6.dp.toPx(), y)
                                path.lineTo(x + 6.dp.toPx(), y)
                                path.lineTo(x, y - 8.dp.toPx())
                                drawPath(path, Color(0XFFBBBBBB))
                            }
                        }
                         */
                    },
                contentDescription = "",
                painter = painterResource(id = if (isShowAngle) R.drawable.img_home_connect_background_new else R.drawable.img_home_unconnect_background_new),
                contentScale = ContentScale.Fit,
            )

        }
        /* 轻度、 中度、 重度标识 */
        StatusIdentifier(
            modifier = Modifier
                .padding(vertical = 10.dp),
            isShowAngle = isShowAngle
        )
    }

}

@Composable
fun BackAngleStatusModule(
    modifier: Modifier = Modifier,
    isShowAngle: Boolean = false,
    angle: Int = 0,
) {
    var size by remember { mutableStateOf(Size.Zero) }
    Column(
        modifier = modifier,
    ) {
        Box {
            Image(
                modifier = Modifier
                    .padding(start = 16.dp)
                    .size(100.dp)
                    .align(Alignment.BottomStart),
                contentDescription = "",
                contentScale = ContentScale.Fit,
                painter = painterResource(
                    id = if (isShowAngle) {
                        when (angle) {
                            in -80 until 0 -> DeviceType.aiBack.connectedDrawable_negative_15
                            in 45..90 -> DeviceType.aiBack.connectedDrawable_45
                            in 30 until 45 -> DeviceType.aiBack.connectedDrawable_30
                            in 15 until 30 -> DeviceType.aiBack.connectedDrawable_15
                            else -> DeviceType.aiBack.connectedDrawable_0
                        }
                    } else {
                        DeviceType.aiBack.unConnecteDrawable
                    }
                ),
            )

            var x: Float
            var y: Float
            val path = Path()
            /* 角度弧线图像 */
            Image(
                modifier = Modifier
                    .padding(top = 5.dp)
                    .align(Alignment.TopStart)
                    .onGloballyPositioned { coordinates ->
                        // Update the size state with the actual size
                        size = Size(
                            coordinates.size.width.toFloat(),
                            coordinates.size.height.toFloat()
                        )
                    }
                    .drawBehind {
                        if (isShowAngle) {

                            val angleTag = floor(angle * 86 / 90F).toInt() + 2

                            // Assuming the lower left corner as the origin (0,0)
                            // Adjust the calculations for x and y to position the drawing point correctly
                            x =
                                (size.width.dp.toPx() * cos(Math.toRadians(angleTag.toDouble()))).toFloat()
                            y =
                                size.height - (size.height.dp.toPx() * sin(Math.toRadians(angleTag.toDouble()))).toFloat()

                            rotate(
                                angle.toFloat(),
                                pivot = Offset(x, y)
                            ) {
                                path.moveTo(x - 6.dp.toPx(), y)
                                path.lineTo(x + 6.dp.toPx(), y)
                                path.lineTo(
                                    x,
                                    y + 8.dp.toPx()
                                ) // Adjusted to point upwards from the lower left corner
                                drawPath(path, Color(0XFFBBBBBB))
                            }
                        }
                    },
                contentDescription = "",
                painter = painterResource(id = if (isShowAngle) R.drawable.img_home_connect_background_new else R.drawable.img_home_unconnect_background_new),
                contentScale = ContentScale.Fit
            )

        }
        /* 轻度、 中度、 重度标识 */
        StatusIdentifier(
            modifier = Modifier
                .fillMaxWidth()
                .padding(start = 10.dp)
                .padding(vertical = 10.dp),
            isShowAngle = isShowAngle
        )
    }

}

@Composable
fun StatusIdentifier(
    modifier: Modifier = Modifier,
    isShowAngle: Boolean = false
) {
    val density = LocalDensity.current
    Row(
        modifier = modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceAround
    ) {
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    if (isShowAngle) Color(0XFF75CFC1) else Color(
                        0XFFD9D9D9
                    ),
                    CircleShape
                )
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = stringResource(id = R.string.mild),
            fontSize = with(density) { 14.dp.toSp() },
            color = Color(0XFF999999),
        )
        Spacer(modifier = Modifier.weight(1F))

        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    if (isShowAngle) Color(0XFFFFD236) else Color(
                        0XFFD9D9D9
                    ),
                    CircleShape
                )
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = stringResource(id = R.string.moderate),
            fontSize = with(density) { 14.dp.toSp() },
            color = Color(0XFF999999)
        )
        Spacer(modifier = Modifier.weight(1F))
        Box(
            modifier = Modifier
                .size(8.dp)
                .background(
                    if (isShowAngle) Color(0XFFFC7349) else Color(
                        0XFFD9D9D9
                    ),
                    CircleShape
                )
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = stringResource(id = R.string.severe),
            fontSize = with(density) { 14.dp.toSp() },
            color = Color(0XFF999999)
        )
    }
}

@Composable
fun VCPreview(
    modifier: Modifier = Modifier,
    onGloballyPositioned: (LayoutCoordinates) -> Unit,
    content: @Composable () -> Unit
) {
    Box(
        modifier = modifier
            .size(100.dp, 120.dp)
            .background(
                color = Color.Transparent,
                shape = RoundedCornerShape(8.dp),
            )
            .clip(RoundedCornerShape(8.dp))
            .onGloballyPositioned { coordinates ->
                onGloballyPositioned(coordinates)
            },
        contentAlignment = Center
    ) {
        content()
    }
}

@Composable
fun AngleStatue(
    modifier: Modifier = Modifier,
    angle: Int
) {
    Column(
        modifier = modifier,
        horizontalAlignment = CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = stringResource(id = R.string.real_time),
            style = TextStyle(
                fontSize = 14.sp,
                fontWeight = FontWeight(500),
                color = Color(0xFF2B56D7),
                textAlign = TextAlign.Right,
            )
        )
        /** 若连接设备时同期开启视频监测，则角度展示数据显示的为设备传回数据 */
        Text(
            text = "${angle}°",
            fontSize = 20.sp,
            color = Color(0XFF2B56D7),
            fontWeight = FontWeight.Medium
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewAngleModules() {
    Box(
        modifier = Modifier
            .padding(top = 5.dp)
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        NeckAngleStatusModule(
            modifier = Modifier
                .align(CenterStart)
                .fillMaxWidth(0.7f),
            isShowAngle = true,
            angle = 50
        )
    }
}