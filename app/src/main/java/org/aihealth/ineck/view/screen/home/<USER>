package org.aihealth.ineck.view.screen.home

import android.content.Context
import android.content.Intent
import android.provider.Settings
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Alignment.Companion.CenterHorizontally
import androidx.compose.ui.Alignment.Companion.CenterStart
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.Screen
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.notification.FloatingCameraService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.startScreen
import org.aihealth.ineck.view.button.DetectionButton
import org.aihealth.ineck.view.button.RecalibrateButton
import org.aihealth.ineck.view.button.TypeSelectButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiNeck
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiNeckSmartVersion
import org.aihealth.ineck.view.screen.TopTipsBlockOfConnectionStatusCard
import org.aihealth.ineck.view.screen.vcguide.AiNeckVCGuideScreen
import org.aihealth.ineck.viewmodel.DetectedResult
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.device.DeviceViewModel
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel

@Composable
fun AiNeckHomeModule(
    modifier: Modifier = Modifier,
    viewModel: MainViewModel,
    deviceViewModel: DeviceViewModel
) {
    /** 上下文 */
    val context = LocalContext.current

    val scope = rememberCoroutineScope()
    // 当前设备是否连接
    val isConnected by deviceViewModel.neckDeviceConnected.collectAsStateWithLifecycle()

    // 设备角度
    val deviceAngle = remember { Animatable(0f) }
    var maxAngle by remember { mutableIntStateOf(0) }
    // 设备电量
    var power by remember {
        mutableIntStateOf(0)
    }

    /** 开启视频检测状态 */
    val cameraScanState = viewModel.homeScreen.isCameraScanModel.collectAsState()

    /** 判断浮动窗口是否为初次锚点的状态 */
    val initAnchorState = viewModel.homeScreen.initFloatingWindowAnchorState.collectAsState()
    viewModel.deviceScreen.initSensor(context = context)

    /** 重力参数状态 */
    val gravityValue = viewModel.deviceScreen.verticalGravity.collectAsState()

    /** 视频监测角度 */
    val angleCVState = remember { Animatable(0f) }

    /** 是否显示悬浮窗权限提示对话框 */
    var showOverlayPermissionDialog by rememberSaveable { mutableStateOf(false) }

    /** 是否显示校准提示对话框 */
    var showCalibrationDialog by rememberSaveable { mutableStateOf(false) }
    var isVibrationModeEnabled =
        deviceViewModel.neckIsVibrationModeEnabled.collectAsStateWithLifecycle()

    val lifecycleOwner = LocalLifecycleOwner.current
    val neckDeviceConfig = deviceViewModel.neckDeviceConfig.collectAsStateWithLifecycle()

    // 使用LaunchedEffect监听设备连接和配置变化
    LaunchedEffect(isConnected, neckDeviceConfig.value) {
        if (isConnected && !neckDeviceConfig.value.isCalibrated) {
            LogUtil.d("设备已连接但未校准，显示校准提示")
            showCalibrationDialog = true
        } else if (isConnected && neckDeviceConfig.value.isCalibrated) {
            // 设备已连接且已校准，确保对话框不显示
            LogUtil.d("设备已连接且已校准，不显示校准提示")
            showCalibrationDialog = false
        }
    }

    // 校准提示对话框
    if (showCalibrationDialog && isConnected) {
        AlertDialog(
            onDismissRequest = { showCalibrationDialog = false },
            title = { Text(stringResource(id = R.string.calibration_suggestion_title)) },
            text = { Text(stringResource(id = R.string.calibration_suggestion_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        showCalibrationDialog = false
                        startScreen(Screen.CalibrationDeviceRoute.route + "?deviceType=${DeviceType.aiNeck}")
                    }
                ) {
                    Text(stringResource(id = R.string.confirm_calibration))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showCalibrationDialog = false
                    }
                ) {
                    Text(stringResource(id = R.string.maybe_later))
                }
            }
        )
    }

    HomeStatusCard(
        modifier = modifier.fillMaxWidth()
    ) {
        TopTipsBlockOfConnectionStatusCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            if (isConnected) {
                GuideToNeckImproveOfAiNeck()
            } else {
                GuideToNeckImproveOfAiNeckSmartVersion()
            }
        }
        // 分割线
        AIHDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )
        Box(
            modifier = Modifier
                .padding(top = 5.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            NeckAngleStatusModule(
                modifier = Modifier
                    .align(CenterStart)
                    .fillMaxWidth(0.7f),
                isShowAngle = isConnected,
                angle = deviceAngle.value.toInt()
            )
            if (isConnected) {
                AngleStatue(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 16.dp, bottom = 16.dp),
                    angle = deviceAngle.value.toInt()
                )
            }

            if (!cameraScanState.value) {
                VCPreview(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .fillMaxWidth(0.3f),
                    onGloballyPositioned = { coordinates ->
                        if (initAnchorState.value) {
                            // 获取在屏幕上的绝对位置
                            coordinates.boundsInRoot().let {
                                viewModel.homeScreen.anchorXOfFloatingWindow.value = it.left
                                viewModel.homeScreen.anchorYOfFloatingWindow.value = it.top
                            }
                            viewModel.homeScreen.initFloatingWindowAnchorState.update { false }
                        }
                    }
                ) {
                    Image(
                        painter = painterResource(R.drawable.video_scan),
                        contentDescription = "background_image",
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.matchParentSize()
                    )
                    DetectionButton(
                        modifier = Modifier
                            .align(Center),
                        onClick = {
                            if (Settings.canDrawOverlays(context)) {
                                if (XXPermissions.isGranted(activity, Permission.CAMERA)) {
                                    viewModel.homeScreen.isBootVCGuideScreen.value = true
                                } else {
                                    viewModel.homeScreen.showPowerDialogVisibleState = true
                                    viewModel.homeScreen.theClickType = 2
                                }
                            } else {
                                showOverlayPermissionDialog = true
                            }
                        }
                    )
                }
            }
        }
        if (isConnected) {
            /* 重新校准Button位置 */
            RecalibrateButton(
                modifier = Modifier
                    .padding(vertical = 5.dp)
                    .fillMaxWidth()
                    .wrapContentHeight(),
                onClick = { startScreen(Screen.CalibrationDeviceRoute.route + "?deviceType=${DeviceType.aiNeck}") }
            )
            AboutDevice(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 5.dp),
                power = power,
                maxAngle = maxAngle,
            )
            TypeSelectButton(
                selectedIndex = if (isVibrationModeEnabled.value) 0 else 1,
                array = stringArrayResource(id = R.array.device_mode),
                onClick = {
                    LogUtil.d(
                        "Click TypeSelectButton",
                        "isVibrationModeEnabled ${isVibrationModeEnabled.value}"
                    )
                    deviceViewModel.toggleVibrationMode(
                        DeviceType.aiNeck,
                        it == 0
                    )
                },
                modifier = Modifier
                    .padding(horizontal = 16.dp, vertical = 5.dp)
                    .align(CenterHorizontally)
            )
        }

    }
    AiNeckVCGuideScreen(
        modifier = Modifier
            .fillMaxWidth(0.9f),
        visible = viewModel.homeScreen.isBootVCGuideScreen.value,
        viewModel = viewModel<VCGuideViewModel>(
            factory = object : ViewModelProvider.Factory {
                override fun <T : ViewModel> create(modelClass: Class<T>): T {
                    if (modelClass.isAssignableFrom(VCGuideViewModel::class.java))
                        return modelClass.getConstructor(Context::class.java)
                            .newInstance(context)
                    throw IllegalArgumentException("")
                }
            }
        ),
        onDismissEvent = { result ->
            when (result) {
                is VCDetectingResult.DetectingSuccess -> {
                    viewModel.homeScreen.isBootVCGuideScreen.value = false
//                            viewModel.homeScreen.isShowVCGuideDetectResultDialog.value = true
                    val angle = (result.result as Triple<*, *, *>).first
                    viewModel.homeScreen.detectResultState.update {
                        DetectedResult.DetectedSuccess(angle as Float)
                    }
                    viewModel.homeScreen.detectResultValueForFitted = angle as Float

                    viewModel.homeScreen.detectResultState.update { DetectedResult.None }
                    viewModel.homeScreen.onChangeCameraScanModel(true)          // 视频监测开启
                    viewModel.deviceScreen.gravityStartListening()                 // 重力监测开启
                }

                is VCDetectingResult.DetectingError -> {
                    viewModel.homeScreen.isBootVCGuideScreen.value = false
                    viewModel.homeScreen.onChangeCameraScanModel(false)
//                            viewModel.homeScreen.isShowVCGuideDetectResultDialog.value = true
                }

                else -> {}
            }
        }
    )
    LaunchedEffect(cameraScanState.value) {
        if (cameraScanState.value) {

            if (Settings.canDrawOverlays(context)) {
                // 监听服务状态
                FloatingCameraService.isServiceRunning.observe(lifecycleOwner) { isRunning ->
                    if (!isRunning) {
                        // 检查是否是用户主动关闭
                        if (FloatingCameraService.isUserClosing.value == true) {
                            // 用户主动关闭，更新UI状态
                            LogUtil.i("用户主动关闭FloatingCameraService")
                            viewModel.homeScreen.onChangeCameraScanModel(false)
                            FloatingCameraService.isUserClosing.value = false  // 重置状态
                        }
                    }
                }

                // 监听角度变化
                FloatingCameraService.headEulerAngleX.observe(lifecycleOwner) { angleX ->
                    scope.launch {
                        // 使用动画过渡到新角度
                        angleCVState.animateTo(
                            targetValue = angleX,
                            animationSpec = tween(durationMillis = 300) // 300ms的过渡时间
                        )
                    }
                }

                // 如果服务未运行，启动服务
                if (FloatingCameraService.isServiceRunning.value != true) {
                    val intent = Intent(baseApplication, FloatingCameraService::class.java).apply {
                        putExtra(
                            "detectResultValueForFitted",
                            viewModel.homeScreen.detectResultValueForFitted
                        )
                        putExtra("deviceType", viewModel.homeScreen.currentDeviceType)
                        putExtra(
                            "initialX",
                            viewModel.homeScreen.anchorXOfFloatingWindow.value.toInt()
                        )
                        putExtra(
                            "initialY",
                            viewModel.homeScreen.anchorYOfFloatingWindow.value.toInt()
                        )
                    }
                    ContextCompat.startForegroundService(baseApplication, intent)
                }
            } else {
                // 显示权限提示对话框
                showOverlayPermissionDialog = true
                viewModel.homeScreen.onChangeCameraScanModel(false)
            }
        }
    }

    // 悬浮窗权限提示对话框
    if (showOverlayPermissionDialog) {
        AlertDialog(
            onDismissRequest = { showOverlayPermissionDialog = false },
            title = { Text(stringResource(id = R.string.overlay_permission_title)) },
            text = { Text(stringResource(id = R.string.overlay_permission_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        showOverlayPermissionDialog = false
                        val intent = Intent(
                            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                            "package:${context.packageName}".toUri()
                        )
                        context.startActivity(intent)
                    }
                ) {
                    Text(stringResource(id = R.string.go_to_settings))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showOverlayPermissionDialog = false
                        // 用户拒绝开启悬浮窗权限，关闭视频检测
                        viewModel.homeScreen.onChangeCameraScanModel(false)
                    }
                ) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }

    // 获取设备状态
    LaunchedEffect(Unit) {
        launch {
            // 监听角度变化
            deviceViewModel.neckAngle.collect { angle ->
                LogUtil.d("HomeScreen", "angle: ${angle.toString()}")
                angle?.let {
                    deviceAngle.animateTo(it.toFloat(), tween(250))
                }
            }
        }

        // 监听电量和最大角度变化
        launch {
            deviceViewModel.neckBatteryLevel.collect { batteryLevel ->
                power = batteryLevel ?: 0
            }
        }

        launch {
            deviceViewModel.neckMaxAngle.collect { maxAngleValue ->
                maxAngle = maxAngleValue ?: 0
            }
        }
    }

    // 监听连接状态变化，管理电量读取定时任务
    var batteryReadingJob: kotlinx.coroutines.Job? = null

    // 上传数据到服务器并管理电量读取定时任务
    DisposableEffect(isConnected) {
        var currentTime = System.currentTimeMillis()

        // 上传数据到服务器
        scope.launch(Dispatchers.IO) {
            if (currentTime - viewModel.homeScreen.lastUpdateTime >= 1000 * 10) {
                viewModel.homeScreen.lastUpdateTime = currentTime
                // 上传当前数据库中基于设备连接的数据
                viewModel.postAngle(DeviceType.aiNeck)
            }
        }

        // 根据连接状态管理电量读取定时任务
        if (isConnected) {
            // 设备连接成功，启动电量读取定时任务
            LogUtil.d("HomeScreen", "设备已连接，启动电量读取定时任务")

            // 立即读取一次初始电量
            deviceViewModel.readBatteryLevel(DeviceType.aiNeck)

            // 创建新的定时任务
            batteryReadingJob = scope.launch {
                while (true) {
                    // 等待60秒后读取
                    delay(60000)
                    deviceViewModel.readBatteryLevel(DeviceType.aiNeck)
                    LogUtil.d("HomeScreen", "定时读取电量")
                }
            }
        } else if (batteryReadingJob != null) {
            // 设备断开，取消电量读取定时任务
            LogUtil.d("HomeScreen", "设备已断开，取消电量读取定时任务")
            batteryReadingJob?.cancel()
            batteryReadingJob = null
        }

        onDispose {
            // 上传最终数据
            scope.launch(Dispatchers.IO) {
                currentTime = System.currentTimeMillis()
                if (currentTime - viewModel.homeScreen.lastUpdateTime >= 1000 * 10) {
                    viewModel.homeScreen.lastUpdateTime = currentTime
                    viewModel.postAngle(DeviceType.aiNeck)
                }
            }

            // 如果定时任务存在，取消它
            if (batteryReadingJob != null) {
                LogUtil.d("HomeScreen", "组件销毁，取消电量读取定时任务")
                batteryReadingJob?.cancel()
                batteryReadingJob = null
            }
        }
    }
}