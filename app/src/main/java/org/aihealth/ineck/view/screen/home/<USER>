package org.aihealth.ineck.view.screen.home

import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R

@Composable
fun Tips(
    modifier: Modifier = Modifier,
    @StringRes stringRes: Int = R.string.tips_1,
    onClick: () -> Unit
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .background(Color(0xFFE5EFFF), shape = RoundedCornerShape(8.dp))
            .padding(start = 15.dp, end = 16.dp)
            .padding(vertical = 10.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(0.8f),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Start
        ) {
            Text(
                modifier = Modifier,
                text = stringResource(id = stringRes),
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight(400),
                    color = Color(0xFF747475),
                    textAlign = TextAlign.Start,
                )
            )
        }
        Row(
            modifier = Modifier
                .widthIn(min = 100.dp)
                .clickable { onClick() },
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.End
        ) {
            Text(
                text = stringResource(id = R.string.go),
                style = TextStyle(
                    fontSize = 14.sp,
                    fontWeight = FontWeight(500),
                    color = Color(0xFF2B56D7),
                    textAlign = TextAlign.Start,
                )
            )
            Icon(
                modifier = Modifier
                    .padding(start = 8.dp)
                    .size(12.dp),
                painter = painterResource(id = R.drawable.ic_to_exerise),
                contentDescription = "btn",
                tint = Color(0xFF728CDC)
            )

        }
    }
}

@Preview(showBackground = true)
@Composable
private fun PreviewTips() {
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        Tips(
            modifier = Modifier.fillMaxWidth(0.7f),
            onClick = {}
        )
    }

}