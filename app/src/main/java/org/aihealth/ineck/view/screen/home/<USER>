package org.aihealth.ineck.view.screen.home

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview

@Composable
fun AiJointConnectView(
    modifier: Modifier = Modifier,
    connected1: Boolean,
    connected2: Boolean,
    connected3: Boolean,
    connected4: <PERSON>olean,
    angle1: String?,
    angle2: String?,
    angle3: String?,
    angle4: String?,
    onLeftHandClick: () -> Unit,
    onRightHandClick: () -> Unit,
    onLefLegClick: () -> Unit,
    onRightLegClick: () -> Unit,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            if (connected1) {
                Text(
                    text = "左手已连接 angle:${angle1}",
                    color = Color.Black
                )
            } else {
                Button(onClick = onLeftHandClick) {
                    Text(text = "左手")
                }
            }

            if (connected2) {
                Text(
                    text = "右手已连接 angle:${angle2}",
                    color = Color.Black
                )
            } else {
                Button(onClick = onRightHandClick) {
                    Text(text = "右手")
                }
            }
        }
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            if (connected3) {
                Text(
                    text = "左腿已连接 angle:${angle3}",
                    color = Color.Black
                )
            } else {
                Button(onClick = onLefLegClick) {
                    Text(text = "左腿")
                }
            }

            if (connected4) {
                Text(
                    text = "右腿已连接 angle:${angle4}",
                    color = Color.Black
                )
            } else {
                Button(onClick = onRightLegClick) {
                    Text(text = "右腿")
                }
            }
        }

    }
}

@Preview
@Composable
private fun AiJointConnectViewPreview() {
//    AiJointConnectView(
//        onLeftHandClick = {},
//        onRightHandClick = {},
//        onLefLegClick = {},
//        onRightLegClick = {}
//    )
}