package org.aihealth.ineck.view.screen.home

import android.content.Context
import android.content.Intent
import android.provider.Settings
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Alignment.Companion.Center
import androidx.compose.ui.Alignment.Companion.CenterStart
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewmodel.compose.viewModel
import com.hjq.permissions.Permission
import com.hjq.permissions.XXPermissions
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.notification.FloatingCameraService
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.view.button.DetectionButton
import org.aihealth.ineck.view.custom.AIHDivider
import org.aihealth.ineck.view.custom.toptipsblock.GuideToNeckImproveOfAiNeckSmartVersion
import org.aihealth.ineck.view.custom.toptipsblock.InnerTopTopsOfInCameraMonitorOfAiNeck
import org.aihealth.ineck.view.screen.TopTipsBlockOfConnectionStatusCard
import org.aihealth.ineck.view.screen.vcguide.AiNeckVCGuideScreen
import org.aihealth.ineck.viewmodel.DetectedResult
import org.aihealth.ineck.viewmodel.MainViewModel
import org.aihealth.ineck.viewmodel.newviewmodel.VCDetectingResult
import org.aihealth.ineck.viewmodel.newviewmodel.VCGuideViewModel

@Composable
fun AiNeckCVHomeModule(
    modifier: Modifier = Modifier,
    viewModel: MainViewModel,
    lifecycleOwner: LifecycleOwner = LocalLifecycleOwner.current
) {
    /** 上下文 */
    val context = LocalContext.current
    
    val scope = rememberCoroutineScope()
    
    /** 开启视频检测状态 */
    val cameraScanState by viewModel.homeScreen.isCameraScanModel.collectAsState()


    /** 判断浮动窗口是否为初次锚点的状态 */
    val initAnchorState = viewModel.homeScreen.initFloatingWindowAnchorState.collectAsState()
    /** 初始化重力传感器 */
    viewModel.deviceScreen.initSensor(context = context)
    /** 重力参数状态 */
    val gravityValue = viewModel.deviceScreen.verticalGravity.collectAsState()

    /** 视频监测角度 */
    val angleCVState = remember { Animatable(0f) }

    /** 是否显示悬浮窗权限提示对话框 */
    var showOverlayPermissionDialog by rememberSaveable { mutableStateOf(false) }

    HomeStatusCard(
        modifier = modifier.fillMaxWidth()
    ) {
        TopTipsBlockOfConnectionStatusCard(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            if (cameraScanState) {
                InnerTopTopsOfInCameraMonitorOfAiNeck(gravityValue.value)
            } else {
                GuideToNeckImproveOfAiNeckSmartVersion()
            }
        }
        // 分割线
        AIHDivider(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        )
        Box(
            modifier = Modifier
                .padding(top = 5.dp)
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
        ) {
            // 中间部分
            NeckAngleStatusModule(
                modifier = Modifier
                    .align(CenterStart)
                    .fillMaxWidth(0.7f),
                isShowAngle = cameraScanState,
                angle = angleCVState.value.toInt()
            )
            if (!cameraScanState) {
                VCPreview(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .fillMaxWidth(0.3f),
                    onGloballyPositioned = { coordinates ->
                        if (initAnchorState.value) {
                            // 获取在屏幕上的绝对位置
                            coordinates.boundsInRoot().let {
                                viewModel.homeScreen.anchorXOfFloatingWindow.value = it.left
                                viewModel.homeScreen.anchorYOfFloatingWindow.value = it.top
                            }
                            viewModel.homeScreen.initFloatingWindowAnchorState.update { false }
                        }
                    }
                ) {
                    Image(
                        painter = painterResource(R.drawable.video_scan),
                        contentDescription = "background_image",
                        contentScale = ContentScale.FillBounds,
                        modifier = Modifier.matchParentSize()
                    )
                    DetectionButton(
                        modifier = Modifier
                            .align(Center),
                        onClick = {
                            // 如果当前有悬浮窗服务在运行，先关闭它
                            if (FloatingCameraService.isServiceRunning.value == true) {
                                FloatingCameraService.isUserClosing.value = true
                                val stopIntent = Intent(context, FloatingCameraService::class.java)
                                context.stopService(stopIntent)
                                viewModel.homeScreen.onChangeCameraScanModel(false)
                                viewModel.deviceScreen.gravityStopListening()
                            }
                            
                            if (Settings.canDrawOverlays(context)) {
                                if (XXPermissions.isGranted(activity, Permission.CAMERA)) {
                                    viewModel.homeScreen.isBootVCGuideScreen.value = true
                                } else {
                                    viewModel.homeScreen.showPowerDialogVisibleState = true
                                    viewModel.homeScreen.theClickType = 2
                                }
                            } else {
                                showOverlayPermissionDialog = true
                            }
                        }
                    )
                }

            }
            if (cameraScanState) {
                AngleStatue(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(end = 16.dp, bottom = 16.dp),
                    angle = angleCVState.value.toInt()
                )
            }
        }


    }

    AiNeckVCGuideScreen(
        modifier = Modifier
            .fillMaxWidth(0.9f),
        visible = viewModel.homeScreen.isBootVCGuideScreen.value,
        viewModel = viewModel<VCGuideViewModel>(
            factory = object : ViewModelProvider.Factory {
                override fun <T : ViewModel> create(modelClass: Class<T>): T {
                    if (modelClass.isAssignableFrom(VCGuideViewModel::class.java))
                        return modelClass.getConstructor(Context::class.java)
                            .newInstance(context)
                    throw IllegalArgumentException("")
                }
            }
        ),
        onDismissEvent = { result ->
            when (result) {
                is VCDetectingResult.DetectingSuccess -> {
                    viewModel.homeScreen.isBootVCGuideScreen.value = false
//                            viewModel.homeScreen.isShowVCGuideDetectResultDialog.value = true
                    val angle = (result.result as Triple<*, *, *>).first
                    viewModel.homeScreen.detectResultState.update {
                        DetectedResult.DetectedSuccess(angle as Float)
                    }
                    viewModel.homeScreen.detectResultValueForFitted = angle as Float

                    viewModel.homeScreen.detectResultState.update { DetectedResult.None }
                    viewModel.homeScreen.onChangeCameraScanModel(true)          // 视频监测开启
                    viewModel.deviceScreen.gravityStartListening()                 // 重力监测开启
                }

                is VCDetectingResult.DetectingError -> {
                    viewModel.homeScreen.isBootVCGuideScreen.value = false
                    viewModel.homeScreen.onChangeCameraScanModel(false)
//                            viewModel.homeScreen.isShowVCGuideDetectResultDialog.value = true
                }

                else -> {}
            }
        }
    )
    LaunchedEffect(cameraScanState){
        if (cameraScanState) {
            if (Settings.canDrawOverlays(context)) {
                // 监听服务状态
                FloatingCameraService.isServiceRunning.observe(lifecycleOwner) { isRunning ->
                    if (!isRunning) {
                        // 检查是否是用户主动关闭
                        if (FloatingCameraService.isUserClosing.value == true) {
                            // 用户主动关闭，更新UI状态
                            LogUtil.i("用户主动关闭FloatingCameraService")
                            viewModel.homeScreen.onChangeCameraScanModel(false)
                            FloatingCameraService.isUserClosing.value = false  // 重置状态
                        }
                    }
                }

                // 监听角度变化
                FloatingCameraService.headEulerAngleX.observe(lifecycleOwner) { angleX ->
                    scope.launch {
                        // 使用动画过渡到新角度
                        angleCVState.animateTo(
                            targetValue = angleX,
                            animationSpec = tween(durationMillis = 300) // 300ms的过渡时间
                        )
                    }
                }

                // 如果服务未运行，启动服务
                if (FloatingCameraService.isServiceRunning.value != true) {
                    val intent = Intent(baseApplication, FloatingCameraService::class.java).apply {
                        putExtra("detectResultValueForFitted", viewModel.homeScreen.detectResultValueForFitted)
                        putExtra("deviceType", viewModel.homeScreen.currentDeviceType)
                        putExtra("initialX", viewModel.homeScreen.anchorXOfFloatingWindow.value.toInt())
                        putExtra("initialY", viewModel.homeScreen.anchorYOfFloatingWindow.value.toInt())
                    }
                    ContextCompat.startForegroundService(baseApplication, intent)
                }
            } else {
                // 显示权限提示对话框
                showOverlayPermissionDialog = true
                viewModel.homeScreen.onChangeCameraScanModel(false)
            }
        }
    }

    // 悬浮窗权限提示对话框
    if (showOverlayPermissionDialog) {
        AlertDialog(
            onDismissRequest = { showOverlayPermissionDialog = false },
            title = { Text(stringResource(id = R.string.overlay_permission_title)) },
            text = { Text(stringResource(id = R.string.overlay_permission_message)) },
            confirmButton = {
                TextButton(
                    onClick = {
                        showOverlayPermissionDialog = false
                        val intent = Intent(
                            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
                            "package:${context.packageName}".toUri()
                        )
                        context.startActivity(intent)
                    }
                ) {
                    Text(stringResource(id = R.string.go_to_settings))
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showOverlayPermissionDialog = false
                        // 用户拒绝开启悬浮窗权限，关闭视频检测
                        viewModel.homeScreen.onChangeCameraScanModel(false)
                    }
                ) {
                    Text(stringResource(id = R.string.cancel))
                }
            }
        )
    }

//    DisposableEffect(Unit) {
//        val observer = LifecycleEventObserver { _, event ->
//            if (event == Lifecycle.Event.ON_START) {
//                /* 在页面结束时对摄像头、重力传感器对象进行关闭处理等 */
//                viewModel.deviceScreen.gravityStopListening()
//                viewModel.homeScreen.initFloatingWindowAnchorState.update { true }
//            }
//        }
//        lifecycleOwner.lifecycle.addObserver(observer)
//        onDispose {
//            lifecycleOwner.lifecycle.removeObserver(observer)
//        }
//    }
}