package org.aihealth.ineck.view.custom

import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Velocity
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

class AIHWheelState(
    initialIndex: Int = 0
) {
    val lazyListState = LazyListState(initialIndex)
    internal var mHeightPx by mutableStateOf(100F)
    val selectedIndex by derivedStateOf { lazyListState.firstVisibleItemIndex + (lazyListState.firstVisibleItemScrollOffset / mHeightPx).roundToInt() }
}

/**
 *  搏轮组件
 */
@Composable
fun AIHWheel(
    state: AIHWheelState,
    list: List<String>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = Modifier
            .then(modifier)
            .fillMaxWidth()
            .height(180.dp)
            .onSizeChanged {
                state.mHeightPx = (it.height / 5).toFloat()
            }
    ) {
        val mHeight = with(LocalDensity.current) {
            state.mHeightPx.toDp()
        }
        val coroutineScope = rememberCoroutineScope()
        HorizontalDivider(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(top = mHeight)
                .fillMaxWidth()
                .height(0.4.dp)
        )
        HorizontalDivider(
            modifier = Modifier
                .align(Alignment.Center)
                .padding(bottom = mHeight)
                .fillMaxWidth()
                .height(0.4.dp)
        )
        LazyColumn(modifier = Modifier
            .fillMaxSize()
            .pointerInput(Unit) {
                detectTapGestures {
                    val index = (it.y / state.mHeightPx).toInt()
                    coroutineScope.launch {
                        state.lazyListState.animateScrollToItem(
                            (index + state.selectedIndex - 2).coerceIn(
                                0,
                                list.size - 1
                            )
                        )
                    }
                }
            }
            .nestedScroll(object : NestedScrollConnection {
                override suspend fun onPreFling(available: Velocity): Velocity {
                    return Velocity(0F, available.y * 0.4F)
                }

                override suspend fun onPostFling(
                    consumed: Velocity,
                    available: Velocity
                ): Velocity {
                    if (state.lazyListState.firstVisibleItemScrollOffset > state.mHeightPx / 2) {
                        state.lazyListState.animateScrollToItem(state.lazyListState.firstVisibleItemIndex + 1)
                    } else {
                        state.lazyListState.animateScrollToItem(state.lazyListState.firstVisibleItemIndex)
                    }
                    return available
                }
            }),state = state.lazyListState) {
            item(-2) {
                Spacer(modifier = Modifier.height(mHeight))
            }
            item(-1) {
                Spacer(modifier = Modifier.height(mHeight))
            }
            items(list.size, key = {it}, contentType = {""}) {
                Box(modifier = Modifier
                    .fillMaxWidth()
                    .height(mHeight), contentAlignment = Alignment.Center){
                    Text(
                        text = list[it],
                        fontSize = if (state.selectedIndex == it) 16.sp else 14.sp,
                        color = if (state.selectedIndex == it) Color(0XFF333333) else Color(0XFF999999),
                    )
                }
            }
            item(list.size) {
                Spacer(modifier = Modifier.height(mHeight))
            }
            item(list.size + 1) {
                Spacer(modifier = Modifier.height(mHeight))
            }
            item(list.size + 2) {
                Spacer(modifier = Modifier.height(mHeight / 2 - 1.dp))
            }
        }
    }
}