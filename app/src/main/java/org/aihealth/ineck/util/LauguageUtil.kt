package org.aihealth.ineck.util

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import android.os.LocaleList
import org.aihealth.ineck.currentLocale
import java.util.Locale

object LauguageUtil {

    @SuppressLint("ObsoleteSdkInt")
    fun changeAppLanguage(context: Context, newLauguage: Locale) {
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.N) {
            val resources = context.resources
            val configuration = resources.configuration
            configuration.setLocale(newLauguage)
            resources.updateConfiguration(configuration, resources.displayMetrics)
        }
    }

//    fun changeLanguage(newLauguage: Locale) {
//        SPUtil.putString(SPConstant.LAUGUAGE, newLauguage.language)
//        currentLocale = newLauguage
//        APPUtil.restartAPP()
//    }


    @SuppressLint("ObsoleteSdkInt")
    fun attachBaseContext(context: Context): Context {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return updateResources(context)
        }
        return context
    }

    private fun updateResources(context: Context):Context {

        val configuration = context.resources.configuration.apply {
            setLocale(currentLocale)
            setLocales(LocaleList(currentLocale))
        }
        return context.createConfigurationContext(configuration)
    }
}