package org.aihealth.ineck.util

import android.util.Log
import org.aihealth.ineck.BuildConfig
import timber.log.Timber


object LogUtil {
    private val isDebug = BuildConfig.DEBUG
    fun v(msg: String, string: String) {
        if (isDebug) {
            Timber.tag(getTag()).v(msg)
        }
    }

    fun d(msg: String) {
        if (isDebug) {
            Log.d(getTag(), msg)
        }
    }

    fun i(msg: String) {
        if (isDebug) {
            Log.i(getTag(), msg)
        }
    }

    fun w(msg: String) {
        if (isDebug) {
            Log.w(getTag(), msg)
        }
    }
    fun w(tag: String, msg: String) {
        if (isDebug) {
            Log.w(tag, msg)
        }
    }

    fun e(msg: String) {
        Log.e(getTag(), msg)

    }
    fun e(tag: String, msg: String,e:Exception) {
        if (isDebug) {
            Log.e(tag, msg,e)
        }
    }
    fun e(tag: String, msg: String) {
        if (isDebug) {
            Log.e(tag, msg)
        }
    }

    fun i(tag: String, msg: String) {
        if (isDebug) {
            Log.i(tag, msg)
        }
    }

    fun d(tag: String, msg: String) {
        if (isDebug) {
            Log.d(tag, msg)
        }
    }

    private fun getTag():String {
//        val caller = Thread.currentThread().stackTrace[4]
//        return "mylog(${caller.fileName.let { it.substring(0,it.lastIndexOf('.')) }}.${caller.methodName}() ${caller.lineNumber}行): "
        return "mylog"
    }
}

val Any.TAG: String
    get() = "mytag" + this::class.java.simpleName