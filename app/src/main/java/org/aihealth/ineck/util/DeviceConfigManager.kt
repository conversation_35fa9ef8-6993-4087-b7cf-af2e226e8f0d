package org.aihealth.ineck.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType

/**
 * 设备配置管理器
 * 负责初始化和管理所有设备的配置
 */
object DeviceConfigManager {
    private val dataStore by lazy { DeviceConfigDataStore(baseApplication) }
    private val coroutineScope = CoroutineScope(Dispatchers.IO)
    
    // 缓存的设备配置
    private val deviceConfigs = mutableMapOf<DeviceType, DeviceConfig>()
    
    // 用于初始化应用时
    fun initialize() {
        coroutineScope.launch {
            // 预加载两种常用设备类型的配置
            loadDeviceConfig(DeviceType.aiNeck)
            loadDeviceConfig(DeviceType.aiBack)
            LogUtil.i("DeviceConfigManager 初始化完成")
        }
    }
    
    /**
     * 加载指定设备类型的配置
     */
    private suspend fun loadDeviceConfig(deviceType: DeviceType) {
        try {
            val config = dataStore.getDeviceConfig(deviceType).first()
            deviceConfigs[deviceType] = config
            LogUtil.d("加载设备配置成功: $deviceType")
        } catch (e: Exception) {
            LogUtil.e("加载设备配置失败: $deviceType, ${e.message}")
        }
    }
    
    /**
     * 获取设备配置 (同步方法，如果缓存中没有会返回空配置)
     * 注意：此方法返回缓存数据，可能不是最新的。建议使用 ensureLoaded() 或 getDeviceConfigFlow()
     */
    fun getDeviceConfig(deviceType: DeviceType): DeviceConfig {
        return deviceConfigs[deviceType] ?: DeviceConfig().apply { 
            this.deviceType = deviceType 
            LogUtil.w("DeviceConfigManager", "缓存中没有找到 $deviceType 的配置，返回默认配置")
        }
    }
    
    /**
     * 获取设备配置Flow (异步流)
     */
    fun getDeviceConfigFlow(deviceType: DeviceType) = dataStore.getDeviceConfig(deviceType)
    
    /**
     * 保存设备配置
     */
    fun saveDeviceConfig(config: DeviceConfig) {
        // 更新缓存
        deviceConfigs[config.deviceType] = config
        
        // 异步保存到DataStore
        coroutineScope.launch {
            dataStore.saveDeviceConfig(config)
            // 重新加载配置以确保缓存同步
            loadDeviceConfig(config.deviceType)
        }
    }
    
    /**
     * 保存校准角度
     */
    fun saveCalibrationAngle(deviceType: DeviceType, angle: Int) {
        // 更新缓存（如果存在）
        deviceConfigs[deviceType]?.let { config ->
            config.maxAngle = angle
            config.isCalibrated = true
        }
        
        // 异步保存到DataStore（这是主要的持久化方式）
        coroutineScope.launch {
            dataStore.saveCalibrationAngle(deviceType, angle)
            // 重新加载配置以确保缓存同步
            loadDeviceConfig(deviceType)
        }
    }
    
    /**
     * 在UI初始化时调用，确保设备配置已加载
     */
    suspend fun ensureLoaded(deviceType: DeviceType): DeviceConfig {
        // 如果缓存中没有，先加载
        if (!deviceConfigs.containsKey(deviceType)) {
            loadDeviceConfig(deviceType)
        }
        return deviceConfigs[deviceType] ?: DeviceConfig().apply { 
            this.deviceType = deviceType 
        }
    }
} 