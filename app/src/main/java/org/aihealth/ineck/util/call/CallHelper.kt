package org.aihealth.ineck.util.call

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import org.aihealth.ineck.R
import org.aihealth.ineck.util.LogUtil

/**
 * 通话助手类，集成通话状态监听器
 * 负责记录通话时间
 */
class CallHelper(private val activity: AppCompatActivity) {

    companion object {
        private const val TAG = "CallHelper"
    }

    // 权限请求
    private val requestCallPhonePermLauncher = activity.registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // 获得权限后继续拨打电话
            pendingPhoneNumber?.let {
                makeDirectCall(it)
                pendingPhoneNumber = null
            }
        } else {
            // 权限被拒绝，退回到使用拨号盘
            Toast.makeText(
                activity,
                activity.getString(R.string.phone_permission_denied),
                Toast.LENGTH_SHORT
            ).show()

            pendingPhoneNumber?.let {
                // 由于无法直接拨打，改用拨号盘
                openDialer(it)
                pendingPhoneNumber = null
            }
        }
    }

    // 读取电话状态权限请求
    private val requestReadPhoneStateLauncher = activity.registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            LogUtil.i(TAG, "READ_PHONE_STATE permission granted, initializing call state listener")
            // 获得权限后初始化通话状态监听器
            initCallStateListener()
        } else {
            LogUtil.e(TAG, "READ_PHONE_STATE permission denied, call tracking will not work properly")
            Toast.makeText(
                activity,
                "无法监听通话状态，通话时长将无法准确记录",
                Toast.LENGTH_LONG
            ).show()
        }
    }

    // 待处理的电话号码
    private var pendingPhoneNumber: String? = null

    // 通话结束回调
    private var callEndCallback: ((duration: Long, isConnected: Boolean) -> Unit)? = null

    // 通话状态监听器
    private var callStateListener: CallStateListener? = null

    // 通话记录是否已被处理的标记，避免重复处理
    private var isCallHandled = false

    /**
     * 设置通话结束回调
     */
    fun setCallEndCallback(callback: (duration: Long, isConnected: Boolean) -> Unit) {
        callEndCallback = callback
    }

    /**
     * 拨打电话 - 直接拨打，需要CALL_PHONE权限
     */
    fun makeCall(phoneNumber: String) {
        if (phoneNumber.isBlank()) {
            Toast.makeText(
                activity,
                activity.getString(R.string.no_phone),
                Toast.LENGTH_SHORT
            ).show()
            return
        }

        pendingPhoneNumber = phoneNumber

        // 先确保已初始化通话状态监听器
        checkAndRequestReadPhoneStatePermission()

        // 检查是否有CALL_PHONE权限
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.CALL_PHONE
            ) != PackageManager.PERMISSION_GRANTED) {

            // 请求CALL_PHONE权限
            requestCallPhonePermLauncher.launch(Manifest.permission.CALL_PHONE)
        } else {
            // 已有权限，直接拨打
            makeDirectCall(phoneNumber)
        }
    }

    /**
     * 检查并请求READ_PHONE_STATE权限
     */
    private fun checkAndRequestReadPhoneStatePermission() {
        if (ContextCompat.checkSelfPermission(
                activity,
                Manifest.permission.READ_PHONE_STATE
            ) != PackageManager.PERMISSION_GRANTED) {

            LogUtil.i(TAG, "Requesting READ_PHONE_STATE permission")
            requestReadPhoneStateLauncher.launch(Manifest.permission.READ_PHONE_STATE)
        } else {
            // 已有权限，初始化监听器
            initCallStateListener()
        }
    }

    /**
     * 执行直接拨号动作
     */
    private fun makeDirectCall(phoneNumber: String) {
        val callIntent = Intent(Intent.ACTION_CALL).apply {
            data = Uri.parse("tel:$phoneNumber")
        }

        try {
            // 重置通话处理标记
            isCallHandled = false

            // 确保已初始化通话状态监听器
            if (callStateListener == null) {
                initCallStateListener()
            }

            // 设置拨号开始时间（用户实际点击拨号的时刻）
            callStateListener?.setDialStartTime()

            // 标记这个通话是由应用发起的
            callStateListener?.callInitiatedByApp = true
            LogUtil.i(TAG, "========= Marked call as initiated by app before making call =========")

            // 启动直接拨号
            activity.startActivity(callIntent)
            LogUtil.i(TAG, "========= Direct call initiated to: $phoneNumber =========")
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error making direct call", e)

            Toast.makeText(
                activity,
                activity.getString(R.string.call_failed) + ": " + e.localizedMessage,
                Toast.LENGTH_SHORT
            ).show()

            // 重置通话状态监听器
            callStateListener?.callInitiatedByApp = false

            // 尝试退回到拨号盘方式
            openDialer(phoneNumber)
        }
    }

    /**
     * 打开拨号盘（无需权限，作为后备方案）
     */
    private fun openDialer(phoneNumber: String) {
        val intent = Intent(Intent.ACTION_DIAL).apply {
            data = Uri.parse("tel:$phoneNumber")
        }

        try {
            // 重置通话处理标记
            isCallHandled = false

            // 确保已初始化通话状态监听器
            if (callStateListener == null) {
                initCallStateListener()
            }

            // 设置拨号开始时间（用户实际点击拨号的时刻）
            callStateListener?.setDialStartTime()

            // 标记这个通话是由应用发起的
            callStateListener?.callInitiatedByApp = true
            LogUtil.i(TAG, "========= Marked call as initiated by app before opening dialer =========")

            // 启动拨号器
            activity.startActivity(intent)
        } catch (e: Exception) {
            LogUtil.e(TAG, "Error opening dialer", e)
            Toast.makeText(
                activity,
                activity.getString(R.string.dialer_error),
                Toast.LENGTH_SHORT
            ).show()

            // 重置通话状态监听器
            callStateListener?.callInitiatedByApp = false
        }
    }

    /**
     * 初始化通话状态监听器
     */
    private fun initCallStateListener() {
        // 如果已经存在，先注销旧的监听器
        callStateListener?.unregister()

        // 创建并注册新的监听器
        callStateListener = CallStateListener(activity, object : CallStateListener.CallEndCallback {
            override fun onCallEnded(duration: Long, isConnected: Boolean) {
                if (!isCallHandled) {
                    // 标记为已处理，防止重复处理
                    isCallHandled = true


                    // 调用回调
                    callEndCallback?.invoke(duration, isConnected)

                    LogUtil.i(TAG, "========= Call ended callback received from CallStateListener, duration: $duration sec, connected: $isConnected =========")
                } else {
                    LogUtil.i(TAG, "========= Ignoring duplicate call end event, call already handled =========")
                }
            }
        })

        // 注册监听器
        callStateListener?.register()
        LogUtil.i(TAG, "========= CallStateListener initialized and registered =========")
    }

    /**
     * 在Activity的onResume中调用
     * 保留此方法以兼容现有调用，但不再用于计算通话时间
     */
    fun onResume() {
        // 通话时间现在由CallStateListener处理
        LogUtil.i(TAG, "========= onResume called - CallStateListener is now handling call duration =========")
    }

    /**
     * 在Activity的onPause中调用
     * 保留此方法以兼容现有调用，但不再用于计算通话时间
     */
    fun onPause() {
        // 通话时间现在由CallStateListener处理
        LogUtil.i(TAG, "========= onPause called - CallStateListener is now handling call duration =========")
    }


    /**
     * 在Activity的onDestroy中调用，释放资源
     */
    fun onDestroy() {
        callStateListener?.unregister()
        callStateListener = null
        LogUtil.i(TAG, "========= CallHelper resources released =========")
    }
} 