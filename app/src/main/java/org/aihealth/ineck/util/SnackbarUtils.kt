package org.aihealth.ineck.util

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.material3.Snackbar
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import kotlinx.coroutines.delay
import org.aihealth.ineck.R


@Composable
fun CustomSnackbar(
    modifier: Modifier = Modifier,
    onCountdownFinished: () -> Unit,
    onSkip: () -> Unit,
) {
    var enable by remember {
        mutableStateOf(true)
    }
    Snackbar(
        modifier = modifier,
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(),
        ) {
            Text(
                modifier = Modifier.align(Alignment.TopStart),
                text = "进入健康问卷链路"
            )
            Row(
                modifier = Modifier.align(Alignment.TopEnd),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Text(
                    text = stringResource(id = R.string.cancel),
                    modifier = Modifier.clickable {
                        enable = false
                        onSkip()
                    }
                )

                CountdownButton(
                    modifier = Modifier,
                    totalTime = 5,
                    onCountdownFinished = {
                        if (enable) {
                            onCountdownFinished()
                        }
                    }
                )
            }

        }
    }
}

@Composable
fun CountdownButton(
    modifier: Modifier,
    totalTime: Int = 10,
    onCountdownFinished: () -> Unit
) {
    var timeLeft by remember { mutableStateOf(totalTime) }
    var autoDo by remember { mutableStateOf(false) }

    LaunchedEffect(autoDo) {
        while (timeLeft > 0) {
            delay(1000L)
            timeLeft--
        }
        if (timeLeft == 0) {
            autoDo = true
            onCountdownFinished()
        }
    }

    Text(
        modifier = modifier.clickable {
            onCountdownFinished()
            if (autoDo) {
                autoDo = false
            }
        },
        text = " $timeLeft 秒后自动进入",
    )
}