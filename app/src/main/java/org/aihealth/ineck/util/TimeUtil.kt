package org.aihealth.ineck.util

import android.annotation.SuppressLint
import android.content.Context
import android.os.Build
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.sp
import org.aihealth.ineck.R
import org.aihealth.ineck.currentLocale
import java.text.ParseException
import java.text.SimpleDateFormat
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

object TimeUtil {

    @SuppressLint("SimpleDateFormat")
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd")
    private val mCalendar = Calendar.getInstance()

    /** 时间戳转换为指定日期格式字符串 */
    fun timestampToDate(
        timestamp: Long,
        pattern: String = "yyyy-MM-dd HH:mm:ss"
    ): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val localDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp * 1000),
                ZoneId.systemDefault()
            )
            val formatter = DateTimeFormatter.ofPattern(pattern)
            localDateTime.format(formatter)
        } else {
            return SimpleDateFormat(pattern).format(timestamp * 1000)
        }

    }
    /** 时间戳转换为指定日期格式字符串 */
    fun milliTimestampToDate(
        timestamp: Long,
        pattern: String = "yyyy-MM-dd HH:mm:ss"
    ): String {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val localDateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp),
                ZoneId.systemDefault()
            )
            val formatter = DateTimeFormatter.ofPattern(pattern)
            localDateTime.format(formatter)
        } else {
            return SimpleDateFormat(pattern).format(timestamp)
        }

    }

    fun dateToSimpleDateFormat(
        date: Date,
        pattern: String = "yyyy-MM-dd HH:mm:ss"
    ): String{
      return  try {
            val sdf = SimpleDateFormat(pattern, Locale.getDefault())  // 创建 SimpleDateFormat 实例
            return sdf.format(date)  // 格式化 Date 对象为字符串
        }catch (e: Exception){
            ""
        }
    }

    /**
     *  将整形秒单位转换为 能够为持续时长显示提供字符串信息
     */
    fun convertSecondsToAnnotatedString(seconds: Int, context: Context): AnnotatedString {
        val spacerDivider = " "
        val minutes = seconds / 60
        return if (minutes < 1) {
            buildAnnotatedString {
                // 中英文的字号不一致
                if (currentLocale == Locale.CHINESE) {
                    // 数字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(seconds.toString())
                        append(spacerDivider)
                    }

                    // 文字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF999999),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(context.getString(R.string.time_sceonds))
                    }
                } else {
                    // 数字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                    ) {
                        append(seconds.toString())
                        append(spacerDivider)
                    }

                    // 文字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 9.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(context.getString(R.string.time_sceonds))
                    }
                }
            }
        } else {
            val hours = minutes / 60
            val minutesString = minutes.coerceIn(1..59).toString()
            val hoursString = hours.toString()
            if (hours > 0) {
                val decimalPart = minutes % 60.0
                val formattedDecimalPart = String.format("%.1f", decimalPart)
                buildAnnotatedString {
                    if (currentLocale == Locale.CHINESE) {
                        // 数字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append("$hoursString.$minutesString$formattedDecimalPart")
                            append(spacerDivider)
                        }
                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF999999),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_hours))
                        }

                    } else {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append("$hoursString.$minutesString$formattedDecimalPart")
                            append(spacerDivider)
                        }

                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_hours))
                        }
                    }
                }
            } else {
                buildAnnotatedString {
                    if (currentLocale == Locale.CHINESE) {
                        // 数字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(minutesString)
                            append(spacerDivider)
                        }
                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF999999),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_minutes))
                        }

                    } else {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(minutesString)
                            append(spacerDivider)
                        }

                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_minutes))
                        }
                    }
                }
            }
        }
    }

    /**
     *  将整形秒单位转换为 能够为持续时长显示提供字符串信息
     */
    fun convertSecondsToAnnotatedString(seconds: Long, context: Context): AnnotatedString {
        val spacerDivider = " "
        val minutes = seconds / 60
        return if (minutes < 1) {
            buildAnnotatedString {
                // 中英文的字号不一致
                if (currentLocale == Locale.CHINESE) {
                    // 数字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(seconds.toString())
                        append(spacerDivider)
                    }

                    // 文字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF999999),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(context.getString(R.string.time_sceonds))
                    }
                } else {
                    // 数字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                    ) {
                        append(seconds.toString())
                        append(spacerDivider)
                    }

                    // 文字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 9.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(context.getString(R.string.time_sceonds))
                    }
                }
            }
        } else {
            val hours = minutes / 60
            val minutesString = minutes.coerceIn(1L..59L).toString()
            val hoursString = hours.toString()
            if (hours > 0) {
                val decimalPart = minutes % 60.0
                val formattedDecimalPart = String.format("%.1f", decimalPart)
                buildAnnotatedString {
                    if (currentLocale == Locale.CHINESE) {
                        // 数字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append("$hoursString.$minutesString$formattedDecimalPart")
                            append(spacerDivider)
                        }
                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF999999),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_hours))
                        }

                    } else {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append("$hoursString.$minutesString$formattedDecimalPart")
                            append(spacerDivider)
                        }

                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_hours))
                        }
                    }
                }
            } else {
                buildAnnotatedString {
                    if (currentLocale == Locale.CHINESE) {
                        // 数字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 14.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(minutesString)
                            append(spacerDivider)
                        }
                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF999999),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_minutes))
                        }

                    } else {
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        ) {
                            append(minutesString)
                            append(spacerDivider)
                        }

                        // 文字
                        withStyle(
                            SpanStyle(
                                color = Color(0xFF2B56D7),
                                fontSize = 9.sp,
                                fontWeight = FontWeight.Normal
                            )
                        ) {
                            append(context.getString(R.string.time_minutes))
                        }
                    }
                }
            }
        }
    }

    /**
     *  将整形秒单位转换为 能够为持续时长显示提供字符串信息
     */
    fun convertSecondsToAnnotatedString14(seconds: Long, context: Context): AnnotatedString {
        val spacerDivider = " "
        val minutes = seconds / 60
        return if (minutes < 1) {
            buildAnnotatedString {
                // 数字
                withStyle(
                    SpanStyle(
                        color = Color(0xFF2B56D7),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                ) {
                    append(seconds.toString())
                    append(spacerDivider)
                }

                // 文字
                withStyle(
                    SpanStyle(
                        color = Color(0xFF2B56D7),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal
                    )
                ) {
                    append(context.getString(R.string.time_sceonds))
                }
            }
        } else {
            val hours = minutes / 60
            val minutesString = minutes.coerceIn(1L..59L).toString()
            val hoursString = hours.toString()
            if (hours > 0) {
                val decimalPart = minutes % 60.0
                val formattedDecimalPart = String.format("%.1f", decimalPart)
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    ) {
                        append("$hoursString.$minutesString$formattedDecimalPart")
                        append(spacerDivider)
                    }

                    // 文字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(context.getString(R.string.time_hours))
                    }
                }
            } else {
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    ) {
                        append(minutesString)
                        append(spacerDivider)
                    }

                    // 文字
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF2B56D7),
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Normal
                        )
                    ) {
                        append(context.getString(R.string.time_minutes))
                    }
                }
            }
        }
    }

    /**
     *
     */
    fun longToFormattedString(timeInMillis: Long): String {
        val date = Date(timeInMillis)
        val format = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
        format.timeZone = TimeZone.getTimeZone("UTC")
        return format.format(date)
    }
    /**
     * 设计一个Kotlin方法，能够将以秒为单位的Int数据按照规则转换为用于显示的字符串。规则如下：
     * 1. 若数据小于1小时，则显示格式为mm:ss。
     * 2. 若显示数据大于1小时，则显示格式为hh:mm:ss。
     */
    fun convertSecondsToStandardString(seconds: Int): String {
        val minutes = seconds / 60
        val hours = minutes / 60
        return if (hours <= 0) {
            String.format("%02d:%02d", minutes, seconds % 60)
        } else {
            String.format("%02d:%02d:%02d", hours, minutes % 60, seconds % 60)
        }
    }

    /**
     *  获取指定日期的所在星期列表数据
     *  @param  currentDate 指定日期
     */
    fun getCalendarsInCurrentWeek(currentDate: Calendar): List<Calendar> {
        val firstDayOfWeek = currentDate.get(Calendar.DAY_OF_WEEK) - 1
        val calendars = mutableListOf<Calendar>()
        for (i in 0..6) {
            val calendar = Calendar.getInstance()
            calendar.set(
                currentDate.get(Calendar.YEAR), currentDate.get(Calendar.MONTH), currentDate.get(
                    Calendar.DAY_OF_MONTH
                )
            )
            calendar.add(Calendar.DAY_OF_MONTH, i - firstDayOfWeek)
            calendars.add(calendar)
        }
        return calendars
    }

    /**
     *
     */
    fun getDay(time: Long): String {
        val hours = time / (60 * 60)
        val remainingSeconds = time % 36

        if (hours > 24) {
            val days = hours / 24
            return "" + days
        } else {
            return "" + 0
        }
    }

    /**
     * 获取当前的日期
     *
     * @return yyyy年MM月dd日
     */
    fun getCurrentDate(): String {
        return dateFormat.format(mCalendar.time)
    }

    /**
     * 判断日期是否与当前日期差7天
     * @param date  2020年04月22日
     * @return true
     */
    fun isDateOutDate(date: String): Boolean {
        try {
            if ((Date().time - dateFormat.parse(date).time) > 7 * 24 * 60 * 60 * 1000) return true

        } catch (e: ParseException) {
            e.printStackTrace()
        }

        return false
    }

    /**
     * 将ISO 8601格式的字符串转换为指定格式的日期时间字符串
     * @param isoDateString ISO 8601格式的字符串，如 "2025-01-03T04:07:00Z"
     * @param pattern 目标日期格式，默认为 "yyyy-MM-dd HH:mm:ss"
     * @return 格式化后的日期时间字符串
     */
    fun isoStringToFormattedDate(
        isoDateString: String,
        pattern: String = "yyyy-MM-dd HH:mm:ss"
    ): String {
        return try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val instant = Instant.parse(isoDateString)
                val localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault())
                val formatter = DateTimeFormatter.ofPattern(pattern)
                localDateTime.format(formatter)
            } else {
                val parseFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
                parseFormat.timeZone = java.util.TimeZone.getTimeZone("UTC")
                val date = parseFormat.parse(isoDateString) ?: return ""
                val outputFormat = SimpleDateFormat(pattern, Locale.getDefault())
                outputFormat.format(date)
            }
        } catch (e: Exception) {
            ""
        }
    }

    /**
     * 将ISO 8601格式的日期时间字符串转换为时间戳（毫秒）
     * 例如："2025-03-21T14:08:39Z" -> 1740081219000
     */
    fun parseIsoDateTimeToTimestamp(isoDateTime: String): Long {
        return try {
            // 创建SimpleDateFormat并设置时区为UTC
            val sdf = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
            sdf.timeZone = TimeZone.getTimeZone("UTC")
            
            // 解析日期字符串并转换为时间戳（毫秒）
            val date = sdf.parse(isoDateTime)
            date?.time ?: System.currentTimeMillis()
        } catch (e: Exception) {
            LogUtil.e("解析日期时间失败: $isoDateTime - ${e.message}")
            // 返回当前时间戳作为后备方案
            System.currentTimeMillis()
        }
    }
    fun convertUtcToLocalTime(utcTimeString: String): String {
        try {
            // 创建解析 UTC 时间的 SimpleDateFormat
            val utcFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
            utcFormat.timeZone = TimeZone.getTimeZone("UTC")

            // 解析 UTC 时间字符串为 Date 对象
            val date = utcFormat.parse(utcTimeString)

            // 创建本地时间格式的 SimpleDateFormat
            val localFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            localFormat.timeZone = TimeZone.getDefault() // 使用设备本地时区

            // 格式化为本地时间
            return localFormat.format(date)
        } catch (e: Exception) {
            e.printStackTrace()
            return utcTimeString // 转换失败时返回原始字符串
        }
    }

}

fun Int.formatTime(): String {
    val minutes = this / 60
    val seconds = this % 60
    return String.format("%02d:%02d", minutes, seconds)
}