package org.aihealth.ineck.util

import cn.authing.sdk.java.client.AuthenticationClient
import cn.authing.sdk.java.dto.IsSuccessRespDto
import cn.authing.sdk.java.dto.LoginTokenRespDto
import cn.authing.sdk.java.dto.ResetPasswordByEmailPassCodeDto
import cn.authing.sdk.java.dto.ResetPasswordDto
import cn.authing.sdk.java.dto.ResetPasswordVerify
import cn.authing.sdk.java.dto.SendEmailDto
import cn.authing.sdk.java.dto.SendEmailRespDto
import cn.authing.sdk.java.dto.SendSMSDto
import cn.authing.sdk.java.dto.SendSMSRespDto
import cn.authing.sdk.java.dto.SignInByWechatPayloadDto
import cn.authing.sdk.java.dto.SignInOptionsDto
import cn.authing.sdk.java.dto.SignUpOptionsDto
import cn.authing.sdk.java.dto.SignUpProfileDto
import cn.authing.sdk.java.dto.SigninByMobileDto
import cn.authing.sdk.java.dto.UserSingleRespDto
import cn.authing.sdk.java.dto.VerifyResetPasswordRequestDto
import cn.authing.sdk.java.model.AuthenticationClientOptions
import com.auth0.jwt.algorithms.Algorithm
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.util.Date
import com.auth0.jwt.JWT as JWTBuilder


object AuthingUtil {
    private const val APP_ID: String = "617f57b1ccfb8327a65e51a0"
    private const val APP_SECRET: String = "e26ede8d9a6d89256ffd5f194924fcd8"
    private const val APP_HOST: String = "https://kfmhs-ewq.authing.cn"
    private lateinit var client: AuthenticationClient
    public fun init() {
        val clientOptions = AuthenticationClientOptions()
        clientOptions.appId = APP_ID
        clientOptions.appSecret = APP_SECRET
        clientOptions.appHost = APP_HOST
        client = AuthenticationClient(clientOptions)
    }

    suspend fun signUpByEmail(email: String, password: String): UserSingleRespDto? =
        withContext(Dispatchers.IO) {
            try {
                val response = client.signUpByEmailPassword(
                    email,
                    password,
                    SignUpProfileDto(),
                    SignUpOptionsDto()
                )

                return@withContext response
            } catch (e: Exception) {
                return@withContext null
            }
        }

    suspend fun loginWithEmail(email: String, password: String): LoginTokenRespDto? =
        withContext(Dispatchers.IO) {
            try {
                val option = SignInOptionsDto()
                option.scope = "openid profile email phone address identities data"
                val response = client.signInByEmailPassword(
                    email,
                    password,
                    option
                )
                return@withContext response

            } catch (e: Exception) {
                return@withContext null
            }
        }

    suspend fun sendSMS(phone: String): SendSMSRespDto? =
        withContext(Dispatchers.IO) {
            try {
                val reqDto = SendSMSDto()
                reqDto.channel = SendSMSDto.Channel.CHANNEL_LOGIN
                reqDto.phoneNumber = phone

                val response = client.sendSms(reqDto)
                return@withContext response
            } catch (e: Exception) {
                return@withContext null
            }
        }

    suspend fun loginWithPhone(phone: String, code: String): LoginTokenRespDto? =
        withContext(Dispatchers.IO) {
            try {
                val option = SignInOptionsDto()
                option.scope = "openid profile phone offline_access tenant_id external_id"
                option.autoRegister = true

                val response = client.signInByPhonePassCode(
                    phone,
                    code,
                    "+86",
                    option
                )
                return@withContext response
            } catch (e: Exception) {
                return@withContext null
            }
        }

    suspend fun loginByWechat(code: String): LoginTokenRespDto? =
        withContext(Dispatchers.IO) {
            try {
                val dto = SigninByMobileDto()
                dto.connection = SigninByMobileDto.Connection.WECHAT
                dto.extIdpConnidentifier = "wechat:mobile"
                dto.wechatPayload = SignInByWechatPayloadDto().apply { this.code = code }
                dto.clientId = APP_ID
                dto.clientSecret = APP_SECRET

                val response = client.signInByMobile(dto)
                return@withContext response
            } catch (e: Exception) {
                return@withContext null
            }
        }
    suspend fun sendEmail(email:String,type:String): SendEmailRespDto? = withContext(Dispatchers.IO){
        try {
            val reqDto = SendEmailDto()

            reqDto.channel = SendEmailDto.Channel.CHANNEL_RESET_PASSWORD
            reqDto.email = email
            val response = client.sendEmail(reqDto)
            LogUtil.i("sendEmail:${response}")
            return@withContext response
        }catch (e: Exception){
            return@withContext null
        }
    }
    suspend fun resetPassword(email: String, code: String, password: String): IsSuccessRespDto? = withContext(
        Dispatchers.IO){
        try {
            val reqDto = VerifyResetPasswordRequestDto()
            reqDto.verifyMethod = VerifyResetPasswordRequestDto.VerifyMethod.EMAIL_PASSCODE
            val rpDto =  ResetPasswordByEmailPassCodeDto()
            rpDto.email = email
            rpDto.passCode = code
            reqDto.emailPassCodePayload = rpDto
            val response = client.verifyResetPasswordRequest(reqDto)
            response?.let {
                if (it.statusCode == 200){
                    val data = it.data as ResetPasswordVerify
                    val t = resetPasswordByToken(data.passwordResetToken,password)
                    return@withContext t
                }else {
                    return@withContext null
                }
            }
            return@withContext null
        }catch (e: Exception){
            return@withContext null
        }
    }
    suspend fun resetPasswordByToken(token:String,password:String): IsSuccessRespDto? = withContext(Dispatchers.IO){
        try {
            val reqDto = ResetPasswordDto()
            reqDto.password = password
            reqDto.passwordResetToken = token
            val response = client.resetPassword(reqDto)
            return@withContext response
        }catch (e: Exception) {
            return@withContext null
        }

    }

    fun JSONObject.toMap(): Map<String, Any?> {
        val map = mutableMapOf<String, Any?>()
        val keys = keys()
        while (keys.hasNext()) {
            val key = keys.next() as String
            var value = this.get(key)
            if (value is JSONObject) {
                value = value.toMap()
            } else if (value === JSONObject.NULL) {
                value = null
            }
            map[key] = value
        }
        return map
    }

    fun modifyJWT(originalToken: String, secretKey: String): String {
        // 解析原有的 JWT 令牌
        val jwt = com.auth0.android.jwt.JWT(originalToken)
        val claims = jwt.claims

        // 提取 'sub' 字段的值
        val sub = jwt.getClaim("sub").asString()

        // 将原始载荷转换为 JSONObject
        val payloadJson = JSONObject()

        // 将原始的声明添加到新的载荷中
        claims.forEach { (key, claim) ->
            when {
                claim.asString() != null -> payloadJson.put(key, claim.asString())
                claim.asInt() != null -> payloadJson.put(key, claim.asInt())
                claim.asBoolean() != null -> payloadJson.put(key, claim.asBoolean())
                claim.asDouble() != null -> payloadJson.put(key, claim.asDouble())
                claim.asLong() != null -> payloadJson.put(key, claim.asLong())
                else -> payloadJson.put(key, JSONObject.NULL)
            }
        }

        // 创建新的 "data" 字段，并添加到载荷中
        val dataJson = JSONObject()
        dataJson.put("userId", sub)

        // 将 "data" 字段添加到载荷中
        payloadJson.put("data", dataJson)

        // 将新的载荷转换为 Map
        val payloadMap = payloadJson.toMap()

        // 创建算法实例
        val algorithm = Algorithm.HMAC256(secretKey)

        // 构建新的 JWT 令牌
        try {
            val newToken = JWTBuilder.create()
                .withPayload(payloadMap)
                .withIssuedAt(Date())
                .withExpiresAt(Date(jwt.expiresAt!!.time))
                .sign(algorithm)
            return newToken
        } catch (e: Exception) {
            return originalToken
        }
    }

    suspend fun logout(token: String): Boolean = withContext(Dispatchers.IO) {
        try {
            client.revokeToken(token)
            return@withContext true
        } catch (e: Exception) {
            return@withContext false
        }
    }

}