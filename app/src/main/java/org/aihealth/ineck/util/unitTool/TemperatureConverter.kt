package org.aihealth.ineck.util.unitTool

import kotlin.math.round

// 温度转换工具类
object TemperatureConverter {

    // 扩展函数：将 Double 保留两位小数
    private fun Double.roundToTwoDecimal(): Double = round(this * 100) / 100

    // 摄氏度转华氏度: F = C * 9/5 + 32
    fun celsiusToFahrenheit(celsius: Double): Double {
        return (celsius * 9 / 5 + 32).roundToTwoDecimal()
    }

    // 华氏度转摄氏度: C = (F - 32) * 5/9
    fun fahrenheitToCelsius(fahrenheit: Double): Double {
        return ((fahrenheit - 32) * 5 / 9).roundToTwoDecimal()
    }
}