package org.aihealth.ineck.util

import android.content.Context
import org.aihealth.ineck.baseApplication
import java.io.ObjectInputStream
import java.io.ObjectOutputStream
import java.io.Serializable

fun Serializable.saveToLocal() {
    baseApplication.openFileOutput(this::class.simpleName, Context.MODE_PRIVATE)
        .use {
            ObjectOutputStream(it).writeObject(this)
        }
}

inline fun <reified T: Serializable> loadByLocal(): T? {
    return try {
        baseApplication.openFileInput(T::class.simpleName)?.use { fin ->
            ObjectInputStream(fin).readObject() as T
        }
    } catch (e: Exception) {
        null
    }
}