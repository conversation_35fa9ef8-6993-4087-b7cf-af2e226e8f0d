package org.aihealth.ineck.util

import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpRect
import androidx.compose.ui.unit.TextUnit
import org.aihealth.ineck.getSafeDensity

fun Dp.toPx():Float{
    return with(getSafeDensity()){
        <EMAIL>()
    }
}


fun Dp.roundToPx(): Int{
    return with(getSafeDensity()){
        <EMAIL>()
    }
}


fun Dp.toSp(): TextUnit {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun TextUnit.toPx(): Float {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun TextUnit.roundToPx(): Int{
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun TextUnit.toDp(): Dp {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun Int.toDp(): Dp {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun Int.toSp(): TextUnit{
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun Float.toDp(): Dp {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

fun Float.toSp(): TextUnit {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}


fun DpRect.toRect(): Rect {
    return with(getSafeDensity()){
        <EMAIL>()
    }
}

