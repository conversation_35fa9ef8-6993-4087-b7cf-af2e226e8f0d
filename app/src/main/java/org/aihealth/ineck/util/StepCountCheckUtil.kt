package org.aihealth.ineck.util

import android.content.Context
import android.content.pm.PackageManager
import android.hardware.Sensor
import android.hardware.SensorManager

class StepCountCheckUtil(private val mContext: Context) {

    //是否有传感器
    private var hasSensor: Boolean = isSupportStepCountSensor()

    fun isSupportStepCountSensor(): <PERSON><PERSON><PERSON> {
        return mContext.packageManager
            .hasSystemFeature(PackageManager.FEATURE_SENSOR_STEP_COUNTER)
    }


    companion object{
        /**
         * 判断该设备是否支持计歩
         *
         * @param context
         * @return
         */
        fun isSupportStepCountSensor(context: Context): <PERSON><PERSON><PERSON> {
            // 获取传感器管理器的实例
            val sensorManager = context
                .getSystemService(Context.SENSOR_SERVICE) as SensorManager
            val countSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER)
            val detectorSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_DETECTOR)
            return countSensor != null || detectorSensor != null
        }
    }

}