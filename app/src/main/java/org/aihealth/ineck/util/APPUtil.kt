package org.aihealth.ineck.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.view.inputmethod.InputMethodManager
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.activity
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.currentLocale


object APPUtil {
    /**
     * 重启应用
     */
    fun restartApp(context: Context) {
        val intent = Intent(context, MainActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
        context.startActivity(intent)
    }

    fun setLocale(language: String, context: Context) {
        // 立即更新 currentLocale 变量，确保 API 请求使用正确的语言参数
        currentLocale = if (language == "zh") java.util.Locale.CHINESE else java.util.Locale.ENGLISH
        LogUtil.i("Updated currentLocale immediately to: $currentLocale")
        
        changeSystemLanguage(language)
        LogUtil.i("currentLocale:$currentLocale")
    }

    /**
     * 跳转到应用详情界面
     */
    fun goAPPDetail() {
        val intent = Intent().apply {
            action = "android.settings.APPLICATION_DETAILS_SETTINGS"
            data = Uri.fromParts("package", baseApplication.packageName, null)
        }
        activity.startActivity(intent)
    }

    /**
     * 修改系统语言
     */
    fun changeSystemLanguage(language: String) {
        val it = Intent(Settings.ACTION_LOCALE_SETTINGS)
        it.putExtra("extra_prefs_show_button_bar", true) //是否显示button bar
        it.putExtra("extra_prefs_set_back_text", "Tip")
        it.putExtra("extra_prefs_set_back_text", "Back")
        activity.startActivity(it)

    }

    /**
     * 跳转到系统语言选择界面
     */
    fun goSystemLanguage() {
        val intent = Intent(Settings.ACTION_LOCALE_SETTINGS)
        activity.startActivity(intent)
    }

    /**
     * 关闭键盘
     */
    fun closeInputMethod() {
        activity.currentFocus?.let { currentFocus ->
            val imm: InputMethodManager? =
                activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager?
            if (imm?.isActive == true) {
                imm.hideSoftInputFromWindow(currentFocus.windowToken, InputMethodManager.HIDE_NOT_ALWAYS)
            }
        }
    }


    fun loadLocale(context: Context) {
        val sharedPreferences = context.getSharedPreferences("app_setting", Activity.MODE_PRIVATE)
        val language = sharedPreferences.getString(
            "app_language",
            context.resources.configuration.locales.get(0).language
        )
        LogUtil.i("loadLocale: $language")
//        setLocale(language!!, context)
    }
}