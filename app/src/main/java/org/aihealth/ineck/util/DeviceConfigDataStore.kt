package org.aihealth.ineck.util

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.booleanPreferencesKey
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.emptyPreferences
import androidx.datastore.preferences.core.intPreferencesKey
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.map
import org.aihealth.ineck.model.DeviceConfig
import org.aihealth.ineck.model.DeviceType
import java.io.IOException

/**
 * DataStore工具类，用于存储和读取设备配置信息
 */
class DeviceConfigDataStore(private val context: Context) {

    companion object {
        // 为每种设备类型创建单独的DataStore
        private val Context.aiNeckDataStore: DataStore<Preferences> by preferencesDataStore(name = "aiNeck_deviceconfig")
        private val Context.aiBackDataStore: DataStore<Preferences> by preferencesDataStore(name = "aiBack_deviceconfig")
        private val Context.defaultDataStore: DataStore<Preferences> by preferencesDataStore(name = "default_deviceconfig")

        // 定义所有存储键
        private val NAME = stringPreferencesKey("name")
        private val MAC = stringPreferencesKey("mac")
        private val VERSION = stringPreferencesKey("version")
        private val SN = stringPreferencesKey("sn")
        private val DEVICE_TYPE = stringPreferencesKey("deviceType")
        private val IS_VIBRATION = booleanPreferencesKey("isVibration")
        private val VIBRATION_ANGLE = intPreferencesKey("vibrationAngle")
        private val VIBRATION_FREQUENCY = intPreferencesKey("vibrationFrequency")
        private val VIBRATION_INTENSITY = intPreferencesKey("vibrationIntensity")
        private val MAX_ANGLE = intPreferencesKey("maxAngle")
        private val IS_CALIBRATED = booleanPreferencesKey("isCalibrated")
    }

    // 根据设备类型获取相应的DataStore
    private fun getDataStoreByType(deviceType: DeviceType): DataStore<Preferences> {
        return when (deviceType) {
            DeviceType.aiNeck, DeviceType.aiNeckCV -> context.aiNeckDataStore
            DeviceType.aiBack, DeviceType.aiBackCV -> context.aiBackDataStore
            else -> {
                context.defaultDataStore
            }
        }
    }

    /**
     * 保存设备配置到DataStore
     */
    suspend fun saveDeviceConfig(config: DeviceConfig) {
        try {
            val dataStore = getDataStoreByType(config.deviceType)
            dataStore.edit { preferences ->
                preferences[NAME] = config.name
                preferences[MAC] = config.mac
                preferences[VERSION] = config.version
                preferences[SN] = config.sn
                preferences[DEVICE_TYPE] = config.deviceType.name
                preferences[IS_VIBRATION] = config.isVibration
                preferences[VIBRATION_ANGLE] = config.vibrationAngle
                preferences[VIBRATION_FREQUENCY] = config.vibrationFrequency
                preferences[VIBRATION_INTENSITY] = config.vibrationIntensity
                preferences[MAX_ANGLE] = config.maxAngle
                preferences[IS_CALIBRATED] = config.isCalibrated
            }
            LogUtil.d("DeviceConfig保存成功: $config")
        } catch (e: Exception) {
            LogUtil.e("DeviceConfig保存失败: ${e.message}")
        }
    }

    /**
     * 保存校准角度（仅在校准完成后调用）
     */
    suspend fun saveCalibrationAngle(deviceType: DeviceType, angle: Int) {
        try {
            val dataStore = getDataStoreByType(deviceType)
            dataStore.edit { preferences ->
                preferences[MAX_ANGLE] = angle
                preferences[IS_CALIBRATED] = true
            }
            LogUtil.d("校准角度保存成功: deviceType=$deviceType, angle=$angle")
        } catch (e: Exception) {
            LogUtil.e("校准角度保存失败: ${e.message}")
        }
    }

    /**
     * 从DataStore读取设备配置
     */
    fun getDeviceConfig(deviceType: DeviceType): Flow<DeviceConfig> {
        val dataStore = getDataStoreByType(deviceType)
        return dataStore.data
            .catch { exception ->
                if (exception is IOException) {
                    LogUtil.e("读取设备配置失败: ${exception.message}")
                    emit(emptyPreferences())
                } else {
                    throw exception
                }
            }
            .map { preferences ->
                DeviceConfig().apply {
                    // 如果在之前的版本中某些字段不存在，将使用默认值
                    this.deviceType = try {
                        DeviceType.valueOf(preferences[DEVICE_TYPE] ?: deviceType.name)
                    } catch (e: Exception) {
                        deviceType
                    }
                    name = preferences[NAME] ?: ""
                    mac = preferences[MAC] ?: ""
                    version = preferences[VERSION] ?: ""
                    sn = preferences[SN] ?: ""
                    isVibration = preferences[IS_VIBRATION] ?: true
                    vibrationAngle = preferences[VIBRATION_ANGLE] ?: 0
                    vibrationFrequency = preferences[VIBRATION_FREQUENCY] ?: 0
                    vibrationIntensity = preferences[VIBRATION_INTENSITY] ?: 12
                    maxAngle = preferences[MAX_ANGLE] ?: 0
                    isCalibrated = preferences[IS_CALIBRATED] ?: false
                    
                    // 记录加载结果
                    LogUtil.d("""
                        读取设备配置成功: 
                        - 设备类型: $deviceType
                        - 设备名称: $name
                        - MAC地址: $mac
                        - 振动设置: 开启=$isVibration, 角度=$vibrationAngle, 频率=$vibrationFrequency, 强度=$vibrationIntensity
                        - 最大角度: $maxAngle
                        - 已校准: $isCalibrated
                    """.trimIndent())
                }
            }
    }
}