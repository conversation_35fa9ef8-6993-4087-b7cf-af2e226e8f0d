package org.aihealth.ineck.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.util.SPUtil.getLong

object SPUtil {

    private val SETUP = "setup"
    private val sharedPreferences by lazy {
        baseApplication.getSharedPreferences(SETUP,Context.MODE_PRIVATE)
    }

    fun getInt(key:String,defValue:Int=0):Int{
        return sharedPreferences.getInt(key,defValue)
    }

    fun putInt(key: String, value:Int){
        sharedPreferences.edit().putInt(key,value).apply()
    }

    fun getString(key:String, defValue:String): String {
        return sharedPreferences.getString(key,defValue)!!
    }


    fun putString(key: String,value:String){
        sharedPreferences.edit().putString(key,value).apply()
    }


    fun getBoolean(key:String,defValue:Boolean=false):Boolean{
        return sharedPreferences.getBoolean(key,defValue)
    }


    fun putBoolean(key: String,value:Boolean){
        sharedPreferences.edit().putBoolean(key,value).apply()
    }


    fun getFloat(key:String,defValue:Float=0f):Float{
        return sharedPreferences.getFloat(key,defValue)
    }

    fun putFloat(key: String,value:Float){
        sharedPreferences.edit().putFloat(key,value).apply()
    }


    fun getLong(key:String,defValue:Long= 0L):Long{
        return sharedPreferences.getLong(key,defValue)
    }

    fun putLong(key: String,value:Long){
        sharedPreferences.edit().putLong(key,value).apply()
    }

    fun putDouble(key: String, value: Double) {
        sharedPreferences.edit().putDouble(key, value).apply()
    }

    @SuppressLint("CommitPrefEdits")
    fun getDouble(key: String, defaultValue: Double = 0.0): Double {
        return sharedPreferences.edit().getDouble(key, defaultValue)
    }
    fun clear() {
        sharedPreferences.edit().clear().apply()
    }

}
fun SharedPreferences.Editor.putDouble(key: String, value: Double): SharedPreferences.Editor {
    return putLong(key, java.lang.Double.doubleToRawLongBits(value))
}
fun SharedPreferences.Editor.getDouble(key: String, defaultValue: Double = 0.0): Double {
    return java.lang.Double.longBitsToDouble(getLong(key, java.lang.Double.doubleToLongBits(defaultValue)))
}