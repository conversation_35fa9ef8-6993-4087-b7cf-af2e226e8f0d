package org.aihealth.ineck.util

/**
 * Country code data class
 */
data class CountryCode(
    val code: String,        // Country code (e.g., "+86")
    val name: String,        // Country name (e.g., "Mainland China")
    val flag: String,        // Flag emoji (e.g., "🇨🇳")
    val isoCode: String      // ISO country code (e.g., "CN")
)

/**
 * Utility class for country codes and phone number formatting
 */
object CountryCodeUtil {
    
    // List of mainstream countries worldwide
    val countryCodes = listOf(
        // Asia
        CountryCode("+86", "Mainland China", "🇨🇳", "CN"),
        CountryCode("+852", "Hong Kong", "🇭🇰", "HK"),
        CountryCode("+886", "Taiwan", "🇹🇼", "TW"),
        CountryCode("+81", "Japan", "🇯🇵", "JP"),
        CountryCode("+82", "South Korea", "🇰🇷", "KR"),
        CountryCode("+65", "Singapore", "🇸🇬", "SG"),
        CountryCode("+91", "India", "🇮🇳", "IN"),
        CountryCode("+66", "Thailand", "🇹🇭", "TH"),
        CountryCode("+60", "Malaysia", "🇲🇾", "MY"),
        CountryCode("+62", "Indonesia", "🇮🇩", "ID"),
        CountryCode("+84", "Vietnam", "🇻🇳", "VN"),
        CountryCode("+63", "Philippines", "🇵🇭", "PH"),
        CountryCode("+971", "UAE", "🇦🇪", "AE"),
        CountryCode("+966", "Saudi Arabia", "🇸🇦", "SA"),
        CountryCode("+90", "Turkey", "🇹🇷", "TR"),
        CountryCode("+972", "Israel", "🇮🇱", "IL"),
        
        // Europe
        CountryCode("+44", "United Kingdom", "🇬🇧", "GB"),
        CountryCode("+49", "Germany", "🇩🇪", "DE"),
        CountryCode("+33", "France", "🇫🇷", "FR"),
        CountryCode("+39", "Italy", "🇮🇹", "IT"),
        CountryCode("+34", "Spain", "🇪🇸", "ES"),
        CountryCode("+31", "Netherlands", "🇳🇱", "NL"),
        CountryCode("+32", "Belgium", "🇧🇪", "BE"),
        CountryCode("+41", "Switzerland", "🇨🇭", "CH"),
        CountryCode("+46", "Sweden", "🇸🇪", "SE"),
        CountryCode("+47", "Norway", "🇳🇴", "NO"),
        CountryCode("+45", "Denmark", "🇩🇰", "DK"),
        CountryCode("+358", "Finland", "🇫🇮", "FI"),
        CountryCode("+48", "Poland", "🇵🇱", "PL"),
        CountryCode("+420", "Czech Republic", "🇨🇿", "CZ"),
        CountryCode("+36", "Hungary", "🇭🇺", "HU"),
        CountryCode("+43", "Austria", "🇦🇹", "AT"),
        CountryCode("+351", "Portugal", "🇵🇹", "PT"),
        CountryCode("+30", "Greece", "🇬🇷", "GR"),
        CountryCode("+7", "Russia", "🇷🇺", "RU"),
        CountryCode("+380", "Ukraine", "🇺🇦", "UA"),
        
        // Americas
        CountryCode("+1", "United States", "🇺🇸", "US"),
        CountryCode("+1", "Canada", "🇨🇦", "CA"),
        CountryCode("+52", "Mexico", "🇲🇽", "MX"),
        CountryCode("+55", "Brazil", "🇧🇷", "BR"),
        CountryCode("+54", "Argentina", "🇦🇷", "AR"),
        CountryCode("+56", "Chile", "🇨🇱", "CL"),
        CountryCode("+57", "Colombia", "🇨🇴", "CO"),
        CountryCode("+51", "Peru", "🇵🇪", "PE"),
        CountryCode("+58", "Venezuela", "🇻🇪", "VE"),
        CountryCode("+593", "Ecuador", "🇪🇨", "EC"),
        CountryCode("+595", "Paraguay", "🇵🇾", "PY"),
        CountryCode("+598", "Uruguay", "🇺🇾", "UY"),
        
        // Oceania
        CountryCode("+61", "Australia", "🇦🇺", "AU"),
        CountryCode("+64", "New Zealand", "🇳🇿", "NZ"),
        
        // Africa
        CountryCode("+27", "South Africa", "🇿🇦", "ZA"),
        CountryCode("+20", "Egypt", "🇪🇬", "EG"),
        CountryCode("+234", "Nigeria", "🇳🇬", "NG"),
        CountryCode("+254", "Kenya", "🇰🇪", "KE"),
        CountryCode("+212", "Morocco", "🇲🇦", "MA"),
        CountryCode("+216", "Tunisia", "🇹🇳", "TN"),
        CountryCode("+233", "Ghana", "🇬🇭", "GH"),
        CountryCode("+251", "Ethiopia", "🇪🇹", "ET"),
        
        // Middle East
        CountryCode("+98", "Iran", "🇮🇷", "IR"),
        CountryCode("+964", "Iraq", "🇮🇶", "IQ"),
        CountryCode("+962", "Jordan", "🇯🇴", "JO"),
        CountryCode("+961", "Lebanon", "🇱🇧", "LB"),
        CountryCode("+973", "Bahrain", "🇧🇭", "BH"),
        CountryCode("+974", "Qatar", "🇶🇦", "QA"),
        CountryCode("+965", "Kuwait", "🇰🇼", "KW"),
        CountryCode("+968", "Oman", "🇴🇲", "OM")
    )
    
    /**
     * Get country code by ISO code
     */
    fun getCountryCodeByIso(isoCode: String): CountryCode? {
        return countryCodes.find { it.isoCode == isoCode }
    }
    
    /**
     * Get country code by phone code
     */
    fun getCountryCodeByPhoneCode(phoneCode: String): CountryCode? {
        return countryCodes.find { it.code == phoneCode }
    }
    
    /**
     * Parse full phone number and return country code and national number
     */
    fun parsePhoneNumber(fullNumber: String): Pair<CountryCode, String> {
        // Manual parsing
        val bestMatch = countryCodes.filter { fullNumber.startsWith(it.code) }
            .maxByOrNull { it.code.length }
        
        return if (bestMatch != null) {
            val nationalNumber = fullNumber.substring(bestMatch.code.length)
                .filter { it.isDigit() } // Remove any formatting
            Pair(bestMatch, nationalNumber)
        } else {
            // Default to United States
            val nationalNumber = fullNumber.filter { it.isDigit() }
            val defaultCountry = countryCodes.find { it.isoCode == "US" } ?: countryCodes.first()
            Pair(defaultCountry, nationalNumber)
        }
    }
    
    /**
     * Format phone number based on country
     */
    fun formatPhoneNumber(countryCode: CountryCode, nationalNumber: String): String {
        return when (countryCode.isoCode) {
            "CN" -> formatChineseNumber(nationalNumber)
            "US" -> formatUSNumber(nationalNumber)
            "GB" -> formatUKNumber(nationalNumber)
            else -> formatGenericNumber(nationalNumber)
        }
    }
    
    /**
     * Format Chinese phone number (11 digits: 1XX XXXX XXXX)
     */
    private fun formatChineseNumber(number: String): String {
        return if (number.length >= 11) {
            "${number.substring(0, 3)} ${number.substring(3, 7)} ${number.substring(7, 11)}"
        } else {
            number
        }
    }
    
    /**
     * Format US phone number (10 digits: XXX XXX XXXX)
     */
    private fun formatUSNumber(number: String): String {
        return if (number.length >= 10) {
            "${number.substring(0, 3)} ${number.substring(3, 6)} ${number.substring(6, 10)}"
        } else {
            number
        }
    }
    
    /**
     * Format UK phone number (11 digits: XXXX XXX XXXX)
     */
    private fun formatUKNumber(number: String): String {
        return if (number.length >= 11) {
            "${number.substring(0, 4)} ${number.substring(4, 7)} ${number.substring(7, 11)}"
        } else {
            number
        }
    }
    
    /**
     * Generic number formatting
     */
    private fun formatGenericNumber(number: String): String {
        return when {
            number.length <= 4 -> number
            number.length <= 7 -> "${number.substring(0, 4)} ${number.substring(4)}"
            else -> "${number.substring(0, 4)} ${number.substring(4, 7)} ${number.substring(7)}"
        }
    }
    
    /**
     * Format phone number for universal storage (E.164 format)
     * This ensures consistent format across all countries
     */
    fun formatForStorage(countryCode: CountryCode, nationalNumber: String): String {
        // Remove all non-digit characters from national number
        val cleanNumber = nationalNumber.filter { it.isDigit() }
        
        // Combine country code and national number without any formatting
        // This creates a universal E.164 format (e.g., +1234567890)
        return "${countryCode.code}$cleanNumber"
    }
    
    /**
     * Validate if the phone number is in correct format for the selected country
     */
    fun isValidPhoneNumber(countryCode: CountryCode, nationalNumber: String): Boolean {
        val cleanNumber = nationalNumber.filter { it.isDigit() }
        
        return when (countryCode.isoCode) {
            "US", "CA" -> cleanNumber.length == 10 // US/Canada: 10 digits
            "CN" -> cleanNumber.length == 11 && cleanNumber.startsWith("1") // China: 11 digits starting with 1
            "GB" -> cleanNumber.length >= 10 && cleanNumber.length <= 11 // UK: 10-11 digits
            "JP" -> cleanNumber.length >= 10 && cleanNumber.length <= 11 // Japan: 10-11 digits
            "KR" -> cleanNumber.length >= 10 && cleanNumber.length <= 11 // Korea: 10-11 digits
            else -> cleanNumber.length >= 7 && cleanNumber.length <= 15 // General: 7-15 digits
        }
    }
    
    /**
     * Validate Chinese phone number (11 digits starting with 1)
     */
    private fun isValidChineseNumber(number: String): Boolean {
        return number.length == 11 && number.startsWith("1")
    }
    
    /**
     * Validate US phone number (10 digits)
     */
    private fun isValidUSNumber(number: String): Boolean {
        return number.length == 10
    }
    
    /**
     * Validate UK phone number (11 digits starting with 7)
     */
    private fun isValidUKNumber(number: String): Boolean {
        return number.length == 11 && number.startsWith("7")
    }
    
    /**
     * Generic number validation (7-15 digits)
     */
    private fun isValidGenericNumber(number: String): Boolean {
        return number.length in 7..15
    }
} 