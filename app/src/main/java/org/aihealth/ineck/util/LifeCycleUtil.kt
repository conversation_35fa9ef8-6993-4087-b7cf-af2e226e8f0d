package org.aihealth.ineck.util

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver

@Composable
fun SetOnLifeCycleListener(
    onStart: () -> Unit = {}, // 开启事件
    onStop: () -> Unit = {} // 停止时间
){
    // 当提供了一个新的lambdas时，可以安全地更新当前的lambdas
    val currentOnStart by rememberUpdatedState(onStart)
    val currentOnStop by rememberUpdatedState(onStop)

    val lifecycleOwner = LocalLifecycleOwner.current
    // 如果“lifecycleOwner”改变，处置并重置效果
    DisposableEffect(lifecycleOwner) {
        // 创建一个观察者来触发我们所记得的回调来发送事件
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_START) {
                currentOnStart()
            } else if (event == Lifecycle.Event.ON_STOP) {
                currentOnStop()
            }
        }

        // 将观察者添加到生命周期中
        lifecycleOwner.lifecycle.addObserver(observer)

        // 当组合函数离开时，会执行
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
}