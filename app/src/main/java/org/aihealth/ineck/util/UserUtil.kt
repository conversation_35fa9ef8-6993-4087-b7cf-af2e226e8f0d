package org.aihealth.ineck.util

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences
import com.umeng.message.PushAgent
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.database
import org.aihealth.ineck.isInChina
import org.aihealth.ineck.model.SPConstant
import org.aihealth.ineck.model.angles.User
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.viewmodel.user

const val Limit_Time = 3*24*60*60L
var token: String = ""

/**
 * 获取当前 FCM token
 */
val currentFCMToken: String
    get() = SPUtil.getString(SPConstant.FCM_TOKEN, "")

/**
 * 获取当前 UPush token
 */
val currentUPushToken: String
    get() = PushAgent.getInstance(baseApplication).registrationId ?: ""

val userSP: SharedPreferences by lazy {
    baseApplication.getSharedPreferences("user", Context.MODE_PRIVATE)
}

fun User.saveToLocal() {
    userSP.edit()
        .putString("uuid", uuid)
        .putString("name", name)
        .putString("email", email)
        .putString("photo", photo)
        .putString("gender", gender)
        .putDouble("height", height)
        .putDouble("weight", weight)
        .putInt("age", age)
        .putString("birthdate", birthdate)
        .putLong("vipStartDate", vipStartDate)
        .putLong("vipEndDate", vipEndDate)
        .putLong("vipActiveTime", vipActiveTime)
        .putBoolean("vipStatus", vipStatus)
        .putBoolean("memberTrial", memberTrial)
        .putInt("membershipId", membershipId)
        .putString("membershipName", membershipName)
        .putInt("followersNumber", followersNumber)
        .putInt("followingNumber", followingNumber)
        .putInt("articleViews", articleViews)
        .putString("device_token", deviceToken)
        .apply()
        
    // 用户数据保存完成后检查并更新推送 token
    checkAndUpdateDeviceToken()
}

/**
 * 检查并更新设备推送 token
 */
fun checkAndUpdateDeviceToken() {
    if (user == null) return // 用户未登录，直接返回
    
    val currentToken = if (isInChina) currentUPushToken else currentFCMToken
    
    if (currentToken.isEmpty()) return // 没有推送 token，直接返回
    
    // 与 user.deviceToken 进行对比
    if (user.deviceToken != currentToken) {
        // Token 不一致，需要更新
        LogUtil.i("Device token changed, updating: old=${user.deviceToken}, new=$currentToken")
        
        // 更新 user 对象中的 deviceToken
        user.deviceToken = currentToken
        
        // 更新到本地存储
        userSP.edit().putString("device_token", currentToken).apply()
        
        // 更新到服务器
        if (isInChina) {
            updateUPshToken(currentToken)
        } else {
            updateFCMToken(currentToken)
        }
    }
}

/**
 * 检查并更新 FCM token (保留原有方法，用于兼容)
 */
fun checkAndUpdateFCMToken() {
    checkAndUpdateDeviceToken()
}

/**
 * 手动触发检查和更新设备 token
 * 可以在应用启动、用户登录后等场景调用
 */
fun refreshDeviceToken() {
    checkAndUpdateDeviceToken()
}

/**
 * 清空用户登出时的相关数据
 * 根据用户需求：退出登录不清空步数数据，步数数据与设备绑定
 */
fun clearAllUserData() {
    // 只清空设备token，不清空步数数据
    clearDeviceToken()
    
    LogUtil.i("用户登出数据已清空（保留步数数据）")
}

/**
 * 清空步数相关数据
 */
fun clearStepData() {
    try {
        // 清空SharedPreferences中的步数数据
        val stepPrefs = baseApplication.getSharedPreferences("hybrid_step_data", Context.MODE_PRIVATE)
        stepPrefs.edit().clear().apply()
        
        // 清空Room数据库中的步数数据
        Thread {
            try {
                database.stepDataDao().deleteAllSteps()
                LogUtil.i("步数数据库已清空")
            } catch (e: Exception) {
                LogUtil.e("清空步数数据库失败: ${e.message}")
            }
        }.start()
        
        LogUtil.i("步数数据已清空")
    } catch (e: Exception) {
        LogUtil.e("清空步数数据失败: ${e.message}")
    }
}

/**
 * 清空用户的 device token（本地和服务器）
 * 用于用户退出登录或注销账户时
 */
fun clearDeviceToken() {
    if (user == null) return
    
    // 清空本地 deviceToken
    user.deviceToken = ""
    
    // 上传空的 device_token 到服务器以覆盖之前的值
    val emptyTokenBody = if (isInChina) {
        mapOf("device_token" to "", "device_platform" to "Android")
    } else {
        mapOf("device_token" to "")
    }
    
    if (isInChina) {
        apiService.updateUPushToken(emptyTokenBody).enqueue(object : retrofit2.Callback<okhttp3.ResponseBody> {
            override fun onResponse(call: retrofit2.Call<okhttp3.ResponseBody>, response: retrofit2.Response<okhttp3.ResponseBody>) {
                LogUtil.i("UPush token cleared successfully")
            }
            override fun onFailure(call: retrofit2.Call<okhttp3.ResponseBody>, t: Throwable) {
                LogUtil.e("Failed to clear UPush token", t.toString())
            }
        })
    } else {
        apiService.updateFCMToken(emptyTokenBody).enqueue(object : retrofit2.Callback<okhttp3.ResponseBody> {
            override fun onResponse(call: retrofit2.Call<okhttp3.ResponseBody>, response: retrofit2.Response<okhttp3.ResponseBody>) {
                LogUtil.i("FCM token cleared successfully")
            }
            override fun onFailure(call: retrofit2.Call<okhttp3.ResponseBody>, t: Throwable) {
                LogUtil.e("Failed to clear FCM token", t.toString())
            }
        })
    }
}

/**
 * 更新 FCM token 到服务器
 */
private fun updateFCMToken(token: String) {
    val body = mapOf("device_token" to token)
    apiService.updateFCMToken(body).enqueue(object : retrofit2.Callback<okhttp3.ResponseBody> {
        override fun onResponse(
            call: retrofit2.Call<okhttp3.ResponseBody>,
            response: retrofit2.Response<okhttp3.ResponseBody>
        ) {
            if (response.isSuccessful) {
                LogUtil.i("FCM token updated successfully")
                // 更新成功后，确保本地存储也是最新的
                userSP.edit().putString("device_token", token).apply()
            }
        }

        override fun onFailure(
            call: retrofit2.Call<okhttp3.ResponseBody>,
            t: Throwable
        ) {
            LogUtil.e("Update FCM token failed", t.toString())
        }
    })
}

/**
 * 更新 UPush token 到服务器
 */
private fun updateUPshToken(token: String) {
    val body = mapOf("device_token" to token,"device_platform" to "Android")
    apiService.updateUPushToken(body).enqueue(object : retrofit2.Callback<okhttp3.ResponseBody> {
        override fun onResponse(
            call: retrofit2.Call<okhttp3.ResponseBody>,
            response: retrofit2.Response<okhttp3.ResponseBody>
        ) {
            if (response.isSuccessful) {
                LogUtil.i("UPush token updated successfully")
                // 更新成功后，确保本地存储也是最新的
                userSP.edit().putString("device_token", token).apply()
            }
        }

        override fun onFailure(
            call: retrofit2.Call<okhttp3.ResponseBody>,
            t: Throwable
        ) {
            LogUtil.e("Update UPush token failed", t.toString())
        }
    })
}

@SuppressLint("CommitPrefEdits")
fun User?.loadByLocal() {
    val user1 = User()
    user1.uuid = userSP.getString("uuid", "")!!
    user1.name = userSP.getString("name", "")!!
    user1.email = userSP.getString("email", "")!!
    user1.photo = userSP.getString("photo", "")!!
    user1.gender = userSP.getString("gender", "U")!!
    user1.height = userSP.edit().getDouble("height", 0.0)
    user1.weight = userSP.edit().getDouble("weight", 0.0)
    user1.birthdate = userSP.getString("birthdate", "")!!
    user1.age = userSP.getInt("age", 0)
    user1.vipStartDate = userSP.getLong("vipStartDate", 0L)
    user1.vipEndDate = userSP.getLong("vipEndDate", 0L)
    user1.vipActiveTime = userSP.getLong("vipActiveTime", 0L)
    user1.vipStatus = userSP.getBoolean("vipStatus", false)
    user1.memberTrial = userSP.getBoolean("memberTrial", false)
    user1.membershipId = userSP.getInt("membershipId", -1)
    user1.membershipName = userSP.getString("membershipName", "")!!
    user1.followersNumber = userSP.getInt("followersNumber", 0)
    user1.followingNumber = userSP.getInt("followingNumber", 0)
    user1.articleViews = userSP.getInt("articleViews", 0)
    user1.deviceToken = userSP.getString("device_token", "")!!
    user = user1
    
    // 用户数据加载完成后检查并更新推送 token
    checkAndUpdateDeviceToken()
}


