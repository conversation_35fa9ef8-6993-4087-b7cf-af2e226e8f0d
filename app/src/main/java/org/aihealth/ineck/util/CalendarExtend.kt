package org.aihealth.ineck.util

import org.aihealth.ineck.currentLocale
import org.aihealth.ineck.util.LogUtil
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

/**
 * 获取当天日期（时,分,秒为0）
 */
fun getDefaultDate(): Calendar {
    return Calendar.getInstance().apply {
        set(Calendar.HOUR_OF_DAY,0)
        set(Calendar.MINUTE,0)
        set(Calendar.SECOND,0)
        set(Calendar.MILLISECOND,0)
    }
}
fun fixUTCDate(date: Calendar, nextDate: Calendar): Pair<String, String> {
    // 使用 SimpleDateFormat 来格式化日期，并处理时区
    val dateFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US).apply {
        timeZone = TimeZone.getTimeZone("UTC")  // 设置时区为 UTC
    }

    // 将 Calendar 转换为 Date，并格式化为 UTC 时间戳
    val startTime = dateFormat.format(date.time)
    val endTime = dateFormat.format(nextDate.time)

    return Pair(startTime, endTime)
}

fun convertUTCToLocal(utcTimestamp: String): Date {
    try {
        // 获取设备的本地时区
        val localTimeZone = TimeZone.getDefault()

        // 尝试解析不同的UTC时间格式
        val utcDate = try {
            // 格式1：包含毫秒 (yyyy-MM-dd'T'HH:mm:ss.SSS'Z')
            val utcFormatWithMillis = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault())
            utcFormatWithMillis.timeZone = TimeZone.getTimeZone("UTC")
            utcFormatWithMillis.parse(utcTimestamp)
        } catch (e1: Exception) {
            try {
                // 格式2：不包含毫秒 (yyyy-MM-dd'T'HH:mm:ss'Z')
                val utcFormatNoMillis = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.getDefault())
                utcFormatNoMillis.timeZone = TimeZone.getTimeZone("UTC")
                utcFormatNoMillis.parse(utcTimestamp)
            } catch (e2: Exception) {
                // 格式3：无Z后缀 (yyyy-MM-dd'T'HH:mm:ss)
                val utcFormatNoZ = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
                utcFormatNoZ.timeZone = TimeZone.getTimeZone("UTC")
                utcFormatNoZ.parse(utcTimestamp)
            }
        }

        // 将 UTC 时间转换为本地时间
        val localFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.getDefault())
        localFormat.timeZone = localTimeZone  // 设置为本地时区
        val localDate = localFormat.parse(localFormat.format(utcDate))
        
        // 添加调试日志
        val displayFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
        LogUtil.d("convertUTCToLocal", "✅ UTC输入: $utcTimestamp | UTC解析: ${displayFormat.format(utcDate)} | 本地转换: ${displayFormat.format(localDate)} | 时区: ${localTimeZone.displayName}")
        
        return localDate
    }catch (e: Exception){
        LogUtil.e("convertUTCToLocal", "❌ 时间转换失败: $utcTimestamp, 错误: ${e.message}")
        return Date()
    }
}
fun getDayStatus(localDate: Date): Int {
    // 获取当前日期和待比较日期的 Calendar 实例
    val calendar = Calendar.getInstance()
    val today = Calendar.getInstance()

    // 获取当前日期的年、月、日（时、分、秒设置为0）
    today.set(Calendar.HOUR_OF_DAY, 0)
    today.set(Calendar.MINUTE, 0)
    today.set(Calendar.SECOND, 0)
    today.set(Calendar.MILLISECOND, 0)

    // 获取待比较日期的年、月、日
    calendar.time = localDate
    calendar.set(Calendar.HOUR_OF_DAY, 0)
    calendar.set(Calendar.MINUTE, 0)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)

    val calendarTime = calendar.timeInMillis
    val todayTime = today.timeInMillis
    val oneDayMillis = 24 * 60 * 60 * 1000L
    
    // 添加调试日志
    val inputDateStr = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(localDate)
    val calendarDateStr = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(calendar.time)
    val todayDateStr = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(today.time)
    
    // 比较日期 - 修复原有的副作用问题
    val result = when {
        calendarTime == todayTime -> 1 // 今天
        calendarTime == todayTime - oneDayMillis -> 0 // 昨天
        else -> -1 // 昨天之前
    }
    
    val resultText = when (result) {
        1 -> "今天"
        0 -> "昨天"
        else -> "更早"
    }
    
    LogUtil.d("getDayStatus", "输入日期: $inputDateStr -> 处理后: $calendarDateStr | 今天: $todayDateStr | 结果: $result ($resultText)")
    
    return result
}

fun Calendar.copy(): Calendar{
    return clone() as Calendar
}

var Calendar.year: Int
    get() = this.get(Calendar.YEAR)
    set(value) {
        return set(Calendar.YEAR,value)
    }

var Calendar.month: Int
    get() = this.get(Calendar.MONTH)
    set(value) {
        return set(Calendar.MONTH,value)
    }

var Calendar.date: Int
    get() = this.get(Calendar.DATE)
    set(value) {
        return set(Calendar.DATE,value)
    }

var Calendar.hour: Int
    get() = this.get(Calendar.HOUR_OF_DAY)
    set(value) {
        return set(Calendar.HOUR_OF_DAY,value)
    }

var Calendar.minute: Int
    get() = this.get(Calendar.MINUTE)
    set(value) {
        return set(Calendar.MINUTE,value)
    }

var Calendar.second: Int
    get() = this.get(Calendar.SECOND)
    set(value) {
        return set(Calendar.SECOND,value)
    }

/**
 * 获取当天是星期几（周日：0，周一：1，周二：2......）
 */
val Calendar.dayOfWeek: Int
    get() = this.get(Calendar.DAY_OF_WEEK) - 1

fun Calendar.addYear(num: Int) {
    add(Calendar.YEAR,num)
}

fun Calendar.addMonth(num: Int) {
    add(Calendar.MONTH,num)
}

fun Calendar.addDate(num: Int) {
    add(Calendar.DATE,num)
}

/**
 * 获取当月一共多少天
 */
val Calendar.monthLastDay: Int
    get() {
        val date = this[Calendar.DATE]
        this[Calendar.DATE] = 1
        roll(Calendar.DATE, -1)
        val result = this[Calendar.DATE]
        this.date = date
        return result
    }

/**
 * 返回10位时间戳
 */
val Calendar.timeInSecond: String
    get() = (timeInMillis / 1000).toString()

/**
 * 中文日期格式字符串：yyyy年MM月dd日
 */
val Calendar.chineseDate: String
    get() = SimpleDateFormat("yyyy年MM月dd日", Locale.CHINESE).format(time)

/**
 * 英日期格式字符串：MMM dd,yyyy
 */
val Calendar.englishDate: String
    get() = SimpleDateFormat("MMM dd,yyyy", Locale.ENGLISH).format(time)

/**
 * 根据当前语言返回相应日期格式
 * 中文：yyyy年MM月dd日
 * 英文：MMM dd,yyyy
 */
val Calendar.localeDate: String
    get() = if (currentLocale == Locale.CHINESE) chineseDate else englishDate


val Calendar.networkTime: String
    get() = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.CHINESE).format(time)

val Calendar.time_UTC: String
    get() = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.ENGLISH).format(time)
