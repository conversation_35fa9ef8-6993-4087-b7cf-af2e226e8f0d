package org.aihealth.ineck.util

import org.aihealth.ineck.model.SPConstant

/**
 * 步数模块状态管理器
 * 处理步数模块与用户登录状态的绑定逻辑
 */
object StepModuleManager {
    
    /**
     * 检查用户是否已登录
     */
    private fun isUserLoggedIn(): Boolean {
        val isLoggedIn = token.isNotEmpty()
        LogUtil.d("StepModuleManager: 检查登录状态 - token: ${if(token.isEmpty()) "空" else "有值"}, 已登录: $isLoggedIn")
        return isLoggedIn
    }
    
    /**
     * 用户是否手动关闭过步数模块
     */
    fun isManuallyDisabled(): Boolean {
        val disabled = userSP.getBoolean(SPConstant.STEP_MANUALLY_DISABLED, false)
        LogUtil.d("StepModuleManager: 检查手动关闭状态 - 手动关闭: $disabled")
        return disabled
    }
    
    /**
     * 用户是否曾经启用过步数模块
     */
    fun hasEverEnabled(): <PERSON>olean {
        val enabled = userSP.getBoolean(SPConstant.STEP_AUTO_START_ENABLED, false)
        LogUtil.d("StepModuleManager: 检查是否曾启用 - 曾启用: $enabled")
        return enabled
    }
    
    /**
     * 获取当前步数模块状态
     */
    fun getCurrentState(): Int {
        val state = userSP.getInt(SPConstant.USE_LOCAL_STEP_DATA, 1)
        LogUtil.d("StepModuleManager: 获取当前状态 - 状态值: $state (${if(state == 0) "开启" else "关闭"})")
        return state
    }
    
    /**
     * 检查是否应该自动启动步数模块
     * 修正逻辑：用户手动启用过且未手动关闭，就应该启动（不管是否登录）
     * 只有在用户从未启用过，或者手动关闭过的情况下才不启动
     */
    fun shouldAutoStart(): Boolean {
        LogUtil.i("🔍 StepModuleManager: 开始检查是否应该自动启动步数模块")
        
        val isLoggedIn = isUserLoggedIn()
        val hasEnabled = hasEverEnabled()
        val isManuallyDisabled = isManuallyDisabled()
        
        // 修正后的逻辑：用户启用过且未手动关闭就启动
        val shouldStart = hasEnabled && !isManuallyDisabled
        
        LogUtil.i("📊 StepModuleManager: 自动启动条件检查结果:")
        LogUtil.i("   - 用户已登录: $isLoggedIn")
        LogUtil.i("   - 曾经启用过: $hasEnabled") 
        LogUtil.i("   - 手动关闭过: $isManuallyDisabled")
        LogUtil.i("   - 最终判断: 曾启用($hasEnabled) && 未手动关闭(${!isManuallyDisabled}) = $shouldStart")
        LogUtil.i("   - 最终结果: ${if(shouldStart) "✅ 应该启动" else "❌ 不应该启动"}")
        
        return shouldStart
    }
    
    /**
     * 用户手动启用步数模块
     */
    fun enableByUser() {
        LogUtil.i("👤 StepModuleManager: 用户手动启用步数模块")
        LogUtil.d("   - 设置状态: 0 (开启)")
        LogUtil.d("   - 设置自动启动权限: true")
        LogUtil.d("   - 清除手动关闭标记: false")
        
        userSP.edit()
            .putInt(SPConstant.USE_LOCAL_STEP_DATA, 0)
            .putBoolean(SPConstant.STEP_AUTO_START_ENABLED, true)
            .putBoolean(SPConstant.STEP_MANUALLY_DISABLED, false)
            .apply()
            
        LogUtil.i("✅ StepModuleManager: 用户手动启用完成")
    }
    
    /**
     * 用户手动关闭步数模块
     */
    fun disableByUser() {
        LogUtil.i("👤 StepModuleManager: 用户手动关闭步数模块")
        LogUtil.d("   - 设置状态: 1 (关闭)")
        LogUtil.d("   - 设置手动关闭标记: true")
        
        userSP.edit()
            .putInt(SPConstant.USE_LOCAL_STEP_DATA, 1)
            .putBoolean(SPConstant.STEP_MANUALLY_DISABLED, true)
            .apply()
            
        LogUtil.i("❌ StepModuleManager: 用户手动关闭完成")
    }
    
    /**
     * 系统自动启用步数模块（用户登录时）
     */
    fun enableBySystem() {
        LogUtil.i("🤖 StepModuleManager: 系统自动启用步数模块")
        LogUtil.d("   - 设置状态: 0 (开启)")
        LogUtil.d("   - 保持其他标记不变")
        
        userSP.edit()
            .putInt(SPConstant.USE_LOCAL_STEP_DATA, 0)
            .apply()
            
        LogUtil.i("✅ StepModuleManager: 系统自动启用完成")
    }
    
    /**
     * 系统自动关闭步数模块（用户登出时）
     */
    fun disableBySystem() {
        LogUtil.i("🤖 StepModuleManager: 系统自动关闭步数模块")
        LogUtil.d("   - 设置状态: 1 (关闭)")
        LogUtil.d("   - 不修改手动关闭标记")
        
        userSP.edit()
            .putInt(SPConstant.USE_LOCAL_STEP_DATA, 1)
            .apply()
            
        LogUtil.i("❌ StepModuleManager: 系统自动关闭完成")
    }
    
    /**
     * 用户登录时的处理逻辑
     * 用户登录时，如果满足自动启动条件，则启用步数模块
     */
    fun onUserLogin() {
        LogUtil.i("🔐 StepModuleManager: 用户登录事件触发")
        
        val hasEnabled = hasEverEnabled()
        val isManuallyDisabled = isManuallyDisabled()
        val shouldEnable = hasEnabled && !isManuallyDisabled
        
        LogUtil.i("🔐 StepModuleManager: 登录时步数模块处理:")
        LogUtil.i("   - 曾经启用过: $hasEnabled")
        LogUtil.i("   - 手动关闭过: $isManuallyDisabled") 
        LogUtil.i("   - 应该启用: $shouldEnable")
        
        if (shouldEnable) {
            enableBySystem()
            LogUtil.i("✅ StepModuleManager: 用户登录，满足条件，自动启用步数模块")
        } else {
            LogUtil.i("❌ StepModuleManager: 用户登录，但不满足自动启用条件")
        }
        
        LogUtil.i("📋 StepModuleManager: 登录后状态总结")
        LogUtil.i(getStatusDescription())
    }
    
    /**
     * 用户登出时的处理逻辑
     */
    fun onUserLogout() {
        LogUtil.i("🚪 StepModuleManager: 用户登出事件触发")
        
        val beforeState = getCurrentState()
        LogUtil.i("   - 登出前状态: ${if(beforeState == 0) "开启" else "关闭"}")
        
        // 登出时关闭步数模块，但不标记为手动关闭
        disableBySystem()
        
        LogUtil.i("✅ StepModuleManager: 用户登出，自动关闭步数模块")
        LogUtil.i("📋 StepModuleManager: 登出后状态总结")
        LogUtil.i(getStatusDescription())
    }
    
    /**
     * 应用启动时的检查逻辑
     */
    fun checkOnAppStart(): Boolean {
        LogUtil.i("🚀 StepModuleManager: ========== 应用启动步数模块检查 ==========")
        
        val beforeState = getCurrentState()
        LogUtil.i("   - 启动前状态: ${if(beforeState == 0) "开启" else "关闭"}")
        
        val shouldStart = shouldAutoStart()
        LogUtil.i("   - 检查结果: ${if(shouldStart) "应该启动" else "不应该启动"}")
        
        val finalResult = if (shouldStart) {
            enableBySystem()
            LogUtil.i("✅ StepModuleManager: 应用启动 - 自动启用步数模块")
            true
        } else {
            val currentState = getCurrentState()
            LogUtil.i("📱 StepModuleManager: 不满足自动启动条件，保持当前状态: ${if(currentState == 0) "开启" else "关闭"}")
            // 移除强制关闭逻辑：如果用户已经手动启用，即使未登录也保持启用状态
            currentState == 0
        }
        
        LogUtil.i("📋 StepModuleManager: 应用启动后最终状态")
        LogUtil.i(getStatusDescription())
        LogUtil.i("🚀 StepModuleManager: ========== 启动检查完成 ==========")
        
        return finalResult
    }
    
    /**
     * 获取状态描述（用于调试）
     */
    fun getStatusDescription(): String {
        return """
            📋 步数模块状态总结:
            - 当前状态: ${if (getCurrentState() == 0) "✅ 开启" else "❌ 关闭"}
            - 用户已登录: ${if (isUserLoggedIn()) "✅ 已登录" else "❌ 未登录"}
            - 曾经启用过: ${if (hasEverEnabled()) "✅ 是" else "❌ 否"}
            - 手动关闭过: ${if (isManuallyDisabled()) "⚠️ 是" else "✅ 否"}
            - 应该自动启动: ${if (shouldAutoStart()) "✅ 是 (曾启用且未手动关闭)" else "❌ 否"}
        """.trimIndent()
    }
}