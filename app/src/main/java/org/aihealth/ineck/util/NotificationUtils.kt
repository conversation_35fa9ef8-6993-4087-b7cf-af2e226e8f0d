package org.aihealth.ineck.util

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.core.app.NotificationCompat
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.model.NotificationConstants

object NotificationUtils {
    private val notificationManager by lazy {
        baseApplication.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }

    /**
     * 创建通知渠道
     */
    fun createNotificationChannels(){
        if (Build.VERSION.SDK_INT > Build.VERSION_CODES.O) {
            // 构建通知渠道
            val bluetoothServiceNotificationChannel = NotificationChannel(
                NotificationConstants.CHANNEL_BLUETOOTH_ID,
                NotificationConstants.CHANNEL_BLUETOOTH,
                NotificationManager.IMPORTANCE_MIN
            ).apply {
                // LED灯
                enableLights(false)
                // 震动
                enableVibration(false)
                //是否显示角标
                setShowBadge(true)
                description = "蓝牙设备连接状态"
            }
            val stepNotificationChannel = NotificationChannel(
                NotificationConstants.CHANNEL_STEP_ID,
                NotificationConstants.CHANNEL_STEP,
                NotificationManager.IMPORTANCE_MIN
            ).apply {
                //如果使用中的设备支持通知灯，则说明此通知通道是否应显示灯
                enableLights(false)
                //是否显示角标
                setShowBadge(false)
                lockscreenVisibility = Notification.VISIBILITY_SECRET
                description = "步数卡路里监测"
            }

            //向系统注册通知渠道，注册后不能改变重要性以及其他通知行为
            try {
                notificationManager.createNotificationChannel(bluetoothServiceNotificationChannel)
                notificationManager.createNotificationChannel(stepNotificationChannel)
            } catch (e: SecurityException) {
                // 捕获 MIUI 系统的权限异常，避免应用崩溃
                LogUtil.w("创建通知渠道时发生权限异常 (通常是 MIUI 系统问题): ${e.message}")
            } catch (e: Exception) {
                LogUtil.e("创建通知渠道失败: ${e.message}")
            }
        }
    }

    /**
     * 检查通知是否开启
     */
    fun checkNotificationEnable(): Boolean {
        return try {
            notificationManager.areNotificationsEnabled()
        } catch (e: SecurityException) {
            LogUtil.w("检查通知权限时发生异常: ${e.message}")
            false // 默认假设通知未启用
        } catch (e: Exception) {
            LogUtil.e("检查通知权限失败: ${e.message}")
            false
        }
    }

    fun sendNotification(id: Int,build: NotificationCompat.Builder) {
        try {
            notificationManager.notify(id,build.build())
        } catch (e: SecurityException) {
            LogUtil.w("发送通知时发生权限异常: ${e.message}")
        } catch (e: Exception) {
            LogUtil.e("发送通知失败: ${e.message}")
        }
    }
}