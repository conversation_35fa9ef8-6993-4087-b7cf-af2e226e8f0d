package org.aihealth.ineck.util

import android.graphics.Bitmap
import android.opengl.GLES20
import androidx.media3.common.Format
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.video.VideoFrameMetadataListener
import java.nio.IntBuffer

@UnstableApi
open class CustomVideoFrameMetadataListener : VideoFrameMetadataListener {

    override fun onVideoFrameAboutToBeRendered(
        presentationTimeUs: Long,
        releaseTimeNs: Long,
        format: Format,
        mediaFormat: android.media.MediaFormat?
    ) {

    }

    private fun getFrameAsBitmap(): Bitmap {
        // 从 OpenGL 纹理中获取 Bitmap 示例
        val width = 1280 // 设置合适的宽度
        val height = 720 // 设置合适的高度
        val size = width * height
        val pixels = IntArray(size)
        val buffer = IntBuffer.wrap(pixels)
        buffer.position(0)
        GLES20.glReadPixels(0, 0, width, height, GLES20.GL_RGBA, GLES20.GL_UNSIGNED_BYTE, buffer)
        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        bitmap.copyPixelsFromBuffer(buffer)
        return bitmap
    }
}
