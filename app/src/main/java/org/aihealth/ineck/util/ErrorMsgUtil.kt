package org.aihealth.ineck.util

import us.zoom.sdk.ZoomVideoSDKErrors.*

object ErrorMsgUtil {
    fun getMsgByErrorCode(errorCode: Int): String {
        return when (errorCode) {
            Errors_Wrong_Usage -> "Incorrect use"
            Errors_Internal_Error -> "Internal error"
            Errors_Uninitialize -> "Uninitialized"
            Errors_Memory_Error -> "Memory error"
            Errors_Load_Module_Error -> "Load module failed"
            Errors_UnLoad_Module_Error -> "Unload module failed"
            Errors_Invalid_Parameter -> "Parameter error"
            Errors_Unknown -> "Unknown error"
            Errors_Auth_Error -> "Authentication error"
            Errors_Auth_Empty_Key_or_Secret -> "Empty key or secret"
            Errors_Auth_Wrong_Key_or_Secret -> "Incorrect key or secret"
            Errors_Auth_DoesNot_Support_SDK -> "Authenticated key or secret does not support SDK"
            Errors_Auth_Disable_SDK -> "Disabled SDK with authenticated key or secret"
            Errors_SessionModule_Not_Found -> "Module not found"
            Errors_SessionService_Invaild -> "The service is invalid"
            Errors_Session_Join_Failed -> "Join session failed"
            Errors_Session_No_Rights -> "You don’t have permission to join this session"
            Errors_Session_Already_In_Progress -> "Joining session…"
            Errors_Session_Dont_Support_SessionType -> "Unsupported session type"
            Errors_Session_Reconncting -> "Reconnecting session…"
            Errors_Session_Disconnect -> "Disconnecting session…"
            Errors_Session_Not_Started -> "This session has not started"
            Errors_Session_Need_Password -> "This session requires password"
            Errors_Session_Password_Wrong -> "Incorrect password"
            Errors_Session_Remote_DB_Error -> "Error received from remote database"
            Errors_Session_Invalid_Param -> "Parameter error when joining the session"
            Errors_Session_Client_Incompatible -> "Client force upgrade"
            Errors_Session_Audio_Error -> "Session audio module error"
            Errors_Session_Audio_No_Microphone -> "Session audio no microphone"
            Errors_Session_Video_Error -> "Session video module error"
            Errors_Session_Video_Device_Error -> "Session video device module error"
            Errors_Session_Live_Stream_Error -> "Live stream error"
            RawDataError_MALLOC_FAILED -> "Raw data memory allocation error"
            RawDataError_NOT_IN_Session -> "Not in session when subscribing to raw data"
            RawDataError_NO_LICENSE -> "License without raw data"
            RawDataError_VIDEO_MODULE_NOT_READY -> "Video module is not ready"
            RawDataError_VIDEO_MODULE_ERROR -> "Video module error"
            RawDataError_VIDEO_DEVICE_ERROR -> "Video device error"
            RawDataError_NO_VIDEO_DATA -> "No video data"
            RawDataError_SHARE_MODULE_NOT_READY -> "Share module is not ready"
            RawDataError_SHARE_MODULE_ERROR -> "Share module error"
            RawDataError_NO_SHARE_DATA -> "No sharing data"
            RawDataError_AUDIO_MODULE_NOT_READY -> "Audio module is not ready"
            RawDataError_AUDIO_MODULE_ERROR -> "Audio module error"
            RawDataError_NO_AUDIO_DATA -> "No audio data"
            else -> errorCode.toString()
        }
    }
}