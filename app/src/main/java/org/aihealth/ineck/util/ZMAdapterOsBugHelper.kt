package org.aihealth.ineck.util

import android.app.AppOpsManager
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.text.TextUtils

class ZMAdapterOsBugHelper private constructor() {

    private var mCanDraw = false
    private var mOnOpChangedListener: AppOpsManager.OnOpChangedListener? = null

    companion object {
        // Singleton instance
        val instance: ZMAdapterOsBugHelper by lazy { ZMAdapterOsBugHelper() }
    }

    fun isNeedListenOverlayPermissionChanged(): Boolean {
        return (Build.VERSION.SDK_INT == Build.VERSION_CODES.O || Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1)
    }

    // Fix the bug that "Settings.canDrawOverlays(context)" returns false even if user allows the app to draw overlay in Android 8.0 and 8.1
    fun startListenOverlayPermissionChange(context: Context) {
        val opsManager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager?
        if (opsManager == null) return

        mCanDraw = Settings.canDrawOverlays(context)
        val myPackageName = context.packageName
        if (TextUtils.isEmpty(myPackageName)) return

        mOnOpChangedListener = object : AppOpsManager.OnOpChangedListener {
            override fun onOpChanged(op: String, packageName: String) {
                if (myPackageName == packageName && AppOpsManager.OPSTR_SYSTEM_ALERT_WINDOW == op) {
                    mCanDraw = !mCanDraw
                }
            }
        }

        mOnOpChangedListener?.let { opsManager.startWatchingMode(AppOpsManager.OPSTR_SYSTEM_ALERT_WINDOW, null, it) }
    }

    fun stopListenOverlayPermissionChange(context: Context) {
        mOnOpChangedListener?.let {
            val opsManager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager?
            opsManager?.stopWatchingMode(it)
            mOnOpChangedListener = null
        }
    }

    fun isCanDraw(): Boolean {
        return mCanDraw
    }
}