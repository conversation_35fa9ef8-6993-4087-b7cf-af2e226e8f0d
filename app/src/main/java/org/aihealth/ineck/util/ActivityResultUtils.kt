package org.aihealth.ineck.util

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.provider.DocumentsContract
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.app.ActivityCompat
import org.aihealth.ineck.MainActivity

object ActivityResultUtils {

    private fun getActivity():MainActivity{
        return MainActivity.getInstance()
    }

    fun startActivity(intent: Intent,callBack:(resultCode: Int,data: Intent?) ->Unit  = {
            _: Int, _: Intent? -> }){
        getActivity().startLauncher(intent){
            callBack(it.resultCode,it.data)
        }
    }

    fun openDocument(mimeTypes:Array<String> = arrayOf("*/*"), callBack:(Uri?)->Unit = {}){
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            .putExtra(Intent.EXTRA_MIME_TYPES,mimeTypes)
            .setType("*/*")
        startActivity(intent){
                _, data ->
            callBack(data?.data)
        }
    }

    fun openDocumentTree(uri: Uri?=null, callBack: (Uri?) -> Unit = {}){
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT_TREE)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O && uri != null) {
            intent.putExtra(DocumentsContract.EXTRA_INITIAL_URI, uri)
        }
        startActivity(intent){
                _, data ->
            callBack(data?.data)
        }
    }


    fun openMultipleDocuments(mimeTypes:Array<String> = arrayOf("*/*"), callBack:(List<Uri>)->Unit = {}){
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            .putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes)
            .putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            .setType("*/*")
        startActivity(intent){
                _, data ->
            callBack(data?.getClipDataUris() ?: emptyList())
        }
    }


    /**
     * 获取权限
     * onGrantedAll: 当权限全部被授予后执行
     * onProhibited: 当权限被禁止请求后执行
     */
    fun requestPermissions(
        permissions: Array<String>,
        onAllGranted: () -> Unit,
        onProhibited: (permission: List<String>) -> Unit = {}
    ){
        val intent = Intent(ActivityResultContracts.RequestMultiplePermissions.ACTION_REQUEST_PERMISSIONS).putExtra(
            ActivityResultContracts.RequestMultiplePermissions.EXTRA_PERMISSIONS, permissions)
        startActivity(intent){ _, _ ->
            val denyPermissions = mutableListOf<String>()
            permissions.forEach {
                if (ActivityCompat.checkSelfPermission(getActivity(),it) == PackageManager.PERMISSION_DENIED) {
                    denyPermissions.add(it)
                }
            }
            if (denyPermissions.isEmpty()) {
                onAllGranted()
            } else {
                onProhibited(denyPermissions)
            }
        }

    }
    fun checkPermissions(permissions: Array<String>):Boolean{
        permissions.forEach {
            if (ActivityCompat.checkSelfPermission(getActivity(),it) == PackageManager.PERMISSION_DENIED) {
                return false
            }
        }
        return true
    }

    private fun Intent.getClipDataUris(): List<Uri> {
        val resultSet = LinkedHashSet<Uri>()
        data?.let { data ->
            resultSet.add(data)
        }
        val clipData = clipData
        if (clipData == null && resultSet.isEmpty()) {
            return emptyList()
        } else if (clipData != null) {
            for (i in 0 until clipData.itemCount) {
                val uri = clipData.getItemAt(i).uri
                if (uri != null) {
                    resultSet.add(uri)
                }
            }
        }
        return ArrayList(resultSet)
    }
}
