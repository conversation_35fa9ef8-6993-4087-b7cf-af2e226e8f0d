package org.aihealth.ineck.util.call

import android.Manifest
import android.content.Context
import android.content.pm.PackageManager
import android.telephony.PhoneStateListener
import android.telephony.TelephonyManager
import androidx.core.app.ActivityCompat
import org.aihealth.ineck.util.LogUtil

/**
 * 监听电话状态的类，用于检测由应用发起的通话何时结束并记录通话时间
 */
class CallStateListener(private val context: Context, private val callback: CallEndCallback) : PhoneStateListener() {

    companion object {
        private const val TAG = "CallStateListener"
    }

    /**
     * 回调接口，当应用发起的通话结束时通知宿主
     */
    interface CallEndCallback {
        fun onCallEnded(duration: Long, isConnected: Boolean)
    }

    private var wasOffHook = false
    private var wasRinging = false

    /**
     * 标记通话是否由应用发起
     * 在应用发起通话前设置为true
     */
    var callInitiatedByApp = false

    // 记录拨号开始时间（用户点击拨号按钮时）
    private var dialStartTime: Long = 0

    // 记录通话接通时间（OFFHOOK状态时）
    private var callStartTime: Long = 0

    // 添加备份计时器，防止主计时器丢失
    private var backupCallStartTime: Long = 0

    // 电话管理器引用
    private var telephonyManager: TelephonyManager? = null

    /**
     * 设置拨号开始时间
     * 在用户点击拨号按钮后立即调用
     */
    fun setDialStartTime() {
        dialStartTime = System.currentTimeMillis()
        LogUtil.d(TAG, "Dial start time set: $dialStartTime")
    }

    @Deprecated("Deprecated in Java")
    override fun onCallStateChanged(state: Int, phoneNumber: String?) {
        LogUtil.i(TAG, "======== Call State Changed: $state, Initiated by App: $callInitiatedByApp, wasOffHook: $wasOffHook, dialStartTime: $dialStartTime ========")

        when (state) {
            TelephonyManager.CALL_STATE_OFFHOOK -> {
                // 电话处于通话中或拨号中状态
                LogUtil.i(TAG, "======== State: OFFHOOK ========")
                wasOffHook = true
                wasRinging = false

                // 如果是应用发起的通话，且没有记录开始时间，则记录
                if (callInitiatedByApp) {
                    val now = System.currentTimeMillis()
                    if (callStartTime == 0L) {
                        callStartTime = now
                        backupCallStartTime = now  // 同时设置备份时间
                        LogUtil.i(TAG, "======== Call connected at: $callStartTime ========")
                    } else {
                        LogUtil.i(TAG, "======== Call already had a start time: $callStartTime, not updating ========")
                    }
                } else {
                    // 尝试捕获可能是由应用发起但没有正确标记的通话
                    if (callStartTime == 0L) {
                        LogUtil.w(TAG, "======== Call not marked as initiated by app, but detected OFFHOOK. Setting call start time anyway. ========")
                        callStartTime = System.currentTimeMillis()
                        backupCallStartTime = callStartTime
                        // 尝试设置标志以修复可能的状态问题
                        callInitiatedByApp = true
                    }
                    LogUtil.i(TAG, "======== After adjustment: callInitiatedByApp=$callInitiatedByApp, callStartTime=$callStartTime ========")
                }

                // 如果没有设置拨号开始时间，则使用通话接通时间作为替代
                if (dialStartTime == 0L) {
                    dialStartTime = callStartTime
                    LogUtil.i(TAG, "======== No dial start time was set, using call start time instead: $dialStartTime ========")
                }
            }

            TelephonyManager.CALL_STATE_IDLE -> {
                // 电话处于空闲状态
                LogUtil.i(TAG, "======== State: IDLE, wasOffHook: $wasOffHook, wasRinging: $wasRinging, callInitiatedByApp: $callInitiatedByApp ========")

                // 检查是否是从通话中状态转为空闲状态，且这个通话是由我们的应用发起的
                if (wasOffHook) {
                    // 计算通话时长（秒）
                    val endTime = System.currentTimeMillis()
                    var duration = 0L
                    var totalDuration = 0L // 从拨号到挂断的总时长

                    // 使用拨号开始时间计算总时长（从拨号到挂断）
                    if (dialStartTime > 0) {
                        totalDuration = (endTime - dialStartTime) / 1000
                        LogUtil.i(TAG, "======== Total duration (dial to hang up): $totalDuration seconds ========")
                    }

                    // 使用主计时器计算实际通话时长（从接通到挂断）
                    if (callStartTime > 0) {
                        duration = (endTime - callStartTime) / 1000
                        LogUtil.i(TAG, "======== Call duration (connect to hang up): $duration seconds ========")

                        // 即使callInitiatedByApp为false，只要有记录通话时间，也视为可能的应用发起通话
                        if (!callInitiatedByApp) {
                            LogUtil.w(TAG, "======== Call not marked as initiated by app, but had start time. Treating as valid call. ========")
                            callInitiatedByApp = true;
                        }
                    }
                    // 如果主计时器无效但备份计时器有效，使用备份计时器
                    else if (backupCallStartTime > 0) {
                        duration = (endTime - backupCallStartTime) / 1000
                        LogUtil.i(TAG, "======== Call duration with backup timer: $duration seconds ========")
                        callInitiatedByApp = true; // 强制认为这是一个由应用发起的通话
                    }

                    // 确保通话时长至少为1秒
                    if (totalDuration <= 0) {
                        if (duration > 0) {
                            totalDuration = duration;
                            LogUtil.w(TAG, "======== Total duration was <= 0, using call duration instead: $totalDuration ========")
                        } else {
                            totalDuration = 1;
                            LogUtil.w(TAG, "======== Both durations were <= 0, setting to 1 second minimum ========")
                        }
                    }

                    // 只要有非零的通话时长，就调用回调
                    // 注意：现在我们放宽条件，即使不是应用发起的通话，也调用回调
                    if (totalDuration > 0) {
                        LogUtil.i(TAG, "======== Call has ended. Total Duration: $totalDuration seconds (including dialing time), Call Duration: $duration seconds, callInitiatedByApp: $callInitiatedByApp ========")
                        // 通话接通的判断：如果callStartTime大于0，说明通话已接通
                        val isConnected = callStartTime > 0 || duration > 0
                        callback.onCallEnded(totalDuration, isConnected) // 通知宿主，附带总时长和接通状态
                    }

                    // 处理完毕后重置所有标记
                    resetState()
                } else if (wasRinging) {
                    // 来电被拒绝或未接听
                    LogUtil.i(TAG, "======== Incoming call ended without being answered ========")
                    resetState()
                } else {
                    // 其他情况，仅重置wasOffHook
                    LogUtil.i(TAG, "======== Call ended but not tracking duration: wasOffHook=$wasOffHook, callInitiatedByApp=$callInitiatedByApp ========")
                    wasOffHook = false
                    wasRinging = false
                }
            }

            TelephonyManager.CALL_STATE_RINGING -> {
                // 电话正在响铃状态（来电）
                LogUtil.i(TAG, "======== State: RINGING ========")
                wasRinging = true

                // 来电不由应用发起，记录事实但不重置时间标记
                if (!callInitiatedByApp) {
                    LogUtil.i(TAG, "======== Incoming call detected ========")
                }
            }
        }
    }

    /**
     * 重置所有状态
     */
    private fun resetState() {
        wasOffHook = false
        wasRinging = false
        callInitiatedByApp = false
        dialStartTime = 0
        callStartTime = 0
        backupCallStartTime = 0
        LogUtil.i(TAG, "======== All call state variables reset ========")
    }

    /**
     * 检查所需权限
     */
    private fun checkPermissions(): Boolean {
        val hasReadPhoneStatePermission = ActivityCompat.checkSelfPermission(
            context, Manifest.permission.READ_PHONE_STATE
        ) == PackageManager.PERMISSION_GRANTED

        if (!hasReadPhoneStatePermission) {
            LogUtil.e(TAG, "Missing READ_PHONE_STATE permission! Call state listener will not work properly.")
            return false
        }

        return true
    }

    /**
     * 供宿主调用的注册方法
     */
    fun register() {
        try {
            // 先检查权限
            if (!checkPermissions()) {
                LogUtil.e(TAG, "======== Cannot register CallStateListener: missing permissions ========")
                return
            }

            // 注册监听
            telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager

            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED) {
                telephonyManager?.listen(this, PhoneStateListener.LISTEN_CALL_STATE)
                LogUtil.i(TAG, "======== CallStateListener registered successfully ========")
            } else {
                LogUtil.e(TAG, "======== Cannot register call state listener: READ_PHONE_STATE permission denied ========")
            }

            // 确保callInitiatedByApp只有在明确发起电话时才设置为true
            if (!wasOffHook) {
                callInitiatedByApp = false
            }

            LogUtil.i(TAG, "======== CallStateListener registered, callInitiatedByApp status: $callInitiatedByApp ========")

            // 额外日志，打印当前线程信息
            LogUtil.d(TAG, "======== Register called on thread: ${Thread.currentThread().name} ========")
        } catch (e: SecurityException) {
            LogUtil.e(TAG, "======== SecurityException registering listener - missing READ_PHONE_STATE permission ========", e)
        } catch (e: Exception) {
            LogUtil.e(TAG, "======== Error registering listener ========", e)
        }
    }

    /**
     * 供宿主调用的注销方法
     */
    fun unregister() {
        try {
            telephonyManager?.listen(this, PhoneStateListener.LISTEN_NONE)
            telephonyManager = null
            resetState()
            LogUtil.i(TAG, "======== CallStateListener unregistered and reset ========")
        } catch (e: Exception) {
            LogUtil.e(TAG, "======== Error unregistering listener ========", e)
        }
    }
}
