package org.aihealth.ineck.util

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * ViewModel扩展方法：启动协程
 * @param block 协程逻辑
 * @param onError 错误回调方法
 * @param onComplete 完成回调方法
 */
fun ViewModel.launch(
    block: suspend CoroutineScope.() -> Unit,
    onError: (e: Throwable) -> Unit = { _: Throwable -> },
    onComplete: () -> Unit = {}
) {
    viewModelScope.launch(
        CoroutineExceptionHandler { _, throwable ->
            run {
                // 这里统一处理错误
                LogUtil.d(throwable.message ?: "")
                onError(throwable)
            }
        }
    ) {
        try {
            block.invoke(this)
        } finally {
            onComplete()
        }
    }
}

fun <T> String.toAny(clazz: Class<T>):T {
    return Gson().fromJson(this,clazz)
}

fun Any.toJson(): String{
    return Gson().toJson(this)
}
fun IntRange.toStringList(): List<String> {
    val list = mutableListOf<String>()
    for (i in start..last) {
        list.add(i.toString())
    }
    return list
}

fun ByteArray.toHexString(): String {
    if (isEmpty()) {
        return ""
    }
    val sb = StringBuilder()
    for (byte in this) {
        sb.append(Integer.toHexString(byte.toInt() and 0XF))
    }
    return sb.toString()
}