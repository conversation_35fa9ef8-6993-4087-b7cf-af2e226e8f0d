package org.aihealth.ineck.util

import android.content.res.Configuration
import android.content.res.Resources
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.currentLocale

val localeResources: Resources
    get() {
        val configuration = Configuration(baseApplication.resources.configuration)
        configuration.setLocale(currentLocale)
        return baseApplication.createConfigurationContext(configuration).resources
    }