package org.aihealth.ineck.util

import org.aihealth.ineck.viewmodel.user


object UnitUtil {

    /**
     * 体重单位
     */
    fun getWeightUnit(): String {
        return if (user.preferences.weightUnit == "M") "kg" else "lb"
    }

    /**
     * 身高单位
     */
    fun getHeightUnit(): String {
        return if (user.preferences.heightUnit == "M") "cm" else "in"
    }

    /**
     * 身高单位换算
     */
    fun convertCmToInches(cm: Double): Double {
        val inches = cm * 0.393701
        return String.format("%.2f", inches).toDouble()
    }

    fun convertInchesToCm(inches: Double): Double {
        val cm = inches * 2.54
        return String.format("%.1f", cm).toDouble()
    }

    /**
     * 体重单位换算
     */
    fun convertKgToLbs(kg: Double): Double {
        val lbs = kg * 2.20462
        return String.format("%.2f", lbs).toDouble()
    }
    fun convertLbsToKg(lbs: Double): Double {
        val kg = lbs * 0.453592
        return String.format("%.2f", kg).toDouble()
    }

    /**
     *  将摄氏度转换为华氏度
     *  @param  celsius 摄氏度浮点数值
     *  @return 转换后的浮点数数值
     */
    fun celsiusToFahrenheit(celsius: Double): Double {
        val fahrenheit = celsius * 1.8 + 32
        return fahrenheit
    }

    /**
     *  将华氏度转换为摄氏度
     *  @param  fahrenheit  华氏度浮点数值
     *  @return 转换后的摄氏度数值
     */
    fun fahrenheitToCelsius(fahrenheit: Double): Double {
        val celsius = (fahrenheit - 32) / 1.8
        return celsius
    }

    /**
     *  血糖测量值单位转换，由毫克/分升（mg/dl）转换为毫摩尔/升(mmol/L)
     *  @param  mgdL    毫克/分升（mg/dl）
     *  @return 毫摩尔/升(mmol/L)
     */
    fun glucoseMgDlToMmolL(mgdL: Double): Double {
        return (mgdL / 18.0)
    }

    /**
     *  血糖值测量单位转换，由毫摩尔/升(mmol/L)转换为毫克/分升（mg/dl）
     *  @param  mmolL   毫摩尔/升(mmol/L)
     *  @return 毫克/分升（mg/dl）
     */
    fun glucoseMmolLToMgDl(mmolL: Double): Double {
        return (mmolL * 18.0)
    }

    /**
     * 卡路里换算
     * 1. You need to know your weight in kilograms, height in meters, how many steps you've taken, and your pace in m/s. You can stick to the general pace values and their MET cofactors:
     *    你需要知道你的体重（公斤）、身高（米）、你走了多少步，以及你的配速（米/秒）。您可以坚持一般的配速值及其 MET 辅助因子：
     *    Slow - 0.9 m/s (2.8 MET);慢速 - 0.9 m/s （2.8 MET）;
     *    Average - 1,34 m/s (3.5 MET);平均 - 1,34 m/s （3.5 MET）;
     *    Fast - 1,79 m/s (5 MET).快速 - 1,79 m/s （5 MET）。
     * 2. Use the following formulas to calculate the stride and the walked distance.
     *    使用以下公式计算步幅和步行距离。
     *    stride = height × 0.414
     *    distance = stride × steps
     * 3. Calculate the walking time:
     *    计算步行时间：
     *    time = distance/speed
     * 4. Finally, count the calories burned:
     *    最后，计算燃烧的卡路里：
     *    calories = time × MET × 3.5 × weight/(200 × 60)
     */
    fun calorieConvert(steps: Int): Double {
        if (steps <= 0) return 0.0
        
        // 获取用户身高体重，如果未设置则使用全球平均值
        val height = if (user.height > 0) user.height else DEFAULT_HEIGHT
        val weight = if (user.weight > 0) user.weight else DEFAULT_WEIGHT
        
        val stride = height / 100 * 0.414
        val distance = (stride * steps)
        val time = distance / 1.34
        
        LogUtil.d("calorieConvert: steps: $steps, height: $height, weight: $weight, stride: $stride, distance: $distance, time: $time")
        return time * 3.5 * weight / (200 * 60)
    }
    
    /**
     * 检查用户是否已填写身高体重
     */
    fun isUserDataComplete(): Boolean {
        return user.height > 0 && user.weight > 0
    }
    
    /**
     * 获取卡路里计算的数据来源描述
     */
    fun getCalorieDataSource(): String {
        return if (isUserDataComplete()) {
            "基于您的个人数据计算"
        } else {
            "基于平均身高体重估算，建议完善个人资料获得更准确结果"
        }
    }
    
    // 全球成年人平均身高体重（WHO数据参考）
    private const val DEFAULT_HEIGHT = 170.0  // 170cm - 全球成年人平均身高
    private const val DEFAULT_WEIGHT = 70.0   // 70kg - 全球成年人平均体重
}

