package org.aihealth.ineck.util

import android.content.Context
import android.content.pm.PackageManager

object VersionUtil {

    /**
     * 获取自己应用内部的版本号
     */
    fun getVersionCode(context: Context): Int {
        var versionCode = 0
        try {
            val packageInfo = context.packageManager.getPackageInfo(context.packageName, 0)
            versionCode =
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    packageInfo.longVersionCode.toInt() // getLongVersionCode() is available for API level 28 and higher
                } else {
                    packageInfo.versionCode // deprecated in API 28
                }
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return versionCode
    }

    /**
     * 获取自己应用内部的版本名
     */
    fun getVersionName(context: Context): String {
        val manager = context.packageManager
        var name = ""
        try {
            val info = manager.getPackageInfo(context.packageName, 0)
            name = info.versionName.toString()
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }

        return name
    }
}