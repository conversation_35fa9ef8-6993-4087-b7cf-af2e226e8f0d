package org.aihealth.ineck.util

import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.aihealth.ineck.navController


/**
 * 安全的导航函数，检查navController状态
 */
private fun safeNavigate(action: () -> Unit) {
    try {
        // 尝试访问navController来检查是否已经初始化
        val currentBackStackEntry = navController.currentBackStackEntry
        
        // 检查navController的当前状态
        if (currentBackStackEntry?.lifecycle?.currentState?.isAtLeast(androidx.lifecycle.Lifecycle.State.CREATED) != true) {
            LogUtil.e("RouteUtil: Navigation not in valid lifecycle state")
            return
        }
        
        action()
    } catch (e: UninitializedPropertyAccessException) {
        LogUtil.e("RouteUtil: navController not initialized")
    } catch (e: Exception) {
        LogUtil.e("RouteUtil: Navigation error: ${e.message}")
    }
}

/**
 * route:String  跳转新页面的路由
 * finish:Boolean 是否关闭当前页面
 */
fun startScreen(route: String, finish: Boolean = false) {
    // 使用Main.immediate调度器确保在主线程上执行
    CoroutineScope(Dispatchers.Main.immediate).launch {
        safeNavigate {
            if (finish) {
                navController.popBackStack()
            }
            LogUtil.i("RouteUtil: Navigating to $route")
            navController.navigate(route) {
                launchSingleTop = true
                // 添加额外的导航选项以避免状态问题
                restoreState = true
            }
        }
    }
}

fun popScreen(route: String) {
    CoroutineScope(Dispatchers.Main.immediate).launch {
        safeNavigate {
            LogUtil.i("RouteUtil: Popping back to $route")
            navController.popBackStack(route, false)
        }
    }
}

fun finish() {
    CoroutineScope(Dispatchers.Main.immediate).launch {
        safeNavigate {
            LogUtil.i("RouteUtil: Finishing current screen")
            navController.popBackStack()
        }
    }
}