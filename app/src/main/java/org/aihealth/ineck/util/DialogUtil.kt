package org.aihealth.ineck.util

import android.view.Gravity
import android.widget.Toast
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.baseApplication
import org.aihealth.ineck.view.dialog.LoadingDialog

object DialogUtil {
    private var loadingDialogVisible by mutableStateOf(false)
    private var tipDialogVisible by mutableStateOf(false)
    private var tipDialogContent:@Composable () -> Unit by mutableStateOf({ })

    @Composable
    fun DialogContent() {
        if (loadingDialogVisible) {
            LoadingDialog()
        }
        if (tipDialogVisible) {
            tipDialogContent()
        }
    }

    fun showLoading(timeoutMillis: Long = 60000) {
        loadingDialogVisible = true
        MainScope().launch {
            delay(timeoutMillis)
            loadingDialogVisible = false
        }
    }

    fun showToast(
        text: String?,
        gravity: Int = Gravity.BOTTOM
    ) = MainScope().launch {
        text?.let {
            Toast.makeText(baseApplication,it,Toast.LENGTH_SHORT).apply {
                setGravity(gravity,0,if (gravity == Gravity.BOTTOM) 30.dp.roundToPx() else 0)
            }.show()
        }

    }
    fun showLongToast(
                      text: String?,
                      gravity: Int = Gravity.BOTTOM
    ) = MainScope().launch {
        text?.let {
            Toast.makeText(baseApplication,it,Toast.LENGTH_LONG).apply {
                setGravity(gravity,0,if (gravity == Gravity.BOTTOM) 30.dp.roundToPx() else 0)
            }.show()
        }

    }

    fun showToast(
        content: @Composable () -> Unit
    ) {
        tipDialogContent = content
        tipDialogVisible = true
    }

    fun hideLoading() {
        loadingDialogVisible = false
    }

    fun hideToast() {
        tipDialogVisible = false
        tipDialogContent = {}
    }
}