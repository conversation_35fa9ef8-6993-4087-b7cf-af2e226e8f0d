package org.aihealth.ineck.util

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorEvent
import android.hardware.SensorEventListener
import android.hardware.SensorManager
import org.aihealth.ineck.database
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.UnitUtil.calorieConvert
import java.text.SimpleDateFormat
import java.util.*

/**
 * 混合步数追踪器
 * 前台时实时监听传感器，后台时通过WorkManager定期同步
 */
class HybridStepTracker(
    private val context: Context,
    private val onStepUpdate: (steps: Int, calories: Double) -> Unit
) : SensorEventListener {
    
    private val sensorManager = context.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val stepSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER) 
        ?: sensorManager.getDefaultSensor(Sensor.TYPE_STEP_DETECTOR)
    
    private val prefs = context.getSharedPreferences("hybrid_step_data", Context.MODE_PRIVATE)
    private var stepSensorType = if (stepSensor?.type == Sensor.TYPE_STEP_COUNTER) 0 else 1
    
    private var isTracking = false
    private var todayDate: String = ""
    private var baseStepCount = 0
    private var currentDaySteps = 0
    
    companion object {
        private const val KEY_LAST_DATE = "last_date"
        private const val KEY_BASE_COUNT = "base_count"
        private const val KEY_DAILY_STEPS = "daily_steps"
        private const val KEY_LAST_SENSOR_VALUE = "last_sensor_value"
        private const val KEY_BACKGROUND_TOTAL_STEPS = "background_total_steps"
        private const val KEY_BACKGROUND_START_TIME = "background_start_time"
    }
    
    /**
     * 开始前台实时追踪
     */
    fun startForegroundTracking() {
        if (stepSensor != null && !isTracking) {
            checkDateAndReset()
            loadTodayData()
            
            // 检查并补偿后台期间的步数
            compensateBackgroundSteps()
            
            sensorManager.registerListener(this, stepSensor, SensorManager.SENSOR_DELAY_UI)
            isTracking = true
            LogUtil.i("HybridStepTracker: 开始前台追踪，传感器类型: $stepSensorType，当前步数: $currentDaySteps")
        } else {
            LogUtil.w("HybridStepTracker: 无法开始追踪，传感器不可用或已在追踪中")
        }
    }
    
    /**
     * 停止前台追踪
     */
    fun stopForegroundTracking() {
        if (isTracking) {
            // 记录应用进入后台时的累积步数（如果支持TYPE_STEP_COUNTER）
            recordBackgroundStartInfo()
            
            sensorManager.unregisterListener(this)
            isTracking = false
            saveCurrentState()
            LogUtil.i("HybridStepTracker: 停止前台追踪，当前步数: $currentDaySteps")
        }
    }
    
    override fun onSensorChanged(event: SensorEvent?) {
        event?.let { 
            val sensorValue = it.values[0].toInt()
            handleSensorData(sensorValue)
        }
    }
    
    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {
        LogUtil.d("HybridStepTracker: 传感器精度变化: $accuracy")
    }
    
    /**
     * 处理传感器数据
     */
    private fun handleSensorData(sensorValue: Int) {
        when (stepSensorType) {
            0 -> handleStepCounter(sensorValue) // TYPE_STEP_COUNTER
            1 -> handleStepDetector(sensorValue) // TYPE_STEP_DETECTOR
        }
        
        val calories = calorieConvert(currentDaySteps)
        onStepUpdate(currentDaySteps, calories)
        saveCurrentState()
    }
    
    /**
     * 处理步数计数器传感器 (TYPE_STEP_COUNTER)
     */
    private fun handleStepCounter(totalSteps: Int) {
        if (baseStepCount == 0) {
            // 第一次获取，设置基准值
            baseStepCount = totalSteps
            currentDaySteps = prefs.getInt(KEY_DAILY_STEPS, 0)
            LogUtil.i("HybridStepTracker: 设置基准步数: $baseStepCount, 已有步数: $currentDaySteps")
        } else {
            // 计算增量步数
            val incrementSteps = if (totalSteps >= baseStepCount) {
                totalSteps - baseStepCount
            } else {
                // 处理传感器重置的情况
                totalSteps
            }
            
            val previousDaySteps = prefs.getInt(KEY_DAILY_STEPS, 0)
            currentDaySteps = maxOf(previousDaySteps, incrementSteps)
            
            LogUtil.d("HybridStepTracker: 传感器总步数: $totalSteps, 基准: $baseStepCount, 当日步数: $currentDaySteps")
        }
    }
    
    /**
     * 处理步数检测器传感器 (TYPE_STEP_DETECTOR)
     */
    private fun handleStepDetector(stepDetected: Int) {
        if (stepDetected == 1) {
            currentDaySteps++
            LogUtil.d("HybridStepTracker: 检测到一步，当前总步数: $currentDaySteps")
        }
    }
    
    /**
     * 检查日期变化并重置数据
     */
    private fun checkDateAndReset() {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val lastDate = prefs.getString(KEY_LAST_DATE, "")
        
        if (lastDate != today) {
            LogUtil.i("HybridStepTracker: 日期变更，重置数据 $lastDate -> $today")
            resetForNewDay(today)
        }
        todayDate = today
    }
    
    /**
     * 重置新一天的数据
     */
    private fun resetForNewDay(newDate: String) {
        prefs.edit()
            .putString(KEY_LAST_DATE, newDate)
            .putInt(KEY_DAILY_STEPS, 0)
            .putInt(KEY_BASE_COUNT, 0)
            .putInt(KEY_LAST_SENSOR_VALUE, 0)
            .apply()
            
        baseStepCount = 0
        currentDaySteps = 0
        LogUtil.i("HybridStepTracker: 新一天数据已重置")
    }
    
    /**
     * 加载今日已有数据 - 优先从数据库获取最新数据
     */
    private fun loadTodayData() {
        try {
            // 优先从数据库获取今日数据，确保数据一致性
            val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
            val dbStepEntity = database.stepDataDao().getCurDataByDate(today)
            val dbSteps = dbStepEntity?.steps ?: 0
            
            // 从SharedPreferences获取基准数据
            val prefsSteps = prefs.getInt(KEY_DAILY_STEPS, 0)
            baseStepCount = prefs.getInt(KEY_BASE_COUNT, 0)
            
            // 使用较大的值，确保数据不倒退
            currentDaySteps = maxOf(dbSteps, prefsSteps)
            
            // 如果数据库数据更新，同步到SharedPreferences
            if (dbSteps > prefsSteps) {
                prefs.edit()
                    .putInt(KEY_DAILY_STEPS, currentDaySteps)
                    .apply()
                LogUtil.i("HybridStepTracker: 数据库数据更新，同步到SharedPreferences: $dbSteps")
            }
            
            LogUtil.i("HybridStepTracker: 加载今日数据 - DB:$dbSteps, Prefs:$prefsSteps, 最终:$currentDaySteps, 基准:$baseStepCount")
        } catch (e: Exception) {
            LogUtil.e("HybridStepTracker: 加载数据失败，使用SharedPreferences: ${e.message}")
            currentDaySteps = prefs.getInt(KEY_DAILY_STEPS, 0)
            baseStepCount = prefs.getInt(KEY_BASE_COUNT, 0)
        }
    }
    
    /**
     * 保存当前状态
     */
    private fun saveCurrentState() {
        prefs.edit()
            .putString(KEY_LAST_DATE, todayDate)
            .putInt(KEY_BASE_COUNT, baseStepCount)
            .putInt(KEY_DAILY_STEPS, currentDaySteps)
            .apply()
    }
    
    /**
     * 获取当前步数
     */
    fun getCurrentSteps(): Int = currentDaySteps
    
    /**
     * 获取当前卡路里
     */
    fun getCurrentCalories(): Double = calorieConvert(currentDaySteps)
    
    /**
     * 检查设备是否支持步数传感器
     */
    fun isSensorAvailable(): Boolean = stepSensor != null
    
    /**
     * 记录应用进入后台时的信息
     */
    private fun recordBackgroundStartInfo() {
        try {
            if (stepSensorType == 0 && stepSensor != null) { // TYPE_STEP_COUNTER
                // 获取当前累积步数，这需要一个临时的监听器
                var currentTotalSteps = 0
                val tempListener = object : SensorEventListener {
                    override fun onSensorChanged(event: SensorEvent?) {
                        event?.let {
                            currentTotalSteps = it.values[0].toInt()
                            // 立即取消监听
                            sensorManager.unregisterListener(this)
                            // 保存到SharedPreferences
                            prefs.edit()
                                .putInt(KEY_BACKGROUND_TOTAL_STEPS, currentTotalSteps)
                                .putLong(KEY_BACKGROUND_START_TIME, System.currentTimeMillis())
                                .apply()
                            LogUtil.i("HybridStepTracker: 记录后台起始累积步数: $currentTotalSteps")
                        }
                    }
                    override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {}
                }
                
                sensorManager.registerListener(tempListener, stepSensor, SensorManager.SENSOR_DELAY_FASTEST)
                // 设置超时，1秒后强制取消监听
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    try {
                        sensorManager.unregisterListener(tempListener)
                        LogUtil.d("HybridStepTracker: 后台记录临时监听器超时取消")
                    } catch (e: Exception) {
                        LogUtil.e("HybridStepTracker: 取消临时监听器失败: ${e.message}")
                    }
                }, 1000)
            }
        } catch (e: Exception) {
            LogUtil.e("HybridStepTracker: 记录后台信息失败: ${e.message}")
        }
    }
    
    /**
     * 补偿后台期间的步数
     */
    private fun compensateBackgroundSteps() {
        try {
            if (stepSensorType != 0 || stepSensor == null) {
                LogUtil.d("HybridStepTracker: 不支持TYPE_STEP_COUNTER，跳过后台补偿")
                return
            }
            
            val backgroundTotalSteps = prefs.getInt(KEY_BACKGROUND_TOTAL_STEPS, -1)
            val backgroundStartTime = prefs.getLong(KEY_BACKGROUND_START_TIME, 0)
            
            if (backgroundTotalSteps == -1 || backgroundStartTime == 0L) {
                LogUtil.d("HybridStepTracker: 无后台数据，跳过补偿")
                return
            }
            
            // 获取当前累积步数
            getCurrentTotalStepsAsync { currentTotalSteps ->
                if (currentTotalSteps > backgroundTotalSteps) {
                    val backgroundSteps = currentTotalSteps - backgroundTotalSteps
                    val backgroundHours = (System.currentTimeMillis() - backgroundStartTime) / 1000 / 3600
                    
                    // 合理性检查：每小时不超过3000步（约2.5km/h的慢走速度）
                    val maxReasonableSteps = (backgroundHours * 3000).toInt()
                    val compensatedSteps = if (backgroundSteps > maxReasonableSteps) {
                        LogUtil.w("HybridStepTracker: 后台步数异常高($backgroundSteps)，限制为$maxReasonableSteps")
                        maxReasonableSteps
                    } else {
                        backgroundSteps
                    }
                    
                    if (compensatedSteps > 0) {
                        currentDaySteps += compensatedSteps
                        saveCurrentState()
                        
                        LogUtil.i("HybridStepTracker: 后台步数补偿完成 - 后台时长: ${backgroundHours}小时，补偿步数: $compensatedSteps")
                        onStepUpdate(currentDaySteps, calorieConvert(currentDaySteps))
                    }
                } else {
                    LogUtil.d("HybridStepTracker: 累积步数未增加，无需补偿")
                }
                
                // 清除后台记录
                prefs.edit()
                    .remove(KEY_BACKGROUND_TOTAL_STEPS)
                    .remove(KEY_BACKGROUND_START_TIME)
                    .apply()
            }
            
        } catch (e: Exception) {
            LogUtil.e("HybridStepTracker: 后台步数补偿失败: ${e.message}")
        }
    }
    
    /**
     * 异步获取当前累积步数
     */
    private fun getCurrentTotalStepsAsync(callback: (Int) -> Unit) {
        try {
            var hasResponded = false
            val tempListener = object : SensorEventListener {
                override fun onSensorChanged(event: SensorEvent?) {
                    if (!hasResponded) {
                        hasResponded = true
                        event?.let {
                            val totalSteps = it.values[0].toInt()
                            sensorManager.unregisterListener(this)
                            callback(totalSteps)
                        }
                    }
                }
                override fun onAccuracyChanged(sensor: Sensor?, accuracy: Int) {}
            }
            
            sensorManager.registerListener(tempListener, stepSensor, SensorManager.SENSOR_DELAY_FASTEST)
            
            // 超时保护
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                if (!hasResponded) {
                    hasResponded = true
                    sensorManager.unregisterListener(tempListener)
                    LogUtil.w("HybridStepTracker: 获取累积步数超时")
                    callback(0)
                }
            }, 2000)
            
        } catch (e: Exception) {
            LogUtil.e("HybridStepTracker: 获取累积步数失败: ${e.message}")
            callback(0)
        }
    }
    
    /**
     * 手动触发一次后台同步数据加载（用于应用启动时）
     */
    fun loadLatestData() {
        checkDateAndReset()
        loadTodayData()
        val calories = calorieConvert(currentDaySteps)
        onStepUpdate(currentDaySteps, calories)
        LogUtil.i("HybridStepTracker: 手动加载最新数据 - 步数: $currentDaySteps")
    }
}