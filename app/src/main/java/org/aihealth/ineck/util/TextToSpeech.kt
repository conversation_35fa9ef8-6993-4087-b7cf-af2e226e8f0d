package org.aihealth.ineck.util

import android.content.Context
import android.speech.tts.TextToSpeech
import java.util.Locale

object TextToSpeech {

    private var textToSpeech: TextToSpeech? = null
    private var isSpeaking = false
    // 语音初始化
    fun ttsCreate(context: Context) {
        textToSpeech = TextToSpeech(context) {
            if (it == TextToSpeech.SUCCESS) {
                textToSpeech?.let { tts ->
                    tts.language = Locale.getDefault()
                    isSpeaking = true
                }
            } else {
                LogUtil.d("TTS初始化失败")
                isSpeaking = false

            }
        }
    }

    fun isSpeakingEnable(): Boolean {
        return isSpeaking
    }

    // 中止朗读
    fun ttsStop() {
        textToSpeech?.stop()
    }

    // 销毁朗读
    fun ttsDestroy() {
        if (textToSpeech?.isSpeaking == false) {
            // 不管是否正在朗读TTS都被打断
            textToSpeech?.stop()
            textToSpeech?.shutdown()
        }
    }

    // 朗读内容
    fun ttsSpeaking(content: String) {
        if (textToSpeech?.isSpeaking == true) {
            ttsStop()
        }

        textToSpeech?.let { txtToSpeech ->
            txtToSpeech.setSpeechRate(1.0f)
            txtToSpeech.speak(
                content, TextToSpeech.QUEUE_ADD, null, null
            )
        }
    }

    fun ttsSpeakingNoStop(content: String) {
        textToSpeech?.let { txtToSpeech ->
            LogUtil.i("voice: ${txtToSpeech.voices}")
            txtToSpeech.setSpeechRate(1.0f)
            txtToSpeech.speak(
                content, TextToSpeech.QUEUE_ADD, null, null
            )
        }
    }

}