package org.aihealth.ineck.work

import android.content.Context
import android.hardware.Sensor
import android.hardware.SensorManager
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import org.aihealth.ineck.database
import org.aihealth.ineck.model.StepEntity
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.UnitUtil.calorieConvert
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

/**
 * 步数后台同步Worker
 * 定期将SharedPreferences中的步数数据同步到Room数据库
 * 处理跨日切换逻辑
 */
class StepSyncWorker(appContext: Context, workerParams: WorkerParameters) :
    CoroutineWorker(appContext, workerParams) {

    private val prefs = applicationContext.getSharedPreferences("hybrid_step_data", Context.MODE_PRIVATE)
    private val stepDataDao = database.stepDataDao()
    private val sensorManager = applicationContext.getSystemService(Context.SENSOR_SERVICE) as SensorManager
    private val stepCounterSensor = sensorManager.getDefaultSensor(Sensor.TYPE_STEP_COUNTER)
    
    companion object {
        private const val KEY_BACKGROUND_LAST_TOTAL_STEPS = "background_last_total_steps"
        private const val KEY_BACKGROUND_LAST_SYNC_TIME = "background_last_sync_time"
        private const val KEY_BACKGROUND_ACCUMULATED_STEPS = "background_accumulated_steps"
    }
    
    override suspend fun doWork(): Result {
        return try {
            LogUtil.i("StepSyncWorker: 开始后台同步步数")
            
            // 尝试从系统传感器获取后台步数
            val backgroundSteps = getBackgroundStepsFromSensor()
            
            // 同步SharedPreferences中的步数到数据库
            syncStepDataToDatabase()
            
            // 如果获取到后台步数，进行补偿
            if (backgroundSteps > 0) {
                compensateBackgroundSteps(backgroundSteps)
            }
            
            LogUtil.i("StepSyncWorker: 后台同步完成，后台步数: $backgroundSteps")
            Result.success()
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 同步失败 ${e.message}")
            // 如果是临时错误，重试；如果是持续错误，直接失败
            if (runAttemptCount < 3) {
                Result.retry()
            } else {
                Result.failure()
            }
        }
    }
    
    /**
     * 同步步数数据到数据库
     */
    private suspend fun syncStepDataToDatabase() {
        val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
        val lastDate = prefs.getString("last_date", today) ?: today
        
        LogUtil.d("StepSyncWorker: 检查日期 - 上次: $lastDate, 今日: $today")
        
        // 检查是否需要重置（跨日处理）
        if (lastDate != today) {
            handleDateChange(lastDate, today)
        }
        
        // 同步当前步数到数据库
        val currentSteps = prefs.getInt("daily_steps", 0)
        if (currentSteps >= 0) { // 即使是0步也要记录，表示用户使用了应用
            saveStepDataToDb(today, currentSteps)
            LogUtil.i("StepSyncWorker: 同步今日数据 - 步数: $currentSteps")
        }
        
        // 清理过期数据（保留最近7天）
        cleanupOldData()
    }
    
    /**
     * 处理日期变更
     */
    private suspend fun handleDateChange(oldDate: String, newDate: String) {
        LogUtil.i("StepSyncWorker: 处理日期变更 $oldDate -> $newDate")
        
        try {
            // 保存昨天的最终数据
            val oldSteps = prefs.getInt("daily_steps", 0)
            if (oldSteps > 0) {
                saveStepDataToDb(oldDate, oldSteps)
                LogUtil.i("StepSyncWorker: 保存昨日最终数据 - 日期: $oldDate, 步数: $oldSteps")
            }
            
            // 重置今天的数据
            prefs.edit()
                .putString("last_date", newDate)
                .putInt("daily_steps", 0)
                .putInt("base_count", 0)
                .putInt("last_sensor_value", 0)
                .apply()
                
            LogUtil.i("StepSyncWorker: 新一天数据已重置")
            
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 处理日期变更失败: ${e.message}")
            throw e
        }
    }
    
    /**
     * 保存步数数据到数据库
     */
    private suspend fun saveStepDataToDb(date: String, steps: Int) {
        try {
            val existingEntity = stepDataDao.getCurDataByDate(date)
            val calories = calorieConvert(steps)
            
            if (existingEntity == null) {
                // 插入新记录
                val newEntity = StepEntity(
                    curDate = date,
                    steps = steps,
                    calories = calories
                )
                stepDataDao.addNewStepData(newEntity)
                LogUtil.i("StepSyncWorker: 插入新记录 - 日期: $date, 步数: $steps, 卡路里: $calories")
                
            } else if (existingEntity.steps < steps) {
                // 更新现有记录（只有当新步数更大时才更新）
                val updatedEntity = StepEntity(
                    id = existingEntity.id,
                    curDate = existingEntity.curDate,
                    steps = steps,
                    calories = calories
                )
                stepDataDao.updateCurData(updatedEntity)
                LogUtil.i("StepSyncWorker: 更新记录 - 日期: $date, 步数: ${existingEntity.steps} -> $steps")
                
            } else {
                LogUtil.d("StepSyncWorker: 无需更新 - 日期: $date, 数据库步数: ${existingEntity.steps}, 当前步数: $steps")
            }
            
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 保存数据库失败: ${e.message}")
            throw e
        }
    }
    
    /**
     * 清理7天前的旧数据
     */
    private suspend fun cleanupOldData() {
        try {
            val calendar = Calendar.getInstance()
            calendar.add(Calendar.DAY_OF_YEAR, -7)
            val sevenDaysAgo = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(calendar.time)
            
            val allData = stepDataDao.getAllDatas()
            var deletedCount = 0
            
            allData.forEach { entity ->
                if (entity.curDate != null && entity.curDate < sevenDaysAgo) {
                    stepDataDao.deleteCurData(entity)
                    deletedCount++
                    LogUtil.d("StepSyncWorker: 删除过期数据 - 日期: ${entity.curDate}")
                }
            }
            
            if (deletedCount > 0) {
                LogUtil.i("StepSyncWorker: 清理完成，删除了 $deletedCount 条过期记录")
            }
            
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 清理旧数据失败: ${e.message}")
            // 清理失败不应该影响主要功能，所以不抛出异常
        }
    }
    
    /**
     * 从系统传感器获取后台步数（应用关闭期间的估算步数）
     */
    private fun getBackgroundStepsFromSensor(): Int {
        return try {
            if (stepCounterSensor == null) {
                LogUtil.w("StepSyncWorker: TYPE_STEP_COUNTER传感器不可用")
                return 0
            }
            
            val currentTime = System.currentTimeMillis()
            val lastSyncTime = prefs.getLong(KEY_BACKGROUND_LAST_SYNC_TIME, currentTime)
            val timeDiff = (currentTime - lastSyncTime) / 1000 / 60 // 分钟
            
            // 如果距离上次同步不到5分钟，跳过（避免频繁读取）
            if (timeDiff < 5) {
                LogUtil.d("StepSyncWorker: 距离上次同步不足5分钟，跳过后台步数获取")
                return 0
            }
            
            // 注意：这里只能获取累积值，无法直接读取传感器的实时数据
            // 因为WorkManager在后台运行时无法注册SensorEventListener
            // 所以我们用另一种方法：记录应用关闭时的累积值，重新打开时计算差值
            
            LogUtil.d("StepSyncWorker: 后台步数获取需要应用前台配合完成")
            0
            
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 获取后台步数失败: ${e.message}")
            0
        }
    }
    
    /**
     * 补偿后台步数到当日记录
     */
    private suspend fun compensateBackgroundSteps(backgroundSteps: Int) {
        try {
            val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
            val currentDailySteps = prefs.getInt("daily_steps", 0)
            val compensatedSteps = currentDailySteps + backgroundSteps
            
            // 更新SharedPreferences
            prefs.edit()
                .putInt("daily_steps", compensatedSteps)
                .putInt(KEY_BACKGROUND_ACCUMULATED_STEPS, backgroundSteps)
                .putLong(KEY_BACKGROUND_LAST_SYNC_TIME, System.currentTimeMillis())
                .apply()
            
            // 更新数据库
            saveStepDataToDb(today, compensatedSteps)
            
            LogUtil.i("StepSyncWorker: 后台步数补偿完成 - 原步数: $currentDailySteps, 补偿: $backgroundSteps, 总计: $compensatedSteps")
            
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 后台步数补偿失败: ${e.message}")
        }
    }
    
    /**
     * 记录应用进入后台时的累积步数（由HybridStepTracker调用）
     */
    fun recordAppBackgroundTime(totalStepsAtBackground: Int) {
        prefs.edit()
            .putInt(KEY_BACKGROUND_LAST_TOTAL_STEPS, totalStepsAtBackground)
            .putLong(KEY_BACKGROUND_LAST_SYNC_TIME, System.currentTimeMillis())
            .apply()
        LogUtil.i("StepSyncWorker: 记录应用后台时刻 - 累积步数: $totalStepsAtBackground")
    }
    
    /**
     * 获取今日步数（用于外部查询）
     */
    suspend fun getTodayStepsFromDb(): StepEntity? {
        return try {
            val today = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
            stepDataDao.getCurDataByDate(today)
        } catch (e: Exception) {
            LogUtil.e("StepSyncWorker: 获取今日数据失败: ${e.message}")
            null
        }
    }
}