package org.aihealth.ineck.work

import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters

class GoogleFitWorker(appContext: Context, workerParams: WorkerParameters) :
    CoroutineWorker(appContext, workerParams) {

    @RequiresApi(Build.VERSION_CODES.O)
    override suspend fun doWork(): Result {
        // 这里调用你的 getDataFromGoogleFit 方法
        // 你可能需要调整方法，使其适合在 Worker 中运行，例如通过 WorkerParameters 传递参数等
//        activity.getDataFromGoogleFit()

        return Result.success()
    }
}
