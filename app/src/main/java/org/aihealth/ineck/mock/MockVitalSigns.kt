package org.aihealth.ineck.mock

import org.aihealth.ineck.model.vitalsigns.BloodGlucose
import org.aihealth.ineck.model.vitalsigns.BloodOxygen
import org.aihealth.ineck.model.vitalsigns.BloodPressure
import org.aihealth.ineck.model.vitalsigns.BodyTemperature
import org.aihealth.ineck.model.vitalsigns.HeartRate
import org.aihealth.ineck.model.vitalsigns.VitalSignGroup

/**
 *  生命体征数据 测试数据 - Mock
 */
val MockVitalSigns = VitalSignGroup(
    bloodGlucose = BloodGlucose(level = 6f),
    bloodOxygen = BloodOxygen(saturation = 98f),
    bloodPressure = BloodPressure(diastolic = 133, systolic = 85),
    bodyTemperature = BodyTemperature(temperature = 36f),
    heartRate = HeartRate(value = 78)
)