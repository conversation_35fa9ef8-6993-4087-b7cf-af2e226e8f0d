package org.aihealth.ineck.notification

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.ContentValues
import android.content.Context
import android.content.Intent
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.Environment
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.provider.MediaStore
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeout
import okhttp3.MultipartBody
import org.aihealth.ineck.MainActivity
import org.aihealth.ineck.network.ProgressRequestBody
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import java.io.File
import java.io.FileInputStream
import java.io.IOException

class ScreenRecordService : Service() {
    private var currentVideoPath: String? = null  // 添加变量保存当前视频路径

    companion object {
        const val CHANNEL_ID = "ScreenRecordServiceChannel"
        const val NOTIFICATION_ID = 10
        const val EXTRA_RESULT_CODE = "resultCode"
        const val EXTRA_DATA = "data"
        const val ACTION_RECORDING_START = "org.aihealth.ineck.ACTION_RECORDING_START"
        const val ACTION_RECORDING_STOP = "org.aihealth.ineck.ACTION_RECORDING_STOP"
        const val ACTION_MEETING_END = "org.aihealth.ineck.ACTION_MEETING_END"
        const val ACTION_UPLOAD_PROGRESS = "org.aihealth.ineck.ACTION_UPLOAD_PROGRESS"
        const val PERMISSION_RECEIVE_UPLOAD_PROGRESS = "org.aihealth.ineck.permission.RECEIVE_UPLOAD_PROGRESS"

        // 保存所有录制视频的路径
        private val recordedVideoPaths = mutableListOf<String>()

        fun addVideoPath(path: String) {
            recordedVideoPaths.add(path)
        }

        fun getVideoPaths(): List<String> = recordedVideoPaths.toList()

        fun clearVideoPaths() {
            recordedVideoPaths.clear()
        }
    }

    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private lateinit var mediaRecorder: MediaRecorder
    private lateinit var projectionManager: MediaProjectionManager
    private var callback: MediaProjection.Callback? = null

    override fun onCreate() {
        super.onCreate()
        projectionManager =
            getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        createNotificationChannel()
        startForegroundServiceWithNotification()
        setupMediaRecorder()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
//            ACTION_MEETING_END -> {
//                // 会议结束时上传所有视频
//                LogUtil.i("upload all videos")
//                uploadAllVideos()
//                return START_NOT_STICKY
//            }

            else -> {
                val resultCode =
                    intent?.getIntExtra(EXTRA_RESULT_CODE, -1) ?: return START_NOT_STICKY
                val data = intent.getParcelableExtra<Intent>(EXTRA_DATA) ?: return START_NOT_STICKY
                try {
                    startRecording(resultCode, data)
                } catch (e: Exception) {
                    e.printStackTrace()
                    DialogUtil.showToast("Please use system tools to record the screen")
                }
                return START_STICKY
            }
        }
    }

    private fun startForegroundServiceWithNotification() {
        val notificationIntent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        )

        val notification: Notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Screen Record")
            .setContentText("Recording...")
//            .setSmallIcon(R.drawable.ic_record) // 替换为您的图标
            .setContentIntent(pendingIntent)
            .build()

        startForeground(NOTIFICATION_ID, notification)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val serviceChannel = NotificationChannel(
                CHANNEL_ID,
                "Screen recording service channel",
                NotificationManager.IMPORTANCE_DEFAULT
            )
            val manager = getSystemService(NotificationManager::class.java)
            manager?.createNotificationChannel(serviceChannel)
        }
    }

    private fun setupMediaRecorder() {
        mediaRecorder = MediaRecorder().apply {
            // 设置音频源
            setAudioSource(MediaRecorder.AudioSource.MIC)
            // 设置视频源
            setVideoSource(MediaRecorder.VideoSource.SURFACE)
            // 设置输出格式
            setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)
            // 设置输出文件路径
            val videoPath = getVideoFilePath()
            LogUtil.i("videoPath: $videoPath")
            setOutputFile(videoPath)
            // 设置视频编码器
            setVideoEncoder(MediaRecorder.VideoEncoder.H264)
            // 设置音频编码器
            setAudioEncoder(MediaRecorder.AudioEncoder.AAC)
            // 设置视频帧率和比特率
            setVideoFrameRate(30)
            setVideoEncodingBitRate(1 * 1024 * 1024) // 5Mbps

            // 设置视频尺寸
            val metrics = resources.displayMetrics
            val width = metrics.widthPixels
            val height = metrics.heightPixels
            setVideoSize(width, height)

            try {
                prepare()
            } catch (e: IOException) {
                e.printStackTrace()
                sendBroadcast(Intent(ACTION_RECORDING_STOP))
                stopSelf()
            }
        }
    }


    private fun getVideoFilePath(): String {
        val directory = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            // 使用 MediaStore 保存视频文件
            cacheDir.absolutePath
        } else {
            // 使用外部存储目录
            externalCacheDir?.absolutePath ?: cacheDir.absolutePath
        }
        currentVideoPath = "$directory/screen_record_${System.currentTimeMillis()}.mp4"
        return currentVideoPath.toString()
    }

    private fun startRecording(resultCode: Int, data: Intent) {
        mediaProjection = projectionManager.getMediaProjection(resultCode, data)
        // 添加 MediaProjection 回调
        callback = object : MediaProjection.Callback() {
            override fun onStop() {
                LogUtil.i("MediaProjection stopped")
                release()
            }
        }

        // 注册回调
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            mediaProjection?.registerCallback(callback!!, Handler(Looper.getMainLooper()))
        }
        mediaProjection?.let { projection ->
            virtualDisplay = projection.createVirtualDisplay(
                "ScreenRecord",
                resources.displayMetrics.widthPixels,
                resources.displayMetrics.heightPixels,
                resources.displayMetrics.densityDpi,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                mediaRecorder.surface,
                null,
                null
            )
            mediaRecorder.start()
            sendLocalBroadcast(ACTION_RECORDING_START)
        } ?: run {
            sendLocalBroadcast(ACTION_RECORDING_STOP)
            stopSelf()
        }
    }

    private fun stopRecording() {
        try {
            mediaRecorder.stop()
            mediaRecorder.reset()
            // 保存到相册
            saveVideoToGallery()
        } catch (e: RuntimeException) {
            e.printStackTrace()
        }

        virtualDisplay?.release()
        mediaProjection?.stop()
        sendLocalBroadcast(ACTION_RECORDING_STOP)
        stopForeground(true)
        stopSelf()
    }
    override fun onDestroy() {
        super.onDestroy()
        stopRecording()
    }
    private fun release() {
        try {
            // 取消注册回调
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && callback != null) {
                mediaProjection?.unregisterCallback(callback!!)
            }
            callback = null

            virtualDisplay?.release()
            virtualDisplay = null

            mediaRecorder.apply {
                reset()
                release()
            }

            mediaProjection?.stop()
            mediaProjection = null
        } catch (e: Exception) {
            LogUtil.e("Error releasing resources: ${e.message}")
        }
    }

    private fun sendLocalBroadcast(action: String) {
        Intent(action).also { intent ->
            // 设置包名，确保广播只在应用内传递
            intent.setPackage(packageName)
            sendBroadcast(intent)
        }
    }

    private fun saveVideoToGallery() {
        currentVideoPath?.let { path ->
            val videoFile = File(path)
            if (!videoFile.exists()) return

            try {
                val values = ContentValues().apply {
                    put(
                        MediaStore.Video.Media.DISPLAY_NAME,
                        "Screen_Record_${System.currentTimeMillis()}.mp4"
                    )
                    put(MediaStore.Video.Media.MIME_TYPE, "video/mp4")
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        put(MediaStore.Video.Media.RELATIVE_PATH, Environment.DIRECTORY_MOVIES)
                        put(MediaStore.Video.Media.IS_PENDING, 1)
                    }
                }

                val resolver = applicationContext.contentResolver
                val collection = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    MediaStore.Video.Media.getContentUri(MediaStore.VOLUME_EXTERNAL_PRIMARY)
                } else {
                    MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                }

                // 插入视频信息
                val uri = resolver.insert(collection, values)

                uri?.let {
                    resolver.openOutputStream(it)?.use { os ->
                        FileInputStream(videoFile).use { is_ ->
                            is_.copyTo(os)
                        }
                    }

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                        values.clear()
                        values.put(MediaStore.Video.Media.IS_PENDING, 0)
                        resolver.update(it, values, null, null)
                    }

                    // 显示保存成功提示
                    Handler(Looper.getMainLooper()).post {
                        DialogUtil.showToast("Video saved to gallery")
                    }

                    // 保存视频路径到列表中
                    addVideoPath(videoFile.absolutePath)
                }
            } catch (e: Exception) {
                e.printStackTrace()
                Handler(Looper.getMainLooper()).post {
                    DialogUtil.showToast("Failed to save video")
                }
            }
        }
    }

    private fun sendProgressBroadcast(progress: Float, currentVideo: Int, totalVideos: Int, isComplete: Boolean = false) {
        Intent(ACTION_UPLOAD_PROGRESS).apply {
            putExtra("progress", progress)
            putExtra("currentVideo", currentVideo)
            putExtra("totalVideos", totalVideos)
            putExtra("isComplete", isComplete)
            `package` = packageName // Restrict to app's package
            flags = Intent.FLAG_INCLUDE_STOPPED_PACKAGES
            addFlags(Intent.FLAG_RECEIVER_REGISTERED_ONLY)
        }.also { intent ->
            sendBroadcast(intent, PERMISSION_RECEIVE_UPLOAD_PROGRESS)
        }
    }

    private fun uploadAllVideos() {
        CoroutineScope(Dispatchers.IO).launch {
            val totalVideos = getVideoPaths().size
            var uploadedVideos = 0

            getVideoPaths().forEach { path ->
                try {
                    val videoFile = File(path)
                    if (!videoFile.exists()) {
                        LogUtil.e("Video file not found: $path")
                        return@forEach
                    }

                    val requestBody = ProgressRequestBody(videoFile, "video/mp4") { progress ->
                        Handler(Looper.getMainLooper()).post {
                            // Update notification
                            val notification = NotificationCompat.Builder(this@ScreenRecordService, CHANNEL_ID)
                                .setContentTitle("The video is being uploaded to the cloud")
                                .setContentText("${(progress * 100).toInt()}%")
                                .setProgress(100, (progress * 100).toInt(), false)
                                .setSmallIcon(android.R.drawable.stat_sys_upload)
                                .setOngoing(true)
                                .build()

                            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                            notificationManager.notify(NOTIFICATION_ID, notification)

                            // Send broadcast with progress
                            sendProgressBroadcast(progress, uploadedVideos + 1, totalVideos)
                        }
                    }

                    // Create multipart form data
                    val multipartBody = MultipartBody.Part.createFormData(
                        "file",
                        videoFile.name,
                        requestBody
                    )

                    val hasSuccessful = withContext(Dispatchers.IO) {
                        try {
                            // 增加上传超时时间到15分钟
                            val response = withTimeout(15 * 60 * 1000) {
                                apiService.uploadVideo(multipartBody)
                            }

                            if (response.isSuccessful) {
                                withContext(Dispatchers.Main) {
                                    DialogUtil.showToast("Video uploaded")
                                }
                                uploadedVideos++
                                videoFile.delete()
                                true
                            } else {
                                LogUtil.e("Upload failed with code: ${response.code()}")
                                videoFile.delete()
                                false
                            }
                        } catch (e: Exception) {
                            LogUtil.e("Upload failed with exception: ${e.message}")
                            false
                        }
                    }

                    if (hasSuccessful) {
                        // Send progress update for completion
                        sendProgressBroadcast(1f, uploadedVideos, totalVideos, true)
                    }
                } catch (e: Exception) {
                    LogUtil.e("Error during upload: ${e.message}")
                }
            }
            // Clear video paths after upload attempts
            clearVideoPaths()
        }
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
}