package org.aihealth.ineck.notification

import android.animation.ArgbEvaluator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.graphics.Color
import android.graphics.PixelFormat
import android.hardware.camera2.CameraCaptureSession
import android.hardware.camera2.CameraDevice
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.WindowManager
import android.view.animation.DecelerateInterpolator
import android.view.animation.LinearInterpolator
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.OptIn
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.cardview.widget.CardView
import androidx.compose.ui.unit.dp
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import androidx.lifecycle.MutableLiveData
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.face.FaceDetection
import com.google.mlkit.vision.face.FaceDetectorOptions
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.aihealth.ineck.R
import org.aihealth.ineck.activity
import org.aihealth.ineck.database
import org.aihealth.ineck.mediapipe.PoseLandmarkerHelper
import org.aihealth.ineck.model.DeviceType
import org.aihealth.ineck.model.angles.Angle
import org.aihealth.ineck.network.NetWork
import org.aihealth.ineck.util.LogUtil
import org.aihealth.ineck.util.toPx
import org.aihealth.ineck.viewmodel.user
import java.util.concurrent.Executors
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.acos
import kotlin.math.sqrt

class FloatingCameraService : Service(), LifecycleOwner {

    private val lifecycleRegistry = LifecycleRegistry(this)

    private lateinit var windowManager: WindowManager
    private var floatingView: View? = null
    private var layoutParams: WindowManager.LayoutParams? = null

    private var initialX = 0
    private var initialY = 0
    private var initialTouchX = 0f
    private var initialTouchY = 0f

    private var cameraDevice: CameraDevice? = null
    private var cameraCaptureSession: CameraCaptureSession? = null
    private val cameraExecutor = Executors.newSingleThreadExecutor()
    private val mainHandler = Handler(Looper.getMainLooper())
    private lateinit var cameraProvider: ProcessCameraProvider
    private lateinit var previewView: PreviewView
    private lateinit var analysisText: TextView
    private var maskView: View? = null
    private var togglePreviewButton: Button? = null
    private var maskBackgroundImageView: ImageView? = null
    private var closeView: View? = null
    private var headScopeImageView: ImageView? = null
    private var bodyIndicatorImageView: ImageView? = null

    private var isMaskActive = false
    private var lastClickTime: Long = 0
    private var clickCount = 0
    private var faceDetected = false
    private var bodyDetected = false
    private var headScopeAnimator: ValueAnimator? = null
    private var bodyIndicatorAnimator: ValueAnimator? = null

    // 姿态检测帮助类
    private var poseLandmarkerHelper: PoseLandmarkerHelper? = null

    // 人脸检测器
    private val faceDetector = FaceDetection.getClient(
        FaceDetectorOptions.Builder()
            .setPerformanceMode(FaceDetectorOptions.PERFORMANCE_MODE_FAST)
            .setLandmarkMode(FaceDetectorOptions.LANDMARK_MODE_ALL)
            .setClassificationMode(FaceDetectorOptions.CLASSIFICATION_MODE_ALL)
            .build()
    )

    private val serviceScope = CoroutineScope(Dispatchers.IO + Job())
    private var currentDeviceType = DeviceType.aiNeckCV

    companion object {
        val headEulerAngleX = MutableLiveData<Float>()
        val headEulerAngleY = MutableLiveData<Float>()
        val headEulerAngleZ = MutableLiveData<Float>()
        val bodyTiltAngle = MutableLiveData<Float>()
        val isServiceRunning = MutableLiveData<Boolean>()
        val isUserClosing = MutableLiveData<Boolean>()
    }

    private var detectResultValueForFitted: Float = 0f

    private var currentAngleAnimator: ValueAnimator? = null
    private var currentDisplayAngle: Float = 0f

    // --- Pinch-to-Zoom states ---
    private val NONE = 0
    private val DRAG = 1
    private val ZOOM = 2
    private var mode = NONE

    private var initialPointerDist = 0f
    private var currentScale = 1.0f
    private var startScaleForPinch = 1.0f // Stores currentScale when a pinch gesture begins
    private val MIN_SCALE = 0.5f
    private val MAX_SCALE = 2.5f // Adjusted max scale

    private var originalLayoutWidth: Int = 0
    private var originalLayoutHeight: Int = 0
    // --- End of Pinch-to-Zoom states ---

    // 存储角度观察者，以便在onDestroy中移除
    private var headAngleObserver: androidx.lifecycle.Observer<Float>? = null
    private var bodyAngleObserver: androidx.lifecycle.Observer<Float>? = null

    override fun onCreate() {
        super.onCreate()
        isServiceRunning.postValue(true)
        lifecycleRegistry.currentState = Lifecycle.State.CREATED
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        addFloatingCameraView()
        lifecycleRegistry.currentState = Lifecycle.State.STARTED
        lifecycleRegistry.currentState = Lifecycle.State.RESUMED
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        detectResultValueForFitted = intent?.getFloatExtra("detectResultValueForFitted", 0f) ?: 0f
        currentDeviceType = intent?.getSerializableExtra("deviceType") as? DeviceType ?: DeviceType.aiNeckCV
        
        // 获取初始位置
        intent?.let {
            val initialX = it.getIntExtra("initialX", 0)
            val initialY = it.getIntExtra("initialY", 0)
            layoutParams?.let { params ->
                params.x = initialX
                params.y = initialY
                windowManager.updateViewLayout(floatingView, params)
            }
        }
        
        // 根据设备类型设置不同的遮罩背景和指示图标
        setupUIForDeviceType()
        
        // 根据设备类型设置不同的视觉模型
        setupCameraForDeviceType()
        
        // 创建通知
        startForegroundServiceWithNotification()
        
        // 开始定期上传角度数据
        startPeriodicAngleUpload()
        
        return START_STICKY
    }
    
    private fun setupUIForDeviceType() {
        // 确保遮罩状态正确初始化
        isMaskActive = false
        
        when (currentDeviceType) {
            DeviceType.aiNeckCV -> {
                maskBackgroundImageView?.setImageResource(R.drawable.neck_cv_sample)
                maskBackgroundImageView?.scaleType = ImageView.ScaleType.FIT_XY
                headScopeImageView?.visibility = View.VISIBLE
                bodyIndicatorImageView?.visibility = View.GONE
            }
            DeviceType.aiBackCV -> {
                maskBackgroundImageView?.setImageResource(R.drawable.back_cv_sample)
                maskBackgroundImageView?.scaleType = ImageView.ScaleType.FIT_XY
                headScopeImageView?.visibility = View.GONE
                bodyIndicatorImageView?.visibility = View.VISIBLE
            }
            else -> {
                // 默认使用 aiNeckCV 的设置
                maskBackgroundImageView?.setImageResource(R.drawable.float_window_background_for_aineck)
                maskBackgroundImageView?.scaleType = ImageView.ScaleType.CENTER_CROP
                headScopeImageView?.visibility = View.VISIBLE
                bodyIndicatorImageView?.visibility = View.GONE
            }
        }
        
        // 应用UI状态
        updateUIVisibilityAfterDoubleClick()
    }
    
    private fun setupCameraForDeviceType() {
        when (currentDeviceType) {
            DeviceType.aiNeckCV -> {
                // 清理可能存在的姿态检测
                poseLandmarkerHelper?.clearPoseLandmarker()
                poseLandmarkerHelper = null
                
                // 设置人脸检测相机
                setupCamera()
            }
            DeviceType.aiBackCV -> {
                // 初始化姿态检测
                initPoseLandmarker()
                
                // 设置相机但使用姿态检测
                setupCamera()
            }
            else -> {
                // 默认使用人脸检测
                setupCamera()
            }
        }
    }
    
    private fun initPoseLandmarker() {
        try {
            // 清理现有的helper
            poseLandmarkerHelper?.clearPoseLandmarker()
            
            poseLandmarkerHelper = PoseLandmarkerHelper(
                context = applicationContext,
                poseLandmarkerHelperListener = object : PoseLandmarkerHelper.LandmarkerListener {
                    override fun onError(error: String, errorCode: Int) {
                        LogUtil.e("Pose landmarker error: $error")
                        // 可以考虑重新初始化或切换到其他检测方式
                    }

                    override fun onResults(resultBundle: PoseLandmarkerHelper.ResultBundle) {
                        mainHandler.post {
                            try {
                                processPoseLandmarkerResults(resultBundle)
                            } catch (e: Exception) {
                                LogUtil.e("Error processing pose landmarker results: ${e.message}")
                                e.printStackTrace()
                            }
                        }
                    }
                }
            )
            LogUtil.d("PoseLandmarkerHelper initialized successfully")
        } catch (e: Exception) {
            LogUtil.e("Error initializing PoseLandmarkerHelper: ${e.message}")
            e.printStackTrace()
            poseLandmarkerHelper = null
        }
    }

    private fun setupCamera() {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(this)
        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()
            bindCameraUseCases()
        }, ContextCompat.getMainExecutor(this))
    }

    @OptIn(ExperimentalGetImage::class)
    private fun bindCameraUseCases() {
        val preview = Preview.Builder()
            .setTargetAspectRatio(AspectRatio.RATIO_4_3)
            .build()
            .also {
                it.setSurfaceProvider(previewView.surfaceProvider)
            }

        val cameraSelector = CameraSelector.Builder()
            .requireLensFacing(CameraSelector.LENS_FACING_FRONT)
            .build()

        val imageAnalysis = ImageAnalysis.Builder()
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .setOutputImageFormat(
                when (currentDeviceType) {
                    DeviceType.aiBackCV -> ImageAnalysis.OUTPUT_IMAGE_FORMAT_RGBA_8888 
                    else -> ImageAnalysis.OUTPUT_IMAGE_FORMAT_YUV_420_888
                }
            )
            .build()

        // 根据设备类型设置不同的图像分析器
        when (currentDeviceType) {
            DeviceType.aiNeckCV -> {
                setupFaceDetectionAnalyzer(imageAnalysis)
            }
            DeviceType.aiBackCV -> {
                setupPoseLandmarkerAnalyzer(imageAnalysis)
            }
            else -> {
                setupFaceDetectionAnalyzer(imageAnalysis)
            }
        }

        try {
            LogUtil.i("Binding camera use cases")
            cameraProvider.unbindAll()
            cameraProvider.bindToLifecycle(
                this,
                cameraSelector,
                preview,
                imageAnalysis
            )
        } catch (e: Exception) {
            LogUtil.e("Use case binding failed: ${e.message}")
            e.printStackTrace()
        }
    }

    @OptIn(ExperimentalGetImage::class)
    private fun setupFaceDetectionAnalyzer(imageAnalysis: ImageAnalysis) {
        imageAnalysis.setAnalyzer(cameraExecutor) { imageProxy ->
            val mediaImage = imageProxy.image
            if (mediaImage != null) {
                val image = InputImage.fromMediaImage(mediaImage, imageProxy.imageInfo.rotationDegrees)
                faceDetector.process(image)
                    .addOnSuccessListener { faces ->
                        mainHandler.post {
                            if (faces.isEmpty()) {
                                // 无人脸检测到时
                                if (faceDetected) {
                                    faceDetected = false
                                    showHeadScopeIndicator(true)
                                }
                            } else {
                                // 有人脸检测到时
                                if (!faceDetected) {
                                    faceDetected = true
                                    showHeadScopeIndicator(false)
                                }
                                
                                // 只有在非遮罩状态下才更新角度
                                if (!isMaskActive) {
                                    faces.forEach { face ->
                                        val eulerX = face.headEulerAngleX * -1f
                                        val eulerY = face.headEulerAngleY
                                        val eulerZ = face.headEulerAngleZ
                                        updateAnalysisText(eulerX)
                                        updateHeadEulerAngles(eulerX, eulerY, eulerZ)
                                    }
                                }
                            }
                        }
                    }
                    .addOnFailureListener { e ->
                        LogUtil.e("Face detection failed $e")
                    }
                    .addOnCompleteListener {
                        imageProxy.close()
                    }
            } else {
                imageProxy.close()
            }
        }
    }

    @OptIn(ExperimentalGetImage::class)
    private fun setupPoseLandmarkerAnalyzer(imageAnalysis: ImageAnalysis) {
        imageAnalysis.setAnalyzer(cameraExecutor) { imageProxy ->
            try {
                // 检查poseLandmarkerHelper是否可用
                val helper = poseLandmarkerHelper
                if (helper != null && !helper.isClose()) {
                    helper.detectLiveStream(imageProxy, true)
                } else {
                    LogUtil.w("PoseLandmarkerHelper is null or closed, skipping detection")
                    imageProxy.close()
                }
            } catch (e: Exception) {
                LogUtil.e("Error in setupPoseLandmarkerAnalyzer: ${e.message}")
                e.printStackTrace()
                try {
                    imageProxy.close()
                } catch (closeException: Exception) {
                    LogUtil.e("Error closing imageProxy in analyzer: ${closeException.message}")
                }
            }
        }
    }

    private fun processPoseLandmarkerResults(resultBundle: PoseLandmarkerHelper.ResultBundle) {
        try {
            val poseLandmarkerResults = resultBundle.results
            if (poseLandmarkerResults.isEmpty() || poseLandmarkerResults[0].landmarks().isEmpty()) {
                // 无人体检测到时
                if (bodyDetected) {
                    bodyDetected = false
                    showBodyIndicator(true)
                }
                return
            }

            val landmark = poseLandmarkerResults[0].landmarks()[0]
            
            // 检查landmark是否有效
            if (landmark.size < 25) {
                LogUtil.w("Insufficient landmark points: ${landmark.size}")
                return
            }
            
            // 检查关键点的可见性，确保肩膀和髋关节点都清晰可见
            val leftShoulder = landmark[11]
            val rightShoulder = landmark[12]
            val leftHip = landmark[23]
            val rightHip = landmark[24]
            
            // 添加null检查
            if (leftShoulder == null || rightShoulder == null || leftHip == null || rightHip == null) {
                LogUtil.w("Key landmark points are null")
                return
            }
            
            val leftShoulderVisible = leftShoulder.visibility().get() > 0.5f
            val rightShoulderVisible = rightShoulder.visibility().get() > 0.5f
            val leftHipVisible = leftHip.visibility().get() > 0.5f
            val rightHipVisible = rightHip.visibility().get() > 0.5f
            
            // 只有当所有关键点都可见时，才认为人体存在
            if (!(leftShoulderVisible && rightShoulderVisible && leftHipVisible && rightHipVisible)) {
                if (bodyDetected) {
                    bodyDetected = false
                    showBodyIndicator(true)
                }
                return
            }

            // 有人体检测到且关键点都可见时
            if (!bodyDetected) {
                bodyDetected = true
                showBodyIndicator(false)
            }
            
            // 计算上半身倾斜角度 (肩膀中点到臀部中点与垂直方向的夹角)
            val x1 = leftShoulder.x() / 2 + rightShoulder.x() / 2
            val y1 = leftShoulder.y() / 2 + rightShoulder.y() / 2
            val z1 = leftShoulder.z() / 2 + rightShoulder.z() / 2
            val x2 = leftHip.x() / 2 + rightHip.x() / 2
            val y2 = leftHip.y() / 2 + rightHip.y() / 2
            val z2 = leftHip.z() / 2 + rightHip.z() / 2
            
            val abX = x2 - x1
            val abY = y2 - y1
            val abZ = z2 - z1
            val cX = 0.0
            val cY = 1.0
            val cZ = 0.0
            
            // 计算向量AB和向量C的点积
            val dotProduct = abX * cX + abY * cY + abZ * cZ

            // 计算向量AB和向量C的模长
            val abMagnitude = sqrt(abX * abX + abY * abY + abZ * abZ)
            val cMagnitude = sqrt(cX * cX + cY * cY + cZ * cZ)

            // 检查除数是否为零
            if (abMagnitude.toDouble() == 0.0 || cMagnitude == 0.0) {
                LogUtil.w("Invalid magnitude calculation")
                return
            }

            // 计算夹角的余弦值
            val cosTheta = dotProduct / (abMagnitude * cMagnitude)
            
            // 确保cosTheta在有效范围内
            val clampedCosTheta = cosTheta.coerceIn(-1.0, 1.0)
            val angle = (acos(clampedCosTheta) * (180.0 / PI)).toFloat()
            
            // 检查角度是否有效
            if (angle.isNaN() || angle.isInfinite()) {
                LogUtil.w("Invalid angle calculation: $angle")
                return
            }
            
            // 只有在非遮罩状态下才更新角度
            if (!isMaskActive) {
                updateAnalysisText(angle)
                updateBodyTiltAngle(angle)
            }
        } catch (e: Exception) {
            LogUtil.e("Error processing pose landmarker results: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun showHeadScopeIndicator(show: Boolean) {
        if (show) {
            headScopeImageView?.visibility = View.VISIBLE
            startHeadScopeAnimation()
        } else {
            headScopeImageView?.visibility = View.GONE
            stopHeadScopeAnimation()
        }
    }

    private fun startHeadScopeAnimation() {
        // 停止之前的动画
        stopHeadScopeAnimation()
        
        // 创建新的颜色动画
        headScopeAnimator = ValueAnimator.ofObject(
            ArgbEvaluator(),
            Color.TRANSPARENT, // 初始透明色 (0x00000000)
            Color.parseColor("#80476DDC") // 目标颜色 (0x80476DDC)
        ).apply {
            duration = 500
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.REVERSE
            interpolator = LinearInterpolator()
            
            addUpdateListener { animator ->
                val animatedValue = animator.animatedValue as Int
                headScopeImageView?.setColorFilter(animatedValue)
            }
            
            start()
        }
    }

    private fun stopHeadScopeAnimation() {
        headScopeAnimator?.cancel()
        headScopeAnimator = null
    }

    private fun addFloatingCameraView() {
        val displayMetrics = Resources.getSystem().displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels
        LogUtil.i(" FloatingCameraService: screenWidth: $screenWidth, screenHeight: $screenHeight")
        
        floatingView = LayoutInflater.from(this).inflate(R.layout.floating_camera_layout, null)
        previewView = floatingView?.findViewById(R.id.previewView) ?: PreviewView(this).apply {
            implementationMode = PreviewView.ImplementationMode.COMPATIBLE
        }
        analysisText = floatingView?.findViewById(R.id.analysisImageView) ?: TextView(this)
        maskView = floatingView?.findViewById(R.id.maskView)
        togglePreviewButton = floatingView?.findViewById(R.id.togglePreviewButton)
        maskBackgroundImageView = floatingView?.findViewById(R.id.maskBackgroundImageView)
        closeView = floatingView?.findViewById(R.id.closeView)
        headScopeImageView = floatingView?.findViewById(R.id.headScopeImageView)
        bodyIndicatorImageView = floatingView?.findViewById(R.id.bodyIndicatorImageView)

        val windowWidth = 120.dp.toPx().toInt()
        val windowHeight = 180.dp.toPx().toInt()

        // Store original dimensions for scaling
        originalLayoutWidth = windowWidth
        originalLayoutHeight = windowHeight

        layoutParams = WindowManager.LayoutParams(
            windowWidth,
            windowHeight,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O)
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            else
                WindowManager.LayoutParams.TYPE_PHONE,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        ).apply {
            gravity = Gravity.TOP or Gravity.START  // 改为 START 而不是 END
        }

        windowManager.addView(floatingView, layoutParams)

        val floatingCard = floatingView?.findViewById<CardView>(R.id.cardViewContainer)

        @SuppressLint("ClickableViewAccessibility")
        floatingCard?.setOnTouchListener { view, event ->
            when (event.action and MotionEvent.ACTION_MASK) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = layoutParams?.x ?: 0
                    initialY = layoutParams?.y ?: 0
                    initialTouchX = event.rawX
                    initialTouchY = event.rawY
                    mode = DRAG
                    view.performClick() // For accessibility, if not consuming all events
                }
                MotionEvent.ACTION_POINTER_DOWN -> {
                    if (event.pointerCount == 2) {
                        initialPointerDist = calculateDistance(event)
                        if (initialPointerDist > 10f) { // Threshold to start zoom
                            mode = ZOOM
                            startScaleForPinch = currentScale
                        }
                    }
                }
                MotionEvent.ACTION_MOVE -> {
                    if (mode == DRAG && event.pointerCount == 1) {
                        val deltaX = (event.rawX - initialTouchX).toInt()
                        val deltaY = (event.rawY - initialTouchY).toInt()

                        var newX = initialX + deltaX
                        var newY = initialY + deltaY

                        // Use current layoutParams width/height for clamping, which might have changed due to zoom
                        val currentWindowWidth = layoutParams?.width ?: originalLayoutWidth
                        val currentWindowHeight = layoutParams?.height ?: originalLayoutHeight

                        newX = newX.coerceIn(0, screenWidth - currentWindowWidth)
                        newY = newY.coerceIn(0, screenHeight - currentWindowHeight)

                        layoutParams?.x = newX
                        layoutParams?.y = newY
                        windowManager.updateViewLayout(floatingView, layoutParams)
                        // LogUtil.i(" FloatingCameraService: view X: ${layoutParams?.x}, view Y: ${layoutParams?.y} , rawTouchX: ${event.rawX}, rawTouchY: ${event.rawY}")
                    } else if (mode == ZOOM && event.pointerCount >= 2) {
                        val newDist = calculateDistance(event)
                        if (newDist > 10f && initialPointerDist > 0.001f) { // Avoid division by zero or too small
                            val pinchScaleFactor = newDist / initialPointerDist
                            var newCalculatedScale = startScaleForPinch * pinchScaleFactor
                            newCalculatedScale = newCalculatedScale.coerceIn(MIN_SCALE, MAX_SCALE)

                            if (abs(newCalculatedScale - currentScale) > 0.01f) { // Tolerance to avoid jitter
                                currentScale = newCalculatedScale

                                val newWidth = (originalLayoutWidth * currentScale).toInt()
                                val newHeight = (originalLayoutHeight * currentScale).toInt()

                                if (newWidth > 0 && newHeight > 0) { // Ensure dimensions are positive
                                    layoutParams?.width = newWidth
                                    layoutParams?.height = newHeight
                                    windowManager.updateViewLayout(floatingView, layoutParams)
                                }
                            }
                        }
                    }
                }
                MotionEvent.ACTION_UP -> {
                    if (mode == DRAG) { // Process click only if it was primarily a tap/drag, not end of zoom
                        val currentTime = System.currentTimeMillis()
                        val deltaX = abs(event.rawX - initialTouchX)
                        val deltaY = abs(event.rawY - initialTouchY)
                        
                        // 只有在移动距离很小时才认为是点击
                        if (deltaX < 20 && deltaY < 20) {
                            if (currentTime - lastClickTime < 500) {
                                // 双击检测
                                clickCount++
                                if (clickCount >= 2) {
                                    isMaskActive = !isMaskActive
                                    updateUIVisibilityAfterDoubleClick()
                                    clickCount = 0
                                }
                            } else {
                                // 重置点击计数
                                clickCount = 1
                            }
                            lastClickTime = currentTime
                        } else {
                            // 拖拽操作，重置点击计数
                            clickCount = 0
                        }
                    }
                    mode = NONE
                }
                MotionEvent.ACTION_POINTER_UP -> {
                    mode = NONE // Simplifies state, or could transition to DRAG if one finger remains
                    // If transitioning to DRAG, would need to update initialTouchX/Y for the remaining pointer.
                }
            }
            true // Consume the event
        }

        closeView?.bringToFront()
        closeView?.setOnClickListener {
            clickCount = 0 // 重置点击计数
            isUserClosing.postValue(true)
            stopSelf()
        }

        togglePreviewButton?.setOnClickListener {
            if (isMaskActive) {
                isMaskActive = false
                clickCount = 0 // 重置点击计数
                updateUIVisibilityAfterDoubleClick()
            }
        }
    }

    private fun updateUIVisibilityAfterDoubleClick() {
        val closeButton = floatingView?.findViewById<View>(R.id.closeView)

        if (isMaskActive) {
            maskView?.visibility = View.VISIBLE
            closeButton?.visibility = View.VISIBLE
            togglePreviewButton?.text = getString(R.string.open_video)
            // 在遮罩激活时隐藏指示器
            headScopeImageView?.visibility = View.GONE
            bodyIndicatorImageView?.visibility = View.GONE
            stopHeadScopeAnimation()
            stopBodyIndicatorAnimation()
        } else {
            maskView?.visibility = View.GONE
            closeButton?.visibility = View.GONE
            
            // 根据设备类型和检测状态决定显示哪种指示器
            when (currentDeviceType) {
                DeviceType.aiNeckCV -> {
                    // 如果未检测到人脸，则显示头部范围指示器
                    if (!faceDetected) {
                        showHeadScopeIndicator(true)
                    }
                    bodyIndicatorImageView?.visibility = View.GONE
                }
                DeviceType.aiBackCV -> {
                    // 如果未检测到人体，则显示身体指示器
                    if (!bodyDetected) {
                        showBodyIndicator(true)
                    }
                    headScopeImageView?.visibility = View.GONE
                }
                else -> {
                    // 默认使用人脸检测
                    if (!faceDetected) {
                        showHeadScopeIndicator(true)
                    }
                    bodyIndicatorImageView?.visibility = View.GONE
                }
            }
        }
    }

    private fun stopCameraPreview() {
        cameraCaptureSession?.close()
        cameraDevice?.close()
        cameraExecutor.shutdown()
    }

    private fun startForegroundServiceWithNotification() {
        val notificationId = 1
        val channelId = "floating_camera_channel"
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                channelId,
                "Floating Camera Service",
                NotificationManager.IMPORTANCE_LOW
            )
            val manager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }

        // 创建一个启动主活动的PendingIntent
        val intent = Intent(this, activity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )

        // 根据设备类型选择标题
        val deviceTypeText = when (currentDeviceType) {
            DeviceType.aiNeckCV -> getString(R.string.ai_neck_cv)
            DeviceType.aiBackCV -> getString(R.string.ai_back_cv)
            else -> getString(R.string.floating_camera_notification_title)
        }

        // 创建并更新通知
        val notification = NotificationCompat.Builder(this, channelId)
            .setContentTitle(deviceTypeText)
            .setContentText(getString(R.string.floating_camera_angle_text, currentDisplayAngle.toInt()))
            .setSmallIcon(R.mipmap.ic_launcher_round)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()

        startForeground(notificationId, notification)
        
        // 注册角度变化监听，更新通知
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        when (currentDeviceType) {
            DeviceType.aiNeckCV -> {
                headAngleObserver = androidx.lifecycle.Observer { angle ->
                    updateNotification(notificationManager, notificationId, deviceTypeText, angle)
                }
                headEulerAngleX.observeForever(headAngleObserver!!)
            }
            DeviceType.aiBackCV -> {
                bodyAngleObserver = androidx.lifecycle.Observer { angle ->
                    updateNotification(notificationManager, notificationId, deviceTypeText, angle)
                }
                bodyTiltAngle.observeForever(bodyAngleObserver!!)
            }
            else -> {
                headAngleObserver = androidx.lifecycle.Observer { angle ->
                    updateNotification(notificationManager, notificationId, deviceTypeText, angle)
                }
                headEulerAngleX.observeForever(headAngleObserver!!)
            }
        }
    }
    
    private fun updateNotification(notificationManager: NotificationManager, notificationId: Int, title: String, angle: Float) {
        // 创建一个启动主活动的PendingIntent
        val intent = Intent(this, activity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        }
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) PendingIntent.FLAG_IMMUTABLE else 0
        )
        
        val notification = NotificationCompat.Builder(this, "floating_camera_channel")
            .setContentTitle(title)
            .setContentText(getString(R.string.floating_camera_angle_text, angle.toInt()))
            .setSmallIcon(R.mipmap.ic_launcher_round)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()

        notificationManager.notify(notificationId, notification)
    }

    private fun updateAnalysisText(newAngle: Float) {
        // 取消正在进行的动画
        currentAngleAnimator?.cancel()
        
        // 创建新的动画
        currentAngleAnimator = ValueAnimator.ofFloat(currentDisplayAngle, newAngle).apply {
            duration = 300 // 保持和 Compose 中一样的过渡时间
            interpolator = DecelerateInterpolator()
            
            addUpdateListener { animator ->
                val animatedValue = animator.animatedValue as Float
                currentDisplayAngle = animatedValue
                analysisText.text = getString(R.string.floating_camera_angle_text, animatedValue.toInt())
            }
            
            start()
        }
    }

    private fun updateHeadEulerAngles(eulerX: Float, eulerY: Float, eulerZ: Float) {
        headEulerAngleX.postValue(eulerX)
        headEulerAngleY.postValue(eulerY)
        headEulerAngleZ.postValue(eulerZ)
        
        // 记录角度到数据库
        serviceScope.launch(Dispatchers.IO) {
            database.angleDao().add(
                Angle(
                    uuid = user.uuid,
                    angle = eulerX.toInt(),
                    type = currentDeviceType.name,
                    timestamp = System.currentTimeMillis() / 1000
                )
            )
        }
    }

    // 定期上传角度数据到服务器
    private fun startPeriodicAngleUpload() {
        serviceScope.launch(Dispatchers.IO) {
            while (true) {
                delay(30000) // 每30秒上传一次
                postAngle()
            }
        }
    }

    private fun postAngle() {
        serviceScope.launch(Dispatchers.IO) {
            val angleList = database.angleDao().find(user.uuid, currentDeviceType.name)
            if (angleList.isNotEmpty()) {
                NetWork.postAngle(currentDeviceType, angleList) {
                    database.angleDao().clear(user.uuid, currentDeviceType.name, angleList.last().timestamp)
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        try {
            isServiceRunning.postValue(false)
            lifecycleRegistry.currentState = Lifecycle.State.DESTROYED
            
            // 先停止相机预览以防止新的分析请求
            stopCameraPreview()
            
            // 清理姿态检测器
            try {
                poseLandmarkerHelper?.clearPoseLandmarker()
                poseLandmarkerHelper = null
            } catch (e: Exception) {
                LogUtil.e("Error clearing poseLandmarkerHelper: ${e.message}")
            }
            
            // 移除浮窗
            floatingView?.let {
                try {
                    windowManager.removeView(it)
                } catch (e: Exception) {
                    LogUtil.e("Error removing floating view: ${e.message}")
                }
            }
            
            // 停止所有动画
            try {
                stopHeadScopeAnimation()
                stopBodyIndicatorAnimation()
                currentAngleAnimator?.cancel()
            } catch (e: Exception) {
                LogUtil.e("Error stopping animations: ${e.message}")
            }
            
            // 上传最后的数据
            try {
                postAngle()
            } catch (e: Exception) {
                LogUtil.e("Error posting final angle: ${e.message}")
            }
            
            // 取消所有协程
            try {
                serviceScope.cancel()
            } catch (e: Exception) {
                LogUtil.e("Error cancelling service scope: ${e.message}")
            }
            
            // 移除通知更新的观察者
            try {
                headAngleObserver?.let { headEulerAngleX.removeObserver(it) }
                bodyAngleObserver?.let { bodyTiltAngle.removeObserver(it) }
            } catch (e: Exception) {
                LogUtil.e("Error removing observers: ${e.message}")
            }
            
        } catch (e: Exception) {
            LogUtil.e("Error in onDestroy: ${e.message}")
            e.printStackTrace()
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null
    override val lifecycle: Lifecycle
        get() = lifecycleRegistry

    private fun calculateDistance(event: MotionEvent): Float {
        if (event.pointerCount < 2) return 0f
        val x = event.getX(0) - event.getX(1)
        val y = event.getY(0) - event.getY(1)
        return sqrt(x * x + y * y)
    }

    private fun showBodyIndicator(show: Boolean) {
        if (show) {
            bodyIndicatorImageView?.visibility = View.VISIBLE
            startBodyIndicatorAnimation()
        } else {
            bodyIndicatorImageView?.visibility = View.GONE
            stopBodyIndicatorAnimation()
        }
    }

    private fun startBodyIndicatorAnimation() {
        // 停止之前的动画
        stopBodyIndicatorAnimation()
        
        // 创建新的颜色动画
        bodyIndicatorAnimator = ValueAnimator.ofObject(
            ArgbEvaluator(),
            Color.TRANSPARENT, // 初始透明色 (0x00000000)
            Color.parseColor("#80476DDC") // 目标颜色 (0x80476DDC)
        ).apply {
            duration = 500
            repeatCount = ValueAnimator.INFINITE
            repeatMode = ValueAnimator.REVERSE
            interpolator = LinearInterpolator()
            
            addUpdateListener { animator ->
                val animatedValue = animator.animatedValue as Int
                bodyIndicatorImageView?.setColorFilter(animatedValue)
            }
            
            start()
        }
    }

    private fun stopBodyIndicatorAnimation() {
        bodyIndicatorAnimator?.cancel()
        bodyIndicatorAnimator = null
    }
    
    private fun updateBodyTiltAngle(angle: Float) {
        bodyTiltAngle.postValue(angle)
        
        // 记录角度到数据库
        serviceScope.launch(Dispatchers.IO) {
            database.angleDao().add(
                Angle(
                    uuid = user.uuid,
                    angle = angle.toInt(),
                    type = currentDeviceType.name,
                    timestamp = System.currentTimeMillis() / 1000
                )
            )
        }
    }

}