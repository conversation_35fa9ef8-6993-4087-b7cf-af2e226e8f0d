package org.aihealth.ineck.notification

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import androidx.core.app.NotificationCompat
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import org.aihealth.ineck.R
import org.aihealth.ineck.network.apiService
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.LogUtil
import java.io.File
import kotlin.math.min

class VideoUploadService : Service() {
    private val serviceScope = CoroutineScope(Dispatchers.IO + Job())
    private val notificationManager by lazy {
        getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }
    private val mainHandler = Handler(Looper.getMainLooper())
    private var lastProgress = 0

    companion object {
        const val ACTION_START_UPLOAD = "action_start_upload"
        const val ACTION_UPLOAD_PROGRESS = "org.aihealth.ineck.action.UPLOAD_PROGRESS"
        const val EXTRA_VIDEO_PATH = "extra_video_path"
        private const val CHANNEL_ID = "video_upload_channel"
        private const val NOTIFICATION_ID = 2
    }

    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
        startForegroundSafely()
    }

    private fun startForegroundSafely() {
        try {
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_sys_upload)
                .setContentTitle(getString(R.string.upload_video_title))
                .setProgress(100, 0, false)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .build()
            
            startForeground(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            LogUtil.e("Error starting foreground service: ${e.message}")
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_UPLOAD -> {
                val videoPath = intent.getStringExtra(EXTRA_VIDEO_PATH)
                if (videoPath != null) {
                    // Send initial 0% progress
                    updateProgress(0)
                    startUpload(videoPath)
                }
            }
        }
        return START_NOT_STICKY
    }

    private fun startUpload(videoPath: String) {
        serviceScope.launch {
            try {
                val videoFile = File(videoPath)
                if (!videoFile.exists()) {
                    throw Exception("Video file not found")
                }

                // Create request body with progress updates
                val requestBody = ProgressRequestBody(videoFile, "video/mp4") { progress ->
                    val progressInt = (progress * 100).toInt()
                    
                    // Only update if progress changed significantly
                    if (progressInt > lastProgress) {
                        lastProgress = progressInt
                        mainHandler.post {
                            updateProgress(progressInt)
                        }
                    }
                }

                // Create multipart form data
                val multipartBody = MultipartBody.Part.createFormData(
                    "file",
                    videoFile.name,
                    requestBody
                )

                // Upload video
                val response = withContext(Dispatchers.IO) {
                    apiService.uploadVideo(multipartBody)
                }

                if (response.isSuccessful) {
                    updateProgress(100)
                    // Small delay to let UI process final progress update
                    delay(500)
                    
                    withContext(Dispatchers.Main) {
                        DialogUtil.showToast("Video uploaded successfully")
                    }
                    notifyUploadComplete()
                    // Delete the local file after successful upload
                    videoFile.delete()
                } else {
                    throw Exception("Upload failed with code: ${response.code()}")
                }
            } catch (e: Exception) {
                LogUtil.e("Upload failed: ${e.message}")
                notifyUploadFailed()
            } finally {
                stopSelf()
            }
        }
    }

    private fun updateProgress(progress: Int) {
        try {
            // Cap progress to 100%
            val safeProgress = min(progress, 100)
            
            // Update notification
            val updatedNotification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_sys_upload)
                .setContentTitle(getString(R.string.upload_video_title))
                .setContentText("$safeProgress%")
                .setProgress(100, safeProgress, false)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .setOngoing(true)
                .build()
                
            notificationManager.notify(NOTIFICATION_ID, updatedNotification)

            // Log progress
            LogUtil.i("Sending upload progress: $safeProgress%")
            
            // 安全发送广播，确保只在应用内部接收
            val intent = Intent(ACTION_UPLOAD_PROGRESS).apply {
                setPackage(packageName)
                putExtra("progress", safeProgress)
                // Add flags to ensure delivery
                addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
            }
            sendBroadcast(intent)
        } catch (e: Exception) {
            LogUtil.e("Error updating notification: ${e.message}")
        }
    }

    private fun notifyUploadComplete() {
        try {
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_sys_upload_done)
                .setContentTitle(getString(R.string.upload_complete))
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true)
                .build()
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            LogUtil.e("Error showing completion notification: ${e.message}")
        }
    }

    private fun notifyUploadFailed() {
        try {
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(android.R.drawable.stat_notify_error)
                .setContentTitle(getString(R.string.upload_failed))
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setAutoCancel(true)
                .build()
            notificationManager.notify(NOTIFICATION_ID, notification)
        } catch (e: Exception) {
            LogUtil.e("Error showing failure notification: ${e.message}")
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                val channel = NotificationChannel(
                    CHANNEL_ID,
                    getString(R.string.video_upload_channel),
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "Channel for video upload progress notifications"
                    setShowBadge(false)
                }
                notificationManager.createNotificationChannel(channel)
            } catch (e: Exception) {
                LogUtil.e("Error creating notification channel: ${e.message}")
            }
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null
}

class ProgressRequestBody(
    private val file: File,
    private val contentType: String,
    private val onProgress: (Float) -> Unit
) : okhttp3.RequestBody() {
    override fun contentType() = contentType.toMediaTypeOrNull()

    override fun contentLength() = file.length()

    override fun writeTo(sink: okio.BufferedSink) {
        val fileLength = file.length()
        val buffer = ByteArray(DEFAULT_BUFFER_SIZE)
        file.inputStream().use { inputStream ->
            var uploaded = 0L
            var read: Int
            var lastReportedProgress = 0f
            
            while (inputStream.read(buffer).also { read = it } != -1) {
                sink.write(buffer, 0, read)
                uploaded += read
                
                val currentProgress = uploaded.toFloat() / fileLength
                // Only report progress if it changed significantly (at least 1%)
                if (currentProgress - lastReportedProgress >= 0.01f) {
                    onProgress(currentProgress)
                    lastReportedProgress = currentProgress
                }
            }
            
            // Ensure 100% is reported
            if (lastReportedProgress < 1f) {
                onProgress(1f)
            }
        }
    }

    companion object {
        private const val DEFAULT_BUFFER_SIZE = 2048
    }
} 