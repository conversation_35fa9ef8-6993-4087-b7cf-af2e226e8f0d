<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"> <!-- Google connect Sdk minSdkVersion 26 -->
    <uses-sdk tools:overrideLibrary="androidx.health.connect.client" />

    <!-- 网络数据请求 -->
    <uses-permission android:name="android.permission.INTERNET" /> <!-- 网络连接状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Wi-Fi网络状态 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 表示应用在具备这项指定功能的设备上会使用该功能，但应用被设计为可以根据需要在不使用这项指定功能的情况下正常工作 -->
    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.telephony"
        android:required="false" /> <!-- 相机权限 -->
    <uses-permission android:name="android.permission.CAMERA" /> <!-- 麦克风权限 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 通知权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Android 13（API 级别 33）及更高版本支持用于从应用发送非豁免（包括前台服务 [FGS]）通知的运行时权限 -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /> <!-- Android 10 引入 前台服务的类型 Android 14 强制要求 -->
    <!-- 前台服务 && 数据同步 && 摄像头 && 蓝牙设备连接 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CONNECTED_DEVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_HEALTH" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_MICROPHONE"
        tools:node="remove" />
    <uses-permission
        android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"
        tools:node="remove" />
    <uses-permission android:name="android.permission.CALL_PHONE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_PHONE_CALL" />
    <!-- Required for using FOREGROUND_SERVICE_PHONE_CALL in Android 14+ -->
    <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_CAMERA" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.PROJECT_MEDIA" /> <!-- Android 12 及以上 蓝牙权限 -->
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" /> <!-- 如果您的应用与已配对的蓝牙设备通信或者获取当前手机蓝牙是否打开 -->
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" /> <!-- 如果您的应用查找蓝牙设备（如蓝牙低功耗 (BLE) 外围设备） -->
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation"
        tools:targetApi="s" />
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        android:maxSdkVersion="30" /> <!-- 如果您的应用支持某项服务并且可以在 Android 10（API 级别 29）或 Android 11 上运行 -->
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" /> <!-- Android 9（API 级别 28）或更低版本， -->
    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        android:maxSdkVersion="28" /> <!-- 使设备保持唤醒状态 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <queries>

        <!-- allows app to access Facebook app features -->
        <provider android:authorities="com.facebook.katana.provider.PlatformProvider" />
        <!-- allows sharing to Messenger app -->
        <provider android:authorities="com.facebook.orca.provider.PlatformProvider" />
    </queries>
    <queries>
        <package android:name="org.aihealth.ineck" />
    </queries> <!-- 声明 微信 软件包名称 -->
    <queries>
        <package android:name="com.tencent.mm" /> <!-- 指定微信包名 -->
    </queries>

    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="29" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" /> <!-- API 28 或更低级别 -->
    <uses-permission android:name="android.permission.BODY_SENSORS" /> <!-- 以 API 级别 29 或更高级别为目标平台 -->
    <uses-permission android:name="android.permission.ACTIVITY_RECOGNITION" /> <!-- 以 API 级别 28 或更低级别为目标平台 -->
    <uses-permission android:name="android.gms.permission.ACTIVITY_RECOGNITION" />
    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS" />
    <uses-permission android:name="com.android.vending.BILLING" /> <!-- Google Health Connect Sdk -->
    <!-- step && calories -->
    <!-- <uses-permission android:name="android.permission.health.READ_STEPS"/> -->
    <!-- <uses-permission android:name="androidx.health.permission.TotalCaloriesBurned.READ"/> -->
    <!-- 声明 Health Connect 软件包名称 -->
    <!-- <queries> -->
    <!-- <package android:name="com.google.android.apps.healthdata" /> -->
    <!-- </queries> -->
    <!-- Add broadcast permissions -->
    <permission
        android:name="org.aihealth.ineck.permission.RECEIVE_UPLOAD_PROGRESS"
        android:protectionLevel="signature" />

    <uses-permission android:name="org.aihealth.ineck.permission.RECEIVE_UPLOAD_PROGRESS" />

    <application
        android:name=".BaseApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:enableOnBackInvokedCallback="true"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:usesCleartextTraffic="true">
        <activity
            android:name=".MainActivity"
            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|smallestScreenSize|uiMode|locale|layoutDirection"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/Launcher"
            android:windowSoftInputMode="adjustResize">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>

            <meta-data
                android:name="android.app.lib_name"
                android:value="" />

            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />
                <data android:host="medical.aihnet.com" />
                <data android:pathPrefix="/follow" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data
                    android:host="${applicationId}"
                    android:path="/thirdpush"
                    android:scheme="agoo" />
            </intent-filter>
        </activity>
        <activity
            android:name=".qrcode.QRCodeActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:theme="@style/Theme.AIH_User" />
        <activity
            android:name=".wxapi.WXEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="org.aihealth.ineck"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="org.aihealth.ineck"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
        <!--        <activity-->
        <!--            android:name="com.facebook.FacebookActivity"-->
        <!--            android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation" />-->
        <!--        <activity-->
        <!--            android:name="com.facebook.CustomTabActivity"-->
        <!--            android:exported="true">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="android.intent.action.VIEW" />-->
        <!---->
        <!--                <category android:name="android.intent.category.DEFAULT" />-->
        <!--                <category android:name="android.intent.category.BROWSABLE" />-->
        <!---->
        <!--                <data android:scheme="@string/fb_login_protocol_scheme" />-->
        <!--            </intent-filter>-->
        <!--        </activity>-->

        <service
            android:name=".bluetooth.BluetoothService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" />
        <service
            android:name=".bluetooth.BleService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice" />
        <service
            android:name=".bluetooth.DFUService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="connectedDevice"
            android:permission="" />
        <service
            android:name=".service.VideoDownloadService"
            android:exported="false"
            android:foregroundServiceType="dataSync">

            <!-- This is needed for Scheduler -->
            <intent-filter>
                <action android:name="androidx.media3.exoplayer.downloadService.action.RESTART" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </service>
        <!-- StepService 已移除，使用HybridStepTracker替代 -->
        <service
            android:name="androidx.appcompat.app.AppLocalesMetadataHolderService"
            android:enabled="false"
            android:exported="false">
            <meta-data
                android:name="autoStoreLocales"
                android:value="true" />
        </service> <!-- Record -->
        <service
            android:name=".notification.ScreenRecordService"
            android:exported="false"
            android:foregroundServiceType="mediaProjection">
            <intent-filter>
                <action android:name="org.aihealth.ineck.ACTION_UPLOAD_PROGRESS" />
            </intent-filter>
        </service>
        <service
            android:name=".notification.FloatingCameraService"
            android:foregroundServiceType="camera" />
        <service
            android:name=".notification.VideoUploadService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="dataSync" />
        <service
            android:name=".service.FCMService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <service
            android:name=".service.CallService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="phoneCall" />

        <meta-data
            android:name="com.facebook.sdk.ApplicationId"
            android:value="@string/facebook_app_id" />
        <meta-data
            android:name="com.facebook.sdk.ClientToken"
            android:value="@string/facebook_client_token" />
        <meta-data
            android:name="asset_statements"
            android:resource="@string/asset_statements" />
    </application>

</manifest>