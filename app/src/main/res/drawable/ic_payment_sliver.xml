<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="56dp"
    android:height="56dp"
    android:viewportWidth="56"
    android:viewportHeight="56">
  <path
      android:pathData="M0,0h56v56h-56z"
      android:fillColor="#C5C5C5"/>
  <path
      android:pathData="M-10465,-7390h23986v15445h-23986z"
      android:fillColor="#ECECEC"/>
  <path
      android:pathData="M-1221,-1582h1892v2564h-1892z"
      android:strokeWidth="4"
      android:fillColor="#00000000"
      android:strokeColor="#000000"/>
  <group>
    <clip-path
        android:pathData="M3,0L53,0A3,3 0,0 1,56 3L56,53A3,3 0,0 1,53 56L3,56A3,3 0,0 1,0 53L0,3A3,3 0,0 1,3 0z"/>
    <path
        android:pathData="M4.148,-3.635L76,-3.635A14,14 0,0 1,90 10.365L90,42.723A14,14 0,0 1,76 56.723L4.148,56.723A14,14 0,0 1,-9.852 42.723L-9.852,10.365A14,14 0,0 1,4.148 -3.635z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="40.074"
            android:startY="-3.635"
            android:endX="78.331"
            android:endY="52.781"
            android:type="linear">
          <item android:offset="0" android:color="#FFD8E8FA"/>
          <item android:offset="1" android:color="#FFB4CAE6"/>
        </gradient>
      </aapt:attr>
    </path>
    <path
        android:pathData="M19.97,37.727H17.694V26.399H19.97V37.727ZM29.817,32.217C29.21,33.165 28.265,33.634 26.978,33.634H24.369V37.727H22.093V26.399H27.069C28.122,26.399 28.95,26.725 29.549,27.37C30.151,28.015 30.45,28.9 30.45,30.028C30.454,30.822 30.242,31.553 29.817,32.217ZM4.994,37.727V13.605C4.994,13.505 4.916,13.424 4.821,13.424H3.048C2.953,13.424 2.875,13.343 2.875,13.244V11.393C2.875,11.294 2.953,11.213 3.048,11.213H11.188C11.284,11.213 11.362,11.294 11.362,11.393V22.261L21.994,11.267C22.024,11.235 22.072,11.213 22.115,11.213H30.043C30.199,11.213 30.272,11.407 30.164,11.52L4.994,37.727ZM27.711,28.719C27.399,28.48 26.887,28.358 26.176,28.358H24.369V31.675H26.48C27.208,31.675 27.702,31.445 27.966,30.985C28.11,30.732 28.179,30.375 28.179,29.915C28.179,29.36 28.023,28.963 27.711,28.719Z">
      <aapt:attr name="android:fillColor">
        <gradient 
            android:startX="6.5"
            android:startY="11"
            android:endX="40.5"
            android:endY="38"
            android:type="linear">
          <item android:offset="0" android:color="#FFC8DEF8"/>
          <item android:offset="1" android:color="#FFBFD8F6"/>
        </gradient>
      </aapt:attr>
    </path>
  </group>
</vector>
